<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-if="permission['overhaul-work-orders-batchAudit']"
        type="primary"
        size="small"
        @click="handleBatchAudit('agree')"
        >批量审核确认</el-button
      >
      <el-button
        v-if="permission['overhaul-work-orders-batchAudit']"
        type="danger"
        size="small"
        @click="handleBatchAudit('refuse')"
        >批量驳回</el-button
      >

      <!-- <el-button
        icon="el-icon-upload2"
        type="primary"
        v-if="permission['overhaul-work-orders-export']"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
      <!--      <el-button-->
      <!--        icon="el-icon-upload2"-->
      <!--        type="success"-->
      <!--        size="small"-->
      <!--        @click="timeNow"-->
      <!--        >执行定时任务</el-button-->
      <!--      >-->
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        :selectable="(row) => row.status === 5"
      ></el-table-column>
      <el-table-column
        prop="no"
        label="工单编号"
        align="center"
        width="130"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.no || '-' }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="orderName"
        label="计划名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.orderName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="orderName"
        label="设备编号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentCode || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeDeptName"
        label="检修部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.executeDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="责任人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="startTime"
        label="开始时间"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="endTime"
        label="结束时间"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="cycleTypeName"
        label="检修周期"
        align="center"
        show-overflow-tooltip
        width="70px"
      >
        <template v-slot="{ row }">{{ row.cycleTypeName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="检修人员"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="overhaulResult"
        label="检修结果"
        align="center"
        width="70px"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.overhaulResult || '-' }}</template>
      </el-table-column>
      <el-table-column label="审核人员" align="center" show-overflow-tooltip>
        <template v-slot="{ row }">{{
          (row.isNeedApproval && row.approvalUserName) || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="90px"
      >
        <template v-slot="{ row }">
          <i
            :style="`color:${overhaulOrderStatusColor(
              row.status
            )};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        key="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createUserName || '-' }}</template>
      </el-table-column> -->
      <el-table-column
        key="createTime"
        label="创建时间"
        align="center"
        width="136px"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createTime || '-' }}</template>
      </el-table-column>
      <!-- <el-table-column
        key="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="updateTime"
        label="更新时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateTime || '-' }}</template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <section class="cell-operate-class">
            <el-button type="text" size="small" @click="detail(scope.row)"
              >查看</el-button
            >
            <!--   执行中1、已完成2、已超期3、超期完成4、待确认5、已驳回6、已关闭7        -->
            <el-button
              v-if="
                Number(scope.row.status) === 5 &&
                permission['overhaul-work-orders-audit']
              "
              type="text"
              size="small"
              @click="examine(scope.row)"
              >审核确认</el-button
            >
            <el-button
              v-if="
                Number(scope.row.status) === 5 &&
                permission['overhaul-work-orders-audit']
              "
              type="text"
              size="small"
              @click="turnDown(scope.row)"
              >驳回</el-button
            >
            <el-button
              type="text"
              size="small"
              v-if="
                [1, 6, 3].includes(scope.row.status) &&
                permission['overhaul-work-orders-overhaul']
              "
              @click="overHaulHandle(scope.row)"
              >检修</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />

    <detail-index ref="detailIndex"></detail-index>
    <!--     驳回-->
    <turn-down ref="turnDown" @success="getList"></turn-down>
    <PerformOverhaul ref="performOverhaul" @success="getList"></PerformOverhaul>
  </basic-container>
</template>

<script>
  import TurnDown from './operate/turn-down.vue';
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import PerformOverhaul from './perform-overhaul/index.vue'; // 查看详情页面
  import Search from './search';
  import Pagination from '@/components/pagination';
  import {
    confirmCheckRepairOrderApi,
    batchAuditOrder,
    getCheckRepairOrderPageApi
  } from '@/api/equiment-full-life-api/repair';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  import { mapGetters } from 'vuex';
  import { manualRepairTaskApi } from '@/api/equiment-full-life-api/common';
  import { overhaulOrderStatusColor } from '@/views/equiment-full-life-cycle/repair/overhaul/util';

  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      DetailIndex,
      Pagination,
      TurnDown,
      PerformOverhaul
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [{}],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {},
        multipleSelection: []
      };
    },
    activated() {
      const { fromAssist, status } = this.$route.params;
      if (fromAssist) {
        let fields = { field: 'status', value: status };
        this.$refs.search.setFields(fields);
      } else {
        this.getList();
      }
    },
    mounted() {
      // this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },
    computed: {
      ...mapGetters(['permission'])
    },
    methods: {
      overhaulOrderStatusColor,
      // 批量审核
      handleBatchAudit(type) {
        console.log(this.multipleSelection);
        if (this.multipleSelection.length === 0) {
          return this.$message.warning('请选择至少一条工单');
        }

        if (type === 'agree') {
          this.handleBatchArgee();
        } else {
          this.$refs['turnDown'].show(this.multipleSelection, true);
        }
      },
      // 批量审核确认
      handleBatchArgee() {
        this.$confirm(`确认选中的工单结果符合标准？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await batchAuditOrder({
                orderIds: this.multipleSelection.map((item) => item.id),
                status: 2
              });
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },
      // 表格多选回调
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      overHaulHandle(row) {
        this.$refs.performOverhaul.show(row.no);
      },
      async timeNow() {
        try {
          await manualRepairTaskApi();
          await this.getList();
        } catch (e) {
          console.error(e);
        }
      },
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/overhaul-order/export-order?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/overhaul-order/export-order?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '检修工单.xlsx'
        );
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getCheckRepairOrderPageApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          // this.list = [{}];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },

      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      },
      //  驳回
      turnDown(row) {
        this.$refs['turnDown'].show(row);
      },
      examine(row) {
        this.$confirm(`确认检修结果符合标准？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await confirmCheckRepairOrderApi({
                id: row.id,
                status: 2
              });
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            // this.$message({
            //   type: 'info',
            //   message: '已取消'
            // });
          });
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
