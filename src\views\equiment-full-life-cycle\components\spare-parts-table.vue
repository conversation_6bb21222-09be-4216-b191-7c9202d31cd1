<template>
  <el-form
    :model="formData"
    inline
    label-suffix=""
    ref="listForm"
    size="mini"
    :show-message="true"
  >
    <el-table
      class="table"
      :data="formData.materialList"
      border
      :header-cell-style="{ background: '#fafafa' }"
      size="mini"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="name"
        label="名称"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="model"
        label="型号"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.model || '-' }}</template>
      </el-table-column>
      <el-table-column prop="count" label="数量" align="center" width="220px">
        <template v-slot="scope">
          <el-form-item
            v-if="!detail"
            :prop="'materialList.' + scope.$index + '.count'"
            :rules="{
              required: true,
              message: '请输入数量',
              trigger: 'blur'
            }"
            label=""
          >
            <el-input-number
              size="small"
              :controls="false"
              v-model="scope.row.count"
              :min="1"
              :max="9999"
              placeholder="请输入数量"
            ></el-input-number>
          </el-form-item>
          <div v-else>{{ scope.row.count }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="!detail"
        prop="currentAmount"
        label="当前库存"
        align="center"
      >
        <template v-slot="{ row }">{{ row.currentAmount || '0' }}</template>
      </el-table-column>
      <el-table-column v-if="!detail" prop="model" label="操作" align="center">
        <template v-slot="scope">
          <el-button
            type="danger"
            link
            size="mini"
            @click="$emit('del', scope.$index)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script>
  export default {
    props: {
      formData: {
        type: Object,
        default: () => {
          return {
            materialList: []
          };
        }
      },
      detail: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      validate() {
        return new Promise((resolve) => {
          this.$refs.listForm.validate((valid) => {
            resolve(valid);
          });
        });
      }
    }
  };
</script>

<style lang="scss" scoped></style>
