<template>
  <el-form
    ref="form"
    size="small"
    :model="form"
    :rules="rules"
    label-suffix=": "
    label-width="90px"
  >
    <el-collapse v-model="collapse" style="border: 0">
      <el-collapse-item name="1" style="padding: 0 20px 0 0">
        <template slot="title">
          <div class="collapse_title">
            <i
              class="el-icon-user-solid"
              style="margin-right: 10px; font-size: 20px"
            ></i
            >基础信息
          </div>
        </template>
        <el-row style="margin-top: 20px">
          <el-col :span="24" v-if="website.tenantMode">
            <el-form-item label="所属租户" prop="tenantId">
              <avue-input-tree
                v-model="form.tenantId"
                type="tree"
                placeholder="请选择所属租户"
                style="width: 100%"
                :dic="tenantData"
              ></avue-input-tree>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录账号" prop="account">
              <el-input placeholder="请输入登录账号" v-model="form.account" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户平台" prop="userType">
              <el-select
                v-model="form.userType"
                placeholder="请选择用户平台"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in systemDicts.type.user_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="密码"
              prop="password"
              v-if="drawerType === 'add'"
            >
              <el-input placeholder="请输入密码" v-model="form.password" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="确认密码"
              prop="password2"
              v-if="drawerType === 'add'"
            >
              <el-input placeholder="请输入确认密码" v-model="form.password2" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item name="2" style="padding: 0 20px 0 0">
        <template slot="title">
          <div class="collapse_title">
            <i
              class="el-icon-s-order"
              style="margin-right: 10px; font-size: 20px"
            ></i
            >详细信息
          </div>
        </template>
        <el-row style="margin-top: 20px">
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="name">
              <el-input placeholder="请输入用户昵称" v-model="form.name" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户姓名" prop="realName">
              <el-input placeholder="请输入用户姓名" v-model="form.realName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              <el-input placeholder="请输入手机号码" v-model="form.phone" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电子邮箱" prop="email">
              <el-input placeholder="请输入电子邮箱" v-model="form.email" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户性别" prop="sex">
              <el-select
                v-model="form.sex"
                placeholder="请选择用户性别"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in sexDicData"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户生日" prop="birthday">
              <el-date-picker
                v-model="form.birthday"
                type="date"
                placeholder="请选择用户生日"
                format="yyyy-MM-dd hh:mm:ss"
                valueFormat="yyyy-MM-dd hh:mm:ss"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item name="3" style="padding: 0 20px 0 0">
        <template slot="title">
          <div class="collapse_title">
            <i
              class="el-icon-s-custom"
              style="margin-right: 10px; font-size: 20px"
            ></i
            >职责信息
          </div>
        </template>
        <el-row style="margin-top: 20px">
          <el-col :span="12">
            <el-form-item label="用户编号" prop="code">
              <el-input placeholder="请输入用户编号" v-model="form.code" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属角色" prop="roleId">
              <avue-input-tree
                v-model="form.roleId"
                type="tree"
                check-strictly
                multiple
                placeholder="请选择所属角色"
                style="width: 100%"
                :dic="roleData"
              ></avue-input-tree>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属部门" prop="deptId">
              <avue-input-tree
                v-model="form.deptId"
                type="tree"
                check-strictly
                multiple
                placeholder="请选择所属部门"
                style="width: 100%"
                :dic="deptData"
              ></avue-input-tree>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属岗位" prop="postId">
              <avue-input-tree
                v-model="form.postId"
                type="tree"
                check-strictly
                multiple
                placeholder="请选择所属岗位"
                style="width: 100%"
                :dic="postData"
              ></avue-input-tree>
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
  </el-form>
</template>
<script>
  import { getTenantData } from '@/api/system/tenant';
  import { getDeptTree } from '@/api/system/dept';
  import { getRoleTree } from '@/api/system/role';
  import { getPostList } from '@/api/system/post';
  import website from '@/config/website';
  import { formatTreeData } from '@/util/util';
  import { rules, sexDicData } from './data';
  import { cloneDeep } from 'lodash';

  export default {
    name: 'user-form',
    systemDicts: ['user_type'],
    props: {
      drawerType: String,
      formData: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    watch: {
      formData: {
        handler(val) {
          this.form = cloneDeep(val);
        },
        deep: true
      },
      'form.tenantId'() {
        if (this.form.tenantId !== '') {
          this.initData(this.form.tenantId);
        }
      }
    },
    data() {
      const validatePass = (rule, value, callback) => {
        if (value === '' || value === undefined) {
          callback(new Error('请输入密码'));
        } else if (
          !/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-z0-9])(?=.*[^A-Z0-9])(?=.*[^a-zA-Z0-9])(?=.*[^a-zA-Z]).{8,100}$/.test(
            value
          )
        ) {
          callback(
            new Error(
              '密码最低8位,须包含至少1个数字、1个大写字母、1个小写字母、1个特殊字符'
            )
          );
        } else {
          callback();
        }
      };
      const validatePass2 = (rule, value, callback) => {
        if (value === '' || value === undefined) {
          callback(new Error('请再次输入密码'));
        } else if (value !== this.form.password) {
          callback(new Error('两次输入密码不一致!'));
        } else {
          callback();
        }
      };
      return {
        rules: {
          ...rules,
          password: [
            { required: true, validator: validatePass, trigger: 'blur' }
          ],
          password2: [
            {
              required: true,
              validator: validatePass2,
              trigger: 'blur'
            }
          ]
        },
        sexDicData,
        collapse: ['1', '2', '3'],
        form: {},
        tenantData: [],
        roleData: [],
        deptData: [],
        postData: []
      };
    },
    mounted() {
      // 非租户模式默认加载管理组数据
      if (!website.tenantMode) {
        this.initData(website.tenantId);
      } else {
        this.getTenantData();
      }
    },
    methods: {
      // 获取租户数据
      async getTenantData() {
        try {
          const res = await getTenantData();
          this.tenantData = formatTreeData(res.data.data, {
            label: 'tenantName',
            value: 'tenantId'
          });
        } catch (e) {
          console.error(e);
        }
      },
      // 初始化角色, 机构, 岗位数据
      async initData(tenantId) {
        try {
          const roleRes = await getRoleTree(tenantId);
          const deptRes = await getDeptTree(tenantId);
          const postRes = await getPostList(tenantId);
          this.roleData = formatTreeData(roleRes.data.data, {
            label: 'title'
          });
          this.deptData = formatTreeData(deptRes.data.data, {
            label: 'title'
          });
          this.postData = formatTreeData(postRes.data.data, {
            label: 'postName',
            value: 'id'
          });
        } catch (e) {
          console.error(e);
        }
      },
      // 校验表单
      validateForm() {
        const res = { valid: false, form: this.form };
        this.$refs.form.validate((valid) => {
          res.valid = valid;
        });
        return res;
      },
      resetFields() {
        this.form = {};
        this.$refs.form.resetFields();
      }
    }
  };
</script>
<style lang="scss" scoped>
  .collapse_title {
    font-size: 16px;
  }
</style>
