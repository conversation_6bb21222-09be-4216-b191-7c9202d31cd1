<template>
  <basic-container>
    <el-button @click="query">query类型跳转</el-button>
    <el-button type="primary" @click="params">params类型跳转</el-button>
    <div style="margin-top: 50px">
      <h5>断点续传-demo</h5>
      <yk-big-files-upload v-model="fileList" />
    </div>
    <div style="margin-top: 50px">
      <h5>部门选择-demo</h5>
      <department v-model="deptList" />
      <!--   展示   -->
      <div>
        <el-tag
          v-for="(tag, index) in deptList"
          :key="tag.id"
          closable
          style="margin-top: 10px; margin-right: 10px"
          @close="closeDept(index)"
        >
          {{ tag.deptName }}
        </el-tag>
      </div>
    </div>
    <div style="margin-top: 50px">
      <h5>人员选择</h5>
      <person v-model="personList" />
      <!--   展示   -->
      <div>
        <el-tag
          v-for="(tag, index) in personList"
          :key="tag.id"
          closable
          style="margin-top: 10px; margin-right: 10px"
          @close="closePerson(index)"
        >
          {{ tag.realName }}
        </el-tag>
      </div>
    </div>
    <div style="margin-top: 50px">
      <h5>文件上传</h5>
      <upload-file />
    </div>
    <div style="margin-top: 50px">
      <h5>文件下载</h5>
      <show-file :list="list" />
    </div>
    <div style="height: 400px; margin-top: 50px">
      <h5>tree</h5>
      <tree-wrapper left-val="left_person" mid-val="mid_person">
        <template v-slot:left>
          <basic-tree is-show />
        </template>
        <template v-slot:mid>
          <div>qww</div>
        </template>
      </tree-wrapper>
    </div>
  </basic-container>
</template>

<script>
  import YkBigFilesUpload from '@/components/yk-big-files-upload';
  import {
    Department,
    Person,
    TreeWrapper
  } from '@/components/yk-organization-select';
  import { BasicTree } from '@/components/yk-tree';
  import { UploadFile, ShowFile } from '@/components/yk-upload-file';
  export default {
    name: 'Test',
    components: {
      YkBigFilesUpload,
      Department,
      Person,
      TreeWrapper,
      BasicTree,
      UploadFile,
      ShowFile
    },
    data() {
      return {
        fileList: [
          {
            id: '1588379660886667265',
            name: 'node-v16.15.1-x64.rar'
          }
        ],
        deptList: [],
        list: [
          {
            name: 'mp (2).png',
            id: '1592783898715144194',
            link: 'http://***************:9000/file/upload/20221116/bc62008f30602cef9741482e8c8bd03e.jpg'
          },
          {
            name: '任务清单 (2) (2).xlsx',
            id: '1592784446910676993',
            link: 'http://***************:9000/file/upload/20221116/32b5398095b427b32519a0ceee619082.xlsx'
          }
        ],
        personList: []
      };
    },
    watch: {
      fileList: {
        handler(arr) {
          console.log('arr', arr);
        },
        deep: true
      },
      deptList: {
        handler(arr) {
          console.log('dept', arr);
        },
        deep: true
      },
      personList: {
        handler(arr) {
          console.log('person', arr);
        },
        deep: true
      }
    },
    methods: {
      query() {
        this.$router.push({
          name: 'tQuery',
          query: {
            id: Math.trunc(Math.random() * 1000)
          }
        });
      },
      params() {
        this.$router.push({
          name: 'tParams',
          params: {
            id: Math.trunc(Math.random() * 1000)
          }
        });
      },
      closeDept(idx) {
        this.deptList.splice(idx, 1);
      },
      closePerson(idx) {
        this.personList.splice(idx, 1);
      }
    }
  };
</script>
