<template>
  <dialog-drawer
    :title="type === 'ADD' ? '新增' : '编辑'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <section>
        <span class="el-base-title">基础信息</span>
        <base-info :details="details.equipmentAccount"></base-info>
      </section>
      <!-- 部位信息 -->
      <span class="el-base-title">标准内容</span>
      <add-standard
        ref="standard"
        :id="eqId"
        :details="details"
        :monitorList="monitorList"
        :oilType="oilType"
        :wayList="wayList"
      ></add-standard>
    </div>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed" :loading="loading"></btn>
    </div>
  </dialog-drawer>
</template>
<script>
  import AddStandard from './add-standard.vue';
  import BaseInfo from './base-info.vue'; // 设备基本信息
  import {
    userDetail,
    userAddOrUpdate,
    getPartList,
    oilingTypeList,
    getOilingWaySelect
  } from '@/api/equiment-full-life-api/oiling';

  export default {
    name: 'RepairViewIndex',
    components: {
      BaseInfo,
      AddStandard
    },
    data() {
      return {
        eqId: '',
        type: 'ADD',
        visible: false,
        loading: false,
        monitorList: [],
        oilType: [], // 油品
        wayList: [], // 手段
        details: {
          equipmentAccount: {},
          lubricateStandardsList: []
        },
        standList: []
      };
    },
    methods: {
      async submit() {
        try {
          this.loading = true;
          const standList = await this.$refs.standard.validForm();
          if (standList.length === 0 && this.type === 'EDIT') {
            this.$confirm(
              `是否确定清空${this.details.equipmentAccount.code}-${this.details.equipmentAccount.name}的润滑标准？清空不影响已生成的工单，但是此设备将不再生成新的工单！`,
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
              .then(async () => {
                try {
                  if (standList) {
                    await userAddOrUpdate({
                      equipmentId: this.eqId,
                      lubricateStandardsList: standList
                    });
                    this.$emit('success');
                    this.visible = false;
                    this.$message.success('操作成功');
                  }
                } catch (e) {
                  this.$message.warning(e.data.msg);
                }
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                });
              });
          } else if (standList.length === 0 && this.type === 'ADD') {
            this.$confirm(`您还没有添加任何标准，是否关闭页面？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(async () => {
                try {
                  if (standList) {
                    // await userAddOrUpdate({
                    //   equipmentId: this.eqId,
                    //   lubricateStandardsList: standList
                    // });
                    // this.$emit('success');
                    this.visible = false;
                    this.$message.success('操作成功');
                  }
                } catch (e) {
                  this.$message.warning(e.data.msg);
                }
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                });
              });
          } else {
            await userAddOrUpdate({
              equipmentId: this.eqId,
              lubricateStandardsList: standList
            });
            this.$emit('success');
            this.visible = false;
            this.$message.success('操作成功');
          }

          this.loading = false;
        } catch (e) {
          console.log(e);
          this.loading = false;
        }
      },
      closed() {
        this.details = { equipmentAccount: {}, lubricateStandardsList: [] };
        this.standList = [];
        this.monitorList = [];
        this.$refs.standard.resetForm();
        this.visible = false;
      },
      //  获取手段；列表
      async oilingWaySelect() {
        try {
          this.loading = true;
          const res = await getOilingWaySelect();
          this.wayList = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  获取油品类型信息
      async getOilTypeList() {
        try {
          this.loading = true;
          const res = await oilingTypeList();
          this.oilType = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  获取部位信息
      async getMonitorList(id) {
        try {
          this.loading = true;
          const res = await getPartList({ equipmentId: id });
          this.monitorList = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      add() {
        this.$refs.standard.show(this.standList);
      },
      // 点击展示
      show(id, type) {
        this.visible = true;
        this.type = type;
        if (id) {
          this.eqId = id;
          this.getDetail(id);
          this.getMonitorList(id);
          this.getOilTypeList(); // 油品类型
          this.oilingWaySelect(); // 润滑方式
        }
      },

      // 获取详情接口
      async getDetail(id) {
        try {
          this.loading = true;
          const res = await userDetail({ equipmentId: id });
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }
</style>
