<template>
  <div class="top-info">
    <upload-file
      v-model="attachList"
      url="/api/szyk-system/attach/put-file-attach-for-simas"
      accept=".jpg, .png, .jpeg, .xlsx, .pdf, .docx, .md"
      :showFile="false"
      @input="handleSuccess"
      ref="file"
      :limit="100"
    ></upload-file>
    <el-form :model="form" inline label-suffix="：" ref="listForm" size="small">
      <el-table
        class="table"
        :data="form.list"
        row-key="id"
        size="small"
        border
        ref="table"
        :header-cell-style="{ background: '#fafafa' }"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="资料名称"
          align="center"
        ></el-table-column>
        <el-table-column prop="type" label="归类" align="center" width="200px">
          <template slot-scope="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.type'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="[
                {
                  required: true,
                  message: '请选择资料归类',
                  trigger: 'change'
                }
              ]"
            >
              <el-select
                size="small"
                v-model="scope.row.type"
                style="width: 150px"
                placeholder="资料类型"
                clearable
                :disabled="true"
              >
                <el-option
                  v-for="item in cateGoryList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="fileCategoryId"
          label="资料类型"
          align="center"
          width="200px"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.fileCategoryId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="[
                {
                  required: true,
                  message: '请选择资料类型',
                  trigger: 'change'
                }
              ]"
            >
              <el-cascader
                placeholder="资料类型"
                v-model="scope.row.fileCategoryId"
                :options="childrenList"
                :show-all-levels="false"
                :props="{
                  checkStrictly: true,
                  label: 'title',
                  emitPath: false,
                  value: 'value',
                  expandTrigger: 'hover'
                }"
                clearable
              ></el-cascader>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column
          prop="extension"
          label="文件类型"
          align="center"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-view"
              type="text"
              size="small"
              @click="handlePreview(scope.row)"
              >预览</el-button
            >
            <el-popconfirm
              title="确定删除吗？"
              @confirm="() => handleDelete(scope)"
            >
              <el-button
                icon="el-icon-delete"
                slot="reference"
                type="text"
                size="small"
                style="margin-left: 10px"
                >删除</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <div class="img_fullScreen" v-show="false">
      <el-image style="height: 100%" ref="image" :preview-src-list="[imageUrl]">
        <span slot="placeholder" class="loading">加载中...</span>
        <span slot="error">图片加载失败!</span>
      </el-image>
    </div>
  </div>
</template>

<script>
  import UploadFile from '@/components/upload-file.vue';
  import { previewFile } from '@/util/preview';
  import { getFileFullUrl } from '@/util/file';

  export default {
    name: 'DeviceBasicList',
    components: { UploadFile },
    props: {
      initData: {
        type: Object,
        default: () => {
          return {};
        }
      },
      //  资料归类类型列表
      cateGoryList: {
        type: Array,
        default: () => {
          return [];
        }
      },
      childrenList: {
        type: Array,
        default: () => {
          return [];
        }
      },
      typeId: {
        type: String,
        default: undefined
      }
    },

    data() {
      return {
        form: {
          list: []
        },
        list: [],
        attachList: [],
        selLoading: false,
        imageUrl: ''
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.setData(this.initData.equipmentFileInfoList);
            });
          }
        }
      }
    },

    mounted() {},

    methods: {
      //  切换资料归类，获取资料类型

      // download(row) {
      //   console.log(row);
      //   // data: { id: row.id, domain: row.domain }
      //   this.$refs['file'].handleDownLoad({
      //     data: { id: row.id, originalName: row.originalName }
      //   });
      // },
      // 预览文件
      handlePreview(file) {
        const imageExtensions = ['jpg', 'jpeg', 'png'];
        const extension = (file.extension || '').toLowerCase();
        if (imageExtensions.includes(extension)) {
          this.imageUrl = getFileFullUrl(file.id);
          // 调用预览方法
          this.$nextTick(() => {
            this.$refs.image.clickHandler();
          });
        } else if (extension === 'md') {
          this.$message.warning('Markdown文件无法预览');
        } else {
          if (!file.originalName) file.originalName = file.name;
          previewFile(file);
        }
      },

      setData(attachInfoList) {
        this.attachList = [];
        //  将已经有了的
        if (attachInfoList) {
          let data = attachInfoList.map((i) => {
            let item = {
              data: {
                ...i
              },
              detailId: i.id,
              ...i.attach,
              originalName: i.name,
              name: i.name,
              fileCategoryId: i.fileCategoryId, // 资料类型id,
              extension: this.getString(i.name), // 文件后缀类型
              type: i.type
            };
            this.attachList.push(i.attach);
            return item;
          });

          this.form.list = data;
        } else {
          this.form.list = [];
        }
      },
      //  上传成功
      handleSuccess(file) {
        const filterFile = file.filter(
          (item1) => !this.form.list.some((item2) => item2.id === item1.id)
        );
        let arr = filterFile ? filterFile : file;
        let data = arr.map((i) => {
          return {
            data: {
              ...i
            },
            id: i.id,
            name: i.originalName, // 资料名称
            type: this.typeId, // 归类类型
            fileCategoryId: undefined, // 资料类型id,
            extension: this.getString(i.originalName) // 文件后缀类型
          };
        });
        this.form.list = [...this.form.list, ...data]; // [...this.list, ...data];
      },

      getString(str) {
        const lastDotIndex = str.lastIndexOf('.');
        let result;
        if (lastDotIndex !== -1) {
          // 截取点后面的部分
          result = str.slice(lastDotIndex + 1);
        } else {
          // 如果没有找到点，则返回原字符串
          result = str;
        }
        return result;
      },
      async validForm() {
        let list;
        if (this.form.list.length > 0) {
          let valid = await this.$refs['listForm'].validate();
          if (valid) {
            list = this.form.list.map((i) => {
              const { name, type, createTime, createUser, extension } = i;
              return {
                ...{
                  name,
                  type,
                  createTime,
                  createUser,
                  extension
                },
                id: i.detailId,
                attachId: i.id,
                fileCategoryId: i.fileCategoryId
              };
            });
            return list;
          }
        } else {
          list = [];
        }
        return list;
      },
      resetForm() {
        this.attachList = [];
        this.form.list = [];
      },
      async handleDelete(scope) {
        let idx = this.attachList.findIndex((i) => {
          return i.id === scope.row.id;
        });
        this.attachList.splice(idx, 1);
        this.form.list.splice(scope.$index, 1);
        // this.form.list.splice(scope.$index, 1);
        // this.$refs['file'].handleRemove(scope.row, this.form.list);
      }
    }
  };
</script>

<style scoped lang="scss"></style>
