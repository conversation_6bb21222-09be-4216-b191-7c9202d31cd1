import request from '@/router/axios';
import website from '@/config/website';

export const getList = (current, size, params) => {
  return request({
    url: '/api/szyk-system/menu/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getLazyList = (parentId, params, type) => {
  return request({
    url: '/api/szyk-system/menu/lazy-list',
    method: 'get',
    params: {
      ...params,
      parentId,
      type
    }
  });
};

export const getLazyMenuList = (parentId, params) => {
  return request({
    url: '/api/szyk-system/menu/lazy-menu-list',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  });
};

export const getMenuList = (current, size, params) => {
  return request({
    url: '/api/szyk-system/menu/menu-list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getMenuTree = (tenantId, type = 'PC') => {
  return request({
    url: '/api/szyk-system/menu/tree',
    method: 'get',
    params: {
      tenantId,
      type
    }
  });
};
//门户请求上级菜单接口
export const getMenuTree_portal = (tenantId, type = 'PORTAL') => {
  return request({
    url: '/api/szyk-system/menu/tree',
    method: 'get',
    params: {
      tenantId,
      type
    }
  });
};

export const removeCheck = (ids) => {
  return request({
    url: '/api/szyk-system/menu/remove-check',
    method: 'get',
    params: {
      ids
    }
  });
};

export const remove = (ids) => {
  return request({
    url: '/api/szyk-system/menu/remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const add = (row) => {
  return request({
    url: '/api/szyk-system/menu/submit',
    method: 'post',
    data: row
  });
};

export const update = (row) => {
  return request({
    url: '/api/szyk-system/menu/submit',
    method: 'post',
    data: row
  });
};

export const getMenu = (id) => {
  return request({
    url: '/api/szyk-system/menu/detail',
    method: 'get',
    params: {
      id
    }
  });
};

export const getTopMenu = () =>
  request({
    url: '/api/szyk-system/menu/top-menu',
    method: 'get'
  });

export const getRoleTopMenu = () =>
  request({
    url: '/api/szyk-system/menu/role-top-menu',
    method: 'get'
  });

export const getRoutes = (type) =>
  request({
    url: '/api/szyk-system/menu/routes',
    method: 'get',
    params: {
      type,
      services: website.services
    }
  });
