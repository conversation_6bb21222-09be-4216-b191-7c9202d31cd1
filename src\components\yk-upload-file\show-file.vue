<template>
  <div class="file_wrapper">
    <ul>
      <li
        v-for="(item, index) in list"
        :key="index"
        @click="() => handleClick(item)"
      >
        <div class="list_style">
          <el-tooltip :content="item.name" placement="top-start">
            <div>
              <el-image
                v-if="isImg(item.name)"
                class="img_box"
                :src="getFileFullUrl(item.id)"
                fit="cover"
                :preview-src-list="[getFileFullUrl(item.id)]"
              ></el-image>
              <div v-else class="img_box" :class="item.name | suffixFile"></div>
              <div class="text_ellipsis">{{ item.name }}</div>
            </div>
          </el-tooltip>
        </div>
      </li>
    </ul>
    <!-- 预览图片 -->
    <div class="img_fullScreen" v-show="false">
      <el-image
        ref="image"
        style="height: 100%"
        :src="imageUrl"
        :preview-src-list="srcList"
      >
        <span slot="placeholder" class="loading">加载中...</span>
        <span slot="error">图片加载失败!</span>
      </el-image>
    </div>
  </div>
</template>

<script>
  import { strFilter, pdfFilter } from '@/constant/common';
  import { getFileFullUrl } from '@/util/file';
  import { previewFile } from '@/util/preview';

  const docMap = ['doc', 'docx'];
  const pptMap = ['ppt', 'pptx'];
  const xlsMap = ['xls', 'xlsx'];
  const mdExtensions = ['md', 'markdown'];
  export default {
    name: 'showAllType',
    props: {
      list: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    data() {
      return {
        visited: false,
        imgName: '图片预览',
        imageUrl: '',
        srcList: []
      };
    },
    filters: {
      // 映射文件类型图片
      suffixFile(fileName) {
        const suffix = fileName.split('.').pop().toLowerCase();
        const isPdf = pdfFilter.some((item) => item === suffix);
        const isDoc = docMap.some((item) => item === suffix);
        const isPpt = pptMap.some((item) => item === suffix);
        const isXls = xlsMap.some((item) => item === suffix);
        if (isPdf) {
          return 'class_pdf';
        } else if (isDoc) {
          return 'class_doc';
        } else if (isPpt) {
          return 'class_ppt';
        } else if (isXls) {
          return 'class_xls';
        } else if (suffix === 'zip' || suffix === 'gz' || suffix === 'bz2') {
          return 'class_zip';
        } else if (suffix === 'rar') {
          return 'class_rar';
        } else {
          return 'class_file';
        }
      }
    },
    methods: {
      getFileFullUrl,
      isImg(fileName) {
        const suffix = fileName.split('.').pop().toLowerCase();
        return strFilter.some((item) => item === suffix);
      },
      // 文件预览/下载
      handleClick(file) {
        const { name, id } = file;
        const suffix = name.split('.').pop().toLowerCase();
        const isImg = strFilter.some((item) => item === suffix);
        if (mdExtensions.includes(suffix)) {
          this.$message.warning('Markdown文件不支持预览，请下载后查看');
        } else if (!isImg) {
          // 文件预览
          previewFile({ id, originalName: name, extension: suffix });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .file_wrapper {
    width: 100%;
    height: 100%;

    ul,
    li {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    ul::after {
      display: block;
      clear: both;
      height: 0;
      overflow: hidden;
      content: '';
    }

    li {
      float: left;
    }

    .list_style {
      width: 50px;
      margin: 0 20px 8px 0;
      overflow: hidden;
      font-size: 12px;
      line-height: 24px;
      white-space: nowrap;
      text-align: center;
      text-overflow: ellipsis;
      cursor: pointer;

      .img_box {
        display: block;
        width: 40px;
        height: 42px;
      }

      .text_ellipsis {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .title {
      font-weight: 600;
    }

    .class_img {
      background-image: url('./assets/img.png');
      background-size: 100% 100%;
    }

    .class_pdf {
      background-image: url('./assets/pdf.png');
      background-size: 100% 100%;
    }

    .class_doc {
      background-image: url('./assets/doc.png');
      background-size: 100% 100%;
    }

    .class_ppt {
      background-image: url('./assets/ppt.png');
      background-size: 100% 100%;
    }

    .class_xls {
      background-image: url('./assets/xls.png');
      background-size: 100% 100%;
    }

    .class_zip {
      background-image: url('./assets/zip.png');
      background-size: 100% 100%;
    }

    .class_rar {
      background-image: url('./assets/rar.png');
      background-size: 100% 100%;
    }

    .class_file {
      background-image: url('./assets/file.png');
      background-size: 100% 100%;
    }
  }

  .img_fullScreen {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 240px);
    padding: 0 10px;
    text-align: center;
  }
</style>
