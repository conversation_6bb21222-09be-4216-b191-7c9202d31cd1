<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="设备编号" prop="code">
        <el-input
          style="width: 140px"
          v-model.trim="form.code"
          placeholder="请输入设备编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="SN编号" prop="sn">
        <el-input
          style="width: 140px"
          v-model.trim="form.sn"
          placeholder="请输入SN编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="设备名称" prop="name">
        <el-input
          style="width: 140px"
          v-model.trim="form.name"
          placeholder="请输入设备名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="设备类型" prop="categoryName">
        <el-input
          placeholder="请选择设备类型"
          type="text"
          style="width: 180px"
          v-model="form.categoryName"
          readonly
          @focus.prevent="selectAssetCategory"
        >
          <template slot="append">
            <i
              class="el-icon-circle-close"
              @click="
                () => {
                  form.categoryId = undefined;
                  form.categoryName = undefined;
                }
              "
            ></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="设备状态" prop="status">
        <el-select
          v-model="form.status"
          style="width: 150px"
          placeholder="请选择设备状态"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['equipment_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择设备类型-->
    <device-type-dialog
      ref="device-type"
      @success="getSingleRow"
    ></device-type-dialog>
  </div>
</template>

<script>
  import DeviceTypeDialog from '@/views/equiment-full-life-cycle/components/select-asset-category-dialog/single-index.vue';

  export default {
    name: 'DeviceListSearch',
    serviceDicts: ['equipment_special_type', 'equipment_status'],
    components: { DeviceTypeDialog },
    data() {
      return {
        form: {
          code: undefined,
          sn: undefined,
          name: undefined,
          specialType: undefined,
          status: undefined,
          categoryId: undefined,
          categoryName: undefined
        }
      };
    },
    methods: {
      //  选择类型
      getSingleRow(row) {
        this.form.categoryId = row.id;
        this.form.categoryName = row.categoryName;
      },
      selectAssetCategory() {
        this.$refs['device-type'].show();
      },
      reset() {
        this.form.categoryId = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
