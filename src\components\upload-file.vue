<template>
  <div>
    <el-upload
      ref="upload"
      class="upload-file"
      :action="baseUrl + url"
      :list-type="listType"
      :accept="accept"
      :data="data"
      :on-preview="handleDownLoad"
      :on-success="handleSuccess"
      :on-change="handleChange"
      :on-error="handleError"
      :before-remove="beforeRemove"
      :before-upload="beforeUpload"
      multiple
      :name="paramFileName"
      :limit="limit"
      :show-file-list="showFile"
      :on-exceed="handleExceed"
      :file-list="fileList"
      :headers="header"
      :auto-upload="autoUpload"
    >
      <!-- <slot> -->
      <el-button
        v-if="isShowBtn"
        ref="btn"
        id="upload-btn"
        :icon="btnIcon ? 'el-icon-upload' : ''"
        type="primary"
        :disabled="disable"
        :size="btnSize"
        >{{ btnText }}</el-button
      >
      <div
        style="line-height: 20px"
        slot="tip"
        class="el-upload__tip"
        v-if="onlyButton"
      >
        <span>{{ formatLimit ? `限${formatLimit}格式 ` : '' }}</span>
        <span>最多上传{{ limit }}个文件</span>
        <span>单个文件{{ 100 >= limitSize ? `${limitSize}MB以内` : '' }}</span>
      </div>
      <div
        slot="file"
        slot-scope="{ file }"
        class="files"
        v-if="file.status === 'success'"
      >
        <span class="name">{{ file.data.originalName }}</span>
        <div class="operate">
          <span class="downLoad" @click="handlePreview(file)">预览 </span>
          <span class="downLoad" @click="handleDownLoad(file)">下载 </span>
          <span
            v-if="isDetailInfo"
            class="delBtn"
            @click="handleRemove(file, fileList)"
            >删除</span
          >
          <span class="status" v-if="isDetailInfo">
            {{
              file.status === 'success' || status ? '上传成功' : '上传中...'
            }}</span
          >
        </div>
      </div>
      <div class="img_fullScreen" v-show="false">
        <el-image
          style="height: 100%"
          ref="image"
          :preview-src-list="[imageUrl]"
        >
          <span slot="placeholder" class="loading">加载中...</span>
          <span slot="error">图片加载失败!</span>
        </el-image>
      </div>
    </el-upload>
  </div>
</template>
<script>
  import { baseUrl } from '@/config/env';
  import { Base64 } from 'js-base64';
  import website from '@/config/website';
  import { getToken } from '@/util/auth';
  import { downloadFileBlob } from '@/util/util';
  import { previewFile } from '@/util/preview';
  import { getFileFullUrl } from '@/util/file';

  export default {
    props: {
      props: {
        type: Object,
        default: () => {
          return {
            name: 'originalName',
            id: 'id',
            data: 'data'
          };
        }
      },
      // 详情不展示删除，上传完成按钮
      isDetailInfo: { type: Boolean, default: true },
      isShowBtn: {
        type: Boolean,
        default: true
      },
      disable: {
        type: Boolean,
        default: () => {
          return false;
        }
      },
      showFile: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      accept: {
        type: String,
        default: 'multipart/form-data'
      },
      value: {
        type: Array,
        default: () => []
      },
      // 仅显示button
      onlyButton: {
        type: Boolean,
        default: true
      },
      // button 文字
      btnText: {
        type: String,
        default: '点击上传'
      },
      // button size
      btnSize: {
        type: String,
        default: 'small'
      },
      // 上传后清空
      successClear: {
        type: Boolean,
        default: false
      },
      // 上传地址
      url: {
        type: String,
        default: '/api/szyk-system/attach/put-file-attach'
      },
      // 限制上传文件个数
      limit: {
        type: Number,
        default: 100
      },
      // 限制上传大小
      limitSize: {
        type: Number,
        default: 20
      },
      // 文件列表的类型
      listType: {
        type: String,
        default: 'text'
      },
      // 文件格式
      formatLimit: {
        type: String,
        default: 'jpg,png,jpeg,xlsx,pdf,docx,md'
      },
      // 入参文件名
      paramFileName: {
        type: String,
        default: 'file'
      },
      // 选取文件后立即进行上传
      autoUpload: {
        type: Boolean,
        default: true
      },
      // 上传文件之前的钩子
      beforeUploadProp: {
        type: Function,
        default: () => {
          return true;
        }
      },
      handleChange: {
        type: Function,
        default: () => {}
      },
      // 按钮图标
      btnIcon: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        status: false,
        fileList: [],
        data: {},
        baseUrl,
        fileName: '',
        header: {
          Authorization: `Basic ${Base64.encode(
            `${website.clientId}:${website.clientSecret}`
          )}`,
          [website.tokenHeader]: 'bearer ' + getToken()
        },
        token: localStorage.getItem('token'),
        imageUrl: '',
        uploadCount: 0, // 当前正在上传的文件数量
        loadingInstance: null // 加载实例
      };
    },

    watch: {
      value: {
        immediate: true,
        handler(newValue) {
          let list = [];
          for (let i in newValue) {
            let item = {};
            item['name'] = newValue[i][this.props.name];
            item['id'] = newValue[i][this.props.id];
            item['data'] = newValue[i];
            list.push(item);
          }
          this.fileList = [...list];
        }
      }
    },
    methods: {
      setFileList(fileList) {
        this.fileList = fileList;
      },
      handleRemove(file, fileList) {
        let idx = fileList.findIndex((i) => i.id == file.id);
        if (idx >= 0) {
          this.fileList.splice(idx, 1);
        }
        this.handleInput(fileList);
        this.$emit('remove', fileList);
      },
      handlePreview(file) {
        console.log(file);
        const { id, name, status } = file;
        if (status === 'success') {
          const extension = name.split('.').pop();
          if (['jpg', 'png', 'jpeg'].includes(extension)) {
            this.imageUrl = getFileFullUrl(id);
            // 调用预览方法
            this.$nextTick(() => {
              this.$refs.image.clickHandler();
            });
          } else if (['pdf', 'xlsx', 'docx'].includes(extension)) {
            previewFile({ id, originalName: name, extension });
          } else if (extension === 'md') {
            this.$message.warning('Markdown文件无法预览');
          }
        } else {
          this.$message.warning('预览失败，请重试');
        }
      },
      handleDownLoad(file) {
        // let path;
        // if (file.data.domain) {
        //   window.open(convertFileUrl(file.data.domain));
        // } else {
        //   path = `/api/szyk-system/attach/download/${file.data.id}`;
        //   downloadFileBlob(
        //     `${path}?${this.website.tokenHeader}=${getToken()}`,
        //     file.data.originalName
        //   );
        // }
        // this.downFile(file.data.link, file.name);

        const path = `/api/szyk-system/attach/download/${file.data.id}`;
        downloadFileBlob(
          `${path}?${this.website.tokenHeader}=${getToken()}`,
          file.data.originalName
        );
      },
      handleInput(fileList) {
        let fileArray = [];
        fileList.forEach((item) => {
          const data = item.response
            ? item.response.data
            : item.data.data || item.data;
          fileArray.push({
            ...data,
            id: data.id || data.attachId
          });
        });
        this.$emit('input', [...fileArray]);
      },
      handleSuccess(response, file, fileList) {
        if (response.code === 200) {
          this.handleUploadComplete();
          this.status = true;
          this.handleInput(fileList);
          this.$emit('success', fileList);
          if (this.successClear) {
            this.fileList = [];
          }
        } else {
          this.status = false;
          this.handleError(response);
        }
      },
      handleError(error) {
        this.handleUploadComplete();
        this.$message.error('上传失败');
        this.$emit('error', error);
      },
      handleExceed() {
        this.$message.warning(`当前限制最多上传 ${this.limit} 个文件`);
      },
      beforeRemove() {
        //     return this.$confirm(`确定移除 ${file.name}？`)
      },
      beforeUpload(file) {
        let fileExt = file.name.replace(/.+\./, '');
        this.fileName = file.name;
        let isTrueFile = true;
        if (this.formatLimit) {
          let formatLimit = this.formatLimit.split(',');
          isTrueFile = formatLimit.indexOf(fileExt.toLowerCase()) !== -1;
        }
        const isLt2M = file.size / 1024 / 1024 < this.limitSize;

        if (!isTrueFile) {
          this.$message.error(`上传文件限${this.formatLimit}格式!`);
          return false;
        }
        if (!isLt2M) {
          this.$message.error('上传文件大小不能超过 ' + this.limitSize + 'MB!');
          return false;
        }
        let propUp = this.beforeUploadProp(file);
        // 开始上传时增加计数
        this.uploadCount++;

        // 第一次上传时显示全局加载
        if (this.uploadCount === 1) {
          this.showLoading();
        }

        return isTrueFile && isLt2M && propUp;
      },
      // 显示全局加载
      showLoading() {
        this.loadingInstance = this.$loading({
          lock: true,
          text: '文件上传中，请稍候...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
      },
      // 关闭全局加载
      closeLoading() {
        if (this.loadingInstance) {
          this.loadingInstance.close();
          this.loadingInstance = null;
          this.uploadCount = 0;
        }
      },
      // 处理上传完成（成功/失败/取消）
      handleUploadComplete() {
        // 减少计数
        this.uploadCount = this.uploadCount - 1;

        // 当所有上传完成时关闭加载
        if (this.uploadCount === 0) {
          this.closeLoading();
        }
      },
      submit() {
        this.$refs['upload'].submit();
      }
    }
  };
</script>
<style lang="scss" scoped>
  .upload-file ::v-deep {
    padding: 0;
    color: #bf1c1c;

    /* min-height: 120px; */
    text-align: left;
  }

  .files {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
    cursor: pointer;

    &:hover {
      background: #f5f7fa;
    }
  }

  .name {
    display: inline-block;
    flex-grow: 1;
    overflow: hidden;
    color: #606266;
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .status {
    color: #67c23a !important;
  }

  .delBtn {
    color: #f56c6c !important;
  }

  .operate {
    flex-shrink: 0;
    padding-left: 20px;
    font-size: 12px;

    span {
      margin-right: 12px;
      color: #1989fa;
      cursor: pointer;
    }
  }

  .img_fullScreen {
    box-sizing: border-box;
    width: 100%;
    height: calc(100vh - 240px);
    padding: 0 10px;
    text-align: center;
  }

  // 去除文件新增/删除动画
  ::v-deep .el-upload-list__item {
    transition: nonne !important;
    transition: none !important;
  }

  ::v-deep .el-upload-list__item-name {
    transition: nonne !important;
    transition: none !important;
  }
</style>
