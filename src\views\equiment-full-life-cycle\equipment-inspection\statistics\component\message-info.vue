<template>
  <div class="alarm-handle"></div>
</template>
<script>
  import dayjs from 'dayjs';
  import { mapGetters } from 'vuex';
  // import { menuPath } from '@/util/menu-path';
  export default {
    components: {},
    props: {
      item: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    computed: {
      ...mapGetters(['permission'])
    },
    data() {
      return {
        dayjs,
        loading: false,
        details: {},
        content: ''
      };
    },
    mounted() {},
    methods: {
      // 点击去处理
      handle(item) {
        this.$router.push({
          path: menuPath(item.bizType),
          query: {
            no: item.bizId
          }
        });
      }
      // routerPath(type) {
      //   switch (type) {
      //     case 'INVENTORY': // 盘点
      //       return '/take-stock/index';
      //     case 'ALLOCATE': // 调拨
      //       return '/asset-allocation-management/index';
      //     case 'RETURN': //退库
      //       return '/return-management/index';
      //     case 'ARRIVAL': //到货单
      //       return '/arrival-note/index/index';
      //   }
      // }
    }
  };
</script>

<style lang="scss" scoped>
  .alarm-handle {
    display: grid;
    grid-template-columns: 70px calc(100% - 150px) 80px;
    padding: 10px;
    border-bottom: 1px solid #d1dbe5 !important;

    .icon {
      width: 48px;
      height: 48px;
    }
  }

  .content {
    overflow: hidden;
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .time {
    margin-left: 25px;
    font-size: 12px;
  }

  .button {
    position: relative;

    ::v-deep {
      .el-button {
        position: absolute;
        right: 5px;
        bottom: 10px;
      }
    }
  }

  .device {
    color: #409eff;
  }

  .title {
    font-weight: 600;
  }

  ::v-deep {
  }

  ._title {
    position: relative;
  }
  .iconfont {
    width: 50px;
    height: 50px;
    border-radius: 5px;
    background: linear-gradient(to bottom, #5e9ffc, #b9d4fe);
    font-size: 16px;
    padding: 5px;
    text-align: center;
    color: #fff;
  }
</style>
