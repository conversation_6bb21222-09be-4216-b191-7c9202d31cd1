<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <info ref="info" :initData="detail"></info>
      <p class="el-base-title">报废设备列表</p>
      <sel-asset ref="asset" :detail="detail"></sel-asset>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"></btn>
      <btn type="close" @click="closed" :loading="loading"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import Info from './base-info.vue';
  import { userSubmit, userDetail } from '@/api/equiment-full-life-api/scrap';
  import SelAsset from './sel-asset.vue';
  export default {
    name: 'AddDevice',
    components: {
      Info,
      SelAsset
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {
          image: []
        }
      };
    },
    watch: {},
    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await userDetail({ id: id });
          this.detail = res.data.data;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      show(id) {
        this.visible = true;
        this.edit = !!id;
        this.eqId = id;
        if (id) {
          this.getDetail(id);
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.form.image = [];
        this.$refs['info'].resetForm();
        this.$refs['asset'].resetForm();
        this.detail = {};
      },
      async submit() {
        let params = await this.$refs['info'].validForm();
        let asset = this.$refs['asset'].validForm();
        this.$confirm(
          '设备报废后，设备所有类型工单全部终止，是否继续？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(async () => {
            // let params = await this.$refs['info'].validForm();
            // let asset = this.$refs['asset'].validForm();
            if (params && asset) {
              await this.submitData({
                id: this.edit ? this.eqId : undefined,
                ...params,
                ...asset
              });
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },

      // 提交设备
      async submitData(params) {
        this.loading = true;
        try {
          await userSubmit(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
