<template>
  <div class="search-content">
    <el-form
      label-suffix=":"
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model.trim="form.deviceName"
          placeholder="请输入设备名称"
          style="width: 150px"
          clearable
          maxlength="50"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="移动后位置" prop="newLocationName">
        <el-input
          v-model.trim="form.newLocationName"
          placeholder="请输入移动后位置"
          style="width: 150px"
          clearable
          maxlength="50"
        >
        </el-input>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'MoveLogSearch',
    data() {
      return {
        form: {
          deviceName: undefined,
          newLocationName: undefined
        }
      };
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
