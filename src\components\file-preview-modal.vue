<template>
  <basic-dialog
    v-if="visible"
    :visible="visible"
    :title="title || '文件预览'"
    :width="width"
    :fullscreen="fullscreen"
    @closed="handleClose"
  >
    <div class="file-preview-container" :class="{ 'is-loading': loading }">
      <!-- Loading indicator -->
      <div v-if="loading" class="loading-overlay">
        <i class="el-icon-loading"></i>
        <span>加载中...</span>
      </div>

      <!-- Image preview -->
      <div v-if="isImage && !loading" class="image-preview">
        <el-image :src="fileUrl" fit="contain" :preview-src-list="[fileUrl]">
          <div slot="error" class="image-error">
            <i class="el-icon-picture-outline"></i>
            <span>图片加载失败</span>
          </div>
        </el-image>
      </div>

      <!-- Document preview with OnlyOffice -->
      <div v-else-if="!isImage && !loading" class="document-preview">
        <onlyoffice-editor
          :src="onlyOfficeSrc"
          :config="editorConfig"
          @ready="onEditorReady"
        />
      </div>
    </div>

    <!-- Footer with actions -->
    <div slot="footer" class="preview-footer">
      <el-button @click="toggleFullscreen" type="text">
        <i :class="fullscreen ? 'el-icon-close' : 'el-icon-full-screen'"></i>
        {{ fullscreen ? '退出全屏' : '全屏' }}
      </el-button>
      <el-button @click="handleClose" type="primary" size="small"
        >关闭</el-button
      >
    </div>
  </basic-dialog>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { OnlyofficeEditor } from 'onlyoffice-vue';
  import { handleDocType } from '@/util/preview';
  import { httpPreviewUrl, httpsPreviewUrl } from '@/config/env';
  import BasicDialog from '@/components/basic-dialog';

  export default {
    name: 'file-preview-modal',
    components: {
      OnlyofficeEditor,
      BasicDialog
    },
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      fileUrl: {
        type: String,
        default: ''
      },
      fileType: {
        type: String,
        default: ''
      },
      fileName: {
        type: String,
        default: ''
      },
      fileId: {
        type: String,
        default: ''
      },
      title: {
        type: String,
        default: ''
      },
      width: {
        type: String,
        default: '80%'
      }
    },
    data() {
      return {
        loading: true,
        fullscreen: false,
        uid: Math.random().toString(36).slice(-8)
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      isImage() {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        return imageExtensions.includes((this.fileType || '').toLowerCase());
      },
      onlyOfficeSrc() {
        let isHttps = window.location.protocol.includes('https');
        return `${
          isHttps ? httpsPreviewUrl : httpPreviewUrl
        }web-apps/apps/api/documents/api.js`;
      },
      editorConfig() {
        let documentType = handleDocType(this.fileType) || 'word';
        let userName = this.userInfo['nick_name'] || 'User';
        let key = this.fileId || this.uid;

        return {
          width: '100%',
          height: '100%',
          documentType,
          document: {
            fileType: this.fileType,
            key,
            url: this.fileUrl,
            title: this.fileName || '',
            permissions: {
              download: false,
              print: false,
              edit: false
            }
          },
          editorConfig: {
            mode: 'view',
            user: {
              name: userName
            }
          }
        };
      }
    },
    watch: {
      visible(newVal) {
        if (newVal) {
          this.loading = true;
          // Add CSP meta tag for HTTPS if needed
          this.addCSPMetaTag();
        }
      }
    },
    methods: {
      onEditorReady() {
        this.loading = false;
      },
      handleClose() {
        this.$emit('update:visible', false);
        this.$emit('close');
      },
      toggleFullscreen() {
        this.fullscreen = !this.fullscreen;
      },
      addCSPMetaTag() {
        let isHttps = window.location.protocol.includes('https');
        if (isHttps) {
          // Add CSP meta tag for HTTPS connections
          const existingMeta = document.querySelector(
            'meta[http-equiv="Content-Security-Policy"]'
          );
          if (!existingMeta) {
            const oMeta = document.createElement('meta');
            oMeta.setAttribute('charset', 'utf-8');
            oMeta.setAttribute('http-equiv', 'Content-Security-Policy');
            oMeta.setAttribute('content', 'upgrade-insecure-requests');
            document.getElementsByTagName('head')[0].appendChild(oMeta);
          }
        }
      }
    },
    mounted() {
      // For images, we can set loading to false immediately
      if (this.isImage) {
        setTimeout(() => {
          this.loading = false;
        }, 300);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .file-preview-container {
    position: relative;
    width: 100%;
    height: 70vh;
    background-color: #303133;
    display: flex;
    align-items: center;
    justify-content: center;

    &.is-loading {
      min-height: 200px;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    z-index: 10;

    i {
      font-size: 32px;
      margin-bottom: 10px;
    }
  }

  .image-preview,
  .document-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;

    i {
      font-size: 32px;
      margin-bottom: 10px;
    }
  }

  .preview-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
