<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @closed="close"
    class="detail-drawer"
    size="80%"
  >
    <div class="details" v-loading="loading" v-if="visible">
      <!-- 基本信息 -->
      <section>
        <p class="el-base-title">基本信息</p>
        <base-info :details="details"></base-info>
      </section>

      <p class="el-base-title">设备列表</p>
      <section style="display: flex; justify-content: space-between">
        <el-radio-group
          style="margin-bottom: 10px"
          v-model="result"
          size="small"
          @change="change"
        >
          <el-radio-button label="all"
            >全部({{ totalNumber.total }})</el-radio-button
          >
          <el-radio-button label="1"
            >正常({{ totalNumber.normal }})</el-radio-button
          >
          <el-radio-button label="0"
            >未盘点({{ totalNumber.notStart }})</el-radio-button
          >
          <el-radio-button label="2"
            >缺失({{ totalNumber.loss }})</el-radio-button
          >
        </el-radio-group>
      </section>
      <sel-asset ref="asset" :list="list" :isShow="false"></sel-asset>

      <pagination
        :page-size.sync="searchParams.size"
        :page-no.sync="searchParams.current"
        :total="total"
        @pagination="getList"
      />
    </div>
  </dialog-drawer>
</template>

<script>
  import SelAsset from './sel-asset.vue';
  import BaseInfo from './base-info.vue';
  import {
    getDevicePartsInventoryViewApi,
    getDeviceInventoryOrderStatisticsApi,
    getDeviceInventoryOrderListApi
  } from '@/api/equiment-full-life-api/device-inventory';
  export default {
    name: 'RepairViewIndex',
    // serviceDicts: ['inventory_methods'],
    components: { BaseInfo, SelAsset },
    data() {
      return {
        id: undefined,
        totalNumber: {},
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        methods: undefined,
        result: 'all',
        row: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {} // 详情数据
      };
    },

    methods: {
      // 列表
      async getList() {
        try {
          const res = await getDeviceInventoryOrderListApi({
            ...this.searchParams,
            planId: this.row.id,
            methods: this.methods,
            result: this.result === 'all' ? undefined : this.result
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  切换物品分类
      change(e) {
        this.searchParams.current = 1;
        this.getList();
      },
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getDevicePartsInventoryViewApi(id);
          this.details = res.data.data;
        } catch (e) {
          this.loading = false;
        }
      },
      //  统计
      async getTotal(row) {
        try {
          const res = await getDeviceInventoryOrderStatisticsApi({
            planId: row.id
          });
          this.totalNumber = res.data.data;
        } catch (e) {}
      },

      // 点击展示
      async show(row) {
        this.row = row;
        this.visible = true;
        if (row.no) {
          await this.getDetail(row.id);
          await this.getList();
          await this.getTotal(row);
        }
      },

      // 关闭弹窗
      close() {
        this.detail = {};
        this.result = 'all';
        this.visible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
