<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      label-position="left"
      class="search-form"
    >
      <el-form-item label="入库类型" prop="inboundType">
        <el-select
          v-model="form.inboundType"
          placeholder="请选择入库类型"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['inbound_type']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商" prop="supplierId">
        <el-select
          v-model="form.supplierId"
          placeholder="请选择供应商"
          clearable
          filterable
        >
          <el-option
            v-for="item in supList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="入库库房" prop="warehouseId">
        <el-select
          v-model="form.warehouseId"
          placeholder="请选择入库库房"
          clearable
          filterable
        >
          <el-option
            v-for="item in whorehouse"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="入库日期" prop="time" class="_label">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { in_storage_status } from '../util';
  import { getSupplierList } from '@/api/equiment-full-life-api/common';
  import { getCheckRepairTypeListApi } from '@/api/equiment-full-life-api/spare-parts';
  import { mapGetters } from 'vuex';
  export default {
    name: 'DeviceListSearch',
    components: {},
    serviceDicts: ['inbound_type'], //out_storage_type
    data() {
      return {
        in_storage_status,
        orgListLoading: false,
        useDeptOptions: [],
        form: {
          no: undefined,
          inboundType: undefined,
          supplierId: undefined,
          warehouseId: undefined,
          time: undefined
        },
        supList: [],
        whorehouse: []
      };
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    created() {
      this.getSupList();
      this.getStoreHouseList();
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      //  获取库放列表
      async getStoreHouseList() {
        let res = await getCheckRepairTypeListApi();
        this.whorehouse = res.data.data;
      },
      //  选择供应商
      async getSupList() {
        let res = await getSupplierList({ current: 1, size: -1 });
        this.supList = res.data.data.records || [];
      },
      submit() {
        let params = {
          ...this.form,
          startInboundDate: this.form.time ? this.form.time[0] : undefined,
          endInboundDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
