<!-- 设备数量统计 = 设备总数、监测设备台数、监测设备部位-->
<template>
  <div class="alarmTrend" v-loading="loading">
    <span class="title">
      <section>
        <span style="color: #333; font-size: 20px"> 年度工作汇总</span>
      </section>
    </span>
    <div class="alarmTrend-warp">
      <div class="the-total-number-of-tickets">
        <div class="total-tickets-wrap">
          <div class="total-title">总工单数量（条）</div>
          <div class="total-num">
            {{ data.allNum }} <span class="unit"> 条</span>
          </div>
        </div>
      </div>
      <div class="total">
        <section class="_dev">
          <div class="img"></div>
          <section class="cont">
            <span class="_dev_num"
              >{{ data.todoNum || 0 }}
              <span class="_dev_percentage">
                {{ data.todoNumPercentage || 0 }}%</span
              >
            </span>
            <span class="_dev_desc">待完成工单量</span>
          </section>
        </section>
        <section class="_dev">
          <div class="img"></div>
          <section class="cont">
            <span class="_dev_num">
              {{ data.overdueNum || 0 }}
              <span class="_dev_percentage">
                {{ data.overdueNumPercentage || 0 }}%</span
              ></span
            >

            <span class="_dev_desc">超期工单量 </span>
          </section>
        </section>
        <section class="_dev">
          <div class="img"></div>
          <section class="cont">
            <span class="_dev_num">
              {{ data.overdueCompletedNum || 0 }}
              <span class="_dev_percentage">
                {{ data.overdueCompletedNumPercentage || 0 }}%</span
              ></span
            >

            <span class="_dev_desc">超期完成工单量</span>
          </section>
        </section>
        <section class="_dev">
          <div class="img"></div>
          <section class="cont">
            <span class="_dev_num">
              {{ data.normalCompletedNum || 0 }}
              <span class="_dev_percentage">
                {{ data.normalCompletedNumPercentage || 0 }}%</span
              ></span
            >
            <span class="_dev_desc">正常完成工单量</span>
          </section>
        </section>
      </div>
    </div>
  </div>
</template>
<script>
  import { curYearWorkSummary } from '@/api/home';

  export default {
    props: {},
    data() {
      return { loading: false, data: {} };
    },
    watch: {},
    mounted() {
      this.$nextTick(async () => {
        await this.getAsset();
      });
    },
    methods: {
      async getAsset() {
        this.loading = true;
        try {
          const res = await curYearWorkSummary();
          if (res.data.data) {
            let obj = res.data.data || {};
            let {
              allNum,
              normalCompletedNum,
              overdueCompletedNum,
              overdueNum,
              todoNum
            } = obj;
            obj.normalCompletedNumPercentage = !allNum
              ? 0
              : ((normalCompletedNum / allNum) * 100).toFixed(2);
            obj.overdueCompletedNumPercentage = !allNum
              ? 0
              : ((overdueCompletedNum / allNum) * 100).toFixed(2);
            obj.overdueNumPercentage = !allNum
              ? 0
              : ((overdueNum / allNum) * 100).toFixed(2);
            obj.todoNumPercentage = !allNum
              ? 0
              : ((todoNum / allNum) * 100).toFixed(2);
            this.data = obj;
          } else {
            this.flag = false;
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      jump(status) {
        this.$router.push({
          path: '/asset-file/index',
          query: {
            status: status
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .alarmTrend {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: #fff;
    border-radius: 5px;
  }

  .alarmTrend-warp {
    height: 86%;
    border-radius: 5px;
  }

  .the-total-number-of-tickets {
    display: flex;
    height: 106px;
    margin: 10px;
    padding: 25px;
    background: url('../../../asset/home/<USER>') right center; //linear-gradient(to left, #aacdf6, #dfe6f4)
    background-size: auto 100%;
    border-radius: 5px;

    .total-tickets-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 50px;
      background-color: #596370;
      border-radius: 50%;

      img {
        width: 36px;
      }
    }

    .total-tickets-wrap {
      margin-left: 30px;
      color: #fff;

      .total-title {
        color: #333;
      }

      .total-num {
        font-size: 36px;
        color: #2566f6;
        font-weight: 600;
        //font-family: electronicFont;
        .unit {
          font-size: 16px;
          color: #333;
          font-family: Arial;
        }
      }
    }
  }

  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    section {
      display: flex;
      width: calc(100% - 220px);
      overflow: hidden;
      text-overflow: ellipsis;
    }

    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    em {
      display: inline-block;
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-style: normal;
      line-height: 10%;
    }
  }

  .total {
    display: grid;
    grid-template-rows: repeat(1, auto); /* 定义两行 */
    grid-template-columns: repeat(2, 1fr); /* 定义两列 */
    height: calc(100% - 150px);
    grid-gap: 10px;
    padding: 0 10px 0 10px;

    ._dev:nth-child(1) {
      background: #f6f7fa;
      .img {
        background: url('~@/asset/home/<USER>') no-repeat center right;
        background-size: 100%;
        width: 36px;
        height: 100%;
      }
      ._dev_num {
        color: #191a20;
      }
    }

    ._dev:nth-child(2) {
      background: #f7f8fb;
      .img {
        background: url('~@/asset/home/<USER>') no-repeat center right;
        background-size: 100%;
        width: 36px;
        height: 100%;
      }
      ._dev_num {
        color: #2566f6;
      }
    }

    ._dev:nth-child(3) {
      background: #faf0e6;
      .img {
        background: url('~@/asset/home/<USER>') no-repeat center right;
        background-size: 100%;
        width: 36px;
        height: 100%;
      }
      ._dev_num {
        color: #ff820a;
      }
    }

    ._dev:nth-child(4) {
      background: #fef0f0;
      .img {
        background: url('~@/asset/home/<USER>') no-repeat center right;
        background-size: 100%;
        width: 36px;
        height: 100%;
      }
      ._dev_num {
        color: #f74240;
      }
    }
  }

  ._dev {
    width: 100%;
    height: 82px;
    display: grid;
    grid-template-columns: 50px auto;
    padding: 10px;

    &_desc {
      display: block;
      margin-top: 40px;
      overflow: hidden;
      color: #a2a3a7;
      font-size: 14px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &_num {
      float: left;
      width: 100%;
      font-weight: 600;
      font-size: 28px;
    }

    &_unit {
      float: left;
      width: 100%;
      color: #3d446e;
      font-size: 14px;
    }

    &_percentage {
      color: #a2a3a7;
      font-size: 13px;
    }
  }

  .unit {
    color: #999db4;
    font-size: 14px;
    font-style: normal;
  }
</style>
