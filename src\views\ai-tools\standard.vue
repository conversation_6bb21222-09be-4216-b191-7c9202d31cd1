<template>
  <div
    class="standard-container"
    v-loading="loading"
    :element-loading-text="loadingText"
  >
    <!-- 顶部表单 -->
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      :inline="true"
      label-suffix="："
      label-position="right"
      label-width="100px"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item label="工单类型" prop="module">
            <el-select
              ref="module"
              v-model="form.module"
              placeholder="请选择工单类型"
              @change="handleModuleChange"
              @click.native="checkPermission"
            >
              <el-option
                v-for="dict in standardTypeDict"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="设备名称" prop="equipmentName" ref="equipment">
            <el-input
              v-model="form.equipmentName"
              readonly
              placeholder="请选择设备名称"
              @focus.prevent="onChooseDeviceClick"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="设备资料" class="file-list">
            <div v-if="equipmentFileInfoList.length" class="file-wrapper">
              <div
                class="file-item"
                v-for="file in equipmentFileInfoList"
                :key="file.id"
                :title="file.name"
              >
                <el-button
                  type="text"
                  size="small"
                  @click="handlePreview(file)"
                  >{{ file.name }}</el-button
                >
                <i
                  class="el-icon-circle-close"
                  @click="handleDelFile(file)"
                ></i>
              </div>
            </div>
            <div v-else>暂未上传</div>
          </el-form-item>
        </el-col>
        <el-col :span="4" class="btn-wrapper">
          <upload-file
            accept=".jpg, .png, .jpeg, .xlsx, .pdf, .docx, .md"
            v-model="equipmentFileInfoList"
            url="/api/szyk-system/attach/put-file-attach-for-simas"
            :showFile="false"
            :onlyButton="false"
            :disable="!(form.module && form.equipmentName)"
            btnText="上传文件"
            @input="handleSuccess"
            ref="file"
          ></upload-file>
          <el-button
            size="small"
            type="primary"
            plain
            @click="handleExecute"
            :disabled="
              !(
                form.module &&
                form.equipmentName &&
                equipmentFileInfoList.length
              )
            "
            >执行</el-button
          >
        </el-col>
      </el-row>
    </el-form>
    <!-- 标准表格 -->
    <component ref="comp" :key="compKey" :is="standardComponent"></component>
    <div class="btn-wrapper text-right">
      <el-button
        v-if="monitorSourceList.length"
        type="text"
        size="small"
        @click="handleShowSourceText"
        >原始文本</el-button
      >
    </div>
    <!-- 按钮 -->
    <div class="bottom-btn-wrapper">
      <btn type="submit" @click="submit" :loading="loading"></btn>
    </div>
    <!-- 设备选择弹窗 -->
    <asset-list ref="assetList" @on-choose="onChooseDeviceSuccess"></asset-list>
    <!-- 原始文本弹窗 -->
    <source-text-dialog ref="sourceText"></source-text-dialog>
  </div>
</template>

<script>
  import UploadFile from '@/components/upload-file.vue';
  import AssetList from './select-asset-dialog/index.vue';
  import {
    InspectStandard,
    MaintainStandard,
    LubricateStandard,
    OverhaulStandard,
    SourceTextDialog
  } from './components';
  import { getDeviceFileDetail } from '@/api/equiment-full-life-api/profile';
  import {
    postAddPreFile,
    postDelPreFile,
    postGenerateStandards
  } from '@/api/ai-tools';
  import { mapGetters } from 'vuex';
  import { previewFile } from '@/util/preview';

  export default {
    components: {
      UploadFile,
      AssetList,
      InspectStandard,
      MaintainStandard,
      LubricateStandard,
      OverhaulStandard,
      SourceTextDialog
    },
    serviceDicts: ['ai_standard_order_type'],
    watch: {
      standardTypeDict: {
        handler(val) {
          if (val && val.length) {
            this.form.module = val[0].value;
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        loadingText: '',
        form: {
          module: undefined,
          equipmentId: undefined,
          equipmentName: undefined
        },
        equipmentFileInfoList: [], // 设备资料
        rules: {
          module: [
            { required: true, message: '请选择工单类型', trigger: 'blur' }
          ],
          equipmentName: [
            { required: true, message: '请选择设备', trigger: 'blur' }
          ]
        },
        // 标准列表
        compKey: Math.random(),
        // 原始文本
        monitorSourceList: []
      };
    },
    computed: {
      ...mapGetters(['permission']),
      standardComponent() {
        switch (this.form.module) {
          case '1':
            return InspectStandard;
          case '2':
            return MaintainStandard;
          case '3':
            return LubricateStandard;
          case '4':
            return OverhaulStandard;
          default:
            return null;
        }
      },
      standardTypeDict() {
        let dict = this.serviceDicts.type['ai_standard_order_type'];
        if (!dict || !dict.length) return [];
        const getDictItem = (val) => dict.find((item) => item.value === val);
        const res = [];
        const dictPermObj = {
          1: 'inspect-standard-add-edit-export',
          2: 'maintenance-standard-add-edit-export',
          3: 'lubrication-standard-add-edit-export',
          4: 'maintenance-standards-add-edit-import'
        };
        for (let key in dictPermObj) {
          const perm = dictPermObj[key];
          if (this.permission[perm]) {
            res.push(getDictItem(key));
          }
        }
        return res;
      }
    },
    methods: {
      checkPermission() {
        if (!this.standardTypeDict.length) {
          this.$message.warning(
            '您没有维护各类标准的权限，请先联系管理员分配权限。'
          );
          this.$refs.module.blur();
        }
      },
      // 工单类型变更
      handleModuleChange() {
        if (!this.form.equipmentId) return;
        this.$refs.comp.close();
        this.$nextTick(() => {
          this.$refs.comp.show(this.form.equipmentId);
        });
      },
      // 选择设备
      onChooseDeviceClick() {
        if (!this.form.module) return this.$message.warning('请选择工单类型');
        this.$refs.assetList.show([], true, this.form.module);
      },
      // 获取设备资料
      async getDeviceFileDetailData() {
        try {
          this.loading = true;
          const { data } = await getDeviceFileDetail({
            id: this.form.equipmentId,
            module: 'PRE'
          });
          this.equipmentFileInfoList = data.data.equipmentFileInfoList || [];
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 选择设备回调
      onChooseDeviceSuccess(device) {
        this.form.equipmentId = device.id;
        this.form.equipmentName = device.name;
        this.$refs['equipment'].clearValidate();

        // 获取设备资料
        this.getDeviceFileDetailData();

        this.$refs.comp.show(device.id);
      },
      // 上传前期资料
      async uploadPreFile() {
        try {
          const fileList = this.equipmentFileInfoList.filter(
            (item) => item.newAdd
          );
          const pData = {
            fileList,
            equipmentId: this.form.equipmentId,
            module: 'PRE'
          };
          const { data } = await postAddPreFile(pData);
          if (data.code === 200) {
            this.$message.success('上传成功');
            this.getDeviceFileDetailData();
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 上传
      handleSuccess(files) {
        this.equipmentFileInfoList = files.map((file) => {
          if (file.originalName) {
            const extension = file.originalName.split('.').pop();
            return {
              ...file,
              newAdd: true,
              id: undefined,
              name: file.originalName,
              extension
            };
          } else {
            return file;
          }
        });

        this.uploadPreFile();
      },
      // 预览文件
      handlePreview(file) {
        previewFile(file.attach);
      },
      async delFile(file) {
        try {
          const { data } = await postDelPreFile({ fileId: file.id });
          if (data.code === 200) {
            this.$message.success('删除成功');
            this.getDeviceFileDetailData();
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 删除文件
      async handleDelFile(file) {
        this.$confirm('是否确认删除？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.delFile(file);
          })
          .catch(() => {});
      },
      // 执行
      async handleExecute() {
        try {
          this.loading = true;
          this.loadingText = '正在生成运维标准...';
          const monitorList = this.$refs.comp
            .getMonitorStandardsData()
            .map((item) => {
              const temp = { ...item };
              if (temp.monitorId === temp.monitorName) {
                delete temp.monitorId;
              }
              return temp;
            });
          const { label } = this.standardTypeDict.find(
            (item) => item.value === this.form.module
          );
          const pData = {
            equipmentId: this.form.equipmentId,
            equipmentName: this.form.equipmentName,
            monitorList,
            orderTypeName: label
          };
          const { data } = await postGenerateStandards(pData);
          const newData = data.data.monitorList.map((item) => {
            return {
              ...item,
              standard: item.standards,
              nameAiFlag: !!item.monitorName,
              standardAiFlag: !!item.standards,
              methodAiFlag: !!item.method
            };
          });
          this.monitorSourceList = data.data.monitorSourceList;

          this.$refs.comp.setMonitorStandardsData(newData);
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
          this.loadingText = '';
        }
      },
      // 查看原始文本
      handleShowSourceText() {
        this.$refs.sourceText.show(this.monitorSourceList);
      },
      // 提交
      submit() {
        this.$refs.form.validate((valid) => {
          if (!valid) return;
          this.$refs.comp.submit();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .standard-container {
    .el-form-item {
      width: 100%;

      ::v-deep .el-form-item__content {
        width: calc(100% - 100px);
      }

      &.file-list {
        margin-bottom: 6px;

        ::v-deep .el-form-item__content {
          width: calc(100% - 120px);
          overflow: hidden;

          .file-wrapper {
            max-height: 94px;
            overflow: auto;

            div,
            button,
            span {
              width: 100%;
              overflow: hidden;
              white-space: nowrap;
              text-align: left;
              text-overflow: ellipsis;
            }

            .file-item {
              position: relative;
              padding-right: 20px;

              .el-button {
                font-size: 14px;
              }

              .el-icon-circle-close {
                position: absolute;
                top: 9px;
                right: 6px;
              }
            }
          }
        }
      }
    }

    .btn-wrapper {
      > div {
        display: inline-block;
        margin-right: 10px;

        .el-button {
          height: 32px;
        }
      }

      &.text-right {
        text-align: right;
      }
    }

    .bottom-btn-wrapper {
      position: absolute;
      bottom: 10px;
      left: 0;
      width: 100%;
      padding: 10px 5px 0;
      text-align: right;
      border-top: 1px solid #e4e7ed;
    }
  }
</style>
