<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @close="close"
    class="detail-drawer"
    size="80%"
  >
    <div class="details" v-loading="loading" v-if="visible">
      <!-- 基本信息 -->
      <section>
        <p class="el-base-title">基本信息</p>
        <base-info :details="details"></base-info>
      </section>

      <p class="el-base-title">备品备件信息</p>
      <sel-asset ref="asset" :detail="details" :isShow="false"></sel-asset>

      <!--      <p class="el-base-title">审核日志</p>-->
      <!--      <el-timeline>-->
      <!--        <el-timeline-item-->
      <!--          v-for="activity in logData"-->
      <!--          :key="activity.id"-->
      <!--          :timestamp="activity.operateTime"-->
      <!--        >-->
      <!--          {{ activity.content | contentFilter }}-->
      <!--        </el-timeline-item>-->
      <!--      </el-timeline>-->
      <div style="padding-bottom: 50px">
        <logs ref="log"></logs>
      </div>
    </div>
  </dialog-drawer>
</template>

<script>
  import SelAsset from '../operate/sel-asset.vue';
  import BaseInfo from './base-info.vue';
  import { getSparePartsReceiveViewApi } from '@/api/equiment-full-life-api/spare-parts';
  import Logs from '@/views/equiment-full-life-cycle/components/logs/index.vue';
  export default {
    name: 'RepairViewIndex',
    components: { Logs, BaseInfo, SelAsset },
    filters: {
      contentFilter(text) {
        // 使用正则表达式匹配日期时间部分和剩余部分
        const regex = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}:\d{2}:\d{2})(.+)$/;
        const matches = text.match(regex);

        if (matches) {
          const rest = matches[5]; // 剩余部分
          // 拼接最终结果
          return rest;
        }

        // 如果没有匹配到，返回原始文本
        return text;
      }
    },
    data() {
      return {
        id: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {}, // 详情数据
        // 审核日志
        logData: []
      };
    },

    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getSparePartsReceiveViewApi(id);
          this.details = res.data.data;
          await this.$refs['log'].getLogs(
            this.details.id,
            'SPARE_PART_RECEIVE'
          );

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 点击展示
      async show(id) {
        this.visible = true;
        if (id) {
          await this.getDetail(id);
        }
      },

      // 关闭弹窗
      close() {
        this.visible = false;
        this.detail = {};
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
