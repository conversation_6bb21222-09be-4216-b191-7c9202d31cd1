<template>
  <el-dialog
    title="选择人员"
    :visible.sync="visible"
    width="70%"
    @closed="closed"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-row id="root-tree">
      <el-col :span="6" class="search">
        <el-input
          size="small"
          placeholder="请输入部门名称搜索"
          v-model="filterText"
          class="search-input"
        >
        </el-input>
        <el-tree
          ref="tree"
          class="dataTree"
          :props="props"
          :load="loadNode"
          lazy
          node-key="id"
          :data="treeData"
          @node-click="nodeClick"
          :filter-node-method="filterNode"
        >
        </el-tree>
      </el-col>
      <el-col :span="18">
        <el-form
          :model="form"
          inline
          label-suffix="："
          ref="baseForm"
          class="_formStyle search-form"
          label-position="left"
          size="small"
        >
          <el-form-item label="登录账号" prop="account">
            <el-input
              v-model="form.account"
              placeholder="请输入登录账号"
            ></el-input>
          </el-form-item>
          <el-form-item label="用户姓名" prop="realName">
            <el-input
              v-model="form.realName"
              placeholder="请输入用户姓名"
            ></el-input>
          </el-form-item>
          <el-button size="small" type="primary" @click="search"
            >查 询</el-button
          >
          <el-button size="small" @click="reset">重 置</el-button>
        </el-form>
        <div class="tree">
          <el-table
            v-loading="loading"
            :autoHeight="true"
            size="small"
            :data="list"
            border
            ref="multipleTable"
            header-row-class-name="_headerRowClassName"
            header-cell-class-name="_headerCellClassName"
            cell-class-name="_cellClassName"
            row-class-name="_rowClassName"
            row-key="id"
            :key="key"
            @selection-change="handleSelectionChange"
            height="500px"
          >
            <el-table-column
              v-if="isMultiple"
              type="selection"
              width="55"
              :reserve-selection="true"
            >
            </el-table-column>
            <el-table-column type="index" label="#"></el-table-column>

            <el-table-column
              prop="account"
              label="登录账号"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="tenantName"
              label="所属租户"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="realName"
              label="用户姓名"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="roleName"
              label="所属角色"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.roleName }}
              </template>
            </el-table-column>

            <el-table-column
              prop="deptName"
              label="所属部门"
              show-overflow-tooltip
            >
            </el-table-column>

            <!--单选的时候出现-->
            <el-table-column label="操作" v-if="!isMultiple">
              <template #default="scope">
                <el-button type="text" size="small" @click="select(scope.row)"
                  >选择</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <div class="pagination">
          <pagination
            :page-size.sync="queryParams.size"
            :page-no.sync="queryParams.current"
            :total="total"
            @pagination="getList"
          />
        </div>
        <!--  多选的时候会出现-->
        <div class="_btn" v-if="isMultiple">
          <el-button @click="visible = false">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script>
  import { getDeptLazyTree, getUserList } from '@/api/system/dept';
  export default {
    components: {},
    props: {
      isMultiple: {
        type: Boolean,
        default: () => {
          return false;
        }
      } // 是否多选
    },
    data() {
      return {
        key: 0,
        filterText: '',
        queryParams: {
          size: 10,
          current: 1
        },
        props: {
          children: 'children',
          label: 'title',
          isLeaf: 'isLeaf'
        },
        form: {
          account: undefined,
          realName: undefined
        },
        treeData: [],
        list: undefined,
        total: 10,
        multipleSelection: [],
        deptId: undefined, // 左边树结构id
        loading: false,
        visible: false,
        isDevice: false, // 选择的是不是设备
        nodeData: undefined, // 当前设备
        selected: undefined
      };
    },
    watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      }
    },
    methods: {
      sizeChange() {
        this.queryParams.current = 1;
        this.getList();
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.title.indexOf(value) !== -1;
      },
      // 点击节点
      nodeClick(e) {
        this.deptId = e.id;
        this.queryParams.current = 1;
        this.getList();
      },
      // 点击确定
      submit() {
        if (this.multipleSelection.length !== 0) {
          this.$emit('multipleSelection', this.multipleSelection);
        } else {
          this.$message.warning('请选择至少一个人员');
        }
        this.visible = false;
      },
      // 获取部门
      async getDept() {
        this.loading = true;
        try {
          let res;

          res = await getDeptLazyTree(0);

          this.treeData = res.data.data.map((it) => {
            let isLeaf = !it.hasChildren;
            // 只展示到设备那一层
            return {
              ...it,
              isLeaf: isLeaf
            };
          });
        } catch ({ message }) {
          console.log(message);
        }
      },
      async loadNode(node, resolve) {
        if (node.level === 0) {
          return resolve(this.data || []);
        } else {
          let res;

          res = await getDeptLazyTree(node.data.id);

          const data = res.data.data.map((it) => {
            let isLeaf = !it.hasChildren;
            return {
              ...it,
              isLeaf: isLeaf
            };
          });
          return resolve(data);
        }
      },
      // 点击选择
      select(row) {
        this.$emit('selected', row);
        this.visible = false;
      },

      handleSelectionChange(val) {
        this.multipleSelection = val;
      },

      async show(selected) {
        this.visible = true;
        this.key++;
        this.selected = selected;
        this.list = [];
        this.queryParams.current = 1;
        await this.getDept();
        await this.getList();
      },

      search() {
        this.getList();
      },

      reset() {
        this.$refs['baseForm'].resetFields();
        this.filterText = undefined;
        this.deptId = undefined;
        this.$refs.tree && this.$refs.tree.setCurrentKey(null);
        this.getList();
      },

      closed() {
        this.deptId = undefined;
        this.filterText = undefined;
        this.$refs.tree && this.$refs.tree.setCurrentKey(null);
        this.$refs['baseForm'].resetFields();
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let params = {
            ...this.queryParams,
            ...this.form,
            deptId: this.deptId
          };
          let res;

          res = await getUserList(params);

          this.total = res.data.data.total;
          this.list = res.data.data.records;
          await this.$nextTick(() => {
            if (this.selected) {
              let selectedArr = this.list.filter((item) =>
                this.selected.some((s) => s.id === item.id)
              );
              selectedArr.map((i) => {
                this.$refs.multipleTable.toggleRowSelection(i, true);
              });
            }
          });

          this.loading = false;
        } catch ({ message }) {
          this.loading = false;
          console.log(message);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep.pagination-container .el-pagination {
    // left: 0 !important;
    // right: unset !important;
    // top: 10px;
    position: unset !important;
  }

  .pagination-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0;
    background: unset !important;
  }

  ._btn {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }

  .search {
    padding-bottom: 15px;
  }

  #root-tree {
    .search-input {
      //padding-top: 20px;
      padding-bottom: 10px;
    }
  }

  .el-tree {
    padding-right: 20px;
    background: unset;
  }

  ::v-deep.el-col {
    margin-bottom: 0 !important;
    padding-right: 15px;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;

    .pagination {
      display: flex;
      justify-content: flex-start;
      width: 70%;
    }

    ._btn {
      display: flex;
      justify-content: flex-end;
      width: 30%;
      margin: 10px 0;
    }
  }

  .tree {
    ::v-deep.el-checkbox {
      margin-right: 0 !important;
    }
  }

  ::v-deep {
    .el-table th.el-table__cell > .cell,
    .el-table-column--selection .cell {
      text-align: center;
    }
  }
</style>
