<template>
  <dialog-drawer
    title="派单"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    size="80%"
    class="device-add"
  >
    <section v-loading="loading">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-suffix="："
        label-position="right"
        label-width="120px"
      >
        <p class="el-base-title">报修信息</p>
        <div>
          <el-button size="small" type="primary" @click="onConvertClick">{{
            isInternal ? '转为外部维修' : '转为内部维修'
          }}</el-button>
        </div>
        <el-row v-if="isInternal">
          <el-col :span="8">
            <el-form-item label="维修人员" prop="receiveUser">
              <el-select v-model="form.receiveUser" filterable>
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.realName"
                  :value="user.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计完成时间" prop="completeTime">
              <el-date-picker
                style="width: 100%"
                type="datetime"
                v-model="form.completeTime"
                value-format="yyyy-MM-dd HH:mm:00"
                placeholder="请选择预计完成时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注说明" prop="">
              <el-input
                style="width: 100%"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                v-model="form.remark"
                maxlength="500"
                placeholder="请输入备注说明"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-else>
          <el-col :span="8">
            <el-form-item label="承修单位" prop="externalOrg">
              <el-input
                maxlength="50"
                v-model="form.externalOrg"
                placeholder="请输入承修单位"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进人员" prop="followUser">
              <el-select v-model="form.followUser" filterable>
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.realName"
                  :value="user.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计完成时间" prop="completeTime">
              <el-date-picker
                style="width: 100%"
                type="datetime"
                v-model="form.completeTime"
                value-format="yyyy-MM-dd HH:mm:00"
                placeholder="请选择预计完成时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="备品备件"
              prop="repairComponentList"
              label-position
            >
              <el-button type="primary" size="small" @click="onAddComponent"
                >添加</el-button
              >
            </el-form-item>
            <div style="padding: 0 50px">
              <el-table size="small" :data="form.repairComponentList">
                <el-table-column label="名称">
                  <template slot-scope="scope">
                    <el-input
                      size="small"
                      maxlength="50"
                      v-model="scope.row.name"
                      placeholder="请输入名称"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="型号">
                  <template slot-scope="scope">
                    <el-input
                      size="small"
                      maxlength="50"
                      v-model="scope.row.model"
                      placeholder="请输入名称"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="数量">
                  <template slot-scope="scope">
                    <el-input-number
                      size="small"
                      v-model="scope.row.count"
                      :min="0"
                      placeholder="请输入数量"
                    ></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template slot-scope="scope">
                    <el-button
                      type="danger"
                      size="small"
                      @click="form.repairComponentList.splice(scope.$index, 1)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注说明" prop="">
              <el-input
                style="width: 100%"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                v-model="form.remark"
                maxlength="500"
                placeholder="请输入备注说明"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
    <!--     选择人员-->
    <recipient-dialog
      ref="recipient"
      @selected="onUserSelect"
    ></recipient-dialog>
  </dialog-drawer>
</template>

<script>
  import { mapGetters } from 'vuex';
  import {
    dispatchRepairApi,
    repairToExternalApi
  } from '@/api/equiment-full-life-api/repair';
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import { roleUserListApi } from '@/api/user';
  export default {
    name: 'repair-internal-dispatch',
    components: { RecipientDialog },
    data() {
      return {
        isInternal: true,
        visible: false,
        detail: {},
        loading: false,
        userListLoading: false,
        userList: [],
        edit: false,
        form: {
          // 维修人
          receiveUser: '',
          // 预计完成时间
          completeTime: '',
          // 派单说明
          remark: '',
          // 承修单位
          externalOrg: '',
          // 承修单位联系方式
          externalTel: '',
          followUser: '',
          followUserName: '',
          repairComponentList: []
        },
        rules: {
          receiveUser: [
            { required: true, message: '请选择维修人员', trigger: 'blur' }
          ],
          completeTime: [
            { required: true, message: '请选择预计完成时间', trigger: 'blur' }
          ],
          externalOrg: [
            { required: true, message: '请选择预计完成时间', trigger: 'blur' }
          ],
          followUser: [
            { required: true, message: '请选择跟进人员', trigger: 'blur' }
          ]
        }
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'permission'])
    },
    methods: {
      onConvertClick() {
        this.isInternal = !this.isInternal;
        this.form = {
          // 维修人
          receiveUser: '',
          // 预计完成时间
          completeTime: '',
          // 派单说明
          remark: '',
          // 承修单位
          externalOrg: '',
          // 承修单位联系方式
          externalTel: '',
          followUser: '',
          followUserName: '',
          repairComponentList: []
        };
      },
      show(row) {
        if (row && row.id) {
          this.visible = true;
          this.getUserList();
          this.detail = row;
        } else {
          this.$message.warning('该数据异常');
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.form.receiveUser = '';
        this.form.completeTime = '';
        this.form.remark = '';
        this.detail = {};
        this.visible = false;
      },
      async submit() {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            try {
              if (this.isInternal) {
                this.loading = true;
                await dispatchRepairApi({
                  id: this.detail.id,
                  ...this.form
                });
              } else {
                const flag = this.form.repairComponentList.every(
                  (item) => item.name && item.model
                );
                if (flag) {
                  this.loading = true;
                  await repairToExternalApi({
                    id: this.detail.id,
                    externalOrg: this.form.externalOrg,
                    followUser: this.form.followUser,
                    completeTime: this.form.completeTime,
                    repairComponentList: this.form.repairComponentList,
                    remark: this.form.remark
                  });
                } else {
                  this.$message.warning('请完善维修配件信息');
                  return;
                }
              }
              this.$message.success('操作成功');
              this.$emit('success');
              this.visible = false;
              this.loading = false;
            } catch (e) {
              this.loading = false;
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      async getUserList() {
        try {
          this.userListLoading = true;
          const res = await roleUserListApi({
            alias: 'repair_user'
          });
          this.userList = res.data.data;
          this.userListLoading = false;
        } catch (e) {
          this.userListLoading = false;
          this.monitorList = [];
        }
      },
      onAddComponent() {
        this.form.repairComponentList.push({
          name: '',
          model: '',
          count: 0
        });
      },
      //  选择人员
      onChooseUser() {
        this.$refs['recipient'].show();
      },
      onUserSelect(user) {
        console.log('user', user);
        this.form.receiveUser = user.id;
        this.form.receiveUserName = user.realName;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-form-item__content {
    height: 32px;
  }
</style>
