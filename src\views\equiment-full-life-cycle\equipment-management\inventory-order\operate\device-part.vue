<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
    >
      <el-table
        v-loading="loading"
        class="table"
        :data="form.list"
        border
        :header-cell-style="{ background: '#fafafa' }"
        size="small"
        stripe
      >
        <el-table-column type="index" label="#" fixed="left"></el-table-column>
        <el-table-column
          prop="deviceCode"
          label="设备编号"
          show-overflow-tooltip
          fixed="left"
        >
        </el-table-column>
        <el-table-column
          prop="deviceName"
          label="设备名称"
          show-overflow-tooltip
          fixed="left"
        >
        </el-table-column>

        <el-table-column
          prop="deviceModel"
          label="规格型号"
          show-overflow-tooltip
          fixed="left"
        >
          <template v-slot="{ row }">{{ row.deviceModel || '-' }}</template>
        </el-table-column>
        <el-table-column
          prop="deviceStatusName"
          label="设备状态"
          show-overflow-tooltip
          fixed="left"
        >
          <template v-slot="{ row }">{{
            row.deviceStatusName || '-'
          }}</template>
        </el-table-column>
        <el-table-column
          prop="deviceUserName"
          label="盘点人员"
          show-overflow-tooltip
          fixed="left"
        >
          <template v-slot="{ row }" style="color: red">{{
            row.deviceUserName || '-'
          }}</template>
        </el-table-column>
        <el-table-column prop="belongDeptName" label="归属部门" width="200px">
          <template v-slot="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.belongDeptName'"
              :rules="{
                required: true,
                message: '请选择归属部门',
                trigger: 'change'
              }"
              label=" "
            >
              <el-input
                style="width: 140px"
                :disabled="!!tabActive"
                placeholder="请选择归属部门"
                v-model.trim="scope.row.belongDeptName"
                @focus.prevent="onSelectDeptClick('belong', scope)"
                readonly
                clearable
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="responsibleUserId"
          label="负责人"
          show-overflow-tooltip
          width="200px"
        >
          <template v-slot="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.responsibleUserId'"
              :rules="{
                required: true,
                message: '请选择人负责人',
                trigger: 'change'
              }"
              label=" "
            >
              <el-select
                style="width: 140px"
                v-model="scope.row.responsibleUserId"
                filterable
                placeholder="请选择人负责人"
                clearable
                :disabled="!!tabActive"
              >
                <el-option
                  v-for="item in userMap[scope.row.belongDeptId]"
                  :key="item.id"
                  :label="item.realName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column prop="useDeptName" label="使用部门" width="250px">
          <template v-slot="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.useDeptName'"
              :rules="{
                required:
                  Number(scope.row.deviceStatus) === 2 ||
                  !!scope.row.useDeptName, // 如果是备用 是非必填
                message: '请选择使用部门',
                trigger: 'change'
              }"
              label=" "
            >
              <el-input
                style="width: 190px"
                placeholder="请选择使用部门"
                v-model.trim="scope.row.useDeptName"
                @focus.prevent="onSelectDeptClick('use', scope)"
                readonly
                clearable
                :disabled="Number(scope.row.deviceStatus) === 2 || !!tabActive"
              >
                <!--非在用状态下, 或者使用部门有值-->
                <template
                  slot="append"
                  v-if="Number(scope.row.deviceStatus) !== 2"
                  ><i
                    class="el-icon-circle-close"
                    @click="clearUseDept(scope)"
                  ></i
                ></template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="useUserId" label="使用人" width="200px">
          <template v-slot="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.useUserId'"
              :rules="{
                required:
                  Number(scope.row.deviceStatus) === 2 ||
                  !!scope.row.useDeptName, // 如果是备用 是非必填
                message: '请选择使用人',
                trigger: 'change'
              }"
              label=" "
            >
              <el-select
                style="width: 140px"
                v-model="scope.row.useUserId"
                filterable
                placeholder="请选择使用人"
                clearable
                :disabled="Number(scope.row.deviceStatus) === 2 || !!tabActive"
              >
                <el-option
                  v-for="item in userMap[scope.row.useDeptId]"
                  :key="item.id"
                  :label="item.realName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="result"
          label="盘点结果"
          show-overflow-tooltip
          width="200px"
        >
          <template v-slot="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.result'"
              :rules="{
                required: true,
                message: '请选择盘点结果',
                trigger: 'change'
              }"
              label=" "
            >
              <el-select
                style="width: 140px"
                v-model="scope.row.result"
                filterable
                placeholder="请选择盘点结果"
                clearable
                :disabled="!!tabActive"
              >
                <el-option
                  v-for="item in [
                    { label: '未盘点', value: 0 },
                    { label: '正常', value: 1 },
                    { label: '缺失', value: 2 }
                  ]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" width="200px">
          <template v-slot="scope">
            <el-form-item :prop="'list.' + scope.$index + '.remark'">
              <el-input
                size="small"
                v-model="scope.row.remark"
                show-word-limit
                :maxlength="200"
                autosize
                placeholder="请输入备注"
                type="textarea"
                :disabled="!!tabActive"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import { getDeviceInventoryOrderListApi } from '@/api/equiment-full-life-api/device-inventory';
  import { getUserListByDeptId } from '@/api/equiment-full-life-api/common';
  import { InputTree } from '@/components/yk-select-tree';
  import DeptDialog from '@/components/dept-dialog/index.vue';
  export default {
    name: 'DeviceBasicList',
    components: { DeptDialog, InputTree },

    watch: {},
    data() {
      return {
        loading: false,
        total: 0,
        deptData: [],
        lazyLoading: false,
        form: {
          list: []
        },
        userMap: {}, // 人员的map
        deptType: undefined,
        currentScope: undefined,
        tabActive: undefined,
        originList: [], // 原始数据list
        newList: [] // 这个存放新的添加的备件字典
      };
    },
    computed: {},
    mounted() {},
    methods: {
      //  选择人员下拉列表
      async getUser(deptId) {
        try {
          let params = {
            deptIds: deptId,
            includeSubDepartments: true // 如果是true ，说明查询的是该部门的所有人员
          };

          let res = await getUserListByDeptId(params);
          return res.data.data;
        } catch ({ message }) {
          console.log(message);
        }
      },
      // 清空使用部门
      clearUseDept(scope) {
        console.log(scope);
        // 清空部门和人
        this.$set(scope.row, 'useDeptId', undefined);
        this.$set(scope.row, 'useDeptName', undefined);
        // 清空使用人
        this.$set(scope.row, 'useUserId', undefined);
      },
      async getList(params) {
        this.tabActive = params.result;
        this.loading = true;
        try {
          const res = await getDeviceInventoryOrderListApi(params);
          let list = res.data.data.records || [];
          await this.getDeptUserMap(list);

          // 打印部门与人员的 Map
          await this.$nextTick(() => {
            this.form.list = list;
          });

          //  列表加载的时候，获取列表的人员map

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  获取部门map
      async getDeptUserMap(list) {
        let belongDeptIds = list.map((it) => {
          return it.belongDeptId;
        });
        let useDeptIds = list.map((it) => {
          return it.useDeptId;
        });
        // 合并并去重
        let uniqueDeptIds = [...new Set([...belongDeptIds, ...useDeptIds])];
        if (uniqueDeptIds.length) {
          let users = await this.getUser(uniqueDeptIds.join(',')); // 获取当前部门的人员列表

          let deptUserMap = {}; // 创建一个普通对象来存储部门与人员的映射
          for (let deptId of uniqueDeptIds) {
            if (users && users.length > 0) {
              // let user = users.filter((it) => it.deptId == deptId);
              let user = users.filter((it) => it.deptPath.includes(deptId));
              deptUserMap[deptId] = user; // 将部门 id 和人员列表存入对象
            }
          }
          this.userMap = deptUserMap; // 将对象赋值给 userMap
        }
      },
      async validForm() {
        let valid = await this.$refs['listForm'].validate();
        if (valid) {
          console.log(this.form.list);
          let p = this.form.list.map((it) => {
            let {
              belongDeptId,
              deviceId,
              id,
              inventoryOrderId,
              responsibleUserId,
              result,
              useDeptId,
              useUserId,
              remark
            } = it;
            return {
              belongDeptId,
              deviceId,
              id,
              inventoryOrderId,
              responsibleUserId,
              result,
              useDeptId,
              useUserId,
              remark
            };
          });
          this.newList = [];
          return p;
        } else {
          return false;
        }
      },
      resetForm() {
        this.newList = [];
        this.form.list = [];
      },
      // 归属部门或者使用部门改变的时候
      onSelectDeptClick(type, scope) {
        this.deptType = type;
        this.currentScope = scope;
        this.$refs['dept-dialog'].show();
      },
      // 归属部门或者使用部门改变的回调函数
      async onSelectDept(dept) {
        console.log('dept', dept);
        if (dept.id) {
          console.log('改变之后的form list', this.form.list);
          if (this.deptType === 'use') {
            //   更新使用部门、使用人、
            this.$set(this.currentScope.row, 'useDeptName', dept.deptName);
            this.$set(this.currentScope.row, 'useDeptId', dept.id);
            await this.getDeptUserMap(this.form.list);

            //    清空责任人
            this.$set(this.currentScope.row, 'useUserId', undefined);
          } else {
            //   更新负责部门、负责人
            this.$set(this.currentScope.row, 'belongDeptName', dept.deptName);
            this.$set(this.currentScope.row, 'belongDeptId', dept.id);
            await this.getDeptUserMap(this.form.list);
            //    清空责任人
            this.$set(this.currentScope.row, 'responsibleUserId', undefined);
          }
        }
      }
    }
  };
</script>

<style scoped lang="scss"></style>
