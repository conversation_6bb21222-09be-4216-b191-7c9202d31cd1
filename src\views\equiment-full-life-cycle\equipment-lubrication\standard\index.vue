<template>
  <el-row>
    <el-col :span="5" style="margin-bottom: 0">
      <basic-container :autoHeight="true">
        <simas-type-tree
          ref="typeTree"
          countTree
          dataScope="2"
          @change="typeChange"
        />
      </basic-container>
    </el-col>
    <el-col :span="19" style="margin-bottom: 0">
      <view-info :categoryId="categoryId" where="position-bearing"></view-info>
    </el-col>
  </el-row>
</template>

<script>
  import ViewInfo from './viewInfo/index.vue';
  import SimasTypeTree from '@/views/equiment-full-life-cycle/components/simas-type-tree.vue';
  export default {
    name: 'DeviceBasic',
    components: {
      SimasTypeTree,
      ViewInfo
    },
    data() {
      return {
        loading: false,
        list: [],
        categoryId: undefined // 部位id
      };
    },

    mounted() {},
    methods: {
      typeChange(node) {
        console.log('node', node);
        this.categoryId = node.data.id;
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    margin-bottom: 10px;
  }

  .table {
    //height: 695px;
    //max-height: 695px;
  }

  ::v-deep {
    .el-tabs__content {
      flex: 1;

      // overflow: auto;
    }

    .el-tabs__header {
      margin: 0 0 10px;
    }
  }
</style>
