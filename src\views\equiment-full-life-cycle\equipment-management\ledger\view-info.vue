<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
      <el-button
        icon="el-icon-upload2"
        type="success"
        size="small"
        @click="exportExcel"
        >导出</el-button
      >
      <el-button
        icon="el-icon-download"
        type="warning"
        size="small"
        @click="importFile"
        >导入</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="sn"
        label="SN编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="model"
        label="规格型号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.model || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="measureUnitName"
        label="计量单位"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <!--      <el-table-column-->
      <!--        prop="processCategoryName"-->
      <!--        label="工艺类别"-->
      <!--        align="center"-->
      <!--        show-overflow-tooltip-->
      <!--      >-->
      <!--        <template slot-scope="scope">-->
      <!--          {{ scope.row.processCategoryName || '-' }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column
        prop="importantLevelName"
        label="设备等级"
        align="center"
        show-overflow-tooltip
        width="96px"
      >
        <template slot-scope="{ row }">
          <el-tag
            :type="getTagType(row.importantLevel)"
            size="small"
            class="table-custom-tag"
          >
            {{ row.importantLevelName || '-' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="特种设备类型"
        align="center"
        show-overflow-tooltip
        width="100px"
      >
        <template slot-scope="scope">
          {{ scope.row.specialTypeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="特种设备检查周期(天)"
        align="center"
        show-overflow-tooltip
        width="140px"
      >
        <template slot-scope="scope">
          {{ scope.row.specialInspectPeriod || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="categoryName"
        label="设备类型"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="imageList"
        label="设备图片"
        align="center"
        show-overflow-tooltip
        width="90px"
      >
        <template slot-scope="scope">
          <el-image
            v-if="scope.row.imageList"
            style="width: 50px; height: 50px"
            :src="getFileFullUrl(scope.row.imageList[0].id)"
            :preview-src-list="[getFileFullUrl(scope.row.imageList[0].id)]"
            fit="cover"
          ></el-image>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <!--      <el-table-column-->
      <!--        prop="rfid"-->
      <!--        label="是否配置RFID"-->
      <!--        align="center"-->
      <!--        show-overflow-tooltip-->
      <!--        width="110px"-->
      <!--      >-->
      <!--        <template slot-scope="scope">-->
      <!--          {{ scope.row.rfid ? '是' : '否' }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column-->
      <!--        prop="rfid"-->
      <!--        label="RFID卡号"-->
      <!--        align="center"-->
      <!--        show-overflow-tooltip-->
      <!--      >-->
      <!--        <template slot-scope="scope">-->
      <!--          {{ scope.row.rfid || '-' }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column
        prop="locationPath"
        label="设备位置"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.locationPath || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="purchaseDate"
        label="购买日期"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.purchaseDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.useDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="userName"
        label="使用人员"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.userName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="productDate"
        label="投产日期"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.productDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="supplier"
        label="生产厂家"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.supplier || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="contact"
        label="联系人员"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.contact || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="tel"
        label="联系电话"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.tel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="创建人员"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="设备状态"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${equipmentStatusColor(row.status)};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="170" fixed="right">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-view"
            type="text"
            size="small"
            @click="detail(scope.row)"
            >查看</el-button
          >
          <el-button
            icon="el-icon-edit"
            type="text"
            size="small"
            @click="operate(scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            title="确定删除吗？"
            @confirm="() => handleDelete(scope.row)"
          >
            <el-button
              icon="el-icon-delete"
              slot="reference"
              type="text"
              size="small"
              style="margin-left: 10px"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex"></detail-index>
    <!--       文件导入-->
    <import-files
      ref="file"
      :templateUrl="'/szyk-simas/equipment-account/export-template'"
      action="/szyk-simas/equipment-account/import-data"
      downLoadFileName="设备台账下载"
      :isShow="false"
      :isInstanceExport="true"
      @refresh="getList"
    ></import-files>
  </basic-container>
</template>

<script>
  import ImportFiles from '@/components/import-files.vue';
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import { getFileFullUrl } from '@/util/file';

  import {
    accountListPage,
    removeAccount
  } from '@/api/equiment-full-life-api/ledger';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination,
      ImportFiles
    },
    props: {
      locationId: String,
      categoryId: String,
      useDept: String
    },
    watch: {
      locationId(val) {
        this.searchParams.locationId = val;
        this.exportParams.locationId = val;
        this.getList();
      },
      categoryId(val) {
        this.searchParams.categoryId = val;
        this.exportParams.categoryId = val;
        this.getList();
      },
      useDept(val) {
        this.searchParams.useDept = val;
        this.exportParams.useDept = val;
        this.getList();
      }
    },
    data() {
      return {
        getFileFullUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          locationId: undefined,
          categoryId: undefined,
          useDept: undefined,
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      // 设备台账 设备状态状态颜色
      equipmentStatusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#35C24B'; // 在用
          case 1:
            return '#155CFF'; // 备用
          case 3:
            return '#EE7C11'; // 维修
          case 4:
            return '#E23F3F'; // 报废
        }
      },
      // 设备登记标签颜色
      getTagType(val) {
        const typeObj = {
          1: 'success',
          2: 'primary',
          3: 'warning'
        };
        return typeObj[val];
      },
      importFile() {
        this.$refs['file'].show();
      },
      print() {},
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/equipment-account/export-account?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/equipment-account/export-account?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '设备台账.xlsx'
        );
      },
      importExcel() {
        this.$refs.file.show();
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await accountListPage({
            ...this.searchParams
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        let id = row ? row.id : undefined;
        this.$refs.add.show(id);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      },
      async handleDelete(row) {
        try {
          await removeAccount({ ids: row.id });

          //  删除的时候，判断当前列表，是不是length 是1 ，如果是1，将current置成1
          if (this.list.length === 1) {
            this.searchParams.current = 1;
          }
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          await this.getList(true);
          this.$emit('refreshTree');
          // 发信号 通知树更新
        } catch (e) {
          this.$message.warning(e.data.msg);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
