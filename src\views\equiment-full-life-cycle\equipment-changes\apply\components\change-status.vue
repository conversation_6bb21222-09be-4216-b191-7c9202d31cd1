<template>
  <div class="app-container">
    <el-tag v-if="status === '1'" type="info" size="mini">{{
      statusName
    }}</el-tag>
    <el-tag v-else-if="status === '2'" type="" size="mini">{{
      statusName
    }}</el-tag>
    <el-tag v-else-if="status === '3'" type="danger" size="mini">{{
      statusName
    }}</el-tag>
    <el-tag v-else-if="status === '4'" type="warning" size="mini">{{
      statusName
    }}</el-tag>
    <el-tag v-else-if="status === '5'" type="success" size="mini">{{
      statusName
    }}</el-tag>
    <el-tag v-else-if="status === '6'" type="success" size="mini">{{
      statusName
    }}</el-tag>
  </div>
</template>
<script>
  export default {
    props: {
      status: {
        type: String,
        default: ''
      },
      statusName: {
        type: String,
        default: ''
      }
    },
    data() {
      return {};
    },
    methods: {}
  };
</script>
