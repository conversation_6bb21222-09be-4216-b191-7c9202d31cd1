<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <section>
        <el-button
          icon="el-icon-plus"
          type="primary"
          size="small"
          @click="operate"
          v-if="permission['device-inventory-add']"
          >新增</el-button
        >
      </section>
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      stripe
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        align="center"
        prop="no"
        label="计划单号"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.no || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="name"
        label="盘点名称"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="createUserName"
        label="制定人员"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="startDate"
        label="盘点日期"
        align="center"
        show-overflow-tooltip
        width="200px"
      >
        <template v-slot="{ row }"
          >{{ row.startDate || '-' }} ~ {{ row.endDate || '-' }}</template
        >
      </el-table-column>
      <el-table-column
        prop="completeTime"
        label="完成时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.completeTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="inventoryDeptName"
        label="盘点部门"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.inventoryDeptName || '-' }}</template>
      </el-table-column>

      <el-table-column
        align="center"
        prop="inventory"
        label="已盘/全部"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.inventory }} / {{ scope.row.totalQuantity }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="statusName"
        label="状态"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${inventoryStatusColor(row.status)};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.createUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.updateUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        width="150"
        align="center"
        label="更新时间"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" fixed="right">
        <template v-slot="{ row }">
          <section class="cell-operate-class">
            <el-button
              type="text"
              size="small"
              @click="detail(row)"
              v-if="permission['device-inventory-order']"
              >查看</el-button
            >
            <!--           执行中可以停止执行-->
            <el-button
              v-if="
                Number(row.status) !== 2 &&
                permission['device-inventory-start-and-stop']
              "
              :class="row.status === 0 ? 'green-btn' : 'danger-btn'"
              type="text"
              size="small"
              @click="sas(row)"
              >{{ Number(row.status) === 0 ? '启动' : '终止' }}
            </el-button>

            <el-popconfirm
              v-if="
                Number(row.status) === 0 &&
                permission['device-inventory-delete']
              "
              :title="`是否确定删除${row.no}盘点计划？`"
              @confirm="() => handleDelete(row)"
            >
              <el-button
                class="danger-btn"
                slot="reference"
                type="text"
                size="small"
                >删除</el-button
              >
            </el-popconfirm>
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex"></detail-index>
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import {
    getDevicePartsCheckListApi,
    delDevicePartsCheckApi,
    startDevicePartsCheckApi,
    stopDevicePartsCheckApi
  } from '@/api/equiment-full-life-api/device-inventory';
  import { inventoryStatusColor } from '@/views/equiment-full-life-cycle/spare-parts-management/util';

  import { mapGetters } from 'vuex';
  export default {
    name: 'MaintenanceList',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination
    },
    props: {},
    computed: {
      ...mapGetters(['permission', 'userInfo'])
    },
    data() {
      return {
        inventoryStatusColor,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        totalObj: {}
      };
    },

    mounted() {
      this.getList();

      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },
    methods: {
      //  启动终止
      sas(row) {
        this.$confirm(
          `是否确定${Number(row.status) === 0 ? '启动' : '终止'}${
            row.no
          }盘点计划？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(async () => {
            this.loading = true;
            try {
              //  启动终止接口 startSparePartsCheckApi /  stopSparePartsCheckApi
              Number(row.status) === 0
                ? await startDevicePartsCheckApi(row.id)
                : await stopDevicePartsCheckApi(row.id);
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.loading = false;
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      //  获取统计数据
      async getList() {
        this.loading = true;
        try {
          let res = await getDevicePartsCheckListApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row);
      },
      async handleDelete(row) {
        this.loading = true;
        try {
          await delDevicePartsCheckApi(row.id);
          await this.getList();
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
