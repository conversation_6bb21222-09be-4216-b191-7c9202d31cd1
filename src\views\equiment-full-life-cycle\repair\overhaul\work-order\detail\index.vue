<template>
  <dialog-drawer
    title="详情"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <span class="el-base-title">设备信息</span>
      <base-info :details="details"></base-info>
      <span class="el-base-title" v-if="details.remark">备注信息</span>
      <el-descriptions
        v-if="details.remark"
        border
        :labelStyle="{ width: '110px', textAlign: 'right' }"
        :contentStyle="{
          width: '300px',
          wordBreak: 'break-all',
          wordWrap: 'break-word'
        }"
        contentClassName="contentClassName"
      >
        <el-descriptions-item label="备注信息：">{{
          details.remark || '-'
        }}</el-descriptions-item>
      </el-descriptions>
      <span class="el-base-title">检修标准</span>
      <standard :list="details.standardList"></standard>
      <!--      <span class="el-base-title">检修图片</span>-->
      <!--      <el-image-->
      <!--        v-for="img in row.maintainRecord.abnormalImageList"-->
      <!--        :key="img.id"-->
      <!--        style="width: 50px; height: 50px"-->
      <!--        :src="convertFileUrl(img.domain)"-->
      <!--        fit="cover"-->
      <!--        :preview-src-list="[convertFileUrl(img.domain)]"-->
      <!--      ></el-image>-->
      <!--      <span class="el-base-title">备品备件消耗 </span>-->
      <!--      &lt;!&ndash;       是不是有耗材&ndash;&gt;-->
      <!--      <consumable :list="details.materialList"></consumable>-->
      <div style="padding-bottom: 50px">
        <logs ref="log"></logs>
      </div>
    </div>
  </dialog-drawer>
</template>
<script>
  import BaseInfo from './base-info.vue';
  import Standard from './standard.vue';
  import Consumable from './consumable.vue';
  import { getCheckRepairOrderViewApi } from '@/api/equiment-full-life-api/repair';
  import { convertFileUrl } from '@/util/util';
  import Logs from '@/views/equiment-full-life-cycle/components/logs/index.vue';

  export default {
    name: 'RepairViewIndex',
    components: {
      Logs,
      BaseInfo,
      Standard,
      Consumable
    },
    data() {
      return {
        convertFileUrl,
        eqId: '',
        visible: false,
        loading: false,
        details: {} // 详情数据
      };
    },
    methods: {
      async show(id) {
        if (id) {
          this.visible = true;
          await this.getDetail(id);
        }
      },
      closed() {
        this.visible = false;
        this.details = {
          equipmentAccount: {}
        };
      },
      // 获取详情接口
      async getDetail(no) {
        try {
          this.loading = true;
          const res = await getCheckRepairOrderViewApi({ no: no });
          this.details = res.data.data;
          await this.$refs['log'].getLogs(this.details.id, 'OVERHAUL_ORDER');
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }
</style>
