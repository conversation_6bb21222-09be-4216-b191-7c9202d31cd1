<template>
  <dialog-drawer
    title="维修"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="detail" v-loading="loading">
      <!-- ticket-information -->
      <span class="el-base-title">工单信息</span>
      <TicketInfo external :detail="detail"></TicketInfo>
      <!-- 工单信息 -->
      <span class="el-base-title">来源信息</span>
      <ResourceInfo :detail="detail"></ResourceInfo>
      <!-- 维修建议 -->
      <p class="el-base-title ai-btn-wrapper">
        维修建议
        <el-button size="small" type="primary" @click="jumpToAI" class="ai-btn">
          故障维修建议</el-button
        >
      </p>
      <el-descriptions
        border
        size="small"
        :labelStyle="{ width: '110px', textAlign: 'right' }"
        :contentStyle="{
          width: '300px',
          wordBreak: 'break-all',
          wordWrap: 'break-word'
        }"
        contentClassName="contentClassName"
      >
        <el-descriptions-item label="维修建议：">{{
          detail.repairSuggest || '-'
        }}</el-descriptions-item>
      </el-descriptions>
      <span class="el-base-title">维修内容</span>
      <el-form
        ref="form"
        :model="form"
        size="small"
        style="margin-bottom: 60px"
        label-position="right"
        label-suffix="："
        label-width="130px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="维修结果"
              :prop="`result`"
              :rules="[
                { required: true, message: '请选择', trigger: 'change' }
              ]"
            >
              <el-select
                v-model="form.result"
                filterable
                style="width: 90%"
                placeholder="请选择"
              >
                <el-option
                  v-for="dict in repairResultsList"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="结果确认方式"
              :prop="`checkMethod`"
              :rules="[{ required: true, message: '请输入', trigger: 'blur' }]"
            >
              <el-input
                placeholder="请输入"
                style="width: 90%"
                v-model.trim="form.checkMethod"
                maxlength="50"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障缺陷原因"
              :prop="`faultReason`"
              :rules="[{ required: true, message: '请输入', trigger: 'blur' }]"
            >
              <el-input
                placeholder="请输入"
                style="width: 95.5%"
                v-model.trim="form.faultReason"
                maxlength="50"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="维修时长"
              :prop="`duration`"
              :rules="[{ required: true, message: '请输入', trigger: 'blur' }]"
            >
              <el-input-number
                v-model.trim="form.duration"
                style="width: 80%"
                :step="0.5"
                :min="0.5"
                :max="1000"
              >
              </el-input-number>
              <span style="padding-left: 10px; color: #606266">小时</span>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="解决方案"
              :prop="`solution`"
              :rules="[{ required: true, message: '请输入', trigger: 'blur' }]"
            >
              <el-input
                placeholder="请输入 解决方案"
                style="width: 97%"
                type="textarea"
                v-model.trim="form.solution"
                maxlength="500"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="更换部件">
              <SparePart :standardItem="form" ref="sparePart"></SparePart>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="上传图片" :prop="`attachList`">
              <upload-img
                v-model="form.attachList"
                placeholder="上传图片"
                :limit="3"
                :clearValid="true"
                @handleSuccess="handleSuccess"
                formatLimit="jpeg,png,jpg"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>
<script>
  import {
    getInternalViewApi,
    repairApi
  } from '@/api/equiment-full-life-api/repair';
  import UploadImg from '@/components/uploadImage.vue';
  import SparePart from '@/views/equiment-full-life-cycle/repair/overhaul/work-order/perform-overhaul/spare-part.vue';
  import { getFileFullUrl } from '@/util/file';
  import Logs from '@/views/equiment-full-life-cycle/components/logs';
  import TicketInfo from '@/views/equiment-full-life-cycle/repair/internal/detail/ticket-information.vue';
  import ResourceInfo from '@/views/equiment-full-life-cycle/repair/internal/detail/resource-information.vue';
  import RepairContent from '@/views/equiment-full-life-cycle/repair/internal/detail/repair-content.vue';
  import RepairVerify from '@/views/equiment-full-life-cycle/repair/internal/detail/repair-verification.vue';
  const form = {
    result: undefined,
    checkMethod: undefined,
    faultReason: undefined,
    duration: undefined,
    solution: undefined,
    attachList: [],
    materialList: []
  };
  export default {
    name: 'RepairViewIndex',
    components: {
      Logs,
      UploadImg,
      SparePart,
      TicketInfo,
      ResourceInfo,
      RepairContent,
      RepairVerify
    },
    data() {
      return {
        eqId: '',
        type: '',
        visible: false,
        loading: false,
        detail: {
          equipmentAccount: {},
          materialList: []
        }, // 详情数据
        form: { ...form },
        repairResultsList: [
          { label: '已修复', value: 1 },
          { label: '未完全修复，可运行', value: 2 }
        ]
      };
    },
    methods: {
      async submit() {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            // 更换部件
            let materialList = this.form.materialList;
            if (materialList && materialList.length) {
              let notCompleteIndex = -1;
              materialList.every((item, index) => {
                if (typeof item.count !== 'number') {
                  notCompleteIndex = index + 1;
                  return false; // 返回 false 会终止 every 的检查
                }
                return true;
              });

              if (notCompleteIndex > -1) {
                this.loading = false;
                this.$message.warning(
                  `请完善第 ${notCompleteIndex} 个部件信息`
                );
                return false;
              }
            }
            try {
              this.loading = true;
              const attachId =
                this.form.attachList
                  .map((item) => {
                    return item.attachId ? item.attachId : item.id;
                  })
                  .join(',') || '';
              const params = {
                repairId: this.detail.id,
                repairRecord: {
                  ...this.form,
                  attachId,
                  attachList: null
                }
              };
              // 提交数据
              await repairApi(params);
              this.$message.success('操作成功');
              this.$emit('success');
              this.visible = false;
              this.loading = false;
            } catch (e) {
              this.loading = false;
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      handleSuccess() {
        this.$refs['form'].clearValidate('attachList');
      },
      getFileFullUrl,
      show(id, type) {
        this.type = type || '';
        this.visible = true;
        if (id) {
          this.getDetail(id);
        }
      },
      closed() {
        this.visible = false;
        this.form = { ...form };
        this.form.attachList = [];
        this.form.materialList = [];
        this.detail = {
          equipmentAccount: {}
        };
      },
      // 获取详情接口
      async getDetail(no) {
        try {
          this.loading = true;
          const res = await getInternalViewApi({ no: no });
          this.detail = res.data.data;
          this.detail.materialList = this.detail.repairComponentList;
          this.form.materialList = this.detail.repairComponentList || [];
          await this.$refs['log'].getLogs(this.detail.id, 'INTERNAL_REPAIR');
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 跳转到故障维修建议
      jumpToAI() {
        const { equipmentId, equipmentAccount, problemComment } = this.detail;
        this.closed();
        this.$router.push({
          name: 'aitools',
          params: {
            deviceId: equipmentId,
            deviceName: equipmentAccount.name,
            defectDesc: problemComment,
            type: 'diagnose'
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  ::v-deep .el-card__body {
    padding-top: 0;
  }

  .ai-btn-wrapper {
    position: relative;

    .ai-btn {
      position: absolute;
      top: -9px;
      right: 0;
    }
  }
</style>
