<template>
  <dialog-drawer
    title="详情"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <span class="el-base-title">设备信息</span>
      <base-info :details="details"></base-info>

      <span class="el-base-title">润滑标准</span>
      <standard :list="[details]"></standard>

      <span class="el-base-title">备品备件消耗 </span>
      <!--       是不是有耗材-->
      <consumable :list="details.materialList"></consumable>
      <span class="el-base-title" v-if="details.remark">备注信息</span>
      <el-descriptions
        v-if="details.remark"
        border
        :labelStyle="{ width: '110px', textAlign: 'right' }"
        :contentStyle="{
          width: '300px',
          wordBreak: 'break-all',
          wordWrap: 'break-word'
        }"
        contentClassName="contentClassName"
      >
        <el-descriptions-item label="备注信息：">{{
          details.remark || '-'
        }}</el-descriptions-item>
      </el-descriptions>
      <div style="padding-bottom: 50px">
        <logs ref="log"></logs>
      </div>
    </div>
  </dialog-drawer>
</template>
<script>
  import Consumable from '@/views/equiment-full-life-cycle/components/consumable.vue';
  import BaseInfo from './base-info.vue';
  import Standard from './standard.vue';
  import { getOrderDetail } from '@/api/equiment-full-life-api/oiling';
  import { convertFileUrl } from '@/util/util';
  import Logs from '@/views/equiment-full-life-cycle/components/logs/index.vue';

  export default {
    name: 'RepairViewIndex',
    components: {
      Logs,
      BaseInfo,
      Standard,
      Consumable
    },
    data() {
      return {
        convertFileUrl,
        eqId: '',
        visible: false,
        loading: false,
        details: {} // 详情数据
      };
    },
    methods: {
      async show(no) {
        if (no) {
          this.visible = true;
          await this.getDetail(no);
        }
      },
      closed() {
        this.visible = false;
        this.details = {
          equipmentAccount: {}
        };
      },
      // 获取详情接口
      async getDetail(no) {
        try {
          this.loading = true;
          const res = await getOrderDetail({ no: no });
          this.details = res.data.data;

          await this.$refs['log'].getLogs(this.details.id, 'LUBRICATE_ORDER');

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }
</style>
