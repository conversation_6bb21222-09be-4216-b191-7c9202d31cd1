<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <!--      <search ref="search" @search="onsubmit" />-->
      <el-button
        v-if="permission['lubrication-way-add-edit']"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 100px)"
      border
      stripe
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>

      <el-table-column
        align="center"
        prop="name"
        label="润滑方式"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        align="center"
        label="备注"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.remark || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.createUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.updateUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        width="150"
        align="center"
        label="更新时间"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.updateTime || '-' }} </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100" fixed="right">
        <template v-slot="{ row }">
          <el-button
            v-if="permission['lubrication-way-add-edit']"
            type="text"
            size="small"
            @click="operate(row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
  </basic-container>
</template>

<script>
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';

  import { getOilingWayList } from '@/api/equiment-full-life-api/oiling';
  import { mapGetters } from 'vuex';

  export default {
    name: 'oilingWay',
    components: {
      Search,
      AddDeviceBase,
      Pagination
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getOilingWayList({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
