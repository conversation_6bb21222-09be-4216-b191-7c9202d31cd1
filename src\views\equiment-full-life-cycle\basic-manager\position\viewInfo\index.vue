<template>
  <basic-container :autoHeight="true">
    <search ref="search" @query="search"></search>
    <el-table
      size="small"
      :data="list"
      border
      v-loading="loading"
      height="calc(100% - 120px)"
    >
      <el-table-column type="index" width="50"></el-table-column>
      <!--      <el-table-column-->
      <!--        prop="code"-->
      <!--        label="设备编号"-->
      <!--        align="center"-->
      <!--      ></el-table-column>-->
      <el-table-column
        prop="sn"
        label="设备SN"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="model"
        label="规格型号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="categoryName"
        label="设备类型"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="statusName"
        label="设备状态"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="model"
        label="操作"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="text"
            @click="configuration(row, 'operate')"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />

    <point-dialog ref="pointDialog"></point-dialog>
  </basic-container>
</template>

<script>
  import Search from './search';
  import PointDialog from '../operate/device-info.vue';
  import { accountListPage } from '@/api/equiment-full-life-api/ledger';
  export default {
    name: 'bearingLibraryIndex',
    components: { PointDialog, Search },
    props: {
      locationId: {
        // 设备id
        type: String,
        default: () => {
          return '';
        }
      },
      // 因为部位绑定轴承和齿数 和 波形绑定算法前面相同，只是配置页面不同，所以公用一套
      where: {
        type: String,
        default: () => {
          return 'position-bearing';
        }
      }
    },
    data() {
      return {
        params: {},
        nodeId: undefined,
        isShowBtn: false,
        searchParams: {
          size: 10,
          current: 1
        },
        total: 0,
        loading: false,
        list: []
      };
    },
    watch: {
      locationId: {
        handler(val) {
          this.isShowBtn = !!val;
          if (val) {
            this.nodeId = val;
            this.getList();
          }
        }
      }
    },
    mounted() {},
    methods: {
      // 點擊搜索
      search(params) {
        this.params = params;
        this.getList();
      },
      // 配置轴承、齿数信息
      configuration(row) {
        console.log('row', row);
        this.$refs['pointDialog'].show(row.id);
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await accountListPage({
            locationId: this.locationId,
            ...this.params,
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
          this.loading = false;
        } catch (message) {
          this.loading = false;
          console.log('err', message);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-scrollbar__view {
      height: 98% !important;
    }
  }
</style>
