<template>
  <div class="num">
    <el-form
      :model="form"
      inline
      label-suffix="："
      ref="baseForm"
      label-position="right"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <span v-for="(item, idx) in form.list" :key="idx">
          <el-col :span="11">
            <el-form-item
              class="nums"
              label=""
              label-width="100px"
              :prop="'list.' + idx + '.time'"
              :rules="{
                required: true,
                message: '请选择时间段',
                trigger: ['change']
              }"
            >
              <el-time-picker
                v-model="item.time"
                is-range
                range-separator="到"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="1" v-if="form.list.length > 1">
            <i class="el-icon-remove" @click="del(idx)"> </i>
          </el-col>
        </span>
      </el-row>
    </el-form>
    <div class="button">
      <el-button
        type="primary"
        @click="add"
        :disabled="this.form.list.length === 10"
        size="small"
      >
        + 继续添加执行时间段
      </el-button>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'bearingLibraryIndex',
    props: {
      detail: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },

    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              if (this.detail.byDaySet && this.detail.byDaySet.length > 0) {
                this.form.list = this.detail.byDaySet.map((item) => {
                  return {
                    time: [item.startTime, item.endTime]
                  };
                });
              }
            });
          }
        }
      }
    },
    data() {
      function codeRepeat(all, val) {
        // 两种判重逻辑 1、本地判重 2、接口返回判重
        let codeArr = all.map((it) => it.teamRank);
        let first = codeArr.indexOf(val);
        let last = codeArr.lastIndexOf(val);

        if (first !== last) {
          return true;
        } else {
          return false;
        }
      }
      return {
        repeatRule: [
          {
            required: true,
            message: '请输入班次名称',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              // 如果是null 空字符串
              if (!value) {
                callback();
              } else if (codeRepeat(this.form.list, value)) {
                callback(new Error('名称不能重复!'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        form: {
          list: [{ time: undefined }]
        },
        obj: {
          time: undefined
        }
      };
    },

    mounted() {},
    methods: {
      add() {
        this.form.list.push({ ...this.obj });
      },
      async validForm() {
        if (this.form.list.length === 0) {
          this.$message.warning('请添加时间');
          return false;
        }
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          let list = this.form.list.map((item) => {
            return {
              ...item,
              startTime: item.time[0],
              endTime: item.time[1]
            };
            // 校验
          });
          let bool = isTimeNonOverlapping(list);
          if (!bool) {
            return list;
          } else {
            this.$message.warning('工作时间设置之间存在时间交叉，请检查');
            return false;
          }
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs['baseForm'].resetFields();
        this.form.list = [{ time: undefined }];
      },
      del(idx) {
        this.form.list.splice(idx, 1);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .button {
    display: flex;
    justify-content: center;
  }
  :deep(.el-form-item) {
    width: 100% !important;
    margin-bottom: 0 !important;
  }
  .el-col {
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
  .el-row {
    margin-bottom: 15px;
  }
  .el-icon-remove {
    color: red;
    font-size: 20px;
    margin-top: 5px;
    cursor: pointer;
  }
</style>
