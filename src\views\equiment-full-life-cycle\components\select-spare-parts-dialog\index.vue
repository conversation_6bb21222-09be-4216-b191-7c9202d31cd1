<template>
  <dialog-popup
    title="选择备品备件字典"
    ref="dialogs"
    :visible="visible"
    @closed="closed"
    width="80%"
    class="selectSensor"
  >
    <search
      ref="search"
      :whorehouseList="whorehouseList"
      @search="onSubmit"
    ></search>
    <el-row>
      <el-row>
        <el-col :span="16" style="height: 460px; overflow-y: scroll">
          <el-table
            ref="multipleTable"
            v-loading="loading"
            :data="dataSource"
            border
            size="small"
            :key="key"
            stripe
            row-key="dictId"
            :headerCellStyle="{ background: '#fafafa' }"
            :reserve-selection="true"
            @select="handleCheckBox"
            @select-all="handleSelectAll"
          >
            <el-table-column type="selection" width="55" :selectable="isCheck">
            </el-table-column>

            <el-table-column
              property="name"
              label="备品备件名称"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              property="no"
              label="备品备件编号"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              property="model"
              label="规格型号"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.model || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              property="measureUnitName"
              label="计量单位"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.measureUnitName || '-' }}
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="8" style="height: 460px; overflow-y: scroll">
          <el-tag
            v-for="tag in allPageSelect"
            :closable="tag.nDelete ? false : true"
            :key="tag.dictId"
            :disable-transitions="false"
            @close="handleClose(tag)"
            style="margin-bottom: 5px; margin-left: 10px"
          >
            {{ tag.name }}
          </el-tag>
          <el-empty
            :image-size="100"
            v-if="allPageSelect.length === 0"
            description="暂无选择数据"
          ></el-empty>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" style="height: 100%">
          <pagination
            :page-size.sync="searchParams.size"
            :page-no.sync="searchParams.current"
            :total="total"
            @pagination="getList"
          />
        </el-col>
        <el-col :span="12" style="height: 100%">
          <div
            style="
              display: flex;
              align-items: flex-end;
              justify-content: flex-end;
            "
          >
            <btn type="confirm" @click="confirm" :loading="loading"></btn>
            <btn type="cancel" @click="closed"></btn>
          </div>
        </el-col>
      </el-row>
    </el-row>
  </dialog-popup>
</template>
<script>
  import {
    getSparePartsPageListApi,
    getCheckRepairTypeListApi
  } from '@/api/equiment-full-life-api/spare-parts';
  import Search from '@/views/equiment-full-life-cycle/components/select-spare-parts-dialog/search.vue';
  import { mapGetters } from 'vuex';
  import { deepClone } from '@/util/util';

  export default {
    components: { Search },
    props: {
      warehouseId: {
        type: String,
        default: () => {
          return undefined;
        }
      },
      // 是哪个模块传过来的
      //inStorage 入库  outStorage 出库  receive 领用
      module: {
        type: String,
        default: () => {
          return '';
        }
      },
      //  有的列表需要传递状态
      status: {
        type: String,
        default: () => {
          return undefined;
        }
      },
      // 是否展示库房搜索
      showWarehouseSearch: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        visible: false,
        loading: false,
        dataSource: [],
        total: 0,
        searchParams: {
          size: 10,
          current: 1
        },
        key: 0,
        // 库房列表
        whorehouseList: [],
        // 所有页上多选的数据之和
        allPageSelect: [],
        originLength: 0 // 已存在的长度
      };
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    watch: {},
    methods: {
      //  该条数据能不能选择； - 如果是
      isCheck(row) {
        if (row.nDelete) {
          // 这个是判断能不能删除的标记
          return false;
        } else {
          return true;
        }
      },
      onSubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      getKey() {
        return Date.now() + '';
      },
      handleCheckBox(rows, row) {
        if (rows.includes(row)) {
          // 新勾选
          this.$set(row, 'num', 1);
          this.$set(row, 'disabled', false);
          this.allPageSelect.push(row);
        } else {
          // 取消勾选
          this.$set(row, 'disabled', true);
          this.$set(row, 'num', undefined);
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item.dictId !== row.dictId
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        if (rows.length) {
          rows.forEach((row) => {
            this.$set(row, 'num', 1);
            this.$set(row, 'disabled', false);
            if (
              !this.allPageSelect.find((item) => item.dictId === row.dictId)
            ) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.dataSource.forEach((row) => {
            this.$set(row, 'num', undefined);
            this.$set(row, 'disabled', true);
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item.dictId !== row.dictId
            );
          });
        }
      },

      handleClose(row) {
        this.$set(row, 'num', undefined);
        let obj = this.dataSource.find((it) => {
          return it.dictId === row.dictId;
        });
        this.$set(row, 'disabled', true);
        this.$refs.multipleTable.toggleRowSelection(obj);
        let index = this.allPageSelect.findIndex(
          (it) => it.dictId === row.dictId
        );
        index !== -1 && this.allPageSelect.splice(index, 1);
      },
      search() {
        this.searchParams.current = 1;
        this.getList();
      },
      //  获取库房列表
      async getStoreHouseList() {
        let res = await getCheckRepairTypeListApi();
        this.whorehouseList = res.data.data;
      },
      show(originList) {
        this.resetFrom();
        let data = deepClone(originList);
        this.allPageSelect = data;
        this.visible = true;
        this.searchParams.current = 1;
        this.showWarehouseSearch && this.getStoreHouseList();
        this.getList(); // 部位列表
        this.key++;
      },

      resetFrom() {
        this.searchParams = {
          size: 10,
          current: 1
        };
        this.allPageSelect = [];
      },
      closed() {
        this.visible = false;
        this.allPageSelect = [];
      },
      // 点击clearable时触发
      clear() {
        this.searchParams.current = 1;
        this.getList();
      },
      //
      async getList() {
        this.loading = true;
        if (this.module === 'inStorage' && !this.warehouseId) {
          // 如果是领用，并且仓库没有仓库id ，直接返回不执行，其他条件继续 执行
          this.loading = false;
          return false;
        }

        try {
          let params = {
            ...this.searchParams,
            status: this.status
          };

          let res = await getSparePartsPageListApi(params);
          let data = res.data.data.records || [];
          this.dataSource = data.map((item) => {
            item.dictId = item.id;
            const mathingObj = this.allPageSelect.find(
              (m) => item.dictId === m.dictId
            );
            if (mathingObj) {
              return { ...mathingObj, ...item };
            } else {
              return item;
            }
          });
          this.$nextTick(() => {
            this.dataSource.map((item) => {
              if (item.num) {
                this.$refs.multipleTable.toggleRowSelection(item, true);
              }
            });
            this.total = res.data.data.total;
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      confirm() {
        if (this.allPageSelect.length === 0) {
          this.$message.warning('请选择备品备件！');
          return;
        }
        //  为什么做如下处理，是因为盘点工单的时候会使用
        let data = this.allPageSelect.map((it) => {
          return {
            ...it,
            dictName: it.name,
            dictNo: it.no,
            inventoryUserName: it.nDelete
              ? it.inventoryUserName
              : this.userInfo.real_name,
            beforeSystemStock: it.nDelete ? it.beforeSystemStock : 0
            // id: it.nDelete ? it.id : undefined // 不能从这个地方清除id
          };
        });
        this.$emit('getAssetList', data);
        this.visible = false;
      },
      onOneChoose(row) {
        this.$emit('on-choose', row);
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .el-pagination__sizes {
    display: none !important;
  }

  .search {
    margin-bottom: 10px;
  }

  /deep/ .pagination-container {
    text-align: left !important;
  }

  .clear {
    float: right;
  }

  /deep/ .el-pagination__jump {
    display: none !important;
  }

  .right {
    margin-left: 20px;
  }

  /deep/ {
    .el-form-item--small {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      padding: 10px 0;
    }
  }

  .table {
    :deep(.el-table__cell) {
      padding: 8px 0 !important;
    }
  }

  .selectSensor {
    :deep(.el-dialog__body) {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  ::v-deep {
    .el-input-number--mini {
      width: 100px !important;
    }
  }
</style>
