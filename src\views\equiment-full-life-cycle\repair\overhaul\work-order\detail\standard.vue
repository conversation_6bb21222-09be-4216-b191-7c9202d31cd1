<template>
  <div class="maintenance-standards-wrap">
    <el-form>
      <div
        v-for="(standardItem, index) in list"
        :key="standardItem.id"
        class="maintenance-standards-item"
      >
        <div class="standard-box">
          <span class="el-base-title"
            >{{ index + 1 }}、{{ standardItem.monitorName || '-' }}</span
          >
          <el-descriptions
            border
            :labelStyle="{ width: '110px', textAlign: 'right' }"
            :contentStyle="{
              width: '300px',
              wordBreak: 'break-all',
              wordWrap: 'break-word'
            }"
            contentClassName="contentClassName"
          >
            <el-descriptions-item label="检修标准：">{{
              standardItem.standard || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="检修方式：">{{
              standardItem.methodsName || '-'
            }}</el-descriptions-item>
            <el-descriptions-item
              v-if="standardItem.record"
              label="检修结果："
              >{{
                standardItem.record.isAbnormal ? '异常' : '正常'
              }}</el-descriptions-item
            >
            <template
              v-if="standardItem.record && standardItem.record.isAbnormal === 1"
            >
              <el-descriptions-item label="故障缺陷名称：">{{
                standardItem.record.faultName || '-'
              }}</el-descriptions-item>
              <el-descriptions-item
                :span="standardItem.record.faultType === 1 ? 1 : 2"
                label="故障缺陷类型："
                >{{
                  standardItem.record.faultTypeName || '-'
                }}</el-descriptions-item
              >
              <el-descriptions-item
                v-if="standardItem.record.faultType === 1"
                label="故障缺陷等级："
                >{{
                  standardItem.record.faultLevelName || '-'
                }}</el-descriptions-item
              >
              <el-descriptions-item :span="3" label="异常描述：">{{
                standardItem.record.faultRemark || '-'
              }}</el-descriptions-item>
              <!--  -->
              <el-descriptions-item
                :span="standardItem.record.isReport == '0' ? 1 : 3"
                label="是否现场处理："
                >{{
                  standardItem.record.isReport == '0'
                    ? '现场已处理'
                    : '现场未处理，需上报'
                }}</el-descriptions-item
              >
              <template v-if="standardItem.record.isReport == '0'">
                <el-descriptions-item label="维修结果：">{{
                  standardItem.record.result == '1'
                    ? '已修复'
                    : '未完全修复，可运行'
                }}</el-descriptions-item>
                <el-descriptions-item label="结果确认方式：">{{
                  standardItem.record.checkMethod || '-'
                }}</el-descriptions-item>
                <el-descriptions-item label="维修时长："
                  >{{ standardItem.record.duration || '-'
                  }}<span style="padding-left: 10px; color: #606266">小时</span>
                </el-descriptions-item>
                <el-descriptions-item label="故障缺陷原因：">{{
                  standardItem.record.faultReason || '-'
                }}</el-descriptions-item>
                <el-descriptions-item label="解决方案：">{{
                  standardItem.record.solution || '-'
                }}</el-descriptions-item>
              </template>
              <el-descriptions-item :span="3" label="更换部件：">
                <SparePart
                  :standardItem="standardItem.record"
                  onlyView
                ></SparePart>
              </el-descriptions-item>
              <el-descriptions-item :span="3" label="上传图片：">
                <upload-img
                  v-model="standardItem.record.attachList"
                  placeholder="上传图片"
                  :editable="false"
                  :limit="3"
                  formatLimit="jpeg,png,jpg"
                />
              </el-descriptions-item>
            </template>
          </el-descriptions>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
  import UploadImg from '@/components/uploadImage.vue';
  import SparePart from '@/views/equiment-full-life-cycle/repair/overhaul/work-order/perform-overhaul/spare-part.vue';

  export default {
    name: 'bearingLibraryIndex',
    components: { UploadImg, SparePart },
    props: {
      list: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped>
  .maintenance-standards-wrap {
    padding: 0 20px;

    .el-base-title {
      color: #409eff;
    }
  }
</style>
