<template>
  <el-form :model="form" size="small" ref="baseForm">
    <el-button size="small" type="primary" @click="choose" class="el-icon-plus">
      选择备品备件
    </el-button>
    <el-table
      v-loading="loading"
      class="table"
      :data="form.list"
      row-key="id"
      size="small"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#"></el-table-column>
      <el-table-column prop="no" label="备品备件编号" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="name" label="备品备件名称" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.model || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="measureUnitName"
        label="计量单位"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.measureUnitName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="count" label="消耗数量" width="300">
        <template slot="header" slot-scope="scope">
          <span style="color: red"> * </span>消耗数量
        </template>
        <template slot-scope="scope">
          <el-form-item
            label=""
            :prop="'list.' + scope.$index + '.count'"
            :rules="[
              {
                required: true,
                message: '请输入消耗数量',
                trigger: 'blur'
              },
              {
                validator: (rule, value, callback) =>
                  validateCode(rule, value, callback)
              }
            ]"
          >
            <!--            <el-input-number-->
            <!--              v-model="scope.row.count"-->
            <!--              placeholder="请输入消耗数量"-->
            <!--              :min="0"-->
            <!--              :max="9999"-->
            <!--              :precision="scope.row.measureUnitPrecision"-->
            <!--              :controls="false"-->
            <!--            ></el-input-number>-->
            <el-input
              style="width: 240px"
              size="small"
              placeholder="请输入消耗数量"
              v-model.trim="scope.row.count"
              clearable
              maxlength="50"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop=" " label="操作" show-overflow-tooltip width="60">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            style="color: red"
            @click="handleDelete(scope.$index, scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <choose-spare-part
      ref="choose"
      @getAssetList="getAssetList"
      showWarehouseSearch
    ></choose-spare-part>
  </el-form>
</template>

<script>
  import ChooseSparePart from '@/views/equiment-full-life-cycle/components/select-receive-spare-parts-dialog';
  import { validateValueThen0 } from '@/util/func';
  export default {
    name: 'DeviceBasicList',
    components: { ChooseSparePart },
    props: {},
    data() {
      const validateCode = (rule, value, callback) => {
        // 获取当前数组
        let arr = rule.field.split('.');
        let idx = Number(arr[1]);
        let max = 9999;
        let measureUnitPrecision = this.form.list[idx].measureUnitPrecision;
        let val = validateValueThen0(value, max, measureUnitPrecision);
        if (!value) {
          callback();
        } else if (val) {
          callback();
        } else {
          callback(
            new Error(
              `请输入大于0且小于${max}的值，精度${measureUnitPrecision}`
            )
          );
        }
      };
      return {
        validateCode,
        loading: false,
        total: 0,
        form: {
          list: []
        }
      };
    },

    methods: {
      // 弹窗返回备件
      getAssetList(list) {
        console.log(list);
        let arr = [...list];
        let arrList = arr.map((item) => {
          return {
            no: item.dictNo,
            name: item.dictName,
            model: item.model,
            dictName: item.dictName,
            stockId: item.id, // 这两个id必传
            dictNo: item.dictNo,
            dictModel: item.model,
            measureUnitName: item.measureUnitName,
            measureUnitId: item.measureUnitId,
            count: item.count,
            measureUnitPrecision: item.measureUnitPrecision
          };
        });
        this.form.list = [...arrList];
      },
      async validForm() {
        //   先校验
        let bool = await this.$refs.baseForm.validate();
        if (bool) {
          return this.form.list;
        } else {
          return [];
        }
      },
      resetForm() {
        this.form = {
          list: []
        };
      },
      //  选择备品备件
      choose() {
        let list = this.form.list.map((it) => {
          return {
            ...it,
            id: it.stockId, // 这个是唯一匹配库存列表的数据
            num: 1
          };
        });
        this.$refs['choose'].show(list, '');
      },
      //  点击删除
      handleDelete(index) {
        this.form.list.splice(index, 1);
      }
    }
  };
</script>

<style scoped lang="scss"></style>
