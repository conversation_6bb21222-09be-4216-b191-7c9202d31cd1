<template>
  <basic-container>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="管理端数据权限" name="admin">
        <datascope></datascope>
      </el-tab-pane>
    </el-tabs>
  </basic-container>
</template>

<script>
  import Datascope from './datascope/datascope.vue';

  export default {
    data() {
      return {
        activeName: 'admin'
      };
    },
    components: { Datascope },
    methods: {
      handleClick() {
        console.log('handleClick');
      }
    }
  };
</script>
