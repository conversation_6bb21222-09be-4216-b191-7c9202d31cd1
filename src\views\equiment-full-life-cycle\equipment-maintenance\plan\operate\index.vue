<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <plan-cycle ref="info" :initData="detail"></plan-cycle>
      <p class="el-base-title">添加设备标准</p>
      <sel-asset ref="asset" :detail="detail"></sel-asset>
    </section>
    <div class="oper_btn">
      <btn type="submit" :loading="loading" @click="submit"></btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import PlanCycle from './plan-cycle.vue';
  import SelAsset from './sel-asset.vue';
  import {
    getPlanDetail,
    addPlan,
    updatePlan
  } from '@/api/equiment-full-life-api/maintenance';
  export default {
    name: 'AddDevice',
    components: {
      PlanCycle,
      SelAsset
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {
          image: []
        }
      };
    },
    watch: {},
    methods: {
      async getDetail(no) {
        this.loading = true;
        try {
          const res = await getPlanDetail({ no: no });
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      show(row) {
        this.visible = true;
        this.edit = !!row.id;
        this.eqId = row.id;

        if (row.id) {
          this.getDetail(row.no);
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.form.image = [];
        this.$refs['info'].resetForm();
        this.$refs['asset'].resetForm();
        this.detail = {};
      },

      async submit() {
        let params = await this.$refs['info'].validForm();
        let asset = this.$refs['asset'].validForm();
        if (params && asset) {
          this.edit
            ? await this.update({
                id: this.eqId,
                ...params,
                ...asset
              })
            : await this.save({
                ...params,
                ...asset
              });
        }
      },

      // 提交设备
      async save(params) {
        this.loading = true;
        try {
          await addPlan(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //    编辑设备
      async update(params) {
        this.loading = true;
        try {
          await updatePlan(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
