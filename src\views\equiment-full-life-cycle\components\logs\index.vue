<template>
  <div>
    <span class="el-base-title">{{ title }}</span>
    <el-timeline v-if="logs.length > 0">
      <el-timeline-item v-for="activity in logs" :key="activity.id">
        <!--        :timestamp="activity.operateTime"-->
        <span :style="`color:${getStatus(activity)}`">
          {{ activity.content }}</span
        >
        <!--        {{ activity.operateUserName }}-->
      </el-timeline-item>
    </el-timeline>
    <span style="color: #606266; font-size: 14px" v-else>暂无工单日志</span>
  </div>
</template>
<script>
  import { getLogList } from '@/api/equiment-full-life-api/common';

  export default {
    name: 'RepairViewIndex',
    components: {},
    props: {
      title: {
        type: String,
        default: '工单日志'
      }
    },
    data() {
      return {
        bizModule: undefined,
        logs: [] // 详情数据
      };
    },
    methods: {
      getStatus(active) {
        if (active.module === 'EQUIPMENT_CHANGE') {
          if (active.bizStatus === '3') {
            return 'red';
          } else {
            return '#303133';
          }
        } else {
          if (active.bizStatus === '6') {
            return 'red';
          } else {
            return '#303133';
          }
        }
      },
      // 获取详情接口
      async getLogs(bizId, module) {
        this.bizModule = module;
        try {
          this.loading = true;
          const res = await getLogList({ size: -1, bizId, module });

          // 处理每条日志的 content
          this.logs = (res.data.data.records || []).map((item) => {
            let newContent = item.content.replace(
              /(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})([^\s])/,
              '$1 $2'
            );
            const keywords = [
              '再次提交',
              '提交',
              '审核通过',
              '撤销',
              '驳回',
              '验收'
            ];
            keywords.forEach((word) => {
              newContent = newContent.replace(
                new RegExp(`([^\\s])(${word})`, 'g'),
                '$1 $2'
              );
            });

            return { ...item, content: newContent };
          });

          this.loading = false;
        } catch (e) {
          console.log(e);
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .mark {
    color: red;
  }

  .noMark {
    color: #303133;
  }
</style>
