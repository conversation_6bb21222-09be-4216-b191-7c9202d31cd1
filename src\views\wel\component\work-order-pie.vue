<template>
  <div class="alarmTrend">
    <span class="title">
      <section>
        <span style="color: #333; font-size: 20px"> 工单占比（当日）</span>
      </section>
    </span>
    <div
      id="pie_ticket_percentage_today"
      class="pie"
      v-if="flag"
      v-loading="loading"
    ></div>
    <custom-empty :size="70" v-else></custom-empty>
  </div>
</template>
<script>
  import { allOrderStat } from '@/api/home';
  import * as echarts from 'echarts/core';
  import {
    TooltipComponent,
    LegendComponent,
    GridComponent,
    TitleComponent
  } from 'echarts/components';
  import { PieChart } from 'echarts/charts';
  import { CanvasRenderer } from 'echarts/renderers';
  import CustomEmpty from '@/components/custom-empty.vue';
  // import { markRaw } from 'vue';
  echarts.use([
    TooltipComponent,
    TitleComponent,
    PieC<PERSON>,
    CanvasRenderer,
    LegendComponent,
    GridComponent
  ]);

  export default {
    name: 'alarmTrend',
    props: {},
    components: { CustomEmpty },

    data() {
      return {
        inspectOrders: [],
        loading: false,
        flag: true,
        total: 0
      };
    },
    computed: {
      uniqueId() {
        return `pie_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      }
    },
    mounted() {
      this.$nextTick(async () => {
        await this.getInspectOrders();
      });
      window.addEventListener('resize', this.resizeChart);
    },
    unmounted() {
      window.removeEventListener('resize', this.resizeChart);
    },
    methods: {
      //  设备分类统计
      async getInspectOrders() {
        this.loading = true;
        try {
          const res = await allOrderStat();
          let arr = res.data.data || [];
          this.inspectOrders = arr;
          await this.init();
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },

      resizeChart() {
        this.status_chart &&
          this.status_chart.resize({ width: 'auto', height: 'auto' });
      },
      //hex颜色转rgb颜色
      HexToRgb(str) {
        let r = /^\#?[0-9A-F]{6}$/;
        //test方法检查在字符串中是否存在一个模式，如果存在则返回true，否则返回false
        if (!r.test(str)) return console.log('输入错误的hex');
        //replace替换查找的到的字符串
        str = str.replace('#', '');
        //match得到查询数组
        let hxs = str.match(/../g);
        for (let i = 0; i < 3; i++) hxs[i] = parseInt(hxs[i], 16);
        //alert(pars
        return hxs;
      },
      //GRB颜色转Hex颜色
      RgbToHex(a, b, c) {
        let r = /^\d{1,3}$/;
        if (!r.test(a) || !r.test(b) || !r.test(c))
          return console.log('输入错误的rgb颜色值');
        let hexs = [a.toString(16), b.toString(16), c.toString(16)];
        for (let i = 0; i < 3; i++)
          if (hexs[i].length == 1) hexs[i] = '0' + hexs[i];
        return '#' + hexs.join('');
      },

      //得到hex颜色值为color的加深颜色值，level为加深的程度，限0-1之间
      getDarkColor(color, level) {
        let r = /^\#?[0-9A-F]{6}$/;
        if (!r.test(color)) return console.log('输入错误的hex颜色值');
        let rgbc = this.HexToRgb(color);
        //floor 向下取整
        for (let i = 0; i < 3; i++) rgbc[i] = Math.floor(rgbc[i] * (1 - level));
        console.log(this.RgbToHex(rgbc[0], rgbc[1], rgbc[2]));
        return this.RgbToHex(rgbc[0], rgbc[1], rgbc[2]);
      },

      init() {
        let chartDom = document.getElementById('pie_ticket_percentage_today');
        this.status_chart = echarts.init(chartDom);
        let pieCenter = ['30%', '50%'];
        let shadowData = this.inspectOrders.map((item) => {
          return {
            name: '',
            value: item.value
          };
        });
        let color = [
          '#1853B1',
          '#50A0F2',
          '#1E62FB',
          '#29AC4C',
          '#FD8424',
          '#FDA745'
        ];
        let darkColor = this.inspectOrders.map((item, i) => {
          return this.getDarkColor(color[i], 0.1);
        });
        console.log('ssssssss', darkColor);

        let option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}个'
          },
          color,
          legend: {
            orient: 'vertical',
            top: 'center',
            right: '10%',
            itemGap: 28,
            itemWidth: 14,
            itemHeight: 14,
            // 使用回调函数
            formatter: function (name) {
              return '{a|' + name + '}';
            },
            textStyle: {
              fontSize: 16,
              color: '#333333',
              rich: {
                a: {
                  color: '#333333',
                  width: 40
                },
                b: {
                  width: 50
                }
              }
            }
          },

          series: [
            {
              name: '阴影',
              type: 'pie',
              hoverAnimation: false,
              legendHoverLink: false,
              radius: ['38%', '55%'],
              center: pieCenter,

              color: darkColor,
              label: {
                normal: {
                  show: false
                }
              },
              labelLine: {
                normal: {
                  show: false
                }
              },
              emphasis: {
                scale: false
              },
              tooltip: {
                show: false
              },

              data: shadowData
            },
            {
              name: '工单占比（当日）',
              type: 'pie',
              // radius: ['30%', '70%'],
              radius: ['50%', '75%'],
              center: pieCenter,
              color: color,
              label: { formatter: '{b}：{c}个' },
              // selectedOffset: 30,
              // selectedMode: true,
              itemStyle: {},
              // labelLine: {
              //   show: false
              // },
              tooltip: {
                show: true,
                borderColor: 'rgba(0,0,0,0.0)',
                backgroundColor: 'rgba(0,0,0,0.5)',
                textStyle: {
                  color: '#fff', // 设置文字颜色
                  fontSize: 12 // 设置文字大小
                }
              },
              emphasis: {
                scale: true, // 启用放大效果
                scaleSize: 20 // 放大比例，可以根据需要调整
              },
              data: this.inspectOrders,
              roundCap: 1 //可选项为1和2，不填则采用原有拼接方式
            }
          ]
        };
        this.status_chart && this.status_chart.setOption(option);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .alarmTrend {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: #fff;
    border-radius: 5px;
  }

  .pie {
    width: 100%;
    height: calc(100% - 50px);
    border-radius: 5px;
  }

  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    section {
      width: calc(100% - 220px);
      overflow: hidden;
      text-wrap: nowrap;
      text-overflow: ellipsis;
    }

    em {
      display: inline-block;
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-style: normal;
      line-height: 10%;
    }
  }

  ::v-deep {
    .el-tabs__nav-wrap::after {
      background-color: unset;
    }

    .el-tabs__nav-wrap {
      top: -20px;
    }
  }

  .all {
    float: right;
    margin-top: 10px;
    color: #3d446e;
    font-weight: 600;
    font-size: 14px;
  }
</style>
