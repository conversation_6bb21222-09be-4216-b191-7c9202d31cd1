<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="工单编号" prop="no">
        <el-input
          style="width: 150px"
          v-model.trim="form.no"
          placeholder="请输入工单编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="计划名称" prop="orderName">
        <el-input
          style="width: 150px"
          v-model.trim="form.orderName"
          placeholder="请输入计划名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item label="工单状态" prop="status">
        <el-select
          v-model="form.status"
          style="width: 150px"
          placeholder="请选择工单状态"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['order_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择日期" prop="time" class="_label">
        <el-date-picker
          v-model="form.time"
          style="width: 250px"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择部门-->
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    serviceDicts: ['order_status'],
    components: {},
    data() {
      return {
        form: {
          no: undefined,
          orderName: undefined,
          status: undefined,
          time: undefined
        }
      };
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      // 清空并重新设置字段
      setFields() {
        this.$refs['search'].resetFields();
        // const { field, value } = fields;
        // this.form[field] = value;
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          startDate: this.form.time ? this.form.time[0] : undefined,
          endDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
