@import "./icon-portal/iconfont.css";
@import "./icon-index/iconfont.css";
@import "./icon-custom/iconfont.css";

// 全局变量
@import "./variables";

// ele样式覆盖
@import "./element-ui";

// 顶部右侧显示
@import "./top";

// 导航标签
@import "./tags";

// 工具类函数
@import "./mixin";

// 侧面导航栏
@import "./sidebar";

// 动画
@import "./animate/vue-transition";

// 主题
@import "./theme/index";

// 适配
@import "./media";

// 通用配置
@import "./normalize";
@import "./color";
@import "./biz-global-style";
@font-face {
  font-family: electronicFont;
  src: url("../asset/fonts/YouSheBiaoTiHei.ttf");
}

a {
  color: #333;
  text-decoration: none;
}

* {
  box-sizing: border-box;
  outline: none;
}

.el-drawer__header {
  margin-bottom: 0 !important;
}

// 弹窗操作按钮,靠右展示
.oper_btn {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 100;
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding: 10px 0;
  background: #fff;
  border-top: 1px solid #f0f0f0;

  > :last-child {
    margin-right: 15px;
  }
}

// 弹窗header样式
.el-dialog__header {
  padding-bottom: 10px;
  color: rgba(0, 0, 0, 85%);
  font-weight: 500;
  font-size: 18px;
  word-wrap: break-word;
  border-bottom: 1px solid #ebeef5;
}

// 滚动条样式
@include scrollBar;

.partsImg {
  .avatar-uploader-icon,
  .avatar,
  .avatar .img {
    width: 50px !important;
    height: 50px !important;
    margin-right: 0;
    margin-bottom: 0;
  }

  .img-block {
    margin-right: 0 !important;
  }

  .avatar-uploader {
    display: flex !important;
  }
}

// 滚动条不能拖动
// .el-scrollbar__bar,.is-vertical{
//   z-index: -1;
// }

.avue-crud__menu {
  background-color: transparent;
}

// 多选表格 居中
.el-checkbox:last-of-type {
  margin-right: 0 !important;
}

.table-vertical-top {
  .el-table__body td.el-table__cell {
    vertical-align: top !important;

    .el-checkbox {
      margin-top: 16px;
    }

    .el-button--text {
      margin-top: 20px;
    }
  }
}

.el-table-column--selection .cell {
  padding-right: 10px;
}

.disabled-filter-popover {
  .el-button--text {
    color: #606266;
    font-size: 16px;
  }

  .is-current {
    & > .el-tree-node__content {
      .el-button--text {
        color: #409eff !important;
      }
    }
  }
}

.samplingFreq {
  .el-select-dropdown__empty {
    display: none !important;
  }
}

.sensorInstanceEwm {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.el-icon-circle-close {
  cursor: pointer;
}

.el-textarea__inner {
  //resize: none; /* 禁用手动调整大小 */
  white-space: pre-wrap; /* 确保换行符生效 */
  word-break: break-all; /* 长单词或 URL 自动换行 */
  //min-height: 32px !important;
  //max-height: 200px !important;
  //height: auto !important;
}

// 共用弹窗样式
.distribute-dialog {
  .distribute-header {
    display: flex;
    justify-content: space-between;

    .avue-crud__dialog__menu {
      padding-right: 24px;
    }
  }

  .el-dialog__header {
    padding: 10px 20px;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__headerbtn {
    top: 12px;
  }

  .el-dialog__body {
    padding-bottom: 80px;
  }

  .el-dialog__footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 10px 20px;
    border-top: 1px solid #ebeef5;
  }

  &.file-preview-dialog {
    .el-dialog__body {
      min-height: 75vh;
      padding: 0;
    }

    .el-dialog__footer {
      display: none;
    }
  }
}

.el-textarea .el-input__count {
  background: unset !important;
}

.ai-logo {
  position: relative;

  &::after {
    position: absolute;
    top: 6px;
    right: 0;
    width: 20px;
    height: 20px;
    background: url(../asset/img/ai.png) no-repeat center center;
    background-size: contain;
    content: "";
  }
}

// cascader 点击文本选中
.location-popper {
  .el-radio {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
  }

  .el-checkbox {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
  }

  .el-radio__input {
    margin-top: 10px;
    margin-left: 8px;
  }

  .el-checkbox__input {
    margin-top: 2px;
    margin-left: 8px;
  }

  .el-cascader-node__postfix {
    top: 10px;
  }
}
