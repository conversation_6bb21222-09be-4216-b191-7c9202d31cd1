<template>
  <div class="search-wrapper">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="工单类型" prop="orderType" v-if="!sType">
        <el-select
          v-model="form.orderType"
          placeholder="请选择工单类型"
          clearable
        >
          <el-option label="全部" :value="undefined"></el-option>
          <el-option
            v-for="item in orderTypeOpts"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <!--      二级页面需要-->
      <!--         按照部门统计-->
      <el-form-item v-if="sType === 'DEPT'" label="选择部门" prop="deptId">
        <InputTree
          v-model="form.deptId"
          lazy
          clearable
          :form="searchParams"
          :dic="deptData"
          style="width: 100%"
          :props="{
            label: 'deptName',
            value: 'id',
            isLeaf: (row) => !row.hasChildren,
            formLabel: 'deptName',
            formValue: 'deptId'
          }"
          :load="lazyLoad"
          :lazyLoading="lazyLoading"
          @search="lazySearch"
        ></InputTree>
      </el-form-item>
      <!--         按照人员统计-->
      <el-form-item v-if="sType === 'USER'" label="人员名称" prop="userName">
        <el-input
          clearable
          v-model="form.userName"
          placeholder="请输入人员名称"
        >
        </el-input>
      </el-form-item>
      <!--        按照设备统计  v-if="sType === '1'"-->
      <el-form-item
        v-if="sType === 'EQUIPMENT'"
        label="设备名称"
        prop="equipmentName"
      >
        <el-input
          clearable
          v-model="form.equipmentName"
          placeholder="请输入设备名称"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="统计时间" prop="time" class="_label">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="截止日期"
          value-format="yyyy-MM-dd"
          :clearable="false"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button plain type="primary" @click="query">查 询</el-button>
        <el-button @click="reset">重 置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import dayjs from 'dayjs';
  import { getLazyList } from '@/api/system/dept';
  import { InputTree } from '@/components/yk-select-tree';

  export default {
    components: { InputTree },
    props: {
      //  选择的类型
      sType: {
        type: String,
        default: undefined
      },
      searchTime: {
        type: Array,
        default: () => {
          return [];
        }
      },
      searchParams: {
        type: Object,
        default: () => {
          return {};
        }
      },
      //  看看使用该组件的是一级页面还是二级页面
      num: {
        type: String,
        default: 'first'
      }
    },
    data() {
      return {
        loading: false,
        orderTypeOpts: [
          { label: '点巡检工单', value: 'INSPECT_ORDER' },
          { label: '内部维修工单', value: 'INTERNAL_REPAIR' },
          { label: '外委维修工单', value: 'EXTERNAL_REPAIR' },
          { label: '检修工单', value: 'OVERHAUL_ORDER' },
          { label: '保养工单', value: 'MAINTAIN_ORDER' },
          { label: '润滑工单', value: 'LUBRICATE_ORDER' }
        ],
        deptData: [],
        lazyLoading: false,
        form: {
          deptId: undefined, // 选择部门
          deptName: undefined,
          userName: undefined, // 选择人员
          equipmentName: undefined,
          //  下面是时间筛选 + 一级页面
          time: [],
          startDate: undefined,
          endDate: undefined,
          orderType: undefined
        }
      };
    },
    mounted() {
      this.$nextTick(() => {
        if (this.num === 'first') {
          // 如果是1级页面
          this.getDefaultTime();
          this.$emit('search', { ...this.form, time: undefined });
        } else {
          setTimeout(() => {
            this.handleSearchParams();
            this.$emit('search', { ...this.form, time: undefined });
          });
        }
      });
    },
    methods: {
      async lazySearch(title) {
        this.lazyLoad(null, null, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node || {};
        const { id } = data || {};
        let params = {
          deptName: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyList(title ? '' : id || 0, params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          if (title) {
            this.deptData = arr;
          } else {
            resolve && resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      // 处理上一级传递的搜索参数
      handleSearchParams() {
        console.log(this.sType, this.searchParams);
        const { userName, dept, equipmentName, dateRange } = this.searchParams;
        // 处理用户
        if (this.sType === 'USER' && userName) {
          this.form.userName = userName;
        }
        // 处理部门(内部维修/外委维修没有部门查询)
        if (this.sType === 'DEPT' && dept) {
          this.form.deptName = dept.deptName;
          this.form.deptId = dept.deptId;
        }
        // 处理设备
        if (this.sType === 'EQUIPMENT' && equipmentName) {
          this.form.equipmentName = equipmentName;
        }
        // 处理时间
        if (dateRange && dateRange[0]) {
          this.form.time = dateRange;
          this.form.startDate = dateRange[0];
          this.form.endDate = dateRange[1];
        } else if (this.searchTime[0]) {
          this.form.time = this.searchTime;
          this.form.startDate = this.searchTime[0];
          this.form.endDate = this.searchTime[1];
        } else {
          this.getDefaultTime();
        }
      },
      //  获取默认时间
      getDefaultTime() {
        const start = dayjs().subtract(30, 'days').format('YYYY-MM-DD');
        const end = dayjs().format('YYYY-MM-DD');
        this.form.time = [start, end];
        this.form.startDate = start;
        this.form.endDate = end;
      },
      // 搜索
      query() {
        const [startDate, endDate] = this.form.time;
        this.form.startDate = startDate;
        this.form.endDate = endDate;
        this.$emit('search', { ...this.form, time: undefined });
      },
      // 重置
      reset() {
        this.form.deptId = undefined;
        this.$refs['search'].resetFields();
        if (this.num === 'first') {
          this.getDefaultTime();
        }
        this.query();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .search-wrapper {
    ::v-deep .el-form-item {
      margin-bottom: 12px;
    }
  }
</style>
