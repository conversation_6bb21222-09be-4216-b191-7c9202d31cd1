<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="110px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="出库类型" prop="outboundType">
            <el-select
              v-model="form.outboundType"
              placeholder="请选择入库类型"
              clearable
              @change="typeChange"
            >
              <el-option
                v-for="item in serviceDicts.type['outbound_type']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="用途" prop="outboundUse">
            <el-select
              v-model="form.outboundUse"
              placeholder="请选择用途"
              clearable
            >
              <el-option
                v-for="item in serviceDicts.type['outbound_use']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!--请领出库的时候隐藏-->
        <el-col :span="8" v-if="form.outboundType === '2'">
          <el-form-item label="出库仓库" prop="warehouseId">
            <el-select
              v-model="form.warehouseId"
              placeholder="请选择出库仓库"
              clearable
              filterable
              @change="changeWarehouse"
            >
              <el-option
                v-for="item in whorehouse"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="出库日期" prop="outboundDate">
            <el-date-picker
              v-model="form.outboundDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              clearable
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.outboundType === '1'">
          <el-form-item label="请领单" prop="issuanceOrderName">
            <el-input
              style="width: 100%"
              placeholder="请选择请领单"
              v-model.trim="form.issuanceOrderName"
              @focus.prevent="onSelectReceiveClick"
              readonly
            >
              <template slot="append">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      selectedRow();
                      form.receiveUserId = undefined;
                      form.receiveUserName = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领用部门" prop="receiveDeptName">
            <el-input
              style="width: 100%"
              placeholder="请选择领用部门"
              v-model.trim="form.receiveDeptName"
              @focus.prevent="onSelectDeptClick()"
              readonly
              clearable
              :disabled="form.outboundType === '1'"
            >
              <template slot="append" v-if="form.outboundType !== '1'">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      form.receiveDeptName = undefined;
                      form.receiveDeptId = undefined;
                      form.receiveUserId = undefined;
                      form.receiveUserName = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.outboundType === '2'">
          <el-form-item label="领用人" prop="receiveUserId">
            <el-select
              v-loading="userLoading"
              v-model="form.receiveUserId"
              filterable
              placeholder="请选择领用人"
              clearable
            >
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="item.realName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="form.outboundType === '1'">
          <el-form-item label="领用人" prop="receiveUserName">
            <el-input
              placeholder="请选择领用人"
              v-model="form.receiveUserName"
              :disabled="form.outboundType === '1'"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              placeholder="请输入备注"
              v-model.trim="form.remark"
              :maxlength="200"
              clearable
              show-word-limit
              type="textarea"
              autosize
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--     选择请领单弹窗-->
    <select-receive-dialog
      ref="receive"
      @selectedRow="selectedRow"
    ></select-receive-dialog>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import SelectReceiveDialog from '@/views/equiment-full-life-cycle/components/select-receive-dialog/index.vue';
  import DeptDialog from '@/components/dept-dialog/index.vue';
  import { getUserListByDeptId } from '@/api/equiment-full-life-api/common';

  export default {
    serviceDicts: ['outbound_type', 'outbound_use'], //out_storage_type
    name: 'InStorageType',
    components: { SelectReceiveDialog, DeptDialog },
    props: {
      initData: {
        type: Object,
        default: () => {}
      },
      whorehouse: {
        type: Array,
        default: () => []
      }
    },

    data() {
      return {
        form: {
          outboundType: '1',
          outboundUse: undefined,
          warehouseId: undefined,
          outboundDate: undefined,
          issuanceOrderId: undefined,
          issuanceOrderName: undefined,
          receiveDeptName: undefined,
          receiveDeptId: undefined,
          receiveUserId: undefined,
          receiveUserName: undefined,
          remark: undefined
        },
        userList: [],
        userLoading: false,
        edit: false,
        rules: {
          outboundType: [
            {
              required: true,
              message: '请选择出库类型',
              trigger: 'change'
            }
          ],
          // warehouseId: [
          //   { required: true, message: '请选择出库仓库', trigger: 'change' }
          // ],
          outboundUse: {
            required: true,
            message: '请选择用途',
            trigger: 'change'
          },
          receiveUserId: [
            {
              required: true,
              message: '请选择领用人',
              trigger: 'change'
            }
          ],
          receiveUserName: [
            { required: true, message: '请选择领用人', trigger: 'blur' }
          ],
          issuanceOrderName: [
            { required: true, message: '请选择请领单', trigger: 'change' }
          ],
          receiveDeptName: [
            {
              required: true,
              message: '请选择领用部门',
              trigger: 'change'
            }
          ],
          outboundDate: [
            { required: true, message: '请选择出库日期', trigger: 'change' }
          ]
        }
      };
    },

    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          } else {
            this.$emit('outTypeChange', this.form.outboundType);
          }
        }
      },
      'form.warehouseId': {
        immediate: true,
        deep: true,
        handler(val) {
          this.$emit('warehouseId', val);
        }
      }
      // whorehouse: {
      //   immediate: true,
      //   deep: true,
      //   handler(val) {
      //     if (val.length > 0) {
      //       this.form.warehouseId = val[0].id;
      //     }
      //   }
      // }
    },
    created() {},
    methods: {
      // 选择部门
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.receiveDeptId = dept.id;
        this.form.receiveDeptName = dept.deptName;
        this.form.receiveUserId = undefined;

        this.getUser(dept.id);
      },
      // 选择人员
      async getUser(deptId) {
        this.userLoading = true;
        try {
          let params = {
            deptId: deptId
            // includeSubDepartments: true
          };
          let res = await getUserListByDeptId(params);
          this.userList = res.data.data;
          this.userLoading = false;
        } catch ({ message }) {
          this.userLoading = false;
          console.log(message);
        }
      },
      //  切换库房，清空请领单
      changeWarehouse() {
        this.form.receiveUserId = undefined;
        this.form.receiveUserName = undefined;
      },
      //  选择请领单
      selectedRow(row) {
        console.log(row);
        if (row) {
          this.form.issuanceOrderId = row.id;
          this.form.issuanceOrderName = row.name;

          this.form.receiveUserId = row.receiveUserId;
          this.form.receiveUserName = row.receiveUserName;
          this.form.receiveDeptId = row.receiveDeptId;
          this.form.receiveDeptName = row.receiveDeptName;
        }
        this.$emit('selectedRowParent', row);
      },
      onSelectReceiveClick() {
        this.$refs['receive'].show(this.form.warehouseId);
      },
      //  出库类型改变
      typeChange(e) {
        console.log(e);
        // 清空领用人
        this.form.receiveUserId = undefined;
        this.form.receiveUserName = undefined;
        // 清空领用部门
        this.form.receiveDeptId = undefined;
        this.form.receiveDeptName = undefined;
        // 清空请领单
        this.form.issuanceOrderName = undefined;
        this.form.issuanceOrderId = undefined;
        //  出库类型选择其他出库的时候，出库库房默认选择第一个
        if (Number(e) === 2) {
          if (this.whorehouse.length > 0) {
            this.form.warehouseId = this.whorehouse[0].id;
            this.$emit('warehouseId', this.whorehouse[0].id);
          }
        } else {
          //  清空仓库
          this.form.warehouseId = undefined;
        }
        this.$emit('outTypeChange', e);
      },
      setData(initData) {
        if (initData.receiveDeptId) {
          this.getUser(initData.receiveDeptId);
        }
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
        this.$emit('outTypeChange', initData.outboundType);
      },

      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return this.form;
        } else {
          return false;
        }
      },
      resetForm() {
        this.form.receiveId = undefined;
        this.form.receiveName = undefined;
        this.$refs['baseForm'].resetFields();
        this.form.outboundType = '1';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-input-number {
        width: 100%;

        .el-input__inner {
          text-align: left;
        }
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
