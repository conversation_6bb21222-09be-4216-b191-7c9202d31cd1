import Intact from './intact-comp.vue';
import Repair from './repair-comp.vue';
import Implement from './implement-comp.vue';
import IntactView from '../components/intact-comp-view.vue';
import RepairList from '../components/repair-list-compont/index.vue';
import ImplementView from '../components/implement-comp-view.vue';
import ImplementList from '../components/implement-comp-list.vue';
//  下面是计划执行率 - 中三级页面组件
import InspectList from '../components/order-list/inspect-list.vue';
import MaintenanceList from '../components/order-list/maintenance-list.vue';
import LubricationList from '../components/order-list/lubrication-list.vue';
import OveralList from '../components/order-list/overal-list.vue';
import InternalList from '../components/order-list/internal-list.vue';
import EnternalList from '../components/order-list/external-list.vue';

export {
  InspectList,
  MaintenanceList,
  LubricationList,
  OveralList,
  InternalList,
  EnternalList,
  Intact,
  Repair,
  Implement,
  IntactView,
  RepairList,
  ImplementView,
  ImplementList
};

const keyArr = [
  {
    type: 'INSPECT_ORDER',
    keyArr: [
      { searchType: 'DEPT', key: 'executeDept' },
      { searchType: 'USER', key: 'onlyQueryExecuteUser' },
      { searchType: 'EQUIPMENT', key: 'equipmentId' }
    ]
  },
  {
    type: 'MAINTAIN_ORDER',
    keyArr: [
      { searchType: 'DEPT', key: 'executeDept' },
      { searchType: 'USER', key: 'onlyQueryExecuteUser' },
      { searchType: 'EQUIPMENT', key: 'equipmentId' }
    ]
  },
  {
    type: 'OVERHAUL_ORDER',
    keyArr: [
      { searchType: 'DEPT', key: 'executeDept' },
      { searchType: 'USER', key: 'onlyQueryExecuteUser' },
      { searchType: 'EQUIPMENT', key: 'equipmentId' }
    ]
  },
  {
    type: 'LUBRICATE_ORDER',
    keyArr: [
      { searchType: 'DEPT', key: 'chargeDept' },
      { searchType: 'USER', key: 'onlyQueryExecuteUser' },
      { searchType: 'EQUIPMENT', key: 'equipmentId' }
    ]
  },
  {
    type: 'INTERNAL_REPAIR',
    keyArr: [
      { searchType: 'DEPT', key: 'receiveDept' },
      { searchType: 'USER', key: 'receiveUser' },
      { searchType: 'EQUIPMENT', key: 'equipmentId' }
    ]
  },
  {
    type: 'EXTERNAL_REPAIR',
    keyArr: [
      { searchType: 'DEPT', key: 'followDept' },
      { searchType: 'USER', key: 'followUser' },
      { searchType: 'EQUIPMENT', key: 'equipmentId' }
    ]
  }
];
export const getKeys = (orderType, searchType) => {
  let key = keyArr
    .find((item) => {
      return item.type === orderType;
    })
    .keyArr.find((i) => {
      return i.searchType === searchType;
    }).key;

  return key;
};
