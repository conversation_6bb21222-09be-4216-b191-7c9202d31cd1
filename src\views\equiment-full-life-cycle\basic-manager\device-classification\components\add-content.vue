<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="80%"
    @closed="hide"
  >
    <el-form
      ref="form"
      :model="form"
      inline
      size="small"
      label-width="150px"
      label-suffix="："
    >
      <el-form-item label="类型名称"
        >{{ editSelected.classifyName }}
      </el-form-item>
      <el-form-item label="类型编号"
        >{{ editSelected.classifyNo }}
      </el-form-item>
    </el-form>
    <el-card class="box-card">
      <div slot="header" class="library-title">属性明细</div>

      <el-form
        ref="form"
        :rules="rules"
        :model="form"
        size="small"
        label-width="200px"
        label-position="right"
        label-suffix="："
        class="sortable-wrap"
      >
        <el-table
          :data="form.stageList"
          border
          stripe
          :header-cell-style="{ backgroundColor: '#fafafa' }"
          v-loading="loading"
          row-key="stageKey"
          style="width: 100%"
        >
          <!-- <el-table-column width="46">
            <template>
              <i
                class="el-icon-rank"
                style="color: #409eff; font-size: 25px"
              ></i>
            </template>
          </el-table-column> -->
          <el-table-column
            align="center"
            width="55"
            type="index"
            label="序号"
          ></el-table-column>
          <el-table-column align="center">
            <template slot="header">
              <span style="color: #f56c6c">*</span> 字段名称
            </template>
            <template slot-scope="scope">
              <el-form-item
                label-width="0"
                :prop="'stageList.' + scope.$index + '.attrName'"
                :rules="rules.attrName"
              >
                <avue-input
                  :maxlength="20"
                  v-model.trim="scope.row.attrName"
                  placeholder="请输入"
                ></avue-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template slot="header">
              <span style="color: #f56c6c">*</span> 字段类型
            </template>
            <template slot-scope="scope">
              <el-form-item
                label-width="0"
                :prop="'stageList.' + scope.$index + '.attrType'"
                :rules="rules.attrType"
              >
                <el-select
                  v-model="scope.row.attrType"
                  filterable
                  :disabled="!!scope.row.id"
                  placeholder="请选择"
                  style="width: 100%"
                  @change="handleType(scope.row)"
                >
                  <el-option
                    v-for="item in serviceDicts.type['attr_type']"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template slot="header">
              <span style="color: #f56c6c">*</span> 是否必填
            </template>
            <template slot-scope="scope">
              <el-form-item
                label-width="0"
                :prop="'stageList.' + scope.$index + '.isRequire'"
                :rules="rules.isRequire"
              >
                <el-switch
                  v-model="scope.row.isRequire"
                  active-color="#13ce66"
                  inactive-value="0"
                  active-value="1"
                >
                </el-switch>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center" width="320">
            <template slot="header">待选值 </template>
            <template slot-scope="scope">
              <el-form-item
                label-width="0"
                v-if="!scope.row.selectData.length"
                :prop="'stageList.' + scope.$index + '.selectData'"
                :rules="rules.selectData"
              >
                <avue-input
                  :disabled="!['0', '1'].includes(scope.row.attrType)"
                  placeholder="请输入"
                  :clearable="false"
                  @focus="handleOptional(scope.row, scope.$index)"
                ></avue-input>
              </el-form-item>
              <el-tag
                v-else
                class="latency-time"
                @click="handleOptional(scope.row, scope.$index)"
                :disabled="!['0', '1'].includes(scope.row.attrType)"
                v-for="item in scope.row.selectData"
                :key="item.id"
                type="success"
                >{{ item.label }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template slot="header"> 精度 </template>
            <template slot-scope="scope">
              <el-form-item
                label-width="0"
                :prop="'stageList.' + scope.$index + '.precision'"
                :rules="[
                  {
                    validator: (rule, value, callback) =>
                      validatePercision(rule, value, callback, scope.row),
                    trigger: 'blur'
                  }
                ]"
              >
                <avue-input
                  :disabled="!['2'].includes(scope.row.attrType)"
                  :maxlength="1"
                  :clearable="false"
                  v-model.trim="scope.row.precision"
                  placeholder="请输入"
                ></avue-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template slot="header"> 日期类型 </template>
            <template slot-scope="scope">
              <el-form-item
                label-width="0"
                :prop="'stageList.' + scope.$index + '.dataType'"
                :rules="rules.dataType"
              >
                <el-select
                  :disabled="!['3'].includes(scope.row.attrType)"
                  v-model="scope.row.dataType"
                  filterable
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="dict in dateList"
                    :key="dict.name"
                    :label="dict.name"
                    :value="dict.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template slot="header"> 字段长度 </template>
            <template slot-scope="scope">
              <el-form-item
                label-width="0"
                :prop="'stageList.' + scope.$index + '.fieldLength'"
                :rules="[
                  {
                    validator: (rule, value, callback) =>
                      validateHundred(rule, value, callback, scope.row),
                    trigger: 'blur'
                  }
                ]"
              >
                <avue-input
                  :disabled="!['4'].includes(scope.row.attrType)"
                  :maxlength="3"
                  v-model.trim="scope.row.fieldLength"
                  placeholder="请输入"
                ></avue-input>
              </el-form-item>
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="handleDelete(scope.$index, scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column> -->
        </el-table>
        <!-- <el-button class="dynamic-add-btn" @click="handleAdd">新 增</el-button> -->
      </el-form>
    </el-card>
    <div slot="footer">
      <el-button
        type="primary"
        class="el-icon-circle-plus-outline"
        size="small"
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
      <el-button
        type="primary"
        class="el-icon-circle-close"
        size="small"
        plain
        @click="hide"
      >
        取消</el-button
      >
    </div>
    <AddOptional @save-success="saveSuccess" ref="optional"></AddOptional>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import reg from '@/util/regexp';
  import { deepClone } from '@/util/util';
  import AddOptional from './add-optional.vue';
  import { ledgerExpandSave } from '@/api/equiment-full-life-api/classification';
  const form = {
    stageList: []
  };
  export default {
    serviceDicts: ['attr_type'],
    components: { Dialog, AddOptional },
    props: {
      indicatorList: { type: Array, require: () => [] }
    },
    data() {
      return {
        visible: false,
        loading: false,
        title: '设置拓展属性',
        form: { ...form },
        rules: {
          attrName: [{ required: true, message: '请输入' }],
          attrType: [{ required: true, message: '请选择' }]
        },
        // historyList: [], // 初次获取的历史数据
        editSelected: {}, // 左树选中项值
        dateList: [{ name: 'YYYY-MM-DD' }, { name: 'YYYY-MM' }]
      };
    },
    methods: {
      // 请输入0到6之间整数
      validatePercision(rule, value, callback, row) {
        // debugger;
        let bool = reg.number.test(value);
        let numVal = Number(value);
        let historyNumVal = Number(row.historyPrecision);
        if (!bool) {
          callback(new Error('请输入整数'));
        } else if ((bool && 0 > numVal) || (bool && numVal > 6)) {
          callback(new Error('请输入0到6之间整数'));
        } else if (
          bool &&
          0 <= numVal &&
          numVal <= 6 &&
          numVal < historyNumVal
        ) {
          callback(new Error(`请输入不小于${historyNumVal}的整数`));
        } else {
          callback();
        }
      },
      // 小于200的正整数验证
      validateHundred(rule, value, callback, row) {
        let bool = reg.number.test(value);
        let numVal = Number(value);
        let historyNumVal = Number(row.historyFieldLength);
        if (!bool) {
          callback(new Error('请输入正整数'));
        } else if ((bool && 0 > numVal) || (bool && numVal > 200)) {
          callback(new Error('请输入小于200的正整数'));
        } else if (bool && numVal < historyNumVal) {
          callback(new Error(`请输入不小于${historyNumVal}的整数`));
        } else {
          callback();
        }
      },
      resetRow(row) {
        let column = ['selectData', 'precision', 'dataType', 'fieldLength'];
        for (const key of column) {
          if (key === 'selectData') {
            row[key] = [];
          } else {
            row[key] = '';
          }
        }
      },
      handleType(row) {
        this.resetRow(row);
        let { attrType } = row;
        // 重置其他字段
        if (['0', '1'].includes(attrType)) {
          row.selectData = [];
        } else if (attrType === '2') {
          row.precision = '0';
        } else if (attrType === '3') {
          row.dataType = '';
        } else if (attrType === '4') {
          row.fieldLength = '50';
        }
      },
      // 弹窗确定
      saveSuccess(arr, index) {
        const list = deepClone(arr);
        this.form.stageList[index].selectData = list;
      },
      handleOptional(row, index) {
        this.$refs.optional.show(row, index);
      },
      async handleDelete(index) {
        await this.confirm('注意：删除会将设备台账对应字段删除，请确认。')
          .then(() => {
            this.form.stageList.splice(index, 1);
          })
          .catch(() => {});
      },
      handleAdd() {
        let arr = this.form.stageList;
        let row = {
          id: '',
          attrName: '',
          attrType: '',
          classifyId: '',
          dataType: '',
          fieldLength: '',
          historyFieldLength: '',
          isRequire: '1',
          precision: '',
          historyPrecision: '',
          selectData: [],
          sort: ''
        };
        arr.push(row);
      },
      validForm(flag) {
        let bool = false;
        this.$refs[flag].validate((valid) => {
          bool = valid;
        });
        if (!bool) return false;
        let stageList = this.form.stageList;
        // 判断是否有值
        if (!stageList.length) {
          return true;
        }
        // if (!stageList.length) {
        //   this.$message.warning('请添加拓展属性');
        //   return false;
        // }
        // 唯一名称校验
        const obj = {};
        let uniqueName = stageList.every((item) => {
          return obj[item.attrName] ? false : (obj[item.attrName] = true);
        });
        if (!uniqueName) {
          this.$message.warning('字段名称不能重复');
          return false;
        }
        // 根据字段类型验证字段
        let bool2 = false;
        for (let index = 0; index < stageList.length; index++) {
          const item = stageList[index];
          if (['0', '1'].includes(item.attrType) && !item.selectData.length) {
            this.$message.warning(`第${index + 1}行，待选值不能为空`);
            return false;
          } else if (item.attrType === '2' && !item.precision) {
            this.$message.warning(`第${index + 1}行，精度不能为空`);
            return false;
          } else if (item.attrType === '3' && !item.dataType) {
            this.$message.warning(`第${index + 1}行，日期类型不能为空`);
            return false;
          } else if (item.attrType === '4' && !item.fieldLength) {
            this.$message.warning(`第${index + 1}行，字段长度不能为空`);
            return false;
          } else {
            bool2 = true;
          }
        }
        return bool2;
      },
      // 设置传值
      getParams() {
        let { classifyNo } = this.editSelected;
        let list = this.form.stageList;
        list = deepClone(list);
        list.forEach((el, index) => {
          el.sort = index;
          el.selectData = JSON.stringify(el.selectData);
        });
        let params = {
          ...list[0],
          categoryId: classifyNo
          // classifyName,
          // classifyNo,
          // id,
          // parentId,
        };
        return params;
      },
      // 提交
      async onSubmit() {
        let bool = this.validForm('form');
        if (!bool) return;
        try {
          this.confirm('此功能将影响设备台账的字段，请仔细检查后再提交。')
            .then(async () => {
              this.loading = true;
              let params = this.getParams();
              await ledgerExpandSave(params);
              this.loading = false;
              this.$message.success('提交成功');
              this.$emit('save-success');
              this.hide();
            })
            .catch(() => {
              this.loading = false;
            });
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
      async show(obj, attrRow) {
        this.visible = true;
        this.editSelected = obj || {};
        this.editSelected.classifyNo = (obj || {}).id;
        this.editSelected.classifyName = (obj || {}).name;
        this.form.stageList = [];
        !attrRow && this.handleAdd();
        attrRow && (await this.getExpand(attrRow));
      },
      // 获取扩展属性
      async getExpand(attrRow) {
        try {
          let list = deepClone([attrRow]);
          list.forEach((item) => {
            // item.selectData = JSON.parse(item.selectData);
            item.isRequire = item.isRequire + '';
            item.precision = item.precision === null ? '' : item.precision + '';
            item.historyPrecision = item.precision;
            // 修改比上一个大
            item.fieldLength =
              item.fieldLength === null ? '' : item.fieldLength + '';
            item.historyFieldLength = item.fieldLength;
            // item.fieldLength = item.fieldLength;
          });
          // this.historyList = deepClone(list);
          this.form.stageList = list;
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
      hide() {
        this.form.stageList = [];
        this.editSelected = {};
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .library-title {
    margin: 5px 0;
    padding-left: 8px;
    border-left: 4px solid #409eff;
  }

  .latency-time {
    margin-bottom: 5px;
    margin-left: 5px;
    cursor: pointer;
  }
</style>
