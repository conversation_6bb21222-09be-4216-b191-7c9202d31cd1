<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="设备编号" prop="deviceCode">
        <el-input
          v-model.trim="form.deviceCode"
          placeholder="请输入"
          style="width: 150px"
          clearable
          maxlength="50"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model.trim="form.deviceName"
          placeholder="请输入"
          style="width: 150px"
          clearable
          maxlength="50"
        >
        </el-input>
      </el-form-item>
      <el-form-item v-if="activeName === '2'" label="到期" prop="isExpired">
        <el-select
          v-model="form.isExpired"
          style="width: 150px"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in statusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="activeName === '3'"
        label="领用人"
        prop="applyUserName"
      >
        <el-input
          v-model.trim="form.applyUserName"
          placeholder="请输入"
          style="width: 150px"
          clearable
          maxlength="50"
        >
        </el-input>
      </el-form-item>
      <el-form-item
        v-if="activeName === '3'"
        label="归还日期范围"
        prop="time"
        class="_label"
      >
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceUseSearch',
    serviceDicts: ['lubricate_plan_status'],
    props: {
      activeName: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        statusList: [
          { label: '未到期', value: '0' },
          { label: '已到期', value: '1' }
        ],
        form: {
          applyUserName: undefined,
          deviceCode: undefined,
          deviceName: undefined,
          time: undefined,
          isExpired: undefined
        }
      };
    },
    methods: {
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.chargeDept = dept.id;
        this.form.chargeDeptName = dept.deptName;
      },
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = {};
        if (this.activeName === '2') {
          let { deviceName, deviceCode, isExpired } = this.form;
          params = {
            deviceName,
            deviceCode,
            isExpired
          };
        } else {
          let { deviceName, deviceCode, applyUserName, time } = this.form;
          let [startBackDate, endBackDate] = time || [];
          params = {
            deviceName,
            deviceCode,
            applyUserName,
            startBackDate,
            endBackDate
          };
        }
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
