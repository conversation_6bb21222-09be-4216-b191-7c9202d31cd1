<template>
  <div class="assistant-wrapper">
    <!-- 返回顶部按钮 -->
    <div v-if="messages.length > 0" class="scroll-control-group">
      <el-button
        class="scroll-control-btn"
        type="primary"
        circle
        icon="el-icon-arrow-up"
        size="small"
        @click="scrollToTop"
        title="回到顶部"
        :disabled="isAtTop"
      >
      </el-button>
      <el-button
        class="scroll-control-btn"
        type="primary"
        circle
        icon="el-icon-arrow-down"
        size="small"
        @click="scrollToBottom"
        title="滚动到底部"
        :disabled="isAtBottom"
      >
      </el-button>
    </div>
    <!-- 顶部标题栏 -->
    <div class="assistant-header">
      <div>
        <span class="title-text">智能助手</span>
      </div>
      <i class="el-icon-close" @click="$emit('close')"></i>
    </div>
    <!-- 对话消息区域 -->
    <div
      class="messages-container"
      ref="messagesContainer"
      @scroll="handleScroll"
    >
      <!-- 欢迎信息 -->
      <div v-if="messages.length === 0" class="welcome-wrapper">
        <div class="welcome-title">您好，我是您的智能助手！</div>
        <p class="welcome-text">需要我为您做什么？</p>
        <div class="suggest-wrapper">
          <el-card
            v-for="(suggestion, index) in defaultSuggestions"
            :key="index"
            shadow="hover"
            @click.native="handleSuggestedQuestion(suggestion)"
          >
            <div class="">
              <span style="margin-right: 10px; font-weight: bold"
                >示例{{ index + 1 }}:</span
              >
              <span>{{ suggestion }}</span>
            </div>
          </el-card>
        </div>
      </div>
      <!-- 消息列表 -->
      <div v-for="(message, index) in messages" :key="index">
        <!-- 用户提问卡片 -->
        <user v-if="message.role === 'user'" :msg="message" />
        <!-- AI回答卡片 -->
        <robot v-else :msg="message" v-on="$listeners" />
      </div>

      <!-- 加载中状态 - 仅在没有消息或最后一条消息是用户消息时显示 -->
      <div
        v-if="
          loading &&
          (messages.length === 0 ||
            (messages.length > 0 &&
              messages[messages.length - 1].role === 'user'))
        "
        class="loading-container"
      >
        <div class="flex items-center justify-center">
          <i class="el-icon-loading"></i>
          <span class="text-base">正在思考中...</span>
        </div>
      </div>
    </div>

    <!-- 输入框 -->
    <div class="input-container">
      <div class="reset-btn-wrapper">
        <el-button round plain size="mini" type="primary" @click="resetChat"
          >新对话</el-button
        >
      </div>
      <div class="input-wrapper">
        <el-input
          v-model="userInput"
          clearable
          type="textarea"
          resize="none"
          placeholder="请输入您的问题..."
          :disabled="loading"
          @keydown.native.enter.prevent="(event) => handleEnterKey(event)"
        />
        <el-button
          type="primary"
          circle
          :icon="loading ? 'el-icon-loading' : 'el-icon-position'"
          @click="handleSendMsg"
        >
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
  import { User, Robot } from './components';
  import { copyText } from '@/util/util';
  import { getSseData } from '@/util/sse.js';
  import { keyMap } from './map';
  import { mapGetters } from 'vuex';

  // 默认建议问题
  const defaultSuggestions = [
    // '总共有多少台设备？',
    // '有多少设备已经存在运维标准？',
    // '帮我查一下工单执行中、已完成、待审核的数量？',
    // '设备点巡检有哪些方法？',
    // '常用设备润滑方法有哪些？'
  ];

  export default {
    name: 'Assistant',
    components: { User, Robot },
    data() {
      return {
        defaultSuggestions,
        chatHistoryKey: undefined,
        // 消息列表
        messages: [],
        conversationId: '',
        // 会话返回数据
        resType: 'text',
        resText: '',
        tableData: {},
        menuData: {},
        chartData: null,
        sourceFileData: [],
        // 输入框
        loading: false,
        userInput: '',
        // 滚动相关
        isUserScrolling: false,
        isNearBottom: true,
        showBackToTop: true,
        isAtTop: true,
        isAtBottom: false,
        userScrollTimestamp: 0
      };
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    mounted() {
      const messagesContainer = this.$refs.messagesContainer;
      messagesContainer.addEventListener('scroll', this.handleScroll);
      // 尝试加载历史会话
      this.loadHistorySession();
    },
    beforeDestroy() {
      const messagesContainer = this.$refs.messagesContainer;
      messagesContainer.removeEventListener('scroll', this.handleScroll);
    },
    methods: {
      copyText,
      // 加载历史会话
      async loadHistorySession() {
        this.chatHistoryKey = `chatHistoryData_${this.userInfo.user_id}`;
        const chatDataJSON = localStorage.getItem(this.chatHistoryKey);
        if (chatDataJSON) {
          this.messages = JSON.parse(chatDataJSON);
          await this.$nextTick();
          this.scrollToBottom(0, 'instant');
        }
      },
      // 检测是否接近底部的函数
      checkIfNearBottom() {
        const messagesContainer = this.$refs.messagesContainer;
        if (!messagesContainer) return;

        const { scrollTop, scrollHeight, clientHeight } = messagesContainer;
        // 如果滚动位置接近底部（距离底部30px以内），则认为是在底部
        return scrollHeight - scrollTop - clientHeight < 80;
      },

      // 滚动到底部
      scrollToBottom(timeout = 50, behavior = 'smooth') {
        const messagesContainer = this.$refs.messagesContainer;
        if (!messagesContainer) return;

        // 使用nextTick确保在DOM更新后滚动
        this.$nextTick(() => {
          // 再增加一个小延迟，确保文档引用信息等内容完全渲染
          setTimeout(() => {
            if (messagesContainer) {
              // 使用平滑滚动代替直接设置scrollTop
              messagesContainer.scrollTo({
                top: messagesContainer.scrollHeight,
                behavior
              });
            }
          }, timeout);
        });
      },
      // 滚动到顶部
      scrollToTop() {
        const messagesContainer = this.$refs.messagesContainer;
        if (!messagesContainer) return;

        messagesContainer.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      },
      // 处理滚动事件
      handleScroll() {
        const messagesContainer = this.$refs.messagesContainer;
        if (!messagesContainer) return;

        // 记录用户滚动时间戳
        this.userScrollTimestamp = Date.now();
        this.isUserScrolling = true;

        // 检查是否接近底部
        this.isNearBottom = this.checkIfNearBottom();

        // 检查是否在顶部
        this.isAtTop = messagesContainer.scrollTop < 50;

        // 检查是否在底部
        const { scrollTop, scrollHeight, clientHeight } = messagesContainer;
        this.isAtBottom = scrollHeight - scrollTop - clientHeight < 5;

        // 3秒后重置用户滚动状态
        setTimeout(() => {
          if (Date.now() - this.userScrollTimestamp >= 3000) {
            this.isUserScrolling = false;
          }
        }, 3000);
      },
      // 处理表格或图表数据
      handleChartData(data) {
        const { menu_result, qa_result } = data;
        // 处理回答
        const { data: qData, type } = qa_result[0] || {};
        if (type === '字符串') {
          this.resType = 'qa_text';
          this.resText = qData;
        } else if (type === '表格') {
          this.resType = 'table';
          this.tableData = qData;
        } else if (type === '饼图') {
          this.resType = 'pie';
          this.chartData = qData.series;
        } else if (type === '柱状图') {
          this.resType = 'bar';
          this.chartData = qData;
        }
        // 处理菜单
        this.menuData = menu_result.split(' | ').reduce((acc, cur) => {
          const [key, value] = cur.split(': ');
          acc[keyMap.get(key)] = value;
          return acc;
        }, {});
      },
      // 处理eventStream数据
      handleEventData(event) {
        if (
          event.data.includes(`"event":"message"`) &&
          event.data.includes('from_variable_selector')
        ) {
          let { answer, conversation_id } = JSON.parse(event.data);
          this.conversationId = conversation_id;
          if (answer) {
            if (
              answer.includes('qa_result') ||
              answer.includes('menu_result')
            ) {
              // 菜单/表格/图表数据
              this.handleChartData(JSON.parse(answer));
            } else {
              // 流式输出长文本
              // 如果已经有AI消息，更新它
              const lastMessage = this.messages.slice(-1)[0];
              if (lastMessage && lastMessage.role === 'assistant') {
                lastMessage.content += answer || '';
              } else {
                // 否则添加新的AI消息
                const newMessage = {
                  id: Date.now().toString(),
                  type: 'text',
                  role: 'assistant',
                  content: answer,
                  fileData: []
                };
                this.messages.push(newMessage);
              }
            }
          }
        } else if (event.data.includes(`"event":"source_list"`)) {
          // 参考文档
          let { data } = JSON.parse(event.data);
          if (data) {
            if (this.resType === 'text') {
              const lastRobot = this.messages.slice(-1)[0];
              lastRobot.fileData = data;
            } else {
              this.sourceFileData = data;
            }
          }
        }

        // 只有在用户没有滚动时才自动滚动到底部
        if (this.isNearBottom) {
          this.scrollToBottom();
        }
      },
      // 执行搜索
      async executeSearch(query) {
        if (!query.trim()) return;
        // 设置加载状态
        this.loading = true;
        // 增加一条用户消息
        this.messages.push({
          id: Date.now().toString(),
          role: 'user',
          content: query
        });
        // 清空输入框
        this.userInput = '';
        // 滚动到底部
        await this.$nextTick();
        this.scrollToBottom();
        const pData = { query };
        if (this.conversationId) pData.conversationId = this.conversationId;
        // 请求接口
        try {
          const url = '/api/szyk-simas/ai-assistant/chat-stream';
          getSseData({
            url,
            data: pData,
            onMessage: this.handleEventData,
            onError: () => {
              this.$message.error('系统错误，请稍后重试');
            },
            onClose: () => {
              this.loading = false;
              console.log({
                tableData: this.tableData,
                chartData: this.chartData,
                fileData: this.sourceFileData,
                menuData: this.menuData
              });
              this.resType !== 'text' &&
                this.messages.push({
                  role: 'assistant',
                  type: this.resType,
                  tableData: this.tableData,
                  chartData: this.chartData,
                  fileData: this.sourceFileData,
                  menuData: this.menuData,
                  content: this.resText
                });

              // 重置相应数据
              this.resType = 'text';
              this.resText = '';
              this.tableData = {};
              this.chartData = null;
              this.menuData = {};
              this.sourceFileData = [];

              localStorage.setItem(
                this.chatHistoryKey,
                JSON.stringify(this.messages)
              );
            }
          });
        } catch (e) {
          this.loading = false;
          console.error(e);
        }
      },
      // 发送消息
      handleSendMsg() {
        !this.loading && this.executeSearch(this.userInput);
      },
      // 处理建议问题点击
      handleSuggestedQuestion(question) {
        this.userInput = question;
        this.executeSearch(question);
      },
      // 处理回车键
      handleEnterKey(event) {
        // 如果按下Shift+Enter，则插入换行符
        if (event.shiftKey) {
          return;
        }
        // 否则发送消息
        this.handleSendMsg();
      },
      // 重置会话
      resetChat() {
        localStorage.removeItem(this.chatHistoryKey);
        this.conversationId = undefined;
        this.messages = [];
        this.scrollToBottom();
        this.$message({
          type: 'success',
          message: '已开始新对话'
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .assistant-wrapper {
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    height: 100%;
    font-family: sans-serif;
    backdrop-filter: blur(10px);

    .scroll-control-group {
      position: absolute;
      right: -10px;
      bottom: 50vh;
      z-index: 10;
      display: flex;
      flex-direction: column;
      gap: 10px;
      transform: translateY(50%);
      visibility: visible;
      opacity: 1;
      transition: opacity 0.3s, visibility 0.3s;

      ::v-deep {
        .el-button {
          margin-left: 0;
          padding: 6px;
        }
      }
    }

    .assistant-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px 10px;
      font-size: 18px;
      border-bottom: 1px solid #e4e7ed;

      .title-text {
        font-weight: 500;
      }

      .el-icon-close {
        color: rgb(96, 98, 102);
        cursor: pointer;
      }
    }

    .messages-container {
      position: relative;
      flex: 1;
      padding-top: 10px;
      overflow-y: auto;

      .welcome-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding-top: 10px;

        .welcome-title {
          margin: 16px 0 4px;
          font-weight: 700;
          font-size: 24px;
        }

        .welcome-text {
          margin: 16px 0 8px;
          color: #6b7280;
        }

        .suggest-wrapper {
          margin-top: 10px;

          .el-card {
            cursor: pointer;

            &:not(:last-child) {
              margin-bottom: 12px;
            }
          }
        }
      }
    }

    .loading-container {
      margin-top: 4px;
      padding-bottom: 100px;
      text-align: center;
    }

    .input-container {
      position: sticky;
      bottom: 0;
      padding-top: 4px;

      .reset-btn-wrapper {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 5px;
        padding-right: 10px;

        .el-button {
          padding: 5px 11px;
        }
      }

      .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 12px;
        box-shadow: 0 -2px 10px #0000000d;

        ::v-deep textarea {
          border: none;
        }

        .el-button.is-circle {
          margin-left: 8px;
          padding: 8px;
          font-size: 14px;
        }
      }
    }

    .el-button--primary.is-disabled,
    .el-button--primary.is-disabled:active,
    .el-button--primary.is-disabled:focus,
    .el-button--primary.is-disabled:hover {
      background-color: #a0cfff !important;
      border-color: #a0cfff !important;
    }
  }
</style>
