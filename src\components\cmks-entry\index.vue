<template>
  <div>
    <div v-if="visible" id="float_win" class="float_win">
      <el-image class="entry-img" :src="CmksEntranceImg" @click="toCmks" />
      <i class="el-icon-circle-close close-icon" @click="handleClose"></i>
    </div>
    <div v-if="!visible" class="expand-icon" @click="visible = true">
      <el-tooltip
        effect="dark"
        content="显示跳转入口"
        placement="bottom-start"
        :visible-arrow="false"
      >
        <el-image :src="LeftExpandImg"></el-image>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
  import CmksEntranceImg from '@/asset/img/cmksEntrance.png';
  import LeftExpandImg from '@/asset/img/left-expand.png';

  export default {
    data() {
      return {
        CmksEntranceImg,
        LeftExpandImg,
        visible: true
      };
    },
    methods: {
      handleClose() {
        this.visible = false;
      },
      toCmks() {
        console.log(process.env.VUE_APP_CMKS_ENTRY);
        window.open(process.env.VUE_APP_CMKS_ENTRY);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .float_win {
    position: fixed;
    right: 30px;
    bottom: 30px;
    z-index: 99999;
    background: none;
    background-color: #ddd;
    cursor: pointer;

    .entry-img {
      display: block;
      width: 300px;
      border-radius: 4px;
    }

    .close-icon {
      position: absolute;
      top: 4px;
      right: 4px;
      color: #fff;
      font-size: 16px;
      cursor: pointer;
      opacity: 0.7;

      &:hover {
        opacity: 1;
      }
    }
  }

  .expand-icon {
    position: fixed;
    right: 0;
    bottom: 48.5px;
    z-index: 100000;
    display: flex;
    align-items: center;
    padding: 4px 6px;
    background: #37383a;
    border-radius: 6px 0 0 6px;
    cursor: pointer;

    .el-image {
      width: 16px;
      height: 16px;
    }

    &:hover {
      background: #007fff;
    }
  }
</style>
