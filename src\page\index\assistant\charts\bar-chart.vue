<template>
  <Echart
    :options="options"
    :key="Math.random()"
    id="chart"
    height="200px"
    width="100%"
    v-on="$listeners"
  ></Echart>
</template>

<script>
  import Echart from './index.vue';
  export default {
    data() {
      return {
        options: {}
      };
    },
    components: {
      Echart
    },
    props: {
      cdata: {
        type: Object,
        default: () => ({})
      }
    },
    watch: {
      cdata: {
        handler(newData) {
          const series = newData.series.map(({ name, data }) => {
            return {
              name: name,
              type: 'bar',
              barWidth: '25%',
              data: data
            };
          });

          this.options = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'none'
              }
            },
            grid: {
              top: '10%',
              right: '3%',
              bottom: '10%'
            },
            // 声明一个 X 轴，类目轴（category）。默认情况下，类目轴对应到 dataset 第一列。
            xAxis: {
              type: 'category',
              axisLabel: {
                interval: 0,
                color: '#333'
              },
              data: newData.x
            },
            // 声明一个 Y 轴，数值轴。
            yAxis: {
              type: 'value',
              minInterval: 1,
              axisLabel: {
                color: '#333'
              }
            },
            series
          };
        },
        immediate: true,
        deep: true
      }
    }
  };
</script>
