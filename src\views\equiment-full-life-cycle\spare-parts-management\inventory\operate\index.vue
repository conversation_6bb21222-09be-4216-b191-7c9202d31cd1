<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">资产盘点</p>
      <base-info
        ref="info"
        :initData="detail"
        :whorehouse="whorehouse"
      ></base-info>
    </section>
    <div class="oper_btn">
      <btn type="submit" :loading="loading" @click="submit"></btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from './base-info.vue';
  import {
    addOrEditSparePartsCheckApi,
    getCheckRepairTypeListApi,
    getSparePartsInOutViewApi
  } from '@/api/equiment-full-life-api/spare-parts';
  export default {
    name: 'AddDevice',
    components: {
      BaseInfo
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {},
        whorehouse: []
      };
    },
    watch: {},
    computed: {},
    methods: {
      //  获取的是所有的库房列表
      async getStoreHouseList() {
        let res = await getCheckRepairTypeListApi();
        this.whorehouse = res.data.data;
      },
      async getDetail(no) {
        this.loading = true;
        try {
          const res = await getSparePartsInOutViewApi({ no: no });
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      show(row) {
        this.visible = true;
        this.edit = !!row.id;
        this.eqId = row.id;
        this.getStoreHouseList();
        // if (row.id) {
        //   this.getDetail(row.no);
        // }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.warehouseId = undefined;
        this.$refs['info'].resetForm();
        this.detail = {};
      },

      async submit() {
        let params = await this.$refs['info'].validForm();
        if (params) {
          await this.save({
            ...params,
            module: 'SPARE_PARTS',
            id: this.edit ? this.eqId : undefined
          });
        }
      },

      // 提交
      async save(params) {
        this.loading = true;
        try {
          await addOrEditSparePartsCheckApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
