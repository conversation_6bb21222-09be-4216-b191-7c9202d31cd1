import request from '@/router/axios';

// 设备点巡检统计
export const getDeviceStatistics = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/inspect-statistics',
    method: 'get',
    params
  });

// 按设备统计统一接口

export const getDeviceStatisticsByDevice = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/equipment-statistics',
    method: 'get',
    params
  });

// 保养统计
export const getMaintainStatistics = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/maintain-statistics',
    method: 'get',
    params
  });

// 润滑统计
export const getLubricateStatistics = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/lubricate-order-statistics',
    method: 'get',
    params
  });

// 维修统计
export const getRepairStatistics = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/repair-statistics',
    method: 'get',
    params
  });

// 故障缺陷统计
export const getFaultStatistics = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/fault-defect-statistics',
    method: 'get',
    params
  });

// 备品备件统计
export const getSpareStatistics = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/component-material-statistics',
    method: 'get',
    params
  });

// 设备绩效管理 - 开始
// 设备完好率 - 统计
export const getIntactVal = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/health-percentage',
    method: 'get',
    params
  });
// 设备维修率 - 统计
export const getRepairVal = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/repair-percentage',
    method: 'get',
    params
  });
// 设备维修率 - 分页
export const getRepairList = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/repair-duration-page',
    method: 'get',
    params
  });
// 密封点泄漏率 - 泄漏率
export const getLeakageVal = (params) =>
  request({
    url: '/api/szyk-simas/sealpoint/leakage-percentage',
    method: 'get',
    params
  });
// 密封点泄漏率 - 分页
export const getLeakageList = (params) =>
  request({
    url: '/api/szyk-simas/sealpoint/page',
    method: 'get',
    params
  });
// 密封点泄漏率 - 详情
export const getLeakageDetail = (id) =>
  request({
    url: `/api/szyk-simas/sealpoint/${id}`,
    method: 'get'
  });
// 密封点泄漏率 - 新增/编辑
export const postLeakageData = (data) =>
  request({
    url: '/api/szyk-simas/sealpoint',
    method: 'post',
    data
  });
// 密封点泄漏率 - 批量更新
export const putLeakageBatchData = (data) =>
  request({
    url: '/api/szyk-simas/sealpoint/batch',
    method: 'put',
    data
  });
// 密封点泄漏率 - 删除
export const deleteLeakage = (id) =>
  request({
    url: `/api/szyk-simas/sealpoint/${id}`,
    method: 'delete'
  });
// 计划执行率 - 分页
export const getImplementList = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/order-statistics',
    method: 'get',
    params
  });

//  设备维修统计 中 - 点击查看的列表
export const getImplementDetailList = (params) =>
  request({
    url: '/api/szyk-simas/repair/page',
    method: 'get',
    params
  });

//  设备完好率
export const getIntactList = (params) =>
  request({
    url: '/api/szyk-simas/equipmentstatusrecord/health-percentage',
    method: 'get',
    params
  });

//  密封点泄漏率
export const getLeakageList2 = (params) =>
  request({
    url: '/api/szyk-simas/sealpointstatusrecord/leakage-percentage',
    method: 'get',
    params
  });
//  密封点操作日志接口
export const getLeakageLogList = (params) =>
  request({
    url: '/api/szyk-simas/operate-log/seal-point-page',
    method: 'get',
    params
  });
//  计划执行率 - 工单统计分组
export const getImplementGroupList = (params) =>
  request({
    url: '/api/szyk-simas/statistical-report/order-statistics-group-by-statisticsDimension',
    method: 'get',
    params
  });
// 设备绩效管理 - 结束
