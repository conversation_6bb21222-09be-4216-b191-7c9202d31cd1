<template>
  <basic-container :autoHeight="true">
    <div class="top-info">
      <search ref="search" @query="search"></search>
      <!--      <el-button-->
      <!--        icon="el-icon-download"-->
      <!--        type="primary"-->
      <!--        size="small"-->
      <!--        @click="downLoad"-->
      <!--        >模板下载（全部）</el-button-->
      <!--      >-->
      <!--      <el-button-->
      <!--        icon="el-icon-download"-->
      <!--        type="primary"-->
      <!--        size="small"-->
      <!--        @click="downLoad('part')"-->
      <!--        >模板下载（{{ num }}）</el-button-->
      <!--      >-->
      <!--       暂时先不做，后端也没有出接口-->
      <!-- <el-button
        icon="el-icon-download"
        v-if="permission['maintenance-standards-add-edit-import']"
        type="primary"
        size="small"
        @click="importExcel"
        >导入</el-button
      > -->
    </div>
    <el-table
      size="small"
      :data="list"
      border
      v-loading="loading"
      height="calc(100% - 180px)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column align="center" type="selection" width="55">
      </el-table-column>
      <el-table-column
        prop="code"
        label="设备编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="sn"
        label="SN编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="model"
        label="规格型号"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.model || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.useDeptName || '-' }} </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="userName"
        label="使用人"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.userName || '-' }} </template>
      </el-table-column>
      <el-table-column
        key="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="createTime"
        label="创建时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="updateTime"
        label="更新时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop=""
        label="操作"
        fixed="right"
        align="center"
        show-overflow-tooltip
        width="200px"
      >
        <template slot-scope="{ row }">
          <section class="cell-operate-class">
            <el-button size="mini" type="text" @click="view(row)"
              >查看</el-button
            >
            <el-button
              v-if="
                permission['maintenance-standards-add-edit-import'] &&
                row.overhaulStandardCount === 0
              "
              size="mini"
              type="text"
              @click="configuration(row, 'ADD')"
              >新增</el-button
            >
            <el-button
              v-if="
                permission['maintenance-standards-add-edit-import'] &&
                row.overhaulStandardCount > 0
              "
              size="mini"
              type="text"
              @click="configuration(row, 'EDIT')"
              >编辑</el-button
            >
            <el-button
              @click="handleDelete(row)"
              class="danger-btn"
              v-if="
                permission['maintenance-standards-empty'] &&
                row.overhaulStandardCount > 0
              "
              slot="reference"
              type="text"
              size="small"
              style="margin-left: 10px"
              >清空标准</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <import-files
      ref="file"
      :templateUrl="'/szyk-simas/overhaul-standard/export-template'"
      action="/szyk-simas/overhaul-standard/import-data"
      downLoadFileName="检修标准模板"
      :isCovered="true"
      :isInstanceExport="true"
      @refresh="getList"
    ></import-files>
    <point-dialog ref="pointDialog" @success="getList"></point-dialog>
    <!--     查看详情-->
    <view-info ref="viewInfo"></view-info>
  </basic-container>
</template>

<script>
  import Search from './search';
  import PointDialog from '../operate/index.vue';
  import ViewInfo from '../detail/index.vue';
  import ImportFiles from '@/components/import-files';
  // import { accountListPage } from '@/api/equiment-full-life-api/ledger';
  import {
    clearCheckRepairDetailApi,
    overhaulStandardPage
  } from '@/api/equiment-full-life-api/repair';
  import { getToken } from '@/util/auth';
  import { downloadFileBlob } from '@/util/util';
  import { mapGetters } from 'vuex';

  export default {
    name: 'bearingLibraryIndex',
    components: { PointDialog, Search, ViewInfo, ImportFiles },
    props: {
      categoryId: {
        // 设备id
        type: String,
        default: () => {
          return '';
        }
      }
    },
    data() {
      return {
        nodeId: undefined,
        isShowBtn: false,
        searchParams: {
          size: 10,
          current: 1
        },
        total: 0,
        loading: false,
        list: [],
        num: 0,
        multipleSelection: []
      };
    },
    watch: {
      categoryId: {
        handler(val) {
          this.isShowBtn = !!val;
          if (val) {
            this.nodeId = val;
            this.getList();
          }
        }
      }
    },
    computed: {
      ...mapGetters(['permission'])
    },
    mounted() {},
    methods: {
      downLoad(type) {
        let params = '';
        let path = '';
        if (type === 'part') {
          if (this.multipleSelection.length === 0) {
            this.$message.warning('请选择设备');
            return;
          }
          params = this.multipleSelection
            .map((it) => {
              return it.id;
            })
            .join(',');
          path = `/api/szyk-simas/equipment-account/export-maintain-standard-template?ids=${params}&`;
        } else {
          path = `/api/szyk-simas/equipment-account/export-maintain-standard-template?`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '检修标准模板.xlsx'
        );
      },

      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/equipment-account/export-maintain-standard-template?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/equipment-account/export-maintain-standard-template?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '检修标准模板.xlsx'
        );
      },
      importExcel() {
        this.$refs.file.show();
      },
      handleSelectionChange(val) {
        this.num = val.length;
        this.multipleSelection = val;
      },
      view(row) {
        this.$refs['viewInfo'].show(row.id);
      },
      //  点击删除
      async handleDelete(row) {
        this.$confirm(
          `是否确定清空${row.code}${row.name}的检修标准？清空不影响已生成的工单，但是此设备将不再生成新的工单！`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(async () => {
            try {
              await clearCheckRepairDetailApi({ equipmentId: row.id });
              this.$message({
                type: 'success',
                message: '清除成功'
              });
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {});
      },
      // 點擊搜索
      search(params) {
        this.searchParams = { ...this.searchParams, ...params };
        this.getList();
      },
      configuration(row, type) {
        this.$refs['pointDialog'].show(row.id, type);
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await overhaulStandardPage({
            categoryId: this.categoryId,
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
          this.loading = false;
        } catch (message) {
          this.loading = false;
          console.log('err', message);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-scrollbar__view {
      height: 98% !important;
    }
  }

  .top-info {
    margin-bottom: 10px;
  }
</style>
