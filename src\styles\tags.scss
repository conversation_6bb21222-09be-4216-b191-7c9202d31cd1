

.avue-tags {
    position: relative;
    box-sizing: border-box;
    margin-bottom: 10px;
    padding: 0 10px;
    overflow: hidden;
    background-color: #fff;
    border-top: 1px solid #f6f6f6;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
    user-select: none;

    .el-tabs--card>.el-tabs__header {
        margin: 0;
    }

    .el-tabs--card>.el-tabs__header .el-tabs__nav,
    .el-tabs--card>.el-tabs__header .el-tabs__item,
    .el-tabs--card>.el-tabs__header {
        border: none;
    }

    .el-tabs--card>.el-tabs__header .el-tabs__item:first-child {
        border-left-width: 1px
    }

    .el-tabs--card>.el-tabs__header .el-tabs__item {
        height: 40px;
        margin: 0 3px;
        color: #ccc;
        font-weight: normal;
        font-size: 13px;
        line-height: 40px;

        &.is-active {
            color: #409EFF;
            border-bottom: 3px solid #409EFF;
        }
    }

    .el-tabs__nav-prev,
    .el-tabs__nav-next {
        width: 20px;
        font-size: 18px;
        line-height: 34px;
        text-align: center;
    }

    &__box {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        padding-right: 106px;

        &--close {
            .el-tabs__item {
                &:first-child {
                    padding: 0 20px !important;

                    .el-icon-close {
                        display: none;
                    }
                }
            }
        }
    }

    &__contentmenu{
        position: fixed;
        z-index: 1024;
        width: 120px;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 1px 2px 10px #ccc;

        .item{
            padding: 8px 20px 8px 15px;
            color: #606266;
            font-size: 14px;
            cursor: pointer;

            &:first-child{
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }

            &:last-child{
                border-bottom-right-radius: 5px;
                border-bottom-left-radius: 5px;
            }

            &:hover{
                color: #fff;
                background-color: #409EFF;
            }
        }
    }

    &__menu {
        position: absolute !important;
        top: 3px;
        right: 0;
        box-sizing: border-box;
        padding: 1px 0 0 15px;
    }
}
