<template>
  <div class="top-info">
    <upload-file
      accept=".jpg, .png, .jpeg, .xlsx, .pdf, .docx, .md"
      v-model="attachList"
      :disable="!!(form.list && form.list.length >= limit)"
      v-if="!eqId"
      url="/api/szyk-system/attach/put-file-attach-for-simas"
      :showFile="false"
      @input="handleSuccess"
      ref="file"
      :limit="limit"
    ></upload-file>
    <el-form :model="form" inline label-suffix="：" ref="listForm" size="small">
      <el-table
        class="table"
        :data="form.list"
        row-key="id"
        size="small"
        border
        ref="table"
        :header-cell-style="{ background: '#fafafa' }"
        :loading="loading"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column prop="name" label="资料名称" align="center">
          <!-- <template v-slot="{ row }"> {{ getFileName(row.name) }}</template> -->
          <template v-slot="{ row }"> {{ row.name }}</template>
        </el-table-column>
        <el-table-column prop="type" label="归类" align="center" width="200px">
          <template slot-scope="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.type'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="[
                {
                  required: true,
                  message: '请选择资料归类',
                  trigger: 'change'
                }
              ]"
            >
              <el-select
                size="small"
                v-model="scope.row.type"
                style="width: 150px"
                placeholder="请选择资料归类"
                clearable
                @change="changeProfileType(scope)"
                :disabled="
                  !scope.row.module && source === 'ledger' && !!scope.row.no
                "
              >
                <el-option
                  v-for="item in cateGoryList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="fileCategoryId"
          label="资料类型"
          align="center"
          width="200px"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.fileCategoryId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="[
                {
                  required: true,
                  message: '请选择资料类型',
                  trigger: 'change'
                }
              ]"
            >
              <el-cascader
                placeholder="资料类型"
                v-model="scope.row.fileCategoryId"
                :options="scope.row.profileTypeList"
                :show-all-levels="false"
                popper-class="location-popper"
                :disabled="
                  !scope.row.module && source === 'ledger' && !!scope.row.no
                "
                :props="{
                  checkStrictly: true,
                  label: 'title',
                  emitPath: false,
                  value: 'value',
                  expandTrigger: 'hover'
                }"
                clearable
              ></el-cascader>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column
          prop="extension"
          label="文件类型"
          align="center"
          width="200px"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column label="操作" align="center" width="200">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-view"
              type="text"
              size="small"
              @click="handlePreview(scope.row)"
              >预览</el-button
            >

            <!--             台账中： module没有值，并且有id 就不能删除，相反，能删除-->
            <span
              v-if="!scope.row.module && source === 'ledger' && !!scope.row.no"
              >-</span
            >
            <el-popconfirm
              title="确定删除吗？"
              @confirm="() => handleDelete(scope)"
              v-else
            >
              <el-button
                icon="el-icon-delete"
                slot="reference"
                type="text"
                v-if="!eqId"
                size="small"
                style="margin-left: 10px"
                >删除</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <div class="img_fullScreen" v-show="false">
      <el-image style="height: 100%" ref="image" :preview-src-list="[imageUrl]">
        <span slot="placeholder" class="loading">加载中...</span>
        <span slot="error">图片加载失败!</span>
      </el-image>
    </div>
  </div>
</template>

<script>
  import UploadFile from '@/components/upload-file.vue';
  import {
    getChildrenList,
    getDeviceTypeLazyList
  } from '@/api/equiment-full-life-api/profile';
  import { getFileName } from '@/views/equiment-full-life-cycle/equipment-management/util';
  import { equipmentFileList } from '@/api/equiment-full-life-api/ledger';
  import { previewFile } from '@/util/preview';
  import { getFileFullUrl } from '@/util/file';
  export default {
    name: 'DeviceBasicList',
    components: { UploadFile },
    props: {
      //  文件上传限制，默认是1个
      limit: {
        type: Number,
        default: 1
      },
      fileData: {
        type: Object,
        default: () => {
          return {};
        }
      },

      // 通用资料中使用 profile   台账中使用ledger
      source: {
        type: String,
        default: 'profile'
      },
      eqId: {
        type: String,
        default: ''
      }
    },

    data() {
      return {
        form: {
          list: []
        },
        cateFileList: [],
        list: [],
        attachList: [],
        selLoading: false,
        cateGoryList: [],
        loading: false, // loading
        isProcessing: false,

        imageUrl: ''
      };
    },
    watch: {
      'fileData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.setData(this.fileData.fileData);
            });
          }
        }
      }
    },

    mounted() {
      this.getCategoryList();
    },

    methods: {
      getFileName,
      // 预览文件
      handlePreview(file) {
        const imageExtensions = ['jpg', 'jpeg', 'png'];
        const extension = (file.extension || '').toLowerCase();
        if (imageExtensions.includes(extension)) {
          this.imageUrl = getFileFullUrl(file.id);
          // 调用预览方法
          this.$nextTick(() => {
            this.$refs.image.clickHandler();
          });
        } else if (extension === 'md') {
          this.$message.warning('Markdown文件无法预览');
        } else {
          if (!file.originalName) file.originalName = file.name;
          previewFile(file);
        }
      },
      //  切换设备类型
      changeFile(val) {
        this.$nextTick(async () => {
          await this.getEquipmentFileList(val);
          //  将原有的类型文件少选出来，删除掉，然后将获取到的新的增加进去
          if (this.form.list.length > 0) {
            let category = this.form.list.filter((item) => {
              return item.module !== null;
            });
            let data = this.getInterface(this.cateFileList);
            Promise.all(data).then((res) => {
              this.$nextTick(() => {
                let list = [...category, ...res];
                this.form.list = list;
                this.loading = false;
              });
            });
          } else {
            let data = this.getInterface(this.cateFileList);
            Promise.all(data).then((res) => {
              let list = res;
              this.$nextTick(() => {
                this.form.list = list;
                this.loading = false;
              });
            });
          }
        });
      },
      //  更换设备类型下的通用资料 equipmentFileList
      async getEquipmentFileList(categoryId) {
        try {
          let params = {
            categoryId: categoryId
          };

          let res = await equipmentFileList(params);
          let data = res.data.data || [];
          let fileList = data.map((i) => {
            return {
              data: {
                ...i.attach
              },
              id: i.id,
              module: i.module,
              name: i.name,
              fileCategoryId: i.fileCategoryId, // 资料类型id,
              extension: this.getString(i.name), // 文件后缀类型
              type: i.type,
              no: 1 // no = 1 是随便设置的一个值，因为通用设备要module = null，是通用设备，但是上传的mudule也没有mudule
            };
          });
          this.cateFileList = fileList;
        } catch ({ message }) {
          this.userLoading = false;
          console.log(message);
        }
      },

      //  获取归类列表
      getCategoryList() {
        this.loading = true;
        getDeviceTypeLazyList({ parentId: '0' })
          .then((res) => {
            this.cateGoryList = res.data.data;
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
      },
      //  切换资料归类，获取资料类型
      changeProfileType(scope) {
        this.$set(scope.row, 'fileCategoryId', undefined);
        this.getChildrenList(scope.row);
      },
      //  根据归类获取子类型数据
      async getChildrenList(row) {
        this.selLoading = true;
        try {
          const res = await getChildrenList({ parentId: row.type });
          let list = res.data.data || [];
          this.$set(row, 'profileTypeList', list);
          this.selLoading = false;
          return list;
        } catch (e) {
          this.selLoading = false;
          console.log(e);
        }
      },

      getInterface(attachInfoList) {
        let data = attachInfoList.map(async (i) => {
          let a = await this.getChildrenList(i).then((res) => {
            let item = {
              ...i,
              data: {
                ...i
              },
              // id:i.attachId?i.attachId:i.id,
              name: i.name,
              fileCategoryId: i.fileCategoryId, // 资料类型id,
              extension: this.getString(i.name), // 文件后缀类型
              type: i.type,
              profileTypeList: res // 归类下的子资料类型
            };
            // this.attachList.push(i);
            return { detailId: i.id, ...item };
          });
          return a;
        });
        return data;
      },
      setData(attachInfoList) {
        this.loading = true;
        this.attachList = [];
        if (attachInfoList) {
          let data = this.getInterface(attachInfoList);
          Promise.all(data).then((res) => {
            this.form.list = res;
            this.loading = false;
          });
        } else {
          this.loading = false;
          this.form.list = [];
        }
      },
      //  上传成功
      handleSuccess(file) {
        const filterFile = file.filter(
          (item1) => !this.form.list.some((item2) => item2.id === item1.id)
        );
        let arr = filterFile ? filterFile : file;
        let data = arr.map((i) => {
          return {
            data: {
              ...i
            },
            // module:'11',
            id: i.id,
            name: i.originalName, // 资料名称
            type: this.typeId, // 归类类型
            fileCategoryId: undefined, // 资料类型id,
            extension: this.getString(i.originalName) // 文件后缀类型
          };
        });
        this.form.list = [...this.form.list, ...data]; // [...this.list, ...data];
      },

      getString(str) {
        if (str) {
          const lastDotIndex = str.lastIndexOf('.');
          let result;
          if (lastDotIndex !== -1) {
            // 截取点后面的部分
            result = str.slice(lastDotIndex + 1);
          } else {
            // 如果没有找到点，则返回原字符串
            result = str;
          }
          return result;
        } else {
          this.$message.error('文件名称返回有错误，请检查');
        }
      },
      async validForm() {
        if (this.form.list.length > 20) {
          return this.$message.warning('请不要上传超过20个文件');
        }
        let list;
        if (this.form.list.length > 0) {
          let valid = await this.$refs['listForm'].validate();
          if (valid) {
            list = this.form.list
              .filter((i) => {
                // 将通用资料过滤出去，不提交
                return i.module !== null;
              })
              .map((i) => {
                const { name, type, createTime, createUser, extension } = i;
                return {
                  ...{
                    name,
                    type,
                    createTime,
                    createUser,
                    extension
                  },
                  id: i.detailId,
                  attachId: i.id ? i.id : i.attachId,
                  fileCategoryId: i.fileCategoryId
                };
              });
          }
        } else {
          // this.$message.warning('请上传文件资料');
          list = [];
        }
        return list;
      },
      resetForm() {
        this.attachList = [];
        this.form.list = [];
        this.isProcessing = false;
      },
      async handleDelete(scope) {
        // this.$refs['file'].handleRemove(scope.row, this.form.list);
        let idx = this.attachList.findIndex((i) => {
          return i.id === scope.row.id;
        });
        this.attachList.splice(idx, 1);
        this.form.list.splice(scope.$index, 1);
      }
    }
  };
</script>

<style scoped lang="scss"></style>
