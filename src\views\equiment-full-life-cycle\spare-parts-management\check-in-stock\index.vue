<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 130px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        align="center"
        prop="dictNo"
        label="备品备件编号"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="dictName"
        label="备品备件名称"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="model"
        label="规格型号"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.model || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="measureUnitName"
        align="center"
        label="计量单位"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="warehouseName"
        align="center"
        label="库房"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column
        prop="currentQuantity"
        align="center"
        label="当前库存"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!--    &lt;!&ndash;    入库&ndash;&gt;-->
    <!--    <in-table ref="in"></in-table>-->
    <!--    &lt;!&ndash;    出库&ndash;&gt;-->
    <!--    <out-table ref="out"></out-table>-->
  </basic-container>
</template>

<script>
  // import InTable from '@/views/equiment-full-life-cycle/spare-parts-management/check-in-stock/detail/in.vue'; // 查看详情页面
  // import OutTable from '@/views/equiment-full-life-cycle/spare-parts-management/check-in-stock/detail/out.vue'; // 查看详情页面

  import Search from './search';
  import Pagination from '@/components/pagination';
  import { getSparePartsStockListApi } from '@/api/equiment-full-life-api/spare-parts';

  export default {
    name: 'PutInStorage',
    components: {
      Search,
      Pagination
    },
    props: {},
    computed: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        // 备注
        currentRow: undefined,
        remarkVisible: false,
        remarkForm: {
          remark: ''
        }
      };
    },

    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      //  点击搜索
      onsubmit(params) {
        this.searchParams = { ...this.searchParams, ...params };
        this.searchParams.current = 1;
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getSparePartsStockListApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 查看
      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
  }

  .btn-wrapper {
    text-align: center;
  }
</style>
