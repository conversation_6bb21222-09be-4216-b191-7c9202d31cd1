<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        icon="el-icon-plus"
        v-if="permission['device-requisition-add-return']"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
      <el-button
        icon="el-icon-check"
        v-if="permission['device-requisition-add-return']"
        type="primary"
        :disabled="!allPageSelect.length"
        size="small"
        @click="returnDevice()"
        >批量归还</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      ref="table"
      @select="handleCheckBox"
      @select-all="handleSelectAll"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        align="center"
        key="selection"
        type="selection"
        width="55"
        :selectable="selectable"
      >
      </el-table-column>
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="orderNo"
        label="领用单号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.orderNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="领用日期"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.receiveStartDate || '-' }}~{{ row.receiveEndDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="applyUserName"
        label="申请人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.applyUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="belongDeptName"
        label="所属部门"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.belongDeptName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="领用状态"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${equipmentStatusColor(row.status)};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="applyTime"
        label="申请时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.applyTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="更新时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateTime || '-' }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <!--         0 =  未开始  1= 执行中 2 = 已完成 3=已终止   -->
        <template v-slot="{ row }">
          <el-button type="text" size="small" @click="detail(row)"
            >查看</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="returnDevice(row)"
            v-if="
              row.status === 2 && permission['device-requisition-add-return']
            "
            >归还</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="assign(row)"
            v-if="
              row.status === 1 && permission['device-requisition-assignment']
            "
            >分配</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <Assignment ref="assign" @success="getList"></Assignment>
    <detail-index ref="detailIndex"></detail-index>
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Assignment from './operate/assignment.vue';
  import Pagination from '@/components/pagination';
  import { convertFileUrl } from '@/util/util';

  import {
    devicereceiveListPage,
    back
  } from '@/api/equiment-full-life-api/acceptance.js';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  import { mapGetters } from 'vuex';
  export default {
    name: 'MaintenanceList',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination,
      Assignment
    },
    props: {},
    data() {
      return {
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {},
        allPageSelect: []
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      // 设备台账 设备状态状态颜色
      equipmentStatusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#155CFF'; // 已分配
          case 1:
            return '#EE7C11'; // 待分配
          case 3:
            return '#35C24B '; // 已归还
        }
      },
      selectable(row) {
        return row.status === 2;
      },
      handleCheckBox(rows, row) {
        let key = 'id';
        if (rows.includes(row)) {
          this.allPageSelect.push(row);
        } else {
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item[key] !== row[key]
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        let key = 'id';
        if (rows.length) {
          rows.forEach((row) => {
            if (!this.allPageSelect.find((item) => item[key] === row[key])) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.list.forEach((row) => {
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item[key] !== row[key]
            );
          });
        }
      },
      // 归还
      async returnDevice(row) {
        await this.confirm('是否确认归还？');
        let receiveIds = row
          ? [row.id]
          : this.allPageSelect.map((item) => item.id);
        this.fetchReturn({ receiveIds });
      },

      async fetchReturn(params) {
        this.loading = true;
        try {
          await back(params);
          this.$message.success('操作成功');
          await this.getList();
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/lubricate/plan/export-plan?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/lubricate/plan/export-plan?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '润滑计划.xlsx'
        );
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await devicereceiveListPage({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      assign(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.assign.show(obj);
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
