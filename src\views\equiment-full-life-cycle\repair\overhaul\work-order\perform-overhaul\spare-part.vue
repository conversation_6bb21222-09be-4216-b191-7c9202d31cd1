<template>
  <el-form :model="standardItem" size="small" ref="baseForm">
    <el-button
      size="small"
      v-if="!onlyView"
      type="primary"
      @click="choose"
      class="el-icon-plus"
    >
      选择备品备件
    </el-button>
    <el-table
      v-loading="loading"
      class="table"
      :data="standardItem.materialList"
      row-key="id"
      size="small"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#"></el-table-column>
      <el-table-column prop="no" label="备品备件编号" show-overflow-tooltip>
        <template v-slot="{ row }">
          {{ row.no || row.dictNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="备品备件名称" show-overflow-tooltip>
        <template v-slot="{ row }">
          {{ row.name || row.dictName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.model || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="measureUnitName"
        label="计量单位"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.measureUnitName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="count"
        label="消耗数量"
        show-overflow-tooltip
        width="180"
      >
        <template slot="header">
          <span style="color: red" v-if="!onlyView"> * </span>消耗数量
        </template>
        <template slot-scope="scope">
          <el-form-item
            label=""
            v-if="!onlyView"
            :prop="'materialList.' + scope.$index + '.count'"
            :rules="{
              required: true,
              message: '请输入消耗数量',
              trigger: 'blur'
            }"
          >
            <el-input-number
              v-model="scope.row.count"
              placeholder="请输入消耗数量"
              :min="0"
              :max="9999"
              :precision="scope.row.measureUnitPrecision"
              :controls="false"
            ></el-input-number>
          </el-form-item>
          <template v-else>
            {{ scope.row.count || '-' }}
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop=" "
        v-if="!onlyView"
        label="操作"
        show-overflow-tooltip
        width="60"
      >
        <template slot-scope="scope">
          <el-popconfirm
            title="确定删除吗？"
            @confirm="() => handleDelete(scope.$index, scope.row)"
          >
            <el-button
              style="color: red"
              slot="reference"
              type="text"
              size="small"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <choose-spare-part
      ref="choose"
      @getAssetList="getAssetList"
      showWarehouseSearch
    ></choose-spare-part>
  </el-form>
</template>

<script>
  import ChooseSparePart from '@/views/equiment-full-life-cycle/repair/select-receive-spare-parts-dialog';
  export default {
    components: { ChooseSparePart },
    props: {
      onlyView: {
        type: Boolean,
        default: false
      },
      standardItem: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        form: {
          list: []
        }
      };
    },

    methods: {
      // 弹窗返回备件
      getAssetList(list) {
        console.log(list);
        let arr = [...list];
        let arrList = arr.map((item) => {
          return {
            ...item,
            no: item.no || item.dictNo,
            name: item.name || item.dictName,
            model: item.model,
            dictName: item.dictName,
            stockId: item.id, // 这两个id必传
            dictNo: item.dictNo,
            dictModel: item.model,
            measureUnitName: item.measureUnitName,
            measureUnitId: item.measureUnitId,
            count: item.count,
            measureUnitPrecision: item.measureUnitPrecision
          };
        });
        // this.standardItem.materialList = [...arrList];
        this.$set(this.standardItem, 'materialList', [...arrList]);
        // this.standardItem.materialList = [...arrList];
      },
      async validForm() {
        //   先校验
        let bool = await this.$refs.baseForm.validate();
        if (bool) {
          return this.standardItem.materialList;
        } else {
          return [];
        }
      },
      resetForm() {
        this.form = {
          list: []
        };
      },
      //  选择备品备件
      choose() {
        this.$refs['choose'].show(this.standardItem.materialList || []);
      },
      //  点击删除
      handleDelete(index) {
        this.standardItem.materialList.splice(index, 1);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table {
    margin-top: 15px;
  }
</style>
