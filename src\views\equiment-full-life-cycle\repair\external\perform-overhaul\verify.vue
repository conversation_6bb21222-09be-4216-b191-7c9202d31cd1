<template>
  <dialog-drawer
    title="验证"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="detail" style="margin-bottom: 80px" v-loading="loading">
      <!-- ticket-information -->
      <span class="el-base-title">工单信息</span>
      <TicketInfo :detail="detail"></TicketInfo>
      <!-- 工单信息 -->
      <span class="el-base-title">来源信息</span>
      <ResourceInfo :detail="detail"></ResourceInfo>
      <!-- 维修建议 -->
      <p class="el-base-title ai-btn-wrapper">
        维修建议
        <el-button size="small" type="primary" @click="jumpToAI" class="ai-btn">
          故障维修建议</el-button
        >
      </p>
      <el-descriptions
        border
        size="small"
        :labelStyle="{ width: '110px', textAlign: 'right' }"
        :contentStyle="{
          width: '300px',
          wordBreak: 'break-all',
          wordWrap: 'break-word'
        }"
        contentClassName="contentClassName"
      >
        <el-descriptions-item label="维修建议：">{{
          detail.repairSuggest || '-'
        }}</el-descriptions-item>
      </el-descriptions>
      <span class="el-base-title">维修内容</span>
      <RepairContent :detail="detail"></RepairContent>
      <span class="el-base-title">维修验证</span>
      <el-form
        ref="form"
        label-position="right"
        label-suffix="："
        label-width="100px"
        :model="form"
      >
        <el-form-item
          label="验证评论"
          :prop="`verifyComment`"
          :rules="[{ required: true, message: '请输入', trigger: 'blur' }]"
        >
          <el-input
            type="textarea"
            v-model.trim="form.verifyComment"
            placeholder="请输入验证评论"
            maxlength="200"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="oper_btn">
      <el-button
        size="small"
        type="success"
        @click="onVerify(1)"
        :loading="loading"
      >
        通过</el-button
      >
      <el-button
        size="small"
        type="danger"
        @click="onVerify(0)"
        :loading="loading"
      >
        驳回</el-button
      >
      <btn type="close" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>
<script>
  import {
    getInternalViewApi,
    verifyRepairApi
  } from '@/api/equiment-full-life-api/repair';
  import UploadImg from '@/components/uploadImage.vue';
  import SparePart from '@/views/equiment-full-life-cycle/repair/overhaul/work-order/perform-overhaul/spare-part.vue';
  import { getFileFullUrl } from '@/util/file';
  import Logs from '@/views/equiment-full-life-cycle/components/logs';
  import TicketInfo from '@/views/equiment-full-life-cycle/repair/internal/detail/ticket-information.vue';
  import ResourceInfo from '@/views/equiment-full-life-cycle/repair/internal/detail/resource-information.vue';
  import RepairContent from '@/views/equiment-full-life-cycle/repair/internal/detail/repair-content.vue';
  import RepairVerify from '@/views/equiment-full-life-cycle/repair/internal/detail/repair-verification.vue';
  export default {
    name: 'RepairViewIndex',
    components: {
      Logs,
      UploadImg,
      SparePart,
      TicketInfo,
      ResourceInfo,
      RepairContent,
      RepairVerify
    },
    data() {
      return {
        eqId: '',
        type: '',
        visible: false,
        loading: false,
        detail: {
          equipmentAccount: {},
          materialList: []
        }, // 详情数据
        form: {
          verifyComment: ''
        }
      };
    },
    methods: {
      handleSuccess() {
        this.$refs['form'].clearValidate('attachList');
      },
      getFileFullUrl,
      verifyRepairApi,
      show(id, type) {
        this.type = type || '';
        this.visible = true;
        if (id) {
          this.getDetail(id);
        }
      },
      closed() {
        this.visible = false;
        this.form.verifyComment = '';
        this.detail = {
          equipmentAccount: {}
        };
      },
      // 获取详情接口
      async getDetail(no) {
        try {
          this.loading = true;
          const res = await getInternalViewApi({ no: no });
          this.detail = res.data.data;
          this.detail.materialList = this.detail.repairComponentList;
          await this.$refs['log'].getLogs(this.detail.id, 'INTERNAL_REPAIR');
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      async onVerify(type) {
        try {
          if (!this.form.verifyComment) {
            return this.$message.warning('请输入验证评论');
          }
          this.loading = true;
          await verifyRepairApi({
            repairId: this.detail.id,
            verifyComment: this.form.verifyComment,
            verifyResult: type
          });
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 跳转到故障维修建议
      jumpToAI() {
        const { equipmentId, equipmentAccount, problemComment } = this.detail;
        this.closed();
        this.$router.push({
          name: 'aitools',
          params: {
            deviceId: equipmentId,
            deviceName: equipmentAccount.name,
            defectDesc: problemComment,
            type: 'diagnose'
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  ::v-deep .el-card__body {
    padding-top: 0;
  }

  .ai-btn-wrapper {
    position: relative;

    .ai-btn {
      position: absolute;
      top: -9px;
      right: 0;
    }
  }
</style>
