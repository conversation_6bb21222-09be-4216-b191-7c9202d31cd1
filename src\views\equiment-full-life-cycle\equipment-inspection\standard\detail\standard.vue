<template>
  <el-table size="small" :data="list" border>
    <el-table-column type="index" align="center" width="50"></el-table-column>
    <el-table-column
      prop="monitorName"
      label="检查部位"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="standard"
      label="检查标准"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="method"
      label="检查方法"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
