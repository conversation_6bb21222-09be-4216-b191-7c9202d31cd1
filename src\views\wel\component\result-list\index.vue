<template>
  <div class="table">
    <span class="title">
      <section>
        <i class="wel_icon iconfont el-icon-ud-biaotitubiao2"></i>
        <span style="color: #3d446e"> 查询结果列表</span>
      </section>
    </span>
    <search @search="search"></search>

    <el-table
      size="small"
      :data="list"
      border
      v-loading="loading"
      height="550px"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="no"
        label="计划编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="计划名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createUserName"
        label="制定人员"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="startDate"
        label="点巡检开始时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.startDate || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="endDate"
        label="点巡检结束时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.endDate || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="equipmentCount"
        label="计划包含设备"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="normalCount"
        label="正常数量"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="abnormalCount"
        label="异常数量"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="omissionCount"
        label="漏检数量"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="statusName"
        label="计划状态"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop=""
        label="操作"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            icon="el-icon-view"
            type="text"
            @click="view(row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <view-page ref="viewInfo"></view-page>
  </div>
</template>

<script>
  import { getStatisticsPlan } from '@/api/equiment-full-life-api/inspect';
  import ViewPage from '@/views/equiment-full-life-cycle/equipment-inspection/plan/detail/index.vue';
  import Search from './search.vue';
  export default {
    name: 'bearingLibraryIndex',
    components: { Search, ViewPage },
    props: {},
    data() {
      return {
        params: {},
        time: undefined,
        searchParams: {
          size: 10,
          current: 1
        },
        total: 0,
        loading: false,
        list: []
      };
    },
    watch: {},
    mounted() {
      this.getList();
    },
    methods: {
      view(row) {
        this.$refs['viewInfo'].show(row.no);
      },

      // 點擊搜索
      search(params) {
        this.searchParams = { ...this.searchParams, ...params };
        this.getList();
      },

      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await getStatisticsPlan({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
          this.loading = false;
        } catch (message) {
          this.loading = false;
          console.log('err', message);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .table {
    padding: 15px;
    border-top: 1px dashed #efefef;
  }

  .title {
    display: flex;
    justify-content: space-between;

    section {
      width: 200px;
      overflow: hidden;
      text-wrap: nowrap;
      text-overflow: ellipsis;
    }

    em {
      display: inline-block;
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-style: normal;
      line-height: 10%;
    }
  }
</style>
