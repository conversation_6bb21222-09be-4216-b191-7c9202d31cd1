<template>
  <el-table
    class="table"
    :data="list"
    border
    :header-cell-style="{ background: '#fafafa' }"
    size="small"
  >
    <el-table-column type="index" label="#" align="center"></el-table-column>
    <el-table-column
      prop="equipmentCode"
      label="设备编号"
      align="center"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column
      prop="equipmentName"
      label="设备名称"
      align="center"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column
      prop="equipmentMonitorName"
      label="润滑部位"
      align="center"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column
      prop="startTime"
      show-overflow-tooltip
      label="首次加油时间"
      align="center"
    >
    </el-table-column>
    <!--        <el-table-column-->
    <!--          v-if="detail.id"-->
    <!--          prop="orderNo"-->
    <!--          label="工单号"-->
    <!--          align="center"-->
    <!--          show-overflow-tooltip-->
    <!--        >-->
    <!--          <template v-slot="{ row }">{{ row.orderNo || '-' }}</template>-->
    <!--        </el-table-column>-->
    <!--        <el-table-column-->
    <!--          v-if="detail.id"-->
    <!--          prop="bizStatus"-->
    <!--          label="工单状态"-->
    <!--          align="center"-->
    <!--          show-overflow-tooltip-->
    <!--        >-->
    <!--          <template v-slot="{ row }">{{ row.bizStatus || '-' }}</template>-->
    <!--        </el-table-column>-->
  </el-table>
</template>

<script>
  export default {
    name: 'DeviceBasicList',
    components: {},
    props: {
      detail: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.list = this.detail.equipmentMonitorList;
            });
          }
        }
      }
    },

    data() {
      return {
        list: []
      };
    },

    mounted() {},
    methods: {}
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }
  .operateBtn {
    margin-bottom: 15px;
  }
  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
