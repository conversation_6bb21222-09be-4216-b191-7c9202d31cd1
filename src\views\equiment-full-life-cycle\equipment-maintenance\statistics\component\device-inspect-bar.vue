<template>
  <div class="alarmTrend">
    <span class="title">
      <section>
        <i class="wel_icon iconfont el-icon-ud-biaotitubiao2"></i>
        <span style="color: #3d446e"> 保养异常统计</span>
      </section>

      <search @changeTime="changeTime"></search>
    </span>
    <div id="alarmTrendChart" v-if="flag" v-loading="loading"></div>
    <custom-empty :size="70" v-else></custom-empty>
  </div>
</template>
<script>
  import * as echarts from 'echarts/core';
  import { getStatisticsException } from '@/api/equiment-full-life-api/maintenance';
  import {
    TooltipComponent,
    LegendComponent,
    GridComponent,
    TitleComponent
  } from 'echarts/components';
  import { BarChart } from 'echarts/charts';
  import { CanvasRenderer } from 'echarts/renderers';
  import CustomEmpty from '@/components/custom-empty.vue';
  import { markRaw } from 'vue';
  import Search from './search.vue';
  echarts.use([
    TooltipComponent,
    TitleComponent,
    BarChart,
    CanvasRenderer,
    LegendComponent,
    GridComponent
  ]);

  export default {
    name: 'alarmTrend',
    props: {},
    components: { Search, CustomEmpty },

    data() {
      return {
        dateList: [],
        abnormalCountList: [],
        normalCountList: [],
        loading: false,
        flag: true
      };
    },
    mounted() {
      this.$nextTick(async () => {
        await this.getCategoryAsset(0);
      });
      window.addEventListener('resize', this.resizeChart);
    },
    unmounted() {
      window.removeEventListener('resize', this.resizeChart);
    },
    methods: {
      changeTime(e) {
        this.getCategoryAsset(e);
      },
      async getCategoryAsset(day) {
        this.loading = true;
        try {
          const res = await getStatisticsException({
            queryDate: day
          });
          if (res.data.data.dateList) {
            this.flag = true;
            this.abnormalCountList = res.data.data.abnormalCountList;
            this.normalCountList = res.data.data.normalCountList;
            this.dateList = res.data.data.dateList;
          } else {
            this.flag = false;
          }
          await this.init();
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },

      resizeChart() {
        this.status_chart &&
          this.status_chart.resize({ width: 'auto', height: 'auto' });
      },
      init() {
        let chartDom = document.getElementById('alarmTrendChart');
        this.status_chart = markRaw(echarts.init(chartDom));

        let option = {
          title: {
            text: '（台）',
            top: '5%',
            textStyle: { color: '#3d446e', fontSize: 14, fontWeight: 'normal' },
            padding: [5, 0, 0, 0]
          },

          legend: {
            orient: 'horizontal',
            top: '5%',
            right: 0,
            itemWidth: 10,
            itemHeight: 10,
            icon: 'circle',
            data: ['异常', '正常']
          },
          grid: { top: '20%', bottom: '10%' },
          xAxis: {
            type: 'category',
            data: this.dateList,
            axisLine: { lineStyle: { color: '#ccc' } },
            axisTick: { length: 3 },
            axisLabel: { color: '#999' }
          },
          yAxis: {
            type: 'value',
            axisLine: { show: true, lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#999' },
            splitLine: {
              lineStyle: { color: ['#CEEDFF'], type: [5, 8], dashOffset: 3 }
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            textStyle: { color: '#424242' },
            renderMode: 'html',
            className: 'tooltip',
            order: 'seriesDesc'
          },
          series: [
            {
              name: '正常',
              type: 'bar',
              stack: 'total', // ! 多条数据总计 => 堆叠
              color: '#52A8FF',

              itemStyle: { borderRadius: 0 },
              data: this.normalCountList
            },
            {
              name: '异常',
              type: 'bar',
              stack: 'total', // ! 多条数据总计 => 堆叠
              color: '#FFC53D',
              itemStyle: { borderRadius: [12, 12, 0, 0] },
              data: this.abnormalCountList
            }
          ]
        };

        this.status_chart && this.status_chart.setOption(option);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .alarmTrend {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: var(--el-bg-color-overlay);
    border-radius: 5px;
  }

  #alarmTrendChart {
    width: 100%;
    height: calc(100% - 50px);
  }

  .title {
    display: flex;
    justify-content: space-between;

    section {
      width: calc(100% - 220px);
      overflow: hidden;
      text-wrap: nowrap;
      text-overflow: ellipsis;
    }

    em {
      display: inline-block;
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-style: normal;
      line-height: 10%;
    }
  }

  ::v-deep {
    .el-tabs__nav-wrap::after {
      background-color: unset;
    }

    .el-tabs__nav-wrap {
      top: -20px;
    }
  }
</style>
