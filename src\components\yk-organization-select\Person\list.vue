<template>
  <el-table
    v-loading="loading"
    :data="arr"
    size="small"
    height="420"
    style="width: 100%"
    header-cell-class-name="headerTh"
    row-class-name="rowClassName"
    @row-click="rowClick"
  >
    <el-table-column type="index" label="序号" width="50" />
    <el-table-column
      prop="realName"
      label="姓名"
      align="center"
      min-width="140"
      show-overflow-tooltip
    />
    <el-table-column
      prop="deptName"
      label="部门"
      align="center"
      show-overflow-tooltip
      min-width="280"
    />
    <el-table-column align="center" label="选择" fixed="right" min-width="60">
      <template slot-scope="scope">
        <el-checkbox
          v-model="scope.row.status"
          @click.native="stopDefault($event)"
          @change="() => $emit('selectEmit', scope.row, scope.row.status)"
        />
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'list',
    props: {
      arr: {
        type: Array,
        default: function () {
          return [];
        }
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    methods: {
      rowClick(row, column, event) {
        if (event.type === 'click') {
          row.status = !row.status;
          this.$emit('selectEmit', row, row.status);
        }
      },
      stopDefault(e) {
        e.stopPropagation();
      }
    }
  };
</script>
<style lang="scss" scoped>
  ::v-deep .headerTh {
    color: #515a6e !important;
    background-color: #f8f8f9 !important;
  }

  ::v-deep .rowClassName {
    cursor: pointer;
  }
</style>
