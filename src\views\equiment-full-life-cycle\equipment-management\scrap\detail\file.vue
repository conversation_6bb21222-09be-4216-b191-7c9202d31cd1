<template>
  <div class="top-info">
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      border
      stripe
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#"></el-table-column>
      <el-table-column prop="code" label="设备编号" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="sn" label="设备SN" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="name" label="设备名称" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.model || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="measureUnitName"
        label="计量单位"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.measureUnitName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="belongDeptName"
        label="归属部门"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.belongDeptName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="responsiblePersonName"
        label="负责人"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{
          row.responsiblePersonName || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.useDeptName || '-' }}</template>
      </el-table-column>
      <el-table-column prop="userName" label="使用人" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.userName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="locationPath"
        label="存放地点"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.locationPath || '-' }}</template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: 'DeviceBasicList',
    components: {},
    props: {
      details: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    watch: {
      'details.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.list = this.details.detailList || [];
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        list: []
      };
    },

    mounted() {},

    methods: {}
  };
</script>

<style scoped lang="scss"></style>
