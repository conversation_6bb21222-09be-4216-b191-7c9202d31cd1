<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <style type="text/css"></style>
  </head>
  <script type="text/javascript">
    var WebReader = {
      OBJ: function () {
        var reader = {};
        var SocketOpen = false;
        var socket = null;
        var target = null;

        reader.onResult = function (func) {
          target.addEvent('Result', func);
        };

        var WSonOpen = function () {
          SocketOpen = true;
        };

        var WSonMessage = function (msg) {
          var str = '';
          str = msg.data;

          var resultData = {
            type: 'Result',
            strStatus: str.substr(4, 2),
            strData: str.substr(6),
            strCmdCode: str.substr(0, 4)
          };

          if (target != null) target.fireEvent(resultData);
        };

        var WSonClose = function () {
          SocketOpen = false;
        };

        var WSonError = function () {
          alert('Server not running !');
        };

        reader.createSocket = function () {
          try {
            if ('WebSocket' in window) {
              socket = new WebSocket('ws://localhost:39002/RFID Reader Service');
            } else if ('MozWebSocket' in window) {
              socket = new MozWebSocket('ws://localhost:39002/RFID Reader Service');
            } else {
              alert('None');
              return false;
            }
            socket.onopen = WSonOpen;
            socket.onmessage = WSonMessage;
            socket.onclose = WSonClose;
            socket.onerror = WSonError;
            target = new EventTarget();
            return true;
          } catch (ex) {
            return false;
          }
        };

        reader.Disconnect = function () {
          if (socket != null) socket.close();
        };

        reader.send = function (strParam) {
          socket.send(strParam);
        };

        return reader;
      }
    };

    function EventTarget() {
      this.handlers = {};
    }

    EventTarget.prototype = {
      constructor: EventTarget,

      addEvent: function (type, handler) {
        if (typeof this.handlers[type] == 'undefined') {
          this.handlers[type] = [];
        }
        this.handlers[type].push(handler);
      },

      fireEvent: function (event) {
        if (!event.target) {
          event.target = this;
        }
        if (this.handlers[event.type] instanceof Array) {
          var handlers = this.handlers[event.type];
          for (var i = 0; i < handlers.length; i++) {
            handlers[i](event);
          }
        }
      },

      removeEvent: function (type, handler) {
        if (this.handlers[type] instanceof Array) {
          var handlers = this.handlers[type];
          for (var i = 0; i < handlers.length; i++) {
            if (handlers[i] == handler) {
              break;
            }
          }
          handlers.splice(i, 1);
        }
      }
    };

    try {
      var reader1 = WebReader.OBJ();
    } catch (e) {}

    if (!reader1.createSocket()) {
    }

    var GFUNC = {
      M1_findCard: 1,
      M1_authentication: 2,
      M1_read: 3,
      M1_write: 4,
      M1_initVal: 5,
      M1_increment: 6,
      M1_decrement: 7,
      M1_readVal: 8,
      M1_updateKey: 9
    };

    var g_device = '00'; //设备句柄号
    var g_isOpen = false; // 检查读写器USB 接口是否已经打开
    var g_blockAddr;
    var g_blockData;
    var g_key;
    var g_keyType;
    var g_vale;
    var g_wantFunc = 0;

    /**
     * Reader callback function，the command response is processed here
     * (读卡器回调函数，在这里对命令应答进行处理)
     **/
    reader1.onResult(function (rData) {
      console.log(
        'rData。。。。。。。。。',
        rData.strCmdCode,
        rData.strData,
        rData.strStatus
      );
      switch (rData.strCmdCode) {
        case '0007': // 开始链接
          if (rData.strStatus != '00') {
            // 设备没有链接
            document.getElementById('tfTips').value =
              'Failed to connect device ! ' + rData.strStatus;
          } else {
            g_isOpen = true;
            document.getElementById('tfTips').value = '连接设备成功！';
          }
          break;

        case '0009': //Sys_Close关闭USB连接
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value = 'USB链接已经关闭!';
          } else {
            document.getElementById('tfTips').value = '读卡器已成功断开连接!';
          }
          break;

        case '0106': //Sys_SetBuzzer // 设置蜂鸣时间
          break;

        case '0105': //Sys_GetSnr // 读取读写器唯一序列号
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value = '获取设备序列号失败！';
          } else {
            document.getElementById('tfTips').value = rData.strData;
            document.getElementById('tfTips').value = '成功获取设备序列号！';
          }
          break;

        case '1001': //读卡失败 00 状态下是读卡成功
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value = '读卡失败!';

            return;
          }

          switch (g_wantFunc) {
            case GFUNC.M1_findCard:
            case GFUNC.M1_authentication:
            case GFUNC.M1_read:
            case GFUNC.M1_write:
            case GFUNC.M1_initVal:
            case GFUNC.M1_increment:
            case GFUNC.M1_decrement:
            case GFUNC.M1_readVal:
            case GFUNC.M1_updateKey:
              reader1.send(g_device + '1002'); //TyA_Anticollision
              break;
          }

          break;

        case '1002': //TyA_Anticollision // 防冲撞
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value = 'TyA_防碰撞失败!';
            return;
          }

          switch (g_wantFunc) {
            case GFUNC.M1_findCard:
              document.getElementById('tfUID').value = rData.strData;
              document.getElementById('tfTips').value = '读卡成功';
            case GFUNC.M1_authentication:
            case GFUNC.M1_read:
            case GFUNC.M1_write:
            case GFUNC.M1_initVal:
            case GFUNC.M1_increment:
            case GFUNC.M1_decrement:
            case GFUNC.M1_readVal:
            case GFUNC.M1_updateKey:
              reader1.send(g_device + '1003' + rData.strData); //TyA_Select  //选定卡
              break;
          }

          break;

        case '1003': //TyA_Select
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value = 'TyA_Select faild !';
            return;
          }

          switch (g_wantFunc) {
            case GFUNC.M1_authentication:
            case GFUNC.M1_read:
            case GFUNC.M1_write:
            case GFUNC.M1_initVal:
            case GFUNC.M1_increment:
            case GFUNC.M1_decrement:
            case GFUNC.M1_readVal:
            case GFUNC.M1_updateKey:
              reader1.send(g_device + '100A' + g_keyType + g_blockAddr + g_key); //TyA_CS_Authentication2
              break;
          }

          break;

        case '100A': //TyA_CS_Authentication2
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value =
              'TyA_CS_Authentication2 faild !';
            return;
          }

          switch (g_wantFunc) {
            case GFUNC.M1_read:
              reader1.send(g_device + '100B' + g_blockAddr); //TyA_CS_Read
              break;

            case GFUNC.M1_write:
              reader1.send(g_device + '100C' + g_blockAddr + g_blockData);
              break;

            case GFUNC.M1_initVal:
              reader1.send(g_device + '100D' + g_blockAddr + g_value);
              break;

            case GFUNC.M1_readVal:
              reader1.send(g_device + '100E' + g_blockAddr);
              break;

            case GFUNC.M1_decrement:
              reader1.send(g_device + '100F' + g_blockAddr + g_value);
              break;

            case GFUNC.M1_increment:
              reader1.send(g_device + '1010' + g_blockAddr + g_value);
              break;
          }

          break;

        case '100B': //TyA_CS_Read
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value = 'TyA_CS_Read faild !';
          } else {
            document.getElementById('tfBlockData').value = rData.strData;
            document.getElementById('tfTips').value =
              'Read block successfully !';
          }
          break;

        case '100C': //TyA_CS_Write
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value = 'TyA_CS_Write faild !';
          } else {
            document.getElementById('tfTips').value =
              'Write block successfully !';
          }
          break;

        case '100D': //TyA_CS_InitValue
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value =
              'TyA_CS_InitValue faild !';
          } else {
            document.getElementById('tfTips').value =
              'Initialize the wallet value successfully !';
          }
          break;

        case '100E': //TyA_CS_ReadValue
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value =
              'TyA_CS_ReadValue faild !';
          } else {
            var hexValue = rData.strData;
            hexValue =
              hexValue.substr(6, 2) +
              hexValue.substr(4, 2) +
              hexValue.substr(2, 2) +
              hexValue.substr(0, 2); //Reverse sorting of high and low bytes (高低字节反过来排序)
            var decValue = parseInt(hexValue, 16); //Convert hexadecimal string to decimal string (十六进制字符串转换为十进制字符串)
            document.getElementById('tfValue').value = decValue; //Show wallet balance (显示电子钱包余额)
            document.getElementById('tfTips').value =
              'Read value successfully !';
          }
          break;

        case '100F': //TyA_CS_Decrement
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value =
              'TyA_CS_Decrement faild !';
          } else {
            document.getElementById('tfTips').value =
              'Decrement value successfully !';
          }
          break;

        case '1010': //TyA_CS_Increment
          if (rData.strStatus != '00') {
            document.getElementById('tfTips').value =
              'TyA_CS_Increment faild !';
          } else {
            document.getElementById('tfTips').value =
              'Increment value successfully !';
          }
          break;
      }
    });

    /**
   * Function：Converts a decimal string to a hexadecimal string with a specified number of digits.
   (十进制字符串转换为指定位数的十六进制字符串)
   * Parameter：decimalStr [IN] Decimal string. (十进制字符串)
   *            length [IN] Specifies the number of digits to convert to hexadecimal. (指定要转换成十六进制的位数)
   * Return：Hexadecimal string. (十六进制字符串)
   **/
    function DecStrToHexStr(decimalStr, length) {
      var num = Number(decimalStr);
      var str = (Array(length).join('0') + num.toString(16)).slice(-length);
      return str;
    }

    /**
     * 功能：数字转换为指定位数的十六进制字符串
     * numValue [IN] 数字值
     * length [IN] 指定要转换成十六进制的位数
     * 返回：十六进制字符串
     **/
    // function NumberToHexStr(numValue, length)
    // {
    //   var str = (Array(length).join('0') + numValue.toString(16)).slice(-length);;
    //   return str;
    // }

    /**
     * Turn on the green light
     * (亮绿灯)
     **/
    function LedGreen() {
      reader1.send(g_device + '0107' + '02');
    }

    /**
     * Turn on the red light
     * (亮红灯)
     **/
    function LedRed() {
      reader1.send(g_device + '0107' + '01');
    }

    /**
     * Connect the reader and initialize the working mode
     * (连接读卡器并初始化工作模式)
     **/
    function Connect() {
      reader1.send(g_device + '0007' + '00'); //Open the USB device with index number 0. (打开索引号为0的USB设备)
      reader1.send(g_device + '0109' + '41'); //Set to ISO14443a working mode. (设置为ISO14443A工作模式)
      reader1.send(g_device + '0108' + '01'); //Turn on the reader antenna. (打开读卡器天线)
      LedGreen();
      setTimeout('LedRed()', '200');
      reader1.send(g_device + '0106' + '10'); //Beeps. (蜂鸣提示)
    }

    /**
     * Reading card number
     * (寻卡并读卡号)
     **/
    function Request() {
      //Check whether the reader is opened or not.
      if (g_isOpen != true) {
        document.getElementById('tfTips').value = '请先连接设备 !';
        return;
      }

      //Clear UID edit box
      document.getElementById('tfUID').value = '';

      //Start read UID
      reader1.send(g_device + '1001' + '52'); //TyA_Request // 寻卡
      g_wantFunc = GFUNC.M1_findCard;
    }
  </script>

  <body>
    <p>&nbsp;</p>
    <div align="center">
      <table width="545" border="0" cellpadding="0" cellspacing="0">
        <tr>
          <th height="24" colspan="6" bgcolor="#FFFFFF" scope="col">
            <strong>MIFARE ONE DEMO</strong>
          </th>
        </tr>
        <tr>
          <th height="24" colspan="6" bgcolor="#FFFFFF" scope="col">&nbsp;</th>
        </tr>
        <tr>
          <th height="24" colspan="6" bgcolor="#FFFFFF" scope="col">
            <form id="form1" name="form1" method="post" action="">
              <div align="left">
                <input
                  name="btnConnect"
                  type="button"
                  id="btnConnect"
                  onclick="Connect()"
                  value="Connect Reader"
                />
              </div>
            </form>
          </th>
        </tr>
        <tr>
          <td height="19" colspan="6" bgcolor="#EFEFEF">&nbsp;</td>
        </tr>
        <tr>
          <td height="30" colspan="6" bgcolor="#EFEFEF">
            <em><strong>[UID]</strong> </em>
            <!--         读卡其数据-->
            <input
              name="tfUID"
              type="text"
              id="tfUID"
              size="20"
              maxlength="16"
            />
            <input
              name="btnRequest"
              type="button"
              id="btnRequest"
              onclick="Request()"
              value="Request"
            />
          </td>
          <input
            name="tfTips"
            type="text"
            id="tfTips"
            size="70"
            maxlength="70"
          />
        </tr>
      </table>
      <p>&nbsp;</p>
    </div>
    <p>&nbsp;</p>
  </body>
</html>
