<template>
  <Echart
    :options="options"
    id="bottomLeftChart"
    height="220px"
    width="220px"
  ></Echart>
</template>

<script>
  import Echart from './index.vue';
  export default {
    props: {
      cdata: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        options: {}
      };
    },
    components: {
      Echart
    },
    watch: {
      cdata: {
        handler(newData) {
          this.options = {
            tooltip: {
              trigger: 'item',
              formatter: '{b} : {c} ({d}%)'
            },
            series: [
              {
                type: 'pie',
                radius: '40%',
                data: newData
              }
            ]
          };
        },
        immediate: true,
        deep: true
      }
    }
  };
</script>
