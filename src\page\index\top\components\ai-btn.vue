<template>
  <div>
    <el-popover
      width="200"
      trigger="hover"
      content="Hi, 我是智能助手"
      placement="right"
      class="img-wrapper"
    >
      <el-image
        slot="reference"
        :src="AIEntrance"
        alt="智能助手"
        class="entrance-img"
        @click="handleShowAssistant"
      ></el-image>
    </el-popover>
    <ai-chat-drawer ref="aiChatDrawer"></ai-chat-drawer>
  </div>
</template>

<script>
  import AiChatDrawer from '../../assistant/drawer.vue';
  import AIEntrance from '@/asset/img/ai-entrance.gif';

  export default {
    components: { AiChatDrawer },
    data() {
      return {
        AIEntrance
      };
    },
    methods: {
      // 打开智能助手
      handleShowAssistant() {
        this.$refs['aiChatDrawer'].show();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .img-wrapper {
    ::v-deep .el-popover__reference-wrapper {
      display: flex;
      align-items: center;

      .entrance-img {
        width: 40px;
        height: 40px;
        margin: 0 12px;
        cursor: pointer;
      }
    }
  }
</style>
