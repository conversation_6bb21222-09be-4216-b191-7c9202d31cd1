<template>
  <el-dialog
    title="新增"
    :visible.sync="open"
    width="750px"
    append-to-body
    :close-on-click-modal="false"
  >
    <div class="details">
      <!-- 基本信息 -->
      <section>
        <p class="el-base-title">基本信息</p>
        <el-form
          ref="form"
          size="small"
          :model="form"
          :rules="rules"
          :inline="true"
          label-suffix="："
          label-width="110px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="设备编号" prop="deviceCode">
                <div class="content-wrapper">
                  {{ form.deviceCode || '-' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备名称" prop="deviceName">
                <el-input
                  v-model="form.deviceName"
                  readonly
                  placeholder="请选择设备名称"
                  style="width: 215px"
                  @focus.prevent="onChooseDeviceClick"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="使用部门" prop="useDeptName">
                <div class="content-wrapper">
                  {{ form.useDeptName || '-' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="使用人" prop="userName">
                <div class="content-wrapper">
                  {{ form.userName || '-' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="移动前位置" prop="originalLocationName">
                <div class="content-wrapper">
                  {{ form.originalLocationName || '-' }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="移动后位置" prop="newLocation">
                <location-panel-list
                  ref="location"
                  v-model="form.newLocation"
                  @getValue="getLocation"
                  size="small"
                  style="width: 225px"
                  :clearable="true"
                ></location-panel-list>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="移动日期" prop="moveTime">
                <div class="content-wrapper">
                  {{ form.moveTime || '-' }}
                </div>
                <!-- <el-date-picker
                  v-model="form.moveTime"
                  type="date"
                  placeholder="请选择移动日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker> -->
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  v-model="form.remark"
                  placeholder="请输入备注"
                  style="width: 570px"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </section>
      <div class="oper_btn">
        <btn type="submit" @click="submit" :loading="loading"></btn>
        <btn type="cancel" @click="closed"></btn>
      </div>
    </div>
    <!-- 设备弹窗 -->
    <asset-list ref="assetList" @on-choose="onChooseDeviceSuccess"></asset-list>
  </el-dialog>
</template>

<script>
  import AssetList from '@/views/equiment-full-life-cycle/components/select-device-dialog/index.vue';
  import LocationPanelList from '@/components/location-pannel-list/index.vue';
  import { postEquipmentMoveLogAdd } from '@/api/equiment-full-life-api/location';
  import dayjs from 'dayjs';

  export default {
    components: { AssetList, LocationPanelList },
    props: {
      value: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: false,
        form: {
          deviceId: '',
          deviceCode: '',
          deviceName: '',
          useDeptName: '',
          userName: '',
          originalLocation: '',
          originalLocationName: '',
          newLocation: '',
          moveTime: '',
          remark: ''
        },
        rules: {
          deviceName: [
            {
              required: true,
              message: '请选择设备名称',
              trigger: ['change', 'blur']
            }
          ],
          newLocation: [
            {
              required: true,
              message: '请选择移动后位置',
              trigger: ['change', 'blur']
            }
          ]
          // moveTime: [
          //   {
          //     required: true,
          //     message: '请选择移动日期',
          //     trigger: ['change', 'blur']
          //   }
          // ]
        }
      };
    },
    computed: {
      open: {
        get() {
          return this.value;
        },
        set() {
          this.$emit('input', false);
        }
      }
    },
    mounted() {
      this.form.moveTime = dayjs().format('YYYY-MM-DD');
    },
    methods: {
      // 显示选择的设备弹窗
      onChooseDeviceClick() {
        this.$refs.assetList.show([], true);
      },
      // 选择设备回调
      onChooseDeviceSuccess(device) {
        console.log(device);
        const form = {};
        form.deviceId = device.id;
        form.deviceCode = device.code;
        form.useDept = device.useDept;
        form.useDeptName = device.useDeptName;
        form.userId = device.userId;
        form.userName = device.userName;
        form.originalLocation = device.locationId;
        form.originalLocationName = device.locationName;
        Object.assign(this.form, form);
        this.$set(this.form, 'deviceName', device.name);
      },
      //  存放地点
      getLocation(val) {
        this.form.newLocation = val;
      },
      closed() {
        this.open = false;
        this.$refs.form.resetFields();
      },
      submit() {
        this.$refs.form.validate(async (valid) => {
          if (!valid) return;
          if (this.form.originalLocation === this.form.newLocation) {
            this.$message.warning('移动后位置不能与移动前位置一样，请修改。');
            return;
          }
          const { data } = await postEquipmentMoveLogAdd(this.form);
          if (data && data.code === 200) {
            this.$message.success('操作成功');
            this.closed();
            this.$emit('refresh');
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .details {
    padding-bottom: 30px;
  }

  .content-wrapper {
    max-width: 200px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  ::v-deep .el-cascader .el-input {
    width: 215px;
  }
</style>
