import Vue from 'vue';
import FilePreviewModal from '@/components/file-preview-modal.vue';

// Global file preview service
class FilePreviewService {
  constructor() {
    this.modalInstance = null;
    this.isModalOpen = false;
  }

  // Initialize the modal component
  init() {
    if (this.modalInstance) {
      return;
    }

    // Create a Vue instance for the modal
    const ModalConstructor = Vue.extend(FilePreviewModal);
    this.modalInstance = new ModalConstructor({
      el: document.createElement('div')
    });

    // Append to body
    document.body.appendChild(this.modalInstance.$el);

    // Listen for close events
    this.modalInstance.$on('close', () => {
      this.isModalOpen = false;
    });
  }

  // Open file preview modal
  openPreview({ fileUrl, fileType, fileName, fileId, title }) {
    this.init();

    if (this.isModalOpen) {
      // Close existing modal first
      this.closePreview();
      // Wait a bit before opening new one
      setTimeout(() => {
        this._showModal({ fileUrl, fileType, fileName, fileId, title });
      }, 100);
    } else {
      this._showModal({ fileUrl, fileType, fileName, fileId, title });
    }
  }

  _showModal({ fileUrl, fileType, fileName, fileId, title }) {
    // Set modal properties
    this.modalInstance.fileUrl = fileUrl;
    this.modalInstance.fileType = fileType;
    this.modalInstance.fileName = fileName;
    this.modalInstance.fileId = fileId;
    this.modalInstance.title = title || fileName || '文件预览';

    // Show modal
    this.modalInstance.visible = true;
    this.isModalOpen = true;
  }

  // Close file preview modal
  closePreview() {
    if (this.modalInstance) {
      this.modalInstance.visible = false;
      this.isModalOpen = false;
    }
  }

  // Destroy the modal instance
  destroy() {
    if (this.modalInstance) {
      this.modalInstance.$destroy();
      if (this.modalInstance.$el && this.modalInstance.$el.parentNode) {
        this.modalInstance.$el.parentNode.removeChild(this.modalInstance.$el);
      }
      this.modalInstance = null;
      this.isModalOpen = false;
    }
  }
}

// Create global instance
const filePreviewService = new FilePreviewService();

// Vue plugin
const FilePreviewPlugin = {
  install(Vue) {
    // Add to Vue prototype for easy access in components
    Vue.prototype.$filePreview = filePreviewService;

    // Add global method
    Vue.filePreview = filePreviewService;
  }
};

export default FilePreviewPlugin;
export { filePreviewService };
