<template>
  <div class="top-info">
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="originalName"
        label="资料名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="typeName"
        label="归类"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="fileCategoryName"
        label="资料类型"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="extension"
        label="文件类型"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-view"
            type="text"
            size="small"
            @click="handlePreview(scope.row)"
            >预览</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="img_fullScreen" v-show="false">
      <el-image style="height: 100%" ref="image" :preview-src-list="[imageUrl]">
        <span slot="placeholder" class="loading">加载中...</span>
        <span slot="error">图片加载失败!</span>
      </el-image>
    </div>
  </div>
</template>

<script>
  import { previewFile } from '@/util/preview';
  import { getFileFullUrl } from '@/util/file';
  export default {
    name: 'DeviceBasicList',
    components: {},
    props: {
      detail: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    watch: {
      previewVisible(val) {
        // 当预览关闭时恢复背景滚动
        if (!val) {
          document.body.style.overflow = '';
        }
      },
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.detail.attachList);
          }
        }
      }
    },

    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        imageUrl: ''
      };
    },
    mounted() {},
    methods: {
      // 预览文件
      handlePreview(file) {
        const imageExtensions = ['jpg', 'jpeg', 'png'];
        const extension = (file.extension || '').toLowerCase();
        if (imageExtensions.includes(extension)) {
          this.imageUrl = getFileFullUrl(file.id);
          // 调用预览方法
          this.$nextTick(() => {
            this.$refs.image.clickHandler();
          });
        } else if (extension === 'md') {
          this.$message.warning('Markdown文件无法预览');
        } else {
          if (!file.originalName) file.originalName = file.name;
          previewFile(file);
        }
      },
      setData(attachInfoList) {
        if (attachInfoList) {
          let data = attachInfoList.map((i) => {
            return {
              ...i,
              type: this.getString(i.originalName),
              typeName: this.detail.typeName,
              fileCategoryName: this.detail.fileCategoryName
            };
          });
          console.log('232323', data);
          this.list = data;
        } else {
          this.list = [];
        }
      },
      getString(str) {
        const lastDotIndex = str.lastIndexOf('.');
        let result;
        if (lastDotIndex !== -1) {
          // 截取点后面的部分
          result = str.slice(lastDotIndex + 1);
        } else {
          // 如果没有找到点，则返回原字符串
          result = str;
        }
        return result;
      }
    }
  };
</script>

<style lang="scss" scoped></style>
