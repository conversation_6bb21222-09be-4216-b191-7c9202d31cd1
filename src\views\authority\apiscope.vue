<template>
  <basic-container>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="管理端接口权限" name="admin">
        <api-scope-manager></api-scope-manager>
      </el-tab-pane>
    </el-tabs>
  </basic-container>
</template>

<script>
  import ApiScopeManager from './apiscope/apiscope_manager.vue';

  export default {
    data() {
      return {
        activeName: 'admin'
      };
    },
    components: { ApiScopeManager },
    methods: {
      handleClick() {
        console.log('handleClick');
      }
    }
  };
</script>
