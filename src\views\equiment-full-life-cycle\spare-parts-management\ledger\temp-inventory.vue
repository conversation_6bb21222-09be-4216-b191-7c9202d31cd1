<template>
  <el-dialog
    title="临时盘点"
    append-to-body
    width="500px"
    :visible.sync="visible"
  >
    <el-form
      ref="form"
      :model="remarkForm"
      :rules="rules"
      size="medium"
      label-position="left"
      label-width="94px"
      label-suffix="："
    >
      <el-form-item label="查询库存">
        <div>{{ currentAmount }}</div>
      </el-form-item>
      <el-form-item label="实际库存" prop="currentAmount">
        <el-input-number
          size="small"
          :controls="false"
          v-model="remarkForm.currentAmount"
          :min="0"
          :max="9999"
          :precision="0"
          placeholder="请输入实际库存"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="盘点原因" prop="remark">
        <el-input
          type="textarea"
          v-model="remarkForm.remark"
          placeholder="请输入盘点原因"
          :rows="4"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>
      <div name="footer" class="btn-wrapper">
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script>
  import { editTempInventory } from '@/api/equiment-full-life-api/spare-parts';

  export default {
    props: {
      visible: Boolean,
      accountId: String,
      currentAmount: Number
    },
    data() {
      return {
        remarkForm: {
          currentAmount: undefined,
          remark: ''
        },
        rules: {
          currentAmount: [
            {
              required: true,
              message: '请输入实际库存',
              trigger: 'blur'
            }
          ],
          remark: [
            {
              required: true,
              message: '请输入盘点原因',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    methods: {
      async handleSubmit() {
        this.$refs.form.validate(async (valid) => {
          if (!valid) return;
          try {
            const params = {
              ...this.remarkForm,
              accountId: this.accountId
            };
            const { data } = await editTempInventory(params);
            console.log(data);
            if (data.code === 200) {
              this.$message.success('操作成功');
              this.cancel();
              this.$emit('refresh');
            }
          } catch (e) {
            console.error(e);
          }
        });
      },
      cancel() {
        this.$refs.form.resetFields();
        this.$emit('close');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .btn-wrapper {
    text-align: center;
  }
</style>
