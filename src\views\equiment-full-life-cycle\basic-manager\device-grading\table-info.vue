<template>
  <el-table
    v-loading="loading"
    class="table"
    :data="tableData"
    row-key="id"
    size="small"
    height="calc(100% - 200px)"
    border
    ref="table"
    :header-cell-style="{ background: '#fafafa' }"
  >
    <el-table-column type="index" label="序号" align="center"></el-table-column>
    <el-table-column
      prop="sn"
      label="SN编号"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="name"
      label="设备名称"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.name || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="model"
      label="规格型号"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.model || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="measureUnitName"
      label="计量单位"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="specialTypeName"
      label="特种设备类型"
      width="100px"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.specialTypeName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="specialInspectPeriod"
      label="特种设备检查周期（天）"
      width="160px"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.specialInspectPeriod || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="importantLevelName"
      label="设备等级"
      align="center"
      show-overflow-tooltip
      width="96px"
    >
      <template slot-scope="{ row }">
        <el-tag
          :type="getTagType(row.importantLevel)"
          size="small"
          class="table-custom-tag"
        >
          {{ row.importantLevelName || '-' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column
      prop="categoryName"
      label="设备类型"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="imageList"
      label="设备图片"
      align="center"
      show-overflow-tooltip
      width="90px"
    >
      <template slot-scope="scope">
        <el-image
          v-if="scope.row.imageList"
          style="width: 50px; height: 50px"
          :src="convertFileUrl(scope.row.imageList[0].domain)"
          fit="cover"
          :preview-src-list="[convertFileUrl(scope.row.imageList[0].domain)]"
        ></el-image>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column
      prop="locationPath"
      label="设备位置"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.locationPath || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="purchaseDate"
      label="购买日期"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.purchaseDate || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="useDeptName"
      label="使用部门"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.useDeptName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="userName"
      label="使用人员"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.userName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="productDate"
      label="投产日期"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.productDate || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="supplier"
      label="生产厂家"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.supplier || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="contact"
      label="联系人员"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.contact || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="tel"
      label="联系电话"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.tel || '-' }}
      </template>
    </el-table-column>
    <!-- <el-table-column
      prop="createUserName"
      label="创建人员"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.createUserName || '-' }}
      </template>
    </el-table-column> -->
    <el-table-column
      prop="statusName"
      label="设备状态"
      align="center"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">
        <i :style="`color:${equipmentStatusColor(row.status)};font-size:18px`"
          >●</i
        >
        {{ row.statusName || '-' }}
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" width="80" fixed="right">
      <template slot-scope="{ row }">
        <el-button
          icon="el-icon-view"
          type="text"
          size="small"
          @click="$emit('dispatch', 'view', row)"
          >查看</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { convertFileUrl } from '@/util/util';

  export default {
    props: {
      loading: Boolean,
      tableData: {
        type: Array,
        default: () => []
      }
    },
    methods: {
      convertFileUrl,
      // 设备登记标签颜色
      getTagType(val) {
        const typeObj = {
          1: 'success',
          2: 'primary',
          3: 'warning'
        };
        return typeObj[val];
      },
      // 设备台账 设备状态状态颜色
      equipmentStatusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#35C24B'; // 在用
          case 1:
            return '#155CFF'; // 备用
          case 3:
            return '#EE7C11'; // 维修
          case 4:
            return '#E23F3F'; // 报废
        }
      }
    }
  };
</script>

<style lang="scss" scoped></style>
