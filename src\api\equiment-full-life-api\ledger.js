import request from '@/router/axios';

/**
 * 台账表接口
 * @param url 接口地址
 */
// 分页列表
export const accountListPage = (params) => {
  return request({
    url: '/api/szyk-simas/maintain-standard/page',
    method: 'get',
    params
  });
};
// 新增或修改
export const getPlanEquipmentList = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/selectDevicePage`,
    method: 'get',
    params
  });
};

export const addOrEditAccount = (data) => {
  return request({
    url: '/api/szyk-common/equipment-account/submit',
    method: 'post',
    data
  });
};

//删除
export const removeAccount = (params) => {
  return request({
    url: '/api/szyk-common/equipment-account/remove',
    method: 'post',
    params
  });
};

// 详情
export const getAccountDetail = (params) => {
  return request({
    url: '/api/szyk-common/equipment-account/detail',
    method: 'get',
    params
  });
};

// 工单详情
export const getEquipmentOrderPage = (params) => {
  return request({
    url: '/api/szyk-simas/bizLog/equipmentOrderPage',
    method: 'get',
    params
  });
};

// 绑定卡片的接口
export const bindCard = (data) => {
  return request({
    url: '/api/szyk-common/equipment-account/bindNfc',
    method: 'post',
    data
  });
};

// 根据设备类型更新资料/equipment-file/list
export const equipmentFileList = (params) => {
  return request({
    url: '/api/szyk-common/equipment-file/list',
    method: 'get',
    params
  });
};
export const getMonitorListApi = (params) => {
  return request({
    url: '/api/szyk-common/equipment-account/monitorList',
    method: 'get',
    params: {
      ...params
    }
  });
};
// 特种设备的分页
export const specialPage = (params) => {
  return request({
    url: '/api/szyk-common/equipment-account/specialPage',
    method: 'get',
    params: {
      ...params
    }
  });
};
// 租赁设备的分页
export const leaseBackPage = (params) => {
  return request({
    url: '/api/szyk-common/equipment-account/leaseBackPage',
    method: 'get',
    params: {
      ...params
    }
  });
};
// 设备租赁归还保存
export const deviceback = (data) => {
  return request({
    url: '/api/szyk-simas/device/deviceback/save',
    method: 'post',
    data
  });
};

// 选择设备 在报废的时候使用
export const selectScrapDevice = (params) => {
  return request({
    url: '/api/szyk-common/equipment-account/allDevicePage',
    method: 'get',
    params
  });
};
