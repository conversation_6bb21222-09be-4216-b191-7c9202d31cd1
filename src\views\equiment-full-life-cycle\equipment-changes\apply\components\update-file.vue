<template>
  <div>
    <el-table
      size="small"
      ref="multipleTable"
      :headerCellStyle="{ background: '#fafafa' }"
      :reserve-selection="true"
      row-key="id"
      :data="list"
      v-loading="loading"
      border
      @select="handleCheckBox"
      @select-all="handleSelectAll"
    >
      <el-table-column v-if="isEdit" type="selection" width="55" align="center">
      </el-table-column>
      <el-table-column
        v-else
        type="index"
        label="序号"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column label="状态" v-if="type === 'verify'" align="center">
        <template v-slot="{ row }">
          <el-tag
            size="mini"
            :type="row.updateStatus === '1' ? 'success' : 'danger'"
            disable-transitions
            >{{ row.updateStatusName }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="资料名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="fileCategoryName"
        label="资料类型"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="typeName"
        label="类型归属"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column width="80px" align="center" label="操作">
        <template v-slot="{ row }">
          <el-button
            icon="el-icon-view"
            type="text"
            size="small"
            @click="handlePreview(row)"
            >预览</el-button
          >
          <upload-file
            v-if="type === 'verify'"
            @success="(attach) => handleSuccess(attach, row)"
            :limit="1"
            :ref="row.id"
            btnText="上传文件"
            :showFileList="false"
            :showFile="false"
            formatLimit="xlsx,pdf,docx,md"
          ></upload-file>
        </template>
      </el-table-column>
    </el-table>
    <div class="img_fullScreen" v-show="false">
      <el-image style="height: 100%" ref="image" :preview-src-list="[imageUrl]">
        <span slot="placeholder" class="loading">加载中...</span>
        <span slot="error">图片加载失败!</span>
      </el-image>
    </div>
  </div>
</template>

<script>
  import UploadFile from '@/components/view-upload-file.vue';
  import { updateOneFile } from '@/api/equiment-full-life-api/change';
  import { previewFile } from '@/util/preview';
  import { getFileFullUrl } from '@/util/file';

  export default {
    name: 'bearingLibraryIndex',
    components: { UploadFile },
    props: {
      list: {
        type: Array,
        default: () => []
      },
      isEdit: { type: Boolean, default: false },
      type: {
        type: String,
        default: ''
      },
      detail: {
        type: Object,
        default: () => {}
      }
    },
    watch: {},

    data() {
      return {
        allPageSelect: [],
        loading: false,
        imageUrl: ''
      };
    },
    mounted() {},
    methods: {
      handlePreview(row) {
        let file;
        if (row.newFile) {
          const { id, originalName } = row.newFile;
          const extension = originalName.split('.').pop();
          file = { id, originalName, extension };
        } else {
          const { attachId: id, name: originalName, extension } = row;
          file = { id, originalName, extension };
        }
        const imageExtensions = ['jpg', 'jpeg', 'png'];
        const extension = (file.extension || '').toLowerCase();
        if (imageExtensions.includes(extension)) {
          this.imageUrl = getFileFullUrl(file.id);
          // 调用预览方法
          this.$nextTick(() => {
            this.$refs.image.clickHandler();
          });
        } else if (extension === 'md') {
          this.$message.warning('Markdown文件无法预览');
        } else {
          if (!file.originalName) file.originalName = file.name;
          previewFile(file);
        }
      },
      async handleSuccess(attach, row) {
        try {
          this.loading = true;
          let [firstAttach] = attach;
          await updateOneFile({
            attachId: firstAttach.attachId,
            equipmentFileId: row.equipmentFileId,
            updateId: row.id
          });
          this.$set(row, 'updateStatus', '1');
          this.$set(row, 'updateStatusName', '已更新');
          this.$set(row, 'name', firstAttach.originalName);
          this.$set(row, 'newFile', firstAttach);
        } catch (error) {
          this.$refs[row.id].$refs.upload.clearFiles();
          console.log(error);
        } finally {
          this.loading = false;
        }
      },
      handleCheckBox(rows, row) {
        if (rows.includes(row)) {
          // 新勾选
          this.$set(row, 'num', 1);
          this.$set(row, 'disabled', false);
          this.allPageSelect.push(row);
        } else {
          // 取消勾选
          this.$set(row, 'disabled', true);
          this.$set(row, 'num', undefined);
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item.id !== row.id
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        if (rows.length) {
          rows.forEach((row) => {
            this.$set(row, 'num', 1);
            this.$set(row, 'disabled', false);
            if (!this.allPageSelect.find((item) => item.id === row.id)) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.list.forEach((row) => {
            this.$set(row, 'num', undefined);
            this.$set(row, 'disabled', true);
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item.id !== row.id
            );
          });
        }
      }
    }
  };
</script>
<style lang="scss" scoped></style>
