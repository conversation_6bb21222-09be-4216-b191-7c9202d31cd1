<template>
  <div class="top-info">
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="originalName"
        label="资料名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="typeName"
        label="归类"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="type"
        label="文件类别"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="num"
        label="数量"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: 'DeviceBasicList',
    components: {},
    props: {
      details: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    watch: {
      'details.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.details.equipmentFileInfoList);
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: []
      };
    },

    mounted() {},

    methods: {
      setData(attachInfoList) {
        if (attachInfoList) {
          let data = attachInfoList.map((i) => {
            return {
              ...i.attach,
              num: 1,
              type: this.getString(i.attach.originalName),
              typeName: i.typeName
            };
          });
          this.list = data;
        } else {
          this.list = [];
        }
      },
      getString(str) {
        const lastDotIndex = str.lastIndexOf('.');
        let result;
        if (lastDotIndex !== -1) {
          // 截取点后面的部分
          result = str.slice(lastDotIndex + 1);
        } else {
          // 如果没有找到点，则返回原字符串
          result = str;
        }
        return result;
      }
    }
  };
</script>

<style scoped lang="scss"></style>
