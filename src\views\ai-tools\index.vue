<template>
  <div class="ai-container">
    <el-row :gutter="10">
      <el-col :span="4" class="left-wrapper">
        <el-card shadow="never">
          <el-button
            v-for="{ type, label, icon, activeIcon } in btnOptions"
            :key="type"
            :class="{ active: activeBtn === type }"
            @click="handleBtnClick(type)"
            ><el-image :src="activeBtn === type ? activeIcon : icon" />{{
              label
            }}</el-button
          >
        </el-card>
      </el-col>
      <el-col :span="20" class="right-wrapper">
        <div class="page-title">
          <div class="main-title">AI工具箱</div>
          <div class="sub-title">智能工具，我是你的智能助手</div>
        </div>
        <el-card shadow="never">
          <component ref="comp" :is="comp"></component>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import Standard from './standard';
  import Diagnose from './diagnose';
  import StandardImg from '@/asset/img/standard.png';
  import StandardActiveImg from '@/asset/img/standard-active.png';
  import DiagnoseImg from '@/asset/img/diagnose.png';
  import DiagnoseActiveImg from '@/asset/img/diagnose-active.png';

  const btnOptions = [
    {
      label: '生成运维标准',
      type: 'standard',
      icon: StandardImg,
      activeIcon: StandardActiveImg
    },
    {
      label: '故障维修建议',
      type: 'diagnose',
      icon: DiagnoseImg,
      activeIcon: DiagnoseActiveImg
    }
  ];

  export default {
    components: { Standard, Diagnose },
    data() {
      return {
        btnOptions,
        activeBtn: 'standard' // 当前选中的按钮
      };
    },
    computed: {
      comp() {
        return this.activeBtn === 'standard' ? 'Standard' : 'Diagnose';
      }
    },
    activated() {
      const { type, deviceId, deviceName, defectDesc } = this.$route.params;
      if (type) {
        this.activeBtn = type;
        if (type === 'diagnose') {
          this.$nextTick(() => {
            this.$refs.comp.setForm({ deviceId, deviceName, defectDesc });
          });
        }
      }
    },
    methods: {
      handleBtnClick(type) {
        this.activeBtn = type;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .ai-container ::v-deep {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 121.5px);
    padding: 0 10px !important;
    background: #eaf4fc;
    box-shadow: 0 0 10px 0 #1373fa;

    .page-title {
      position: relative;
      display: flex;
      align-items: center;
      margin: 18px 0 10px;
      padding-left: 50px;
      font-family: 'Noto Sans SC';

      &::before {
        position: absolute;
        left: 8px;
        width: 42px;
        height: 38px;
        background: url('../../asset/img/ai-logo.png') no-repeat center;
        background-size: contain;
        content: '';
      }

      .main-title {
        margin-left: 12px;
        color: #333;
        font-weight: 600;
        font-size: 26px;
      }

      .sub-title {
        margin-left: 12px;
        color: #666;
        font-size: 16px;
      }
    }

    .el-row {
      flex: 1;

      .el-col {
        height: 100%;

        &.left-wrapper {
          .el-card__body {
            background: #f6faff;

            .el-button {
              width: 100%;
              margin-bottom: 10px;
              margin-left: 0;
              font-size: 16px;
              background: transparent;
              border: none;

              > span {
                display: flex;
                align-items: center;

                .el-image {
                  width: 22px;
                  margin-right: 4px;
                }
              }

              &.active {
                color: #1e62fb !important;
                background-color: #e1ecfd;
                border-radius: 16px;
              }
            }
          }
        }

        &.right-wrapper {
          height: calc(100% - 63px);
        }

        .el-card {
          height: 100%;

          .el-card__body {
            position: relative;
            height: 100%;
          }
        }
      }
    }
  }
</style>
