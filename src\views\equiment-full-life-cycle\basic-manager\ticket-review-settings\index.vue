<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>

      <el-table-column
        prop="name"
        label="工单类型"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.orderTypeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        label="是否需审核"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <el-switch
            :value="row.isApproval"
            :active-value="1"
            :inactive-value="0"
            @change="handleSwitch(row)"
            active-text="是"
            inactive-text="否"
          >
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="修改人员"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.updateUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="修改时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <template v-if="row.updateUserName">
            {{ row.updateTime | dateFormat }}
          </template>
          <template v-else>-</template>
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center" width="200" fixed="right">
        <template v-slot="{ row }">
          <el-button
            icon="el-icon-edit"
            type="text"
            size="small"
            @click="operate(row)"
            >编辑</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>

    <!-- <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    /> -->
    <add-device-base ref="add" @success="getList"></add-device-base>
  </basic-container>
</template>

<script>
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';

  import {
    getSettingList,
    settingApproval
  } from '@/api/equiment-full-life-api/ticket-review-settings';
  import { dateFormat } from '@/util/date';

  export default {
    name: 'oilingWay',
    components: {
      Search,
      AddDeviceBase,
      Pagination
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      async handleSwitch(row) {
        let val = row.isApproval;
        let msg = val
          ? `您将关闭${row.orderTypeName}审核功能，是否确认；`
          : `您将开始${row.orderTypeName}审核功能（具体审核人员需要通过角色权限设置），是否确认；`;
        await this.confirm(msg);
        let { id, isApproval, orderType } = row;
        try {
          await settingApproval({
            id,
            isApproval: isApproval === 0 ? 1 : 0,
            orderType
          });
          this.success('操作成功');
        } catch (error) {
          return this.getList();
        }
        this.getList();
        // if (val) {
        //   this.$set(row, 'isApproval', 0);
        // } else {
        //   this.$set(row, 'isApproval', 1);
        // }
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getSettingList();
          this.list = res.data.data || [];
          // this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      }
    },
    filters: {
      dateFormat(date) {
        if (!date) return '-';
        let val = new Date(date);
        return dateFormat(val, 'yyyy-MM-dd hh:mm');
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
