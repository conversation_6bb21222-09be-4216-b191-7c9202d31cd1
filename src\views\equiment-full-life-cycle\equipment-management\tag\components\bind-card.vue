<template>
  <span>
    <el-dialog
      title="提示"
      :visible.sync="centerDialogVisible"
      width="30%"
      center
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      @close="closed"
    >
      <section v-loading="loading">
        <div class="cards">
          <div>
            <el-input
              size="small"
              placeholder="SN编号"
              v-model="devEqumentNo"
              :disabled="true"
            >
              <template slot="prepend" @click="Request">SN编号</template>
            </el-input>
          </div>
          <div>
            <div style="margin-top: 15px">
              <el-input
                size="small"
                placeholder="请点击按钮获取卡号"
                v-model="UUID"
                :disabled="true"
              >
                <template slot="prepend"> 卡号</template>
                <template slot="append">
                  <span @click="Request">{{ btnText }} </span>
                </template>
              </el-input>
            </div>
          </div>
        </div>
        <p class="desc">确定将以上设备卡号和SN编号绑定吗？</p>
        <span slot="footer" class="dialog-footer">
          <el-button @click="closed" size="small">取 消</el-button>
          <el-button type="primary" @click="confirm" size="small"
            >确 定</el-button
          >
        </span>
      </section>
    </el-dialog>
  </span>
</template>
<script>
  export default {
    props: {
      content: {
        type: String,
        default: () => {
          return '读取';
        }
      }
    },
    data() {
      return {
        devEqumentNo: undefined,
        btnText: '获取卡号',
        g_isOpen: false, // 检查读写器USB 接口是否已经打开
        centerDialogVisible: false,
        loading: false,
        UUID: undefined
      };
    },
    methods: {
      confirm() {
        if (this.UUID) {
          this.$emit('doDateMessage', this.UUID);
          this.centerDialogVisible = false;
        } else {
          this.$message.warning('请先获取卡号');
        }
      },
      show(code, UUID) {
        console.log('show', code, UUID);
        this.centerDialogVisible = true;
        this.devEqumentNo = code;
        if (UUID) {
          this.btnText = '重新获取卡号';
        } else {
          this.btnText = '获取卡号';
        }
        this.UUID = UUID;
      },

      Request() {
        this.$emit('request');
      },
      closed() {
        this.UUID = undefined;
        this.centerDialogVisible = false;
        this.loading = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  a {
    color: #1890ff;
    cursor: pointer;
  }
  ::v-deep {
    .el-input-group__prepend {
      width: 50px;
    }
    .el-input-group__append {
      cursor: pointer;
      background: #409eff;
      color: #fff;
    }
  }
  .desc {
    text-align: center;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
  }
</style>
