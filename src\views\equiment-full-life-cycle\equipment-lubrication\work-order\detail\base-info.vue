<template>
  <!-- 详情基本信息 -->
  <div v-if="details">
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
      size="small"
    >
      <el-descriptions-item label="工单编号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备名称：">{{
        details.name || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="润滑部门：">{{
        details.chargeDeptName || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="设备位置：">{{
        details.locationPath || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="计划时间：">{{
        details.planTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="执行时间：">{{
        details.executeTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="责任人：">{{
        details.executeUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="工单状态：">{{
        details.statusName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="驳回原因：" v-if="details.status === 6">{{
        details.rejectReason || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  import { convertFileUrl } from '@/util/util';

  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},
    data() {
      return {};
    },
    methods: { convertFileUrl }
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
