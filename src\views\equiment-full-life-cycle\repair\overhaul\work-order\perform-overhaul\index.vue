<template>
  <dialog-drawer
    title="检修"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" style="padding-bottom: 80px" v-loading="loading">
      <!-- 基本信息 -->
      <span class="el-base-title">设备信息</span>
      <base-info :details="details"></base-info>
      <span class="el-base-title">检修标准</span>
      <standard ref="standard" :standardForm="standardForm"></standard>
      <!-- <div style="padding-bottom: 50px">
        <logs ref="log"></logs>
      </div> -->
      <div class="oper_btn" style="margin-top: 80px">
        <btn type="submit" @click="submit" :loading="loading"> </btn>
        <btn type="cancel" @click="closed"></btn>
      </div>
    </div>
  </dialog-drawer>
</template>
<script>
  import BaseInfo from '@/views/equiment-full-life-cycle/repair/overhaul/work-order/detail/base-info.vue';
  import Standard from './standard.vue';
  import Consumable from './consumable.vue';
  import {
    getCheckRepairOrderViewApi,
    submitCheckRepairOrderApi
  } from '@/api/equiment-full-life-api/repair';
  import { convertFileUrl, deepClone } from '@/util/util';
  import Logs from '@/views/equiment-full-life-cycle/components/logs/index.vue';

  export default {
    name: 'PerformOverhaulIndex',
    components: {
      Logs,
      BaseInfo,
      Standard,
      Consumable
    },
    data() {
      return {
        standardForm: {
          standardList: []
        },
        convertFileUrl,
        eqId: '',
        visible: false,
        loading: false,
        details: {} // 详情数据
      };
    },
    methods: {
      async submit() {
        let { standardList } = this.standardForm;
        // 验证
        const isAll = standardList.every((item) => item.isAbnormal === 0);
        if (isAll) {
          await this.confirm('检查结果均为正常，是否提交？');
          this.submitOverhaul();
        } else {
          //  存在异常情况
          const flag = standardList.every((item, index) => {
            if (item.materialList) {
              let notCompleteIndex = -1;
              item.materialList.every((item, index) => {
                if (!item.count) {
                  notCompleteIndex = index + 1;
                  return false; // 返回 false 会终止 every 的检查
                }
                return true;
              });

              if (notCompleteIndex > -1) {
                this.loading = false;
                this.$message.warning(
                  `请完善序号${index + 1} 第 ${notCompleteIndex} 个部件信息`
                );
                return false;
              }
            }

            if (item.isAbnormal === 1 && item.isReport === 0) {
              if (
                item.faultName &&
                item.faultType &&
                Number.isInteger(item.result) &&
                (item.faultType === '1' ? item.faultLevel : true) &&
                item.faultRemark &&
                item.duration &&
                item.checkMethod &&
                item.faultReason &&
                item.solution &&
                item.attachList &&
                item.attachList.length > 0
              ) {
                return true;
              } else {
                this.$message.warning(
                  `请完善序号${index + 1} ${item.monitorName}异常信息`
                );
                this.loading = false;
                return false;
              }
            } else if (item.isAbnormal === 1 && item.isReport === 1) {
              if (
                item.faultName &&
                item.faultType &&
                (item.faultType === '1' ? item.faultLevel : true) &&
                item.faultRemark &&
                item.attachList &&
                item.attachList.length > 0
              ) {
                return true;
              } else {
                this.$message.warning(
                  `请完善序号${index + 1} ${item.monitorName}异常信息`
                );
                this.loading = false;
                return false;
              }
            } else if (item.isAbnormal === 0) {
              // 全部正常的情况
              return true;
            } else {
              this.$message.warning(
                `请完善序号${index + 1} ${item.monitorName}点巡检信息`
              );
              this.loading = false;
              return false;
            }
          });
          if (flag) {
            this.submitOverhaul();
          }
        }
        let isBool = await this.$refs['standard'].validForm();
        if (!isBool) return;
        // 验证end
      },
      async submitOverhaul() {
        let { equipmentAccount, id } = this.details;
        let { standardList } = this.standardForm;
        const params = {
          equipmentId: equipmentAccount.id,
          overhaulRecordList: standardList.map((item) => {
            return {
              monitorId: item.monitorId,
              standardId: item.id,
              isAbnormal: item.isAbnormal, //检修结果
              faultName: item.faultName, //故障缺陷名称
              faultType: item.faultType, //故障缺陷类型
              faultLevel: item.faultType === '1' ? item.faultLevel : undefined, //故障缺陷等级
              faultRemark: item.faultRemark, //故障缺陷描述
              isReport: item.isAbnormal === 0 ? undefined : item.isReport, //是否现场处理
              result: item.result, //检修结果id
              checkMethod: item.checkMethod, //检查方式
              duration:
                Number(item.isAbnormal) === 0 || item.isReport === 1
                  ? undefined
                  : item.duration || 0.5, //维修时长
              faultReason: item.faultReason, //故障缺陷原因
              solution: item.solution, //解决方案
              attachId:
                item.attachList &&
                item.attachList.map((attach) => attach.id).join(','),
              materialList: (item.materialList || []).map((item) => ({
                ...item
              }))
            };
          }),
          orderId: id
        };
        try {
          await submitCheckRepairOrderApi(params);
          this.$message.success('提交成功');
          this.$emit('success');
          this.closed();
        } catch (error) {
          console.log(error);
        }
      },
      async show(id) {
        if (id) {
          this.visible = true;
          await this.getDetail(id);
        }
      },
      closed() {
        this.visible = false;
        this.details = {};
        this.standardForm.standardList = [];
      },
      // 获取详情接口
      async getDetail(no) {
        try {
          this.loading = true;
          const res = await getCheckRepairOrderViewApi({ no: no });
          let detail = res.data.data || {};
          this.details = detail;
          let standardList = deepClone(detail.standardList || []);
          standardList.forEach((element, index) => {
            element.isAbnormal = 0;
            element.isReport = 0;
            element.serialNumber = index + 1;
          });
          this.standardForm = {
            standardList
          };
          // await this.$refs['log'].getLogs(this.details.id, 'OVERHAUL_ORDER');
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }
</style>
