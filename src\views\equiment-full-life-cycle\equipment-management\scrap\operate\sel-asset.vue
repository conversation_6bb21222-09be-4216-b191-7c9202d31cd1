<template>
  <div>
    <el-button
      size="small"
      type="primary"
      style="margin-bottom: 15px"
      @click="selAsset"
      v-if="isDele"
      >+ 选择设备</el-button
    >
    <el-button
      v-if="list.length > 0"
      icon="el-icon-delete"
      type="danger"
      size="small"
      @click="delAll"
      >删除所选</el-button
    >
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      border
      stripe
      size="small"
      :header-cell-style="{ background: '#fafafa' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="index" label="#"></el-table-column>
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column prop="code" label="设备编号" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="sn" label="设备SN" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="name" label="设备名称" show-overflow-tooltip>
      </el-table-column>

      <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.model || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="measureUnitName"
        label="计量单位"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.measureUnitName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="belongDeptName"
        label="归属部门"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.belongDeptName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="responsiblePersonName"
        label="负责人"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{
          row.responsiblePersonName || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.useDeptName || '-' }}</template>
      </el-table-column>
      <el-table-column prop="userName" label="使用人" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.userName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="locationPath"
        label="存放地点"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.locationPath || '-' }}</template>
      </el-table-column>
      <el-table-column prop="model" label="操作" v-if="isDele">
        <template v-slot="scope">
          <el-button
            style="color: red"
            size="small"
            type="text"
            @click="del(scope)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <asset-list
      ref="assetList"
      @getAssetList="getAssetList"
      selType="scrap"
      :nqStatus="'4'"
    ></asset-list>
  </div>
</template>

<script>
  import AssetList from '@/views/equiment-full-life-cycle/components/select-asset-scrap-dialog';
  export default {
    name: 'DeviceBasicList',
    components: { AssetList },
    props: {
      isDele: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      detail: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.list = this.detail.detailList;
            });
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        multipleSelection: [],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },

    mounted() {},
    methods: {
      delAll() {
        if (this.multipleSelection.length === 0) {
          this.$message.warning('请先选择要删除的标准');
          return;
        }
        const filteredArray = this.list.filter(
          (item) =>
            !this.multipleSelection.some(
              (deleteItem) => deleteItem.id === item.id
            )
        );
        this.list = [...filteredArray];
      },

      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      del(scope) {
        this.list.splice(scope.$index, 1);
      },
      validForm() {
        if (this.list.length === 0) {
          this.$message.warning('请选择设备');
          return;
        }
        let ids = this.list
          .map((it) => {
            return it.id;
          })
          .join(',');
        return { equipmentIds: ids };
      },
      resetForm() {
        this.list = [];
      },
      //  获取设备
      getAssetList(val) {
        this.list = val;
      },
      selAsset() {
        let list = this.list.map((item) => {
          return {
            ...item,
            num: 1
          };
        });
        this.$refs['assetList'].show(list);
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }
  .operateBtn {
    margin-bottom: 15px;
  }
  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
