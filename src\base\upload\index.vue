<template>
  <el-upload ref="upload" v-bind="$_attrs" :headers="headers">
    <slot />
  </el-upload>
</template>

<script>
  import { baseUrl } from '@/config/env';
  import { Base64 } from 'js-base64';
  import website from '@/config/website';
  import { getToken } from '@/util/auth';

  export default {
    name: 'CUpload',
    props: {
      value: {
        type: Array,
        default: () => []
      },
      clearValid: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        headers: {
          Authorization: `Basic ${Base64.encode(
            `${website.clientId}:${website.clientSecret}`
          )}`,
          [website.tokenHeader]: 'bearer ' + getToken()
        },
        fileList: []
      };
    },
    methods: {
      setValue() {
        let files = this.$refs.upload.uploadFiles.filter(
          (file) => file.status === 'success'
        );
        let newVal = files.map((item) => {
          let { response, id } = item;
          if (response) {
            id = response.data.id;
          }
          return { id };
        });
        this.isSetValue = true;
        this.$emit('input', newVal);
        if (this.clearValid) {
          this.$emit('handleSuccess');
        }
      }
    },
    computed: {
      $_attrs() {
        const baseurl = `${baseUrl}/api/szyk-resource/oss/endpoint/put-file-attach`;
        return {
          name: 'file',
          action: baseurl,
          accept: 'multipart/form-data',
          'file-list': this.fileList,
          'on-success': this.setValue,
          'on-remove': this.setValue,
          multiple: true,
          ...this.$attrs
        };
      }
    },
    watch: {
      value: {
        immediate: true,
        handler(newVal) {
          if (this.isSetValue) {
            return (this.isSetValue = false);
          }
          if (newVal && newVal instanceof Array) {
            this.fileList = newVal.map((item) => {
              return {
                ...item
              };
            });
          }
        }
      }
    }
  };
</script>
