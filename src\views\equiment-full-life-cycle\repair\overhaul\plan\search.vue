<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="计划周期" prop="cycleType">
        <el-select
          v-model="form.cycleType"
          placeholder="请选择计划周期"
          style="width: 150px"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['plan_cycle'].filter((item) => {
              return item.value !== 'DAY';
            })"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="负责部门" prop="executeDeptName">
        <InputTree
          v-model="form.executeDept"
          lazy
          clearable
          :form="form"
          :dic="deptData"
          style="width: 190px"
          :props="{
            label: 'deptName',
            value: 'id',
            isLeaf: (row) => !row.hasChildren,
            formLabel: 'executeDeptName',
            formValue: 'executeDept'
          }"
          :load="lazyLoad"
          :lazyLoading="lazyLoading"
          @search="lazySearch"
        ></InputTree>
      </el-form-item>
      <el-form-item label="计划名称" prop="name">
        <el-input
          v-model.trim="form.name"
          placeholder="请输入计划名称"
          clearable
          style="width: 150px"
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="选择日期" prop="time" class="_label">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          style="width: 250px"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="计划状态" prop="status">
        <el-select
          v-model="form.status"
          style="width: 150px"
          placeholder="请选择计划状态"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['overhaul_plan_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择部门-->
    <!-- <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog> -->
  </div>
</template>

<script>
  // import DeptDialog from '@/components/dept-dialog/index.vue';
  import { getLazyList } from '@/api/system/dept';
  import { InputTree } from '@/components/yk-select-tree';
  export default {
    name: 'DeviceListSearch',
    components: { InputTree },
    serviceDicts: ['plan_cycle', 'overhaul_plan_status'],
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        form: {
          cycleType: undefined,
          executeDept: undefined,
          executeDeptName: undefined,
          name: undefined,
          time: undefined,
          status: undefined
        },
        deptData: [],
        lazyLoading: false
      };
    },
    methods: {
      async lazySearch(title) {
        this.lazyLoad(null, null, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node || {};
        const { id } = data || {};
        let params = {
          deptName: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyList(title ? '' : id || 0, params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          if (title) {
            this.deptData = arr;
          } else {
            resolve && resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      reset() {
        this.form.executeDept = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          queryStartDate: this.form.time ? this.form.time[0] : undefined,
          queryEndDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
