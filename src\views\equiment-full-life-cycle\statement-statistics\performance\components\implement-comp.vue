<template>
  <div class="comp-wrapper">
    <!-- 搜索 -->
    <implement-comp-search
      ref="search"
      @search="search"
      num="first"
    ></implement-comp-search>
    <!-- 总结 -->
    <div class="summary-wrapper">
      <!-- <el-button
        icon="el-icon-upload2"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <!-- 表格 -->
    <el-table
      v-loading="loading"
      ref="table"
      class="table"
      :data="listData"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="orderTypeName"
        label="工单类型"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="orderNum"
        label="全部工单数量"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="completeNum"
        label="完成数量"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="unCompleteNum"
        label="未完成数量"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="completeRate"
        label="完成比例"
        align="center"
        show-overflow-tooltip
        sortable
      >
        <template v-slot="{ row }">
          {{ (Number(row.completeRate) * 100).toFixed(2) }}%
        </template>
      </el-table-column>
      <el-table-column prop="" label="操作" align="center">
        <template v-slot="{ row }">
          <el-button type="text" @click="view(row)" size="small">
            查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { getToken } from '@/util/auth';
  import { downloadFileBlob } from '@/util/util';
  import { getImplementList } from '@/api/equiment-full-life-api/statement-statistics.js';
  import implementCompSearch from '@/views/equiment-full-life-cycle/statement-statistics/performance/components/implement-comp-search.vue';

  export default {
    components: { implementCompSearch },
    data() {
      return {
        loading: false,
        orderTypeOpts: [
          { label: '点巡检工单', value: 'INSPECT_ORDER' },
          { label: '内部维修工单', value: 'INTERNAL_REPAIR' },
          { label: '外委维修工单', value: 'EXTERNAL_REPAIR' },
          { label: '检修工单', value: 'OVERHAUL_ORDER' },
          { label: '保养工单', value: 'MAINTAIN_ORDER' },
          { label: '润滑工单', value: 'LUBRICATE_ORDER' }
        ],
        searchParams: {
          current: 1,
          size: 10
        },
        // 列表
        listData: []
      };
    },
    created() {},
    methods: {
      // 点击查看
      view(row) {
        console.log('searchParams', this.searchParams);
        this.$emit('compView', {
          comp: 'ImplementView',
          paramsObj: {
            ...row,
            startDate: this.searchParams.startDate,
            endDate: this.searchParams.endDate
          }
        });
      },
      search(form) {
        this.searchParams = {
          ...this.searchParams,
          ...form
        };
        this.getList();
      },
      reset() {
        this.searchParams.current = 1;
        this.$refs['search'].resetFields();
        this.search();
      },
      refresh() {
        this.getList();
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          const params = { ...this.searchParams };
          let res = await getImplementList(params);
          this.listData = res.data.data || [];
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 导出
      exportExcel() {
        let path =
          '/api/szyk-simas/statistical-report/export-order-statistics?';
        const params = {
          ...this.searchParams
        };
        delete params.time;
        delete params.current;
        delete params.size;

        for (let key in params) {
          params[key] && (path += `${key}=${params[key]}&`);
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '计划执行率统计.xlsx'
        );
      }
    }
  };
</script>

<style lang="scss" scoped>
  .comp-wrapper {
    height: 100%;

    .search-wrapper {
      ::v-deep .el-form-item {
        margin-bottom: 10px;
      }
    }

    .summary-wrapper {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 10px;
    }
  }
</style>
