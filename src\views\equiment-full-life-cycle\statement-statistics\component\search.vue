<template>
  <div class="search-content">
    <el-row>
      <el-form
        label-suffix="："
        :inline="true"
        ref="search"
        :model="form"
        size="small"
        class="search-form"
      >
        <el-col :span="4">
          <el-form-item label="统计时间" prop="queryDate">
            <el-select v-model="form.queryDate" placeholder="请选择计划周期">
              <el-option
                v-for="item in queryDateOpts"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="选择日期" prop="time" class="_label">
            <el-date-picker
              v-model="form.time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              clearable
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="选择部门" prop="deptId" v-if="isShowDept">
            <InputTree
              v-model="form.deptId"
              lazy
              clearable
              :form="form"
              :dic="deptData"
              style="width: 100%"
              :props="{
                label: 'deptName',
                value: 'id',
                isLeaf: (row) => !row.hasChildren,
                formLabel: 'deptName',
                formValue: 'deptId'
              }"
              :load="lazyLoad"
              :lazyLoading="lazyLoading"
              @search="lazySearch"
            ></InputTree>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="工单状态" prop="status" v-if="isShowOrderStatus">
            <el-select
              v-model="form.status"
              placeholder="请选择工单状态"
              clearable
            >
              <el-option
                v-for="item in statusArr"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" v-if="isShowQueryType">
          <el-form-item label="统计方式" prop="sparePartConsumptionType">
            <el-select
              v-model="form.sparePartConsumptionType"
              placeholder="请选择统计方式"
              clearable
            >
              <el-option
                v-for="item in queryTypeOpts"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item class="no-label-form-item">
            <btn type="search" @click="submit" />
            <btn type="reset" @click="reset" />
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
  import { getLazyList } from '@/api/system/dept';
  import { InputTree } from '@/components/yk-select-tree';

  export default {
    name: 'DeviceListSearch',
    components: { InputTree },
    props: {
      // 是否显示部门
      isShowDept: {
        type: Boolean,
        default: true
      },
      // 是否显示统计方式
      isShowQueryType: {
        type: Boolean,
        default: false
      },
      // 状态选项
      statusArr: {
        type: Array,
        default: () => []
      },
      // 是否显示工单状态
      isShowOrderStatus: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        queryDateOpts: [
          { label: '今日', value: 3 },
          { label: '近7天', value: 2 },
          { label: '近30天', value: 1 },
          { label: '近一年', value: 0 }
        ],
        queryTypeOpts: [
          { label: '保养消耗', value: 'MAINTAIN_ORDER' },
          { label: '维修消耗', value: 'REPAIR_ORDER' },
          { label: '润滑消耗', value: 'LUBRICATE_ORDER' },
          { label: '检修消耗', value: 'OVERHAUL_ORDER' }
        ],
        deptData: [],
        lazyLoading: false,
        form: {
          deptName: undefined,
          deptId: undefined,
          queryDate: 3, // 默认查今日
          time: undefined,
          sparePartConsumptionType: undefined,
          status: undefined
        }
      };
    },
    created() {
      this.$nextTick(() => {
        this.submit();
      });
    },
    methods: {
      async lazySearch(title) {
        this.lazyLoad(null, null, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node || {};
        const { id } = data || {};
        let params = {
          deptName: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyList(title ? '' : id || 0, params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          if (title) {
            this.deptData = arr;
          } else {
            resolve && resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      reset() {
        this.form.deptName = undefined;
        this.form.deptId = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      resetStatus() {
        this.form.status = undefined;
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          startDate: this.form.time ? this.form.time[0] : undefined,
          endDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-col {
      margin-bottom: 0 !important;
    }

    .el-range-separator {
      padding: 0 15px;
    }

    .el-form-item {
      width: 100%;
      margin-right: 0 !important;

      &:not(.no-label-form-item) {
        .el-form-item__content {
          width: calc(100% - 90px);
        }
      }
    }

    .el-select,
    .el-input,
    .el-date-editor {
      width: 100% !important;
    }
  }
</style>
