import request from '@/router/axios';
// 备品备件相关接口
// 库房管理--------------------------------------------------
//  分页列表
export const getStoreHouseListApi = (params) => {
  return request({
    url: `/api/szyk-simas/spare-parts/warehouse/page`,
    method: 'get',
    params
  });
};
//提交
export const addOrEditCheckRepairDetailApi = (data) =>
  request({
    url: '/api/szyk-simas/spare-parts/warehouse/submit',
    method: 'post',
    data
  });

// 详情
export const getCheckRepairDetailViewApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/warehouse/detail',
    method: 'get',
    params
  });

//  删除
export const delCheckRepairDetailApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/warehouse/remove',
    method: 'post',
    params
  });
// 库房列表
export const getCheckRepairTypeListApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/warehouse/list',
    method: 'get',
    params
  });

//  台账相关接口 【备品备件字典相关接口】--------------------
export const getSparePartsPageListApi = (params) =>
  request({
    url: '/api/szyk-simas/sparepartsdict/page',
    method: 'get',
    params
  });

//提交
export const addOrEditSparePartsApi = (data) =>
  request({
    url: '/api/szyk-simas/sparepartsdict/saveOrUpdate',
    method: 'post',
    data
  });

//详情
export const getSparePartsViewApi = (id) =>
  request({
    url: `/api/szyk-simas/sparepartsdict/detail/${id}`,
    method: 'get'
  });

//启用、停用相关接口
export const startStopSparePartsApi = (id, params) =>
  request({
    url: `/api/szyk-simas/sparepartsdict/status/${id}`,
    method: 'put',
    params
  });

//  删除相关接口
export const delSparePartsApi = (params) =>
  request({
    url: `/api/szyk-simas/sparepartsdict`,
    method: 'delete',
    params
  });
// 临时盘点
export const editTempInventory = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/inventory/interim-inventory',
    method: 'post',
    params
  });

// 备品备件领用管理 ------------------------------------------------
export const getSparePartsReceiveListApi = (params) =>
  request({
    url: '/api/szyk-simas/sparepartsissuanceorder/page',
    method: 'get',
    params
  });
// 提交
export const addSparePartsReceiveApi = (data) =>
  request({
    url: '/api/szyk-simas/sparepartsissuanceorder/save',
    method: 'post',
    data
  });
export const editSparePartsReceiveApi = (data) =>
  request({
    url: '/api/szyk-simas/sparepartsissuanceorder/update',
    method: 'put',
    data
  });
// 详情
export const getSparePartsReceiveViewApi = (id) =>
  request({
    url: `/api/szyk-simas/sparepartsissuanceorder/detail/${id}`,
    method: 'get'
  });
// 审核
export const auditSparePartsReceiveApi = (data, id) =>
  request({
    url: `/api/szyk-simas/sparepartsissuanceorder/approval/${id}`,
    method: 'put',
    data
  });

// 撤销
export const cancelSparePartsReceiveApi = (id) =>
  request({
    url: `/api/szyk-simas/sparepartsissuanceorder/cancel/${id}`,
    method: 'put'
  });

// 备品备入库、出库管理-------------------------------------------------------------
//分页
export const getSparePartsInListApi = (params) =>
  request({
    url: '/api/szyk-simas/sparepartsinboundorder/page',
    method: 'get',
    params
  });

export const getSparePartsOutListApi = (params) =>
  request({
    url: '/api/szyk-simas/sparepartsoutboundorder/page',
    method: 'get',
    params
  });

// 详情
export const getSparePartsInOutViewApi = (id) =>
  request({
    url: `/api/szyk-simas/sparepartsinboundorder/detail/${id}`,
    method: 'get'
  });
// 出库查看详情
export const getSparePartsOutViewApi = (id) =>
  request({
    url: `/api/szyk-simas/sparepartsoutboundorder/detail/${id}`,
    method: 'get'
  });
// 提交
export const addOrEditSparePartsInApi = (data) =>
  request({
    url: '/api/szyk-simas/sparepartsinboundorder/save',
    method: 'post',
    data
  });

//  出库提交3
export const addSparePartsOutApi = (data) =>
  request({
    url: '/api/szyk-simas/sparepartsoutboundorder/save',
    method: 'post',
    data
  });
//  出库编辑
export const editSparePartsOutApi = (data) =>
  request({
    url: '/api/szyk-simas/sparepartsoutboundorder/update',
    method: 'post',
    data
  });
// 撤销
export const cancelSparePartsInOutApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/storage/cancel',
    method: 'post',
    params
  });

//  出库撤销
export const cancelSparePartsOutApi = (id) =>
  request({
    url: `/api/szyk-simas/sparepartsoutboundorder/cancel/${id}`,
    method: 'put'
  });
// 删除
export const delSparePartsInOutApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/storage/remove',
    method: 'post',
    params
  });

//  出库删除
export const delSparePartsOutApi = (id) =>
  request({
    url: `/api/szyk-simas/sparepartsoutboundorder/${id}`,
    method: 'delete'
  });
// 备注
export const addSparePartsRemarkApi = (data) =>
  request({
    url: '/api/szyk-simas/sparepartsinboundorder/completionRemark',
    method: 'post',
    data
  });

// 盘点相关接口-------------------------------------------
// 分页
export const getSparePartsCheckListApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/inventoryplan/page',
    method: 'get',
    params
  });

//  盘点计划列表统计
export const getSparePartsCheckListStaticApi = (params) =>
  request({
    url: '/api/szyk-simas/inventory/plan/count',
    method: 'get',
    params
  });
// 详情
export const getSparePartsCheckViewApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/inventoryplan/detail',
    method: 'get',
    params
  });

// 提交
export const addOrEditSparePartsCheckApi = (data) =>
  request({
    url: '/api/szyk-simas/spare-parts/inventoryplan/submit',
    method: 'post',
    data
  });

//启动
export const startSparePartsCheckApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/inventoryplan/start',
    method: 'get',
    params
  });
// 终止
export const stopSparePartsCheckApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/inventoryplan/stop',
    method: 'get',
    params
  });

// 删除
export const delSparePartsCheckApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/inventoryplan/remove',
    method: 'get',
    params
  });
//  物品列表分页
export const getSparePartsCheckItemListApi = (params) =>
  request({
    url: '/api/szyk-simas/sparepartsinventoryitem/page',
    method: 'get',
    params
  });

//物品列表统计
export const getSparePartsCheckItemStatisticsApi = (params) =>
  request({
    url: '/api/szyk-simas/sparepartsinventoryitem/statistics',
    method: 'get',
    params
  });

// pc端计划详情->选择仓库列表：/szyk-simas/inventory/plan/warehouse
export const getSparePartsCheckWarehouseListApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/inventoryplan/warehouse',
    method: 'get',
    params
  });

// 设备盘点表接口
// 物品盘点列表分页
export const getInventoryThingListApi = (params) =>
  request({
    url: '/api/szyk-simas/equipment/inventory/account-page',
    method: 'get',
    params
  });
// PC 盘点统计
export const getInventoryThingStatisticsApi = (params) =>
  request({
    url: '/api/szyk-simas/equipment/inventory/count',
    method: 'get',
    params
  });

// 在业务中添加备品备件
export const addSparePartsToEquipmentApi = (params) =>
  request({
    url: '/api/szyk-simas/spare-parts/account/select-page',
    method: 'get',
    params
  });

// 备品备件库存接口
export const getSparePartsStockListApi = (params) =>
  request({
    url: '/api/szyk-simas/sparepartsstock/page',
    method: 'get',
    params
  });

// 备品备件盘点工单列表
export const getSparePartsInventoryListApi = (params) =>
  request({
    url: '/api/szyk-simas/sparepartsinventoryorder/page',
    method: 'get',
    params
  });

// 完成盘点工单
export const completeSparePartsInventoryApi = (id) =>
  request({
    url: `/api/szyk-simas/sparepartsinventoryorder/complete/${id}`,
    method: 'put'
  });

// 盘点工单详情
export const getSparePartsInventoryViewApi = (id) =>
  request({
    url: `/api/szyk-simas/sparepartsinventoryorder/detail/${id}`,
    method: 'get'
  });

// 暂存盘点草稿
export const saveSparePartsInventoryApi = (data) =>
  request({
    url: '/api/szyk-simas/sparepartsinventoryitem/draft',
    method: 'put',
    data
  });

// 提交盘点工单
export const submitSparePartsInventoryApi = (id, data) =>
  request({
    url: `/api/szyk-simas/sparepartsinventoryitem/submit/${id}`,
    method: 'put',
    data
  });
