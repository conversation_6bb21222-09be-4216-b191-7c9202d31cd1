<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="SN编号：">{{
        details.sn || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备名称：">{{
        details.name || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="规格型号：">{{
        details.model || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="使用部门：">{{
        details.useDeptName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备类型：">{{
        details.categoryName || '-'
      }}</el-descriptions-item>
      <!-- <el-descriptions-item label="工艺类别：">{{
        details.processCategoryName || '-'
      }}</el-descriptions-item> -->
      <el-descriptions-item label="设备等级：">{{
        details.importantLevelName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="使用人：">{{
        details.userName || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    components: {},
    data() {
      return {};
    },
    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
