<template>
  <div>
    <el-table
      v-loading="listLoading"
      class="table"
      :data="list"
      border
      :header-cell-style="{ background: '#fafafa' }"
      size="small"
      height="450px"
      stripe
    >
      <el-table-column type="index" label="#"></el-table-column>
      <el-table-column prop="deviceCode" label="设备编号" show-overflow-tooltip>
      </el-table-column>

      <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.deviceName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="deviceModel"
        label="规格型号"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.deviceModel || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="deviceStatusName"
        label="设备状态"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.deviceStatusName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="deviceUserName"
        label="盘点人员"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.deviceUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="belongDeptName"
        label="归属部门"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.belongDeptName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="responsibleUserName"
        label="负责人"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{
          row.responsibleUserName || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column prop="useUserName" label="使用人" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="resultName" label="盘点结果" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.resultName || '-' }}</template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.remark || '-' }}</template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: 'DeviceBasicList',
    props: {
      list: {
        type: Array,
        default: () => []
      },
      listLoading: {
        type: Boolean,
        default: () => false
      }
    },
    watch: {},
    data() {
      return {};
    },

    mounted() {},
    methods: {}
  };
</script>

<style scoped lang="scss"></style>
