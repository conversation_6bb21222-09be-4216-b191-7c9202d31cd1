<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
:contentStyle="{ width: '300px', wordBreak: 'break-all', wordWrap: 'break-word' }"
      contentClassName="contentClassName"
      :column="3"
      size="small"
    >
      <el-descriptions-item label="报废单号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="报废名称：">{{
        details.name || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="操作人员：">{{
        details.operateUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="报废日期：">{{
        details.scrapDate || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="备注：">{{
        details.remark || '-'
      }}</el-descriptions-item>
    </el-descriptions>
    <section v-if="details.attachList">
      <span class="el-base-title">审批材料</span>
      <el-image
        v-for="item in details.attachList"
        :key="item.id"
        style="width: 100px; height: 100px"
        :src="getFileFullUrl(item.id)"
        fit="cover"
        :preview-src-list="[getFileFullUrl(item.id)]"
      ></el-image>
    </section>
  </div>
</template>
<script>
  import { getFileFullUrl } from '@/util/file'; //
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return { getFileFullUrl };
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
