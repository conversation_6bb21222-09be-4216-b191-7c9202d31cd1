<template>
  <div style="height: 100%">
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100%)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="sn"
        label="设备编号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.sn || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="categoryName"
        label="设备类型"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.categoryName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.useDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="count"
        label="维修次数"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="completeCount"
        label="完成次数"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.completeCount }}</template>
      </el-table-column>

      <el-table-column
        prop="completeRate"
        label="完成率"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ Number(row.completeRate) === 0 ? 0 : row.completeRate }}%
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getDeviceList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/pagination/index.vue';
  import { getDeviceStatisticsByDevice } from '@/api/equiment-full-life-api/statement-statistics';
  export default {
    name: 'DeviceBasicList',
    components: {
      Pagination
    },
    props: {},
    data() {
      return {
        loading: false,
        searchParams: {
          current: 1,
          size: 10
        },
        total: 0,
        list: []
      };
    },
    mounted() {},

    methods: {
      getData(search) {
        this.searchParams.current = 1;
        this.searchParams = { ...this.searchParams, ...search };
        this.getDeviceList();
      },
      //  按照设备统计
      async getDeviceList() {
        this.loading = true;
        try {
          let res = await getDeviceStatisticsByDevice({
            ...this.searchParams,
            bizModule: 'REPAIR'
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
