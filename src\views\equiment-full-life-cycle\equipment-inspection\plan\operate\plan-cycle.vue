<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="120px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="计划周期" prop="cycleType">
            <el-select
              v-model="form.cycleType"
              placeholder="请选择计划周期"
              @change="changeType"
              clearable
            >
              <el-option
                v-for="dict in serviceDicts.type['plan_cycle']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option> </el-select
          ></el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重复执行间隔" prop="cycleInterval">
            <el-input
              placeholder="请输入重复执行间隔"
              v-model.trim="form.cycleInterval"
              clearable
            >
              <template slot="prepend">每隔</template>
              <template slot="append"
                >{{ returnCycleText(form.cycleType) }}执行一次</template
              >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划名称" prop="name">
            <el-input
              placeholder="请输入计划名称"
              v-model.trim="form.name"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="计划开始时间" prop="startDate">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              placeholder="请选择计划开始时间"
              clearable
              value-format="yyyy-MM-dd"
              :picker-options="startOptions"
              @change="clearEndTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划截止时间" prop="endDate">
            <el-date-picker
              v-model="form.endDate"
              type="date"
              placeholder="请选择计划截止时间"
              clearable
              value-format="yyyy-MM-dd"
              :picker-options="endOptions"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="点巡检部门" prop="executeDeptName">
            <el-input
              style="width: 100%"
              placeholder="请选择点巡检部门"
              v-model.trim="form.executeDeptName"
              @focus.prevent="onSelectDeptClick"
              readonly
            >
              <template slot="append">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      form.executeDept = undefined;
                      form.executeDeptName = undefined;
                      form.executeUser = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="责任人" prop="executeUser">
            <el-select
              v-loading="userLoading"
              v-model="form.executeUser"
              filterable
              placeholder="请选择责任人"
              clearable
            >
              <el-option
                v-for="item in executeUserList"
                :key="item.id"
                :label="item.realName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.cycleType === 'DAY'">
          <el-form-item label="" prop="">
            <template slot="label"
              ><span style="color: red">*</span>工作时间设置</template
            >
            <operate-classes ref="classes" :detail="initData"></operate-classes>
          </el-form-item>
        </el-col>
        <!--        按月-->
        <el-col :span="8" v-if="form.cycleType === 'MONTH'">
          <el-form-item label="工作时间设置" prop="byMonthSet">
            <el-select
              v-model="form.byMonthSet"
              placeholder="请选择工作时间设置"
              multiple
              clearable
            >
              <el-option
                v-for="k in dayOfMonthList"
                :key="k.value"
                :label="k.label"
                :value="k.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!--  按周-->
        <el-col :span="8" v-if="form.cycleType === 'WEEK'">
          <el-form-item label="工作时间设置" prop="byWeekSet">
            <el-select
              v-model="form.byWeekSet"
              placeholder="请选择工作时间设置"
              multiple
              clearable
            >
              <el-option
                v-for="dict in serviceDicts.type['week_date']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import {
    dayOfWeekList,
    dayOfMonthList,
    getIntervalMax,
    returnCycleText,
    monthOfYear,
    getMidnight
  } from '../../util';
  import OperateClasses from './operating-classes.vue';
  import DeptDialog from '@/components/dept-dialog/index.vue';
  import { isLiftNum0 } from '@/util/func';
  import { getUserListByDeptId } from '@/api/equiment-full-life-api/common';
  export default {
    serviceDicts: ['plan_cycle', 'week_date'],
    name: 'AddDeviceInfo',
    components: { DeptDialog, OperateClasses },
    props: {
      initData: {
        type: Object,
        default: () => {}
      }
    },

    data() {
      return {
        monthOfYear,
        getIntervalMax,
        returnCycleText,
        dayOfWeekList,
        dayOfMonthList,
        executeUserList: [], // 负责人
        userLoading: false,
        form: {
          cycleType: undefined,
          cycleInterval: undefined,
          name: undefined,
          startDate: undefined,
          endDate: undefined,
          executeDeptName: undefined,
          executeDept: undefined,
          executeUser: undefined,
          byDaySet: undefined,
          byMonthSet: undefined,
          byWeekSet: undefined
        },
        edit: false,
        startOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now();
          }
        },
        endOptions: {
          disabledDate(time) {
            return time.getTime() <= Date.now();
          }
        },
        rules: {
          cycleType: [
            {
              required: true,
              message: '请选择计划周期',
              trigger: ['change']
            }
          ],
          cycleInterval: [
            {
              required: true,
              message: '请输入重复执行间隔',
              trigger: ['blur']
            },
            {
              validator: (rule, value, callback) => {
                // 如果是null 空字符串
                if (!value) {
                  callback();
                } else if (isLiftNum0(value, 9999)) {
                  callback();
                } else {
                  callback(new Error('请输入大于等于0且小于9999的整数'));
                }
              },
              trigger: 'blur'
            }
          ],
          name: [
            {
              required: true,
              message: '请输入计划名称',
              trigger: ['blur']
            }
          ],
          startDate: [
            {
              required: true,
              message: '请选择计划开始时间',
              trigger: ['change']
            }
          ],
          endDate: [
            {
              required: true,
              message: '请选择计划截止时间',
              trigger: ['change']
            }
          ],
          executeDeptName: [
            {
              required: true,
              message: '请选择点巡检部门',
              trigger: ['change']
            }
          ],

          byMonthSet: [
            {
              required: true,
              message: '请选择选择工作时间设置',
              trigger: 'change'
            }
          ],
          byWeekSet: [
            {
              required: true,
              message: '请选择选择工作时间设置',
              trigger: 'change'
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    created() {},

    methods: {
      // 选择人员
      async getUser(deptId) {
        this.userLoading = true;
        try {
          let params = {
            // alias: 'inspect_user',
            deptId: deptId
          };

          let res = await getUserListByDeptId(params);
          this.executeUserList = res.data.data;

          this.userLoading = false;
        } catch ({ message }) {
          this.userLoading = false;
          console.log(message);
        }
      },
      // change
      clearEndTime(val) {
        if (
          val &&
          this.form.endDate &&
          getMidnight(val) >= getMidnight(this.form.endDate)
        ) {
          this.form.endDate = val;
        } else {
          this.form.endDate = undefined;
        }

        // 更新 picker-options 的 disabledDate 函数
        this.endOptions.disabledDate = this.handleEndDateDisabled;
      },
      handleEndDateDisabled(date) {
        // 结束时间不能小于开始时间
        return (
          getMidnight(date) < getMidnight(this.form.startDate) ||
          getMidnight(date) <= getMidnight(new Date())
        );
      },

      async timeSetting(type) {
        if (type === 'DAY') {
          let res = await this.$refs['classes'].validForm();
          if (res) {
            return { byDaySet: res };
          } else {
            return false;
          }
        } else if (type === 'MONTH') {
          return { byMonthSet: this.form.byMonthSet };
        } else if (type === 'WEEK') {
          return { byWeekSet: this.form.byWeekSet };
        }
      },

      // 选择部门
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.executeDept = dept.id;
        this.form.executeDeptName = dept.deptName;
        this.form.executeUser = undefined;
        this.getUser(dept.id);
      },
      setData(initData) {
        if (initData.executeDept) {
          this.getUser(initData.executeDept);
        }
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },

      select(data) {
        this.form.name = data.name;
        this.form.id = data.id;
      },
      //  切换类型的时候,清空执行日期
      changeType() {
        this.form.cycleInterval = undefined;
        this.form.byDaySet = undefined;
        this.form.byWeekSet = undefined;
        this.form.byMonthSet = undefined;
      },

      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          let timeSetting = await this.timeSetting(this.form.cycleType);
          if (timeSetting) {
            return {
              ...this.form,
              ...timeSetting
            };
          } else {
            return false;
          }
        } else {
          return false;
        }
      },
      resetForm() {
        this.executeUserList = [];
        this.$refs['baseForm'].resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-input-number {
        width: 100%;

        .el-input__inner {
          text-align: left;
        }
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
