<template>
  <el-form
    class="_form"
    ref="form"
    :model="form"
    size="small"
    :inline="true"
    label-suffix="："
  >
    <el-form-item label="计划名称或编号" prop="keywords">
      <el-input
        v-model.trim="form.keywords"
        placeholder="请输入计划名称或编号"
      ></el-input>
    </el-form-item>
    <el-form-item label="时间选择" prop="time">
      <el-date-picker
        v-model="form.time"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        clearable
      >
      </el-date-picker>
    </el-form-item>
    <el-form-item>
      <el-button icon="el-icon-search" type="primary" @click="submit"
        >搜索</el-button
      >
      <el-button icon="el-icon-delete" @click="reset">清空</el-button>
      <el-button
        icon="el-icon-upload2"
        type="success"
        size="small"
        @click="exportExcel"
        >导出</el-button
      >
    </el-form-item>
  </el-form>
</template>

<script>
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';

  export default {
    name: 'alarmSearch',
    components: {},
    data() {
      return {
        form: {
          time: [],
          keywords: undefined
        }
      };
    },
    created() {},
    methods: {
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.form).length === 0) {
          path = `/api/szyk-simas/statistics/export-maintain-plan?`;
        } else {
          let p = {
            ...this.form,
            queryStartDate: this.form.time ? this.form.time[0] : undefined,
            queryEndDate: this.form.time ? this.form.time[1] : undefined
          };
          delete p.time;
          for (const key in p) {
            if (p[key]) {
              params += `${key}=${p[key]}&`;
            }
          }
          path = `/api/szyk-simas/statistics/export-maintain-plan?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '查询结果列表.xlsx'
        );
      },
      reset() {
        this.$refs['form'].resetFields();
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          queryStartDate: this.form.time ? this.form.time[0] : undefined,
          queryEndDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep {
    .el-form {
      margin-bottom: 15px;
    }
  }
</style>
