<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
    >
      <el-form-item label="快速查找" prop="categoryName">
        <el-input
          v-model.trim="form.categoryName"
          maxlength="50"
          placeholder="请输入类型名称"
          clearable
        ></el-input>
      </el-form-item>

      <el-button
        size="small"
        icon="el-icon-search"
        type="primary"
        @click="submit"
        >搜索</el-button
      >
      <el-button size="small" icon="el-icon-delete" @click="reset"
        >清空</el-button
      >
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    data() {
      return {
        form: {
          categoryName: undefined
        }
      };
    },
    created() {},
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.$emit('reset', this.form);
        this.submit();
      },
      submit() {
        let params = {
          ...this.form
        };
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped>
  :deep {
    .el-range-editor {
      width: 250px;
    }

    .el-range-separator {
      padding: 0 15px;
    }
  }
</style>
