<template>
  <div>
    <el-table
      class="table"
      :data="list"
      border
      :header-cell-style="{ background: '#fafafa' }"
      size="small"
      height="500px"
      stripe
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>
      <el-table-column prop="code" label="设备编号" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="name" label="设备名称" show-overflow-tooltip>
      </el-table-column>

      <el-table-column
        prop="categoryName"
        label="设备类型"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.categoryName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="locationPath"
        label="存放地点"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.locationPath || '-' }}</template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: 'DeviceBasicList',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {};
    },

    mounted() {},
    methods: {}
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }
  .operateBtn {
    margin-bottom: 15px;
  }
  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
