<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      label-width="90px"
      :inline="true"
      ref="search"
      :model="form"
      size="small"
    >
      <el-form-item label="SN编号" prop="sn">
        <el-input
          style="width: 100%"
          v-model.trim="form.sn"
          placeholder="请输入SN编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="设备名称" prop="name">
        <el-input
          style="width: 100%"
          v-model.trim="form.name"
          placeholder="请输入设备名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item label="设备等级" prop="importantLevel">
        <el-select
          v-model="form.importantLevel"
          placeholder="请选择设备等级"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['important_level']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择设备状态" clearable>
          <el-option
            v-for="item in serviceDicts.type['equipment_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    serviceDicts: ['important_level', 'equipment_status'],
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        form: {
          code: undefined,
          name: undefined,
          importantLevel: undefined,
          status: undefined
        }
      };
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
