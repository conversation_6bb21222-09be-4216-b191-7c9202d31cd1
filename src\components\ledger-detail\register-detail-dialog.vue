<template>
  <el-dialog
    title="查看"
    :visible.sync="visible"
    @closed="resetForm"
    top="5vh"
    width="740px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="details">
      <el-form
        ref="form"
        :model="form"
        label-width="120px"
        label-suffix="："
        size="small"
        v-loading="loading"
      >
        <!-- 基本信息 -->
        <section>
          <p class="el-base-title">检验基本信息</p>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="设备编号" prop="equipmentCode">
                <div class="content-wrapper">
                  {{ form.equipmentCode }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备名称" prop="equipmentName">
                <div class="content-wrapper">
                  {{ form.equipmentName }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检验类型" prop="inspectTypeName">
                <div class="content-wrapper">
                  {{ form.inspectTypeName }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检验机构" prop="inspectOrg">
                <div class="content-wrapper">
                  {{ form.inspectOrg }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检验日期" prop="inspectDate">
                <div class="content-wrapper">
                  {{ form.inspectDate }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="下次检验日期" prop="nextInspectDate">
                <div class="content-wrapper">
                  {{ form.nextInspectDate }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检验人员" prop="inspectUser">
                <div class="content-wrapper">
                  {{ form.inspectUser }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="安全管理员" prop="securityAdmin">
                <div class="content-wrapper">
                  {{ form.securityAdmin }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新人" prop="updateUserName">
                <div class="content-wrapper">
                  {{ form.updateUserName }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新时间" prop="updateTime">
                <div class="content-wrapper">
                  {{ form.updateTime }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <!-- 基本信息 -->
        <section>
          <p class="el-base-title">检验结论</p>
          <el-form-item label="检验结论" prop="inspectResult">
            <div class="content-wrapper">
              {{ form.inspectResultName }}
            </div>
          </el-form-item>
          <el-form-item label="主要问题" prop="importantIssues">
            <el-input
              :value="form.importantIssues || '-'"
              placeholder="请输入主要问题"
              type="textarea"
              :rows="3"
              maxlength="1000"
              readonly
              resize="none"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="处理措施" prop="measures">
            <el-input
              :value="form.measures || '-'"
              placeholder="请输入处理措施"
              type="textarea"
              :rows="3"
              maxlength="1000"
              readonly
              resize="none"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              :value="form.remark || '-'"
              placeholder="请输入备注"
              type="textarea"
              :rows="3"
              maxlength="1000"
              readonly
              resize="none"
            >
            </el-input>
          </el-form-item>
        </section>
        <div class="attach-wrapper">
          <div class="attach-title">
            <div class="attach-title-text">附件上传</div>
            <div class="attach-title-comment">
              (检验报告、整改报告及验证资料、其他相关证明材料等)
            </div>
          </div>
          <show-file
            v-if="form.attachList && form.attachList.length"
            :list="form.attachList"
          />
          <div v-else>暂无</div>
        </div>
      </el-form>
    </div>

    <div slot="footer" class="oper_btn">
      <btn type="close" @click="closed"></btn>
    </div>
  </el-dialog>
</template>

<script>
  import { ShowFile } from '@/components/yk-upload-file';
  import { getInspectLogDetail } from '@/api/equiment-full-life-api/special-device';

  export default {
    name: 'SpecialEquipmentUseRegisterDetailk',
    components: { ShowFile },
    data() {
      return {
        visible: false,
        loading: false,
        // 使用明细
        form: {}
      };
    },
    methods: {
      show(row) {
        console.log('row', JSON.parse(JSON.stringify(row)));
        this.visible = true;
        row.id && this.getDetail(row.id);
      },
      async getDetail(id) {
        try {
          this.loading = true;
          const res = await getInspectLogDetail({ id });
          this.form = res.data.data;
          // 处理附件名
          this.form.attachList = this.form.attachList.map((item) => ({
            ...item,
            name: item.originalName
          }));
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      resetForm() {
        this.visible = false;
        this.loading = false;
        this.$refs.form.resetFields();
      },
      closed() {
        this.visible = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  .details {
    padding-bottom: 30px;
  }

  ::v-deep {
    .el-form-item {
      margin-bottom: 8px;
    }

    .el-radio input[aria-hidden='true'] {
      display: none !important;
    }

    .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled)
      .el-radio__inner {
      box-shadow: none !important;
    }
  }

  .attach-wrapper {
    padding-left: 10px;

    .attach-title {
      display: flex;
      margin-bottom: 12px;
      font-size: 14px;

      &-text {
        color: #3e4553;
        font-weight: bold;
      }

      &-comment {
        margin-left: 12px;
        color: #a4a8b5;
      }
    }
  }
</style>
