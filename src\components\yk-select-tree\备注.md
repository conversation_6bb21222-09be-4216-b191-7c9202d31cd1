1、 导入

import { getLazyList } from '@/api/system/dept';
import { InputTree } from '@/components/yk-select-tree';
components: { InputTree },

2、 使用
说明：formLabel formValue 为绑定form中对应的字段， 如下选中部门后选中的值的key value 分别赋值给form.useDeptName form.useDept
     <el-form-item label="使用部门" prop="useDeptName">
        <InputTree
          v-model="form.useDept"
          lazy
          clearable
          :form="form"
          :dic="deptData"
          style="width: 190px"
          :props="{
            label: 'deptName',
            value: 'id',
            isLeaf: (row) => !row.hasChildren,
            formLabel: 'useDeptName',
            formValue: 'useDept'
          }"
          :load="lazyLoad"
          :lazyLoading="lazyLoading"
          @search="lazySearch"
        ></InputTree>
      </el-form-item>

data() {
  return {
    deptData: [],
    lazyLoading: false,
    form: {
        useDeptName: undefined,
        useDept: undefined
      }
  }
}

3、 methods接口请求

      async lazySearch(title) {
        this.lazyLoad(null, null, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node || {};
        const { id } = data || {};
        let params = {
          deptName: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyList(title ? '' : id || 0, params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          if (title) {
            this.deptData = arr;
          } else {
            resolve && resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
4、注意重置的时候要手动清空

   reset() {
      this.form.useDept = undefined; // 清空选中的部门id
      this.$refs['search'].resetFields();
      this.submit();
    },