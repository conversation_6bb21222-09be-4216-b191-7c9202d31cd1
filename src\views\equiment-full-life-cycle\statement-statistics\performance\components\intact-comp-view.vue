<template>
  <div class="comp-wrapper">
    <!-- 搜索 -->
    <div class="search-wrapper">
      <el-form
        label-suffix="："
        :inline="true"
        ref="search"
        :model="searchParams"
        size="small"
      >
        <el-form-item label="统计部门" prop="useDeptId">
          <InputTree
            v-model="searchParams.useDeptId"
            lazy
            clearable
            :form="searchParams"
            :dic="deptData"
            style="width: 100%"
            :props="treeProps"
            :load="lazyLoad"
            :lazyLoading="lazyLoading"
            @search="lazySearch"
          ></InputTree>
        </el-form-item>
        <el-form-item label="设备等级" prop="importantLevel">
          <el-select
            v-model="searchParams.importantLevel"
            placeholder="请选择设备等级"
            clearable
          >
            <el-option
              v-for="item in serviceDicts.type['important_level']"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="categoryName">
          <el-input
            placeholder="请选择设备类型"
            type="text"
            v-model="searchParams.categoryName"
            readonly
            @focus.prevent="selectAssetCategory"
          >
            <template slot="append">
              <i class="el-icon-circle-close" @click="clearCategory"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <btn type="search" @click="query" />
          <btn type="reset" @click="reset" />
        </el-form-item>
      </el-form>
    </div>
    <!-- 总结 -->
    <div class="summary-wrapper">
      <div class="left">
        <!--        <i class="el-icon-d-arrow-left" @click="back">返回 &nbsp;</i>-->
        <div class="font-bold">设备状态统计</div>
        <div class="num">完好率: {{ sumVal }}</div>
      </div>
      <section>
        <el-button
          icon="el-icon-arrow-left"
          type="primary"
          plain
          size="small"
          @click="back"
          >返回</el-button
        >
        <!-- <el-button
          icon="el-icon-upload2"
          type="primary"
          size="small"
          @click="exportExcel"
          >导出</el-button
        > -->
      </section>
    </div>
    <!-- 表格 -->
    <el-table
      v-loading="loading"
      ref="table"
      class="table"
      :data="listData"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="model"
        label="设备型号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.model || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="locationPath"
        label="设备位置"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.locationPath || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="设备状态"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${equipmentStatusColor(row.status)};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!--     选择设备类型-->
    <device-type-dialog
      ref="device-type"
      @success="getSingleRow"
    ></device-type-dialog>
  </div>
</template>

<script>
  import Pagination from '@/components/pagination';
  import { getLazyList } from '@/api/system/dept';
  import { InputTree } from '@/components/yk-select-tree';
  import { getToken } from '@/util/auth';
  import { downloadFileBlob } from '@/util/util';
  import { selectScrapDevice } from '@/api/equiment-full-life-api/ledger';
  import { getIntactVal } from '@/api/equiment-full-life-api/statement-statistics.js';
  import DeviceTypeDialog from '@/views/equiment-full-life-cycle/components/select-asset-category-dialog/single-index.vue';

  export default {
    serviceDicts: ['important_level'],
    components: { DeviceTypeDialog, Pagination, InputTree },
    data() {
      return {
        loading: false,
        treeProps: {
          label: 'deptName',
          value: 'id',
          isLeaf: (row) => !row.hasChildren,
          formLabel: 'useDeptName',
          formValue: 'useDeptId'
        },
        deptData: [],
        lazyLoading: false,
        searchParams: {
          status: '3',
          useDeptName: undefined,
          useDept: undefined,
          importantLevel: undefined,
          categoryId: undefined,
          categoryName: undefined,
          current: 1,
          size: 10
        },
        sumVal: undefined,
        // 列表
        listData: [],
        total: 0
      };
    },
    created() {
      this.refresh();
    },
    methods: {
      // 设备台账 设备状态状态颜色
      equipmentStatusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#35C24B'; // 在用
          case 1:
            return '#155CFF'; // 备用
          case 3:
            return '#EE7C11'; // 维修
          case 4:
            return '#E23F3F'; // 报废
        }
      },
      async lazySearch(title) {
        this.lazyLoad(null, null, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node || {};
        const { id } = data || {};
        let params = {
          deptName: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyList(title ? '' : id || 0, params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          if (title) {
            this.deptData = arr;
          } else {
            resolve && resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      // 返回设备完好率首页
      back() {
        this.$emit('compView', { comp: 'Intact' });
      },
      selectAssetCategory() {
        this.$refs['device-type'].show();
      },

      //  选择类型
      getSingleRow(row) {
        this.searchParams.categoryId = row.id;
        this.searchParams.categoryName = row.categoryName;
      },
      query() {
        this.searchParams.current = 1;
        this.getList();
        this.getSumVal();
      },
      reset() {
        this.clearCategory();
        this.$refs['search'].resetFields();
        this.query();
      },
      //  清空类型
      clearCategory() {
        this.searchParams.categoryId = undefined;
        this.searchParams.categoryName = undefined;
      },
      refresh() {
        this.getSumVal();
        this.getList();
      },
      // 获取统计数据
      async getSumVal() {
        try {
          const { data } = await getIntactVal({
            deptId: this.searchParams.useDept
          });
          this.sumVal = data.data;
        } catch (e) {
          console.error(e);
        }
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await selectScrapDevice(this.searchParams);
          this.listData = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 导出
      exportExcel() {
        let path =
          '/api/szyk-simas/statistical-report/export-health-percentage?';
        if (this.searchParams.useDept) {
          path += `deptId=${this.searchParams.useDept}&`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '设备完好率统计.xlsx'
        );
      }
    }
  };
</script>

<style lang="scss" scoped>
  .comp-wrapper {
    height: 100%;

    .search-wrapper {
      ::v-deep .el-form-item {
        margin-bottom: 10px;
      }
    }

    .summary-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      .left {
        display: flex;
        align-items: center;

        .font-bold {
          font-weight: bold;
        }

        .num {
          margin-left: 12px;
          color: #409eff;
        }
      }
    }
  }

  .el-icon-d-arrow-left {
    padding: 0 8px;
    color: #409eff;
    cursor: pointer;
  }
</style>
