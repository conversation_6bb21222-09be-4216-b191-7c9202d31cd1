export const returnLabel = (list, value) => {
  return list.find((item) => item.value === value).label;
};
export const dayOfWeekList = [
  {
    value: 'MON',
    label: '星期一'
  },
  {
    value: 'TUE',
    label: '星期二'
  },
  {
    value: 'WED',
    label: '星期三'
  },
  {
    value: 'THU',
    label: '星期四'
  },
  {
    value: 'FRI',
    label: '星期五'
  },
  {
    value: 'SAT',
    label: '星期六'
  },
  {
    value: 'SUN',
    label: '星期日'
  }
];
export const hourOfDayList = [
  { value: 0, label: '0时' },
  { value: 1, label: '1时' },
  { value: 2, label: '2时' },
  { value: 3, label: '3时' },
  { value: 4, label: '4时' },
  { value: 5, label: '5时' },
  { value: 6, label: '6时' },
  { value: 7, label: '7时' },
  { value: 8, label: '8时' },
  { value: 9, label: '9时' },
  { value: 10, label: '10时' },
  { value: 11, label: '11时' },
  { value: 12, label: '12时' },
  { value: 13, label: '13时' },
  { value: 14, label: '14时' },
  { value: 15, label: '15时' },
  { value: 16, label: '16时' },
  { value: 17, label: '17时' },
  { value: 18, label: '18时' },
  { value: 19, label: '19时' },
  { value: 20, label: '20时' },
  { value: 21, label: '21时' },
  { value: 22, label: '22时' },
  { value: 23, label: '23时' }
];
export const dayOfMonthList = [
  { value: '1' },
  { value: '2' },
  { value: '3' },
  { value: '4' },
  { value: '5' },
  { value: '6' },
  { value: '7' },
  { value: '8' },
  { value: '9' },
  { value: '10' },
  { value: '11' },
  { value: '12' },
  { value: '13' },
  { value: '14' },
  { value: '15' },
  { value: '16' },
  { value: '17' },
  { value: '18' },
  { value: '19' },
  { value: '20' },
  { value: '21' },
  { value: '22' },
  { value: '23' },
  { value: '24' },
  { value: '25' },
  { value: '26' },
  { value: '27' },
  { value: '28' },
  { value: '29' },
  { value: '30' },
  { value: '31' }
];
export const monthOfYear = [
  { value: 1, label: '一月' },
  { value: 2, label: '二月' },
  { value: 3, label: '三月' },
  { value: 4, label: '四月' },
  { value: 5, label: '五月' },
  { value: 6, label: '六月' },
  { value: 7, label: '七月' },
  { value: 8, label: '八月' },
  { value: 9, label: '九月' },
  { value: 10, label: '十月' },
  { value: 11, label: '十一月' },
  { value: 12, label: '十二月' }
];
// 返回0- 100的数组
export const getArray = (num) => {
  let arr = [];
  for (let i = 0; i < num; i++) {
    arr.push(i);
  }
  return arr;
};

//启用警用
export const enableList = (status) => {
  if (Number(status) === 1) {
    return '停用';
  } else {
    return '启用';
  }
};
export const statusList = [
  { label: '启用', value: 0 },
  { label: '停用', value: 1 }
];
export const isTimeNonOverlapping = (timeRanges) => {
  console.log('timeRanges....', timeRanges);
  timeRanges.sort(
    (a, b) =>
      new Date(`2000-01-01T${a.time[0]}`).getTime() -
      new Date(`2000-01-01T${b.time[0]}`).getTime()
  );

  for (let i = 0; i < timeRanges.length - 1; i++) {
    const currentEnd = new Date(`2000-01-01T${timeRanges[i].time[1]}`);
    const nextStart = new Date(`2000-01-01T${timeRanges[i + 1].time[0]}`);

    // 如果当前时间段的结束时间大于或等于下一个时间段的开始时间，则存在交叉
    if (currentEnd >= nextStart) {
      console.log(
        `时间段 ${timeRanges[i].time} 和 ${timeRanges[i + 1].time} 存在交叉.`
      );
      return true; // 发现交叉，直接返回true
    }
  }

  console.log('所有时间段之间无交叉.');
  return false; // 遍历完无交叉，返回false
};

export const returnTips = (type) => {
  switch (type) {
    case 'DAY':
      return '提示：以上重复模式包含周末，如果该模式不符合业务要求，可制定周计划。';
    case 'WEEK':
      return '提示：如果以上重复模式不符合业务要求，可制定月计划。';
    case 'MONTH':
      return '提示：可以设置执行间隔，满足季度计划、年度计划执行要求。';
    default:
      return '提示：根据选择的周期及执行间隔提示相应的内容。';
  }
};
export const returnCycleTips = (type, val) => {
  if (val) {
    switch (type) {
      case 'DAY':
        return `每${val}天执行一次`;
      case 'WEEK':
        return `每${val}周执行一次`;
      case 'MONTH':
        return `每${val}月执行一次`;
      default:
        return '';
    }
  }
};
export const returnCycleText = (type) => {
  switch (type) {
    case 'DAY':
      return `天`;
    case 'WEEK':
      return `周`;
    case 'MONTH':
      return `月`;
    default:
      return '天';
  }
};
export const getIntervalMax = (type) => {
  switch (type) {
    case 'DAY':
      return 999;
    case 'WEEK':
      return 999;
    case 'MONTH':
      return 999;
    default:
      return 999;
  }
};
export const getMidnight = (dateStr) => {
  // 将日期字符串转换为午夜的日期对象
  const date = new Date(dateStr);
  return new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate()
  ).getTime();
};

function timeToMinutes(time) {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
}
export function validateTimeRanges(timeRanges) {
  // 从开始时间判断时间跨度是否大于24小时
  // 用每个的endTime - 首个startTime
  // let totalMinutes = 0;
  let rangeStartMinutes = 0;
  let previousEndMinutes = null;
  let adjustedDay = 0;

  for (let i = 0; i < timeRanges.length; i++) {
    console.log('序号' + i);
    const current = timeRanges[i];

    if (!current.startTime || !current.endTime) {
      console.error(`索引 ${i} 的时间范围无效，因为开始或结束时间未定义。`);
      return false;
    }
    if (i === 0) {
      rangeStartMinutes = timeToMinutes(current.startTime);
    }

    let startMinutes = timeToMinutes(current.startTime) + adjustedDay;
    let endMinutes = timeToMinutes(current.endTime) + adjustedDay;
    // 如果当前的开始时间小于上个结束时间也表示跨天
    if (startMinutes < previousEndMinutes) {
      console.log('与上个时间跨天');
      startMinutes += 1440;
      endMinutes += 1440;
      adjustedDay += 1440;
    }
    // 如果当前的开始时间小于前一个结束时间，则表示跨天，需要调整时间。
    if (endMinutes < startMinutes) {
      console.log('当前时间跨天');
      // startMinutes += 1440;
      endMinutes += 1440;
      adjustedDay += 1440;
    }

    previousEndMinutes = endMinutes;
    // totalMinutes += endMinutes - startMinutes;
    if (endMinutes - rangeStartMinutes > 1440) {
      console.error(`总时间超过24小时。`);
      return false;
    }
  }

  console.log('所有时间范围均有效。');
  return true;
}

// 点检计划的状态 ，返回各种状态的颜色
export function inspectPlanStatusColor(val) {
  let status = Number(val);
  switch (status) {
    case 0:
      return '#F2F2F6'; // 未开始
    case 1:
      return '#155CFF'; //执行中
    case 2:
      return '#00BB7D'; // 已完成
    case 3:
      return '#EC9A29'; //已终止
  }
}

// 点巡检工单 状态颜色 返回
export function inspectOrderStatusColor(val) {
  let status = Number(val);
  switch (status) {
    case 1:
      return '#155CFF'; //执行中"
    case 2:
      return '#35C24B'; // 已完成
    case 3:
      return '#E23F3F'; //已超期
    case 4:
      return '#EE7C11'; //超期完成"
    case 5:
      return '#EC9A29'; //待验证""
    case 6:
      return '#E23F3F'; //已驳回"
    case 7:
      return '#808080'; //已关闭
  }
}
