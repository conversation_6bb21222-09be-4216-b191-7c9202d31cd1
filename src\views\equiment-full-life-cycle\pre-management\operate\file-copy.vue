<template>
  <div class="top-info">
    <upload-file
      accept=".jpg, .png, .jpeg, .xlsx, .pdf, .docx, .md"
      v-model="attachList"
      url="/api/szyk-system/attach/put-file-attach-for-simas"
      :showFile="false"
      @input="handleSuccess"
      ref="file"
      :limit="100"
    ></upload-file>
    <el-form :model="form" inline label-suffix="：" ref="listForm" size="small">
      <el-table
        class="table"
        :data="form.list"
        row-key="id"
        size="small"
        border
        ref="table"
        :header-cell-style="{ background: '#fafafa' }"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="资料名称"
          align="center"
        ></el-table-column>
        <el-table-column prop="type" label="归类" align="center" width="200px">
          <template slot-scope="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.type'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="[
                {
                  required: true,
                  message: '请选择资料归类',
                  trigger: 'change'
                }
              ]"
            >
              <el-select
                size="small"
                v-model="scope.row.type"
                style="width: 150px"
                placeholder="资料类型"
                clearable
                @change="changeProfileType(scope)"
              >
                <el-option
                  v-for="item in cateGoryList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="fileCategoryId"
          label="资料类型"
          align="center"
          width="200px"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.fileCategoryId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="[
                {
                  required: true,
                  message: '请选择资料类型',
                  trigger: 'change'
                }
              ]"
            >
              <!--              <el-select-->
              <!--                size="small"-->
              <!--                v-model="scope.row.fileCategoryId"-->
              <!--                style="width: 150px"-->
              <!--                placeholder="资料类型"-->
              <!--                clearable-->
              <!--                v-loading="selLoading"-->
              <!--              >-->
              <!--                <el-option-->
              <!--                  v-for="item in scope.row.profileTypeList"-->
              <!--                  :key="item.value"-->
              <!--                  :label="item.title"-->
              <!--                  :value="item.value"-->
              <!--                >-->
              <!--                </el-option>-->
              <!--              </el-select>-->

              <el-cascader
                placeholder="资料类型"
                v-model="scope.row.fileCategoryId"
                :options="scope.row.profileTypeList"
                :show-all-levels="false"
                :props="{
                  checkStrictly: true,
                  label: 'title',
                  value: 'value',
                  expandTrigger: 'hover'
                }"
                clearable
              ></el-cascader>
            </el-form-item>
          </template>
        </el-table-column>
        <!--        <el-table-column-->
        <!--          prop="createTime"-->
        <!--          label="添加时间"-->
        <!--          align="center"-->
        <!--          show-overflow-tooltip-->
        <!--        ></el-table-column>-->
        <!--        <el-table-column-->
        <!--          prop="createUser"-->
        <!--          label="添加人"-->
        <!--          align="center"-->
        <!--          show-overflow-tooltip-->
        <!--        ></el-table-column>-->
        <el-table-column
          prop="extension"
          label="文件类型"
          align="center"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template slot-scope="scope">
            <!--            <el-button-->
            <!--              icon="el-icon-view"-->
            <!--              type="text"-->
            <!--              size="small"-->
            <!--              @click="download(scope.row)"-->
            <!--              >下载</el-button-->
            <!--            >-->

            <el-popconfirm
              title="确定删除吗？"
              @confirm="() => handleDelete(scope)"
            >
              <el-button
                icon="el-icon-delete"
                slot="reference"
                type="text"
                size="small"
                style="margin-left: 10px"
                >删除</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
  import UploadFile from '@/components/upload-file.vue';
  import { getChildrenList } from '@/api/equiment-full-life-api/profile';

  export default {
    name: 'DeviceBasicList',
    components: { UploadFile },
    props: {
      initData: {
        type: Object,
        default: () => {
          return {};
        }
      },
      //  资料归类类型列表
      cateGoryList: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },

    data() {
      return {
        form: {
          list: []
        },
        list: [],
        attachList: [],
        selLoading: false
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.setData(this.initData.attachInfoList);
            });
          }
        }
      }
    },

    mounted() {},

    methods: {
      //  切换资料归类，获取资料类型
      changeProfileType(scope) {
        console.log('切换资料类型......', scope, scope.row.type);
        this.$set(scope.row, 'fileCategoryId', undefined);
        this.getChildrenList(scope.row);
      },
      //  根据归类获取子类型数据
      async getChildrenList(row) {
        this.selLoading = true;
        try {
          const res = await getChildrenList({ parentId: row.type });
          let list = res.data.data || [];
          this.$set(row, 'profileTypeList', list);
          this.selLoading = false;
          return list;
        } catch (e) {
          this.selLoading = false;
          console.log(e);
        }
      },
      // download(row) {
      //   console.log(row);
      //   // data: { id: row.id, domain: row.domain }
      //   this.$refs['file'].handleDownLoad({
      //     data: { id: row.id, originalName: row.originalName }
      //   });
      // },
      setData(attachInfoList) {
        this.attachList = [];
        console.log('编辑返回的数据...............', attachInfoList);
        if (attachInfoList) {
          let data = attachInfoList.map(async (i) => {
            let a = await this.getChildrenList(i).then((res) => {
              let item = {
                ...i.attach,
                originalName: i.name,
                name: i.name,
                fileCategoryId: i.fileCategoryId, // 资料类型id,
                extension: this.getString(i.name), // 文件后缀类型
                type: i.type,
                profileTypeList: res // 归类下的子资料类型
              };
              this.attachList.push(i.attach);
              return item;
            });
            return a;
          });
          Promise.all(data).then((res) => {
            this.form.list = res;
          });
          console.log(this.attachList);
        } else {
          this.form.list = [];
        }
      },
      //  上传成功
      handleSuccess(file) {
        console.log('file。。。。', file);
        let data = file.map((i) => {
          return {
            data: {
              ...i
            },
            id: i.id,
            name: i.originalName, // 资料名称
            type: undefined, // 归类类型
            fileCategoryId: undefined, // 资料类型id,
            extension: this.getString(i.originalName), // 文件后缀类型
            profileTypeList: [] // 归类下的子资料类型
          };
        });
        this.form.list = data; // [...this.list, ...data];
      },

      getString(str) {
        const lastDotIndex = str.lastIndexOf('.');
        let result;
        if (lastDotIndex !== -1) {
          // 截取点后面的部分
          result = str.slice(lastDotIndex + 1);
        } else {
          // 如果没有找到点，则返回原字符串
          result = str;
        }
        return result;
      },
      async validForm() {
        let list;
        if (this.form.list.length > 0) {
          let valid = await this.$refs['listForm'].validate();
          if (valid) {
            list = this.form.list.map((i) => {
              const { name, type, createTime, createUser, extension } = i;
              return {
                ...{
                  name,
                  type,
                  createTime,
                  createUser,
                  extension
                },
                attachId: i.id,
                fileCategoryId: i.fileCategoryId.join(',')
              };
            });
            return list;
          }
        } else {
          list = [];
        }
        return list;
      },
      resetForm() {
        this.attachList = [];
        this.form.list = [];
      },
      async handleDelete(scope) {
        this.form.list.splice(scope.$index, 1);
        this.$refs['file'].handleRemove(scope.row, this.form.list);
      }
    }
  };
</script>

<style scoped lang="scss"></style>
