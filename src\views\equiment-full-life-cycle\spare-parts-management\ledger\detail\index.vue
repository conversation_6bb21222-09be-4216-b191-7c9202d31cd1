<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @closed="close"
    class="detail-drawer"
    width="80%"
  >
    <div class="details" v-loading="loading">
      <el-tabs tab-position="top" style="height: 100%">
        <el-tab-pane label="设备信息">
          <!-- 基本信息 -->
          <section>
            <span class="el-base-title">基础信息</span>
            <base-info :details="details"></base-info>
          </section>
          <section>
            <span class="el-base-title">文件资料</span>
            <file-name :details="details"></file-name>
          </section>
          <section v-if="details.imageList">
            <span class="el-base-title">设备图片</span>
            <el-image
              style="width: 100px; height: 100px"
              :src="convertFileUrl(details.imageList[0].domain)"
              fit="cover"
              :preview-src-list="[convertFileUrl(details.imageList[0].domain)]"
            ></el-image>
          </section>
        </el-tab-pane>
        <el-tab-pane label="关联工单">
          <order-list
            v-if="visible"
            ref="orderList"
            :equipmentId="id"
          ></order-list>
        </el-tab-pane>
        <!--        <el-tab-pane label="设备履历">设备履历</el-tab-pane>-->
      </el-tabs>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from './base-info.vue';
  import FileName from './file.vue';
  import { convertFileUrl } from '@/util/util';
  import { getAccountDetail } from '@/api/equiment-full-life-api/ledger';
  import OrderList from './order-list.vue';
  export default {
    name: 'RepairViewIndex',
    components: { BaseInfo, FileName, OrderList },
    data() {
      return {
        convertFileUrl,
        id: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: { id: undefined } // 详情数据
      };
    },

    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getAccountDetail({ id: id });
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      // 点击展示
      async show(id) {
        this.visible = true;
        if (id) {
          this.id = id;
          await this.getDetail(id);
        }
      },

      // 关闭弹窗
      close() {
        this.$refs['orderList'].resetForm();
        this.visible = false;
        this.detail = { id: undefined };
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

</style>
