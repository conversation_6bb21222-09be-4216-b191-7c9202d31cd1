<template>
  <dialog-popup
    title="选择设备类型"
    ref="dialogs"
    :visible="visible"
    @closed="closed"
    width="80%"
    class="selectSensor"
  >
    <search ref="search" @search="onSubmit"></search>
    <el-row>
      <el-row>
        <el-col :span="16" style="height: 460px; overflow-y: scroll">
          <el-table
            ref="multipleTable"
            v-loading="loading"
            :data="dataSource"
            size="small"
            border
            :key="key"
            row-key="id"
            :headerCellStyle="{ background: '#fafafa' }"
            :reserve-selection="true"
            @select="handleCheckBox"
            @select-all="handleSelectAll"
          >
            <el-table-column type="selection" width="55" align="center">
            </el-table-column>
            <el-table-column
              property="categoryName"
              label="设备类型名称"
              align="center"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              property="pathName"
              label="路径"
              align="center"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              property="remark"
              label="类型编码"
              align="center"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">{{ row.remark || '-' }}</template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="8" style="height: 460px; overflow-y: scroll">
          <el-tag
            v-for="tag in allPageSelect"
            :closable="true"
            :key="tag.id"
            :disable-transitions="false"
            @close="handleClose(tag)"
            style="margin-bottom: 5px; margin-left: 10px"
          >
            {{ tag.categoryName }}
          </el-tag>
          <el-empty
            :image-size="100"
            v-if="allPageSelect.length === 0"
            description="暂无选择数据"
          ></el-empty>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" style="height: 100%">
          <pagination
            :page-size.sync="searchParams.size"
            :page-no.sync="searchParams.current"
            :total="total"
            @pagination="getList"
          />
        </el-col>
        <el-col :span="12" style="height: 100%">
          <div
            style="
              display: flex;
              align-items: flex-end;
              justify-content: flex-end;
            "
          >
            <btn type="confirm" @click="confirm" :loading="loading"></btn>
            <btn type="cancel" @click="closed"></btn>
          </div>
        </el-col>
      </el-row>
    </el-row>
  </dialog-popup>
</template>
<script>
  import { getDeviceTypePageList } from '@/api/equiment-full-life-api/device-type';
  import Search from './search.vue';

  export default {
    components: { Search },
    props: {},
    data() {
      return {
        visible: false,
        loading: false,
        dataSource: [],
        total: 0,
        searchParams: {
          size: 10,
          current: 1
        },
        key: 0,
        // 所有页上多选的数据之和
        allPageSelect: [],
        originLength: 0 // 已存在的长度
      };
    },
    watch: {},
    methods: {
      onSubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      getKey() {
        return Date.now() + '';
      },
      handleCheckBox(rows, row) {
        if (rows.includes(row)) {
          // 新勾选
          this.$set(row, 'num', 1);
          this.$set(row, 'disabled', false);
          this.allPageSelect.push(row);
        } else {
          // 取消勾选
          this.$set(row, 'disabled', true);
          this.$set(row, 'num', undefined);
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item.id !== row.id
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        if (rows.length) {
          rows.forEach((row) => {
            this.$set(row, 'num', 1);
            this.$set(row, 'disabled', false);
            if (!this.allPageSelect.find((item) => item.id === row.id)) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.dataSource.forEach((row) => {
            this.$set(row, 'num', undefined);
            this.$set(row, 'disabled', true);
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item.id !== row.id
            );
          });
        }
      },

      handleClose(row) {
        this.$set(row, 'num', undefined);
        this.$set(row, 'disabled', true);
        this.$refs.multipleTable.toggleRowSelection(row);
        let index = this.allPageSelect.findIndex((it) => it.id === row.id);
        index !== -1 && this.allPageSelect.splice(index, 1);
      },
      search() {
        this.searchParams.current = 1;
        this.getList();
      },
      show(originList) {
        console.log(' selected: []\n', originList);
        this.resetFrom();
        this.allPageSelect = originList;
        this.visible = true;
        this.searchParams.current = 1;
        this.getList(); // 部位列表
        this.key++;
      },

      resetFrom() {
        this.searchParams = {
          size: 10,
          current: 1
        };
        this.allPageSelect = [];
      },
      closed() {
        this.allPageSelect = [];
        this.visible = false;
      },
      // 点击clearable时触发
      clear() {
        this.searchParams.current = 1;
        this.getList();
      },
      //
      async getList() {
        this.loading = true;
        try {
          let params;
          params = {
            ...this.searchParams
          };
          let res = await getDeviceTypePageList(params);
          let data = res.data.data.records || [];
          this.dataSource = data.map((item) => {
            const mathingObj = this.allPageSelect.find((m) => item.id === m.id);
            if (mathingObj) {
              return mathingObj;
            } else {
              return item;
            }
          });
          this.$nextTick(() => {
            this.dataSource.map((item) => {
              if (item.num) {
                this.$refs.multipleTable.toggleRowSelection(item, true);
              }
            });
            this.total = res.data.data.total;
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      confirm() {
        if (this.allPageSelect.length === 0) {
          this.$message.warning('请选择设备类型！');
          return;
        }

        this.$emit('success', this.allPageSelect);
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .el-pagination__sizes {
    display: none !important;
  }

  .search {
    margin-bottom: 10px;
  }

  /deep/ .pagination-container {
    text-align: left !important;
  }

  .clear {
    float: right;
  }

  /deep/ .el-pagination__jump {
    display: none !important;
  }

  .right {
    margin-left: 20px;
  }

  /deep/ {
    .el-form-item--small {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      padding: 10px 0;
    }
  }

  .table {
    :deep(.el-table__cell) {
      padding: 8px 0 !important;
    }
  }

  .selectSensor {
    :deep(.el-dialog__body) {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  ::v-deep {
    .el-input-number--mini {
      width: 100px !important;
    }
  }
</style>
