<template>
  <div class="table-content">
    <div class="top-info">
      <el-button icon="el-icon-plus" type="primary" size="small" @click="add"
        >添加标准</el-button
      >
      <el-button
        icon="el-icon-delete"
        type="danger"
        size="small"
        @click="delAll"
        >删除所选</el-button
      >
    </div>
    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
      hide-required-asterisk
    >
      <el-table
        ref="point-table"
        class="table table-vertical-top"
        :data="form.monitorList"
        style="width: 100%"
        size="mini"
        border
        stripe
        height="calc(100vh - 450px)"
        :header-cell-style="{ background: '#fafafa' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="monitorId" align="center">
          <div slot="header" class="required">检修部位</div>
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.monitorId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="{
                required: true,
                message: '请选择检修部位',
                trigger: 'change'
              }"
              label=" "
              :class="{
                'ai-logo': scope.row.nameAiFlag
              }"
            >
              <el-select
                v-model="scope.row.monitorId"
                placeholder="请选择检修部位"
                clearable
                filterable
                allow-create
                @change="(val) => handleMonitorChange(val, scope.row)"
              >
                <el-option
                  v-for="dict in monitorList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                  :disabled="isItemSelected(dict.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="monitorType">
          <div slot="header" class="required">部位类型</div>
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.monitorType'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="{
                required: true,
                message: '请选择部位类型',
                trigger: 'change'
              }"
              label=" "
            >
              <el-select
                v-model="scope.row.monitorType"
                placeholder="请选择部位类型"
                clearable
              >
                <el-option
                  v-for="dict in serviceDicts.type['monitor_type']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="standard" align="center">
          <div slot="header" class="required">检修标准</div>
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.standard'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="{
                required: true,
                message: '请输入检修标准',
                trigger: 'change'
              }"
              label=" "
              :class="{
                'ai-logo': scope.row.standardAiFlag
              }"
            >
              <el-input
                v-model.trim="scope.row.standard"
                placeholder="请输入检修标准"
                clearable
                type="textarea"
                :rows="3"
                maxlength="50"
                @change="scope.row.standardAiFlag = false"
              >
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="methodsId" align="center">
          <div slot="header" class="required">检修方式</div>
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.methodsId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="{
                required: true,
                message: '请选择检修方式',
                trigger: 'change'
              }"
            >
              <el-select
                v-model="scope.row.methodsId"
                placeholder="请选择检修方式"
                clearable
              >
                <el-option
                  v-for="dict in wayList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column prop="code" label="操作" align="center" width="70px">
          <template v-slot="scope">
            <el-button type="text" size="small" @click="del(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>
<script>
  import { getPartList } from '@/api/equiment-full-life-api/maintenance';

  export default {
    name: 'PointList',
    components: {},
    serviceDicts: ['monitor_type'],
    props: {
      details: {
        type: Object,
        default: () => {
          return {
            overhaulStandardList: []
          };
        }
      },
      //  部位列表
      // monitorList: {
      //   type: Array,
      //   default: () => {
      //     return [];
      //   }
      // },
      //  检修方式列表
      wayList: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    watch: {
      'details.equipmentId': {
        immediate: true,
        handler(val) {
          if (val) {
            this.$nextTick(async () => {
              await this.getMonitorList(val);
              let list = this.details.overhaulStandardList || [];
              if (list.length > 0) {
                // 如果已经添加了标准
                await this.setList(list);
              } else {
                // 没有添加标准
                await this.setList(this.monitorList);
              }
            });
          }
        }
      }
    },
    data() {
      return {
        num: 0,
        monitorList: [], // 部位列表
        form: {
          monitorList: []
        },
        currentIdx: undefined, // 当前的要匹配的索引
        // 某个部位已选择的传感器列表
        initSelectList: [],
        multipleSelection: [],
        repeatCode: [] // 接口返回的 重复的编码
      };
    },
    methods: {
      //  获取所有的部位
      async getMonitorList(id) {
        try {
          this.loading = true;
          const res = await getPartList({ equipmentId: id });
          let data = res.data.data || [];
          this.monitorList = data.map((item) => {
            return {
              name: item.name,
              id: item.id,
              type: item.type,
              monitorName: item.name,
              monitorId: item.id,
              monitorType: item.type
            };
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 选择部位回调
      handleMonitorChange(val, row, auto) {
        !auto && (row.nameAiFlag = false);
        let monitor = this.monitorList.find((item) => {
          return item.id === val;
        });
        if (monitor) {
          row.monitorName = monitor.name;
          this.$set(row, 'monitorType', monitor.type);
        } else {
          row.monitorId = val;
          row.monitorName = val;
        }
      },
      //  部位选择的直接禁用 不能继续选择
      isItemSelected(id) {
        return this.form.monitorList.some((item) => item.monitorId === id);
      },
      async delAll() {
        if (this.multipleSelection.length === 0) {
          this.$message.warning('请先选择要删除的标准');
          return;
        }
        await this.confirm('是否确认删除？');
        const filteredArray = this.form.monitorList.filter(
          (item) =>
            !this.multipleSelection.some(
              (deleteItem) => deleteItem.num === item.num
            )
        );

        this.form.monitorList = [...filteredArray];
      },

      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      del(scope) {
        console.log(scope.$index);
        this.form.monitorList.splice(scope.$index, 1);
      },
      // 初始化设置列表（编辑时）
      setList(list) {
        const listNum = list.map((it) => {
          return {
            ...it,
            num: this.num++
          };
        });
        this.form.monitorList = listNum;
        listNum.forEach((item) => {
          let val = item.monitorId || item.monitorName;
          this.handleMonitorChange(val, item, true);
        });
      },

      // 点击添加部位 在列表新增一行列表
      add() {
        this.num++;
        //1.0选择设备 1.1输入设备；
        let listData = [
          {
            num: this.num,
            monitorId: undefined,
            methodsId: undefined,
            standard: undefined
          }
        ];
        // 点击新增的时候，如果部位超过20个（最多20个），那么就新增，如果超过20个
        if (this.form.monitorList.length >= 50) {
          this.$message.warning('最多能增加50个标准');
          return;
        }
        this.form.monitorList = [...this.form.monitorList, ...listData];
      },

      async validForm() {
        if (this.form.monitorList.length === 0) {
          // this.$message.warning('请添加标准');
          return [];
        }

        let valid = await this.$refs['listForm'].validate();
        if (valid) {
          console.log('this.form.monitorList', this.form.monitorList);
          let params = this.form.monitorList.map((i) => {
            return {
              monitorId: i.monitorId,
              monitorName: i.monitorName,
              monitorType: i.monitorType,
              standard: i.standard,
              methodsId: i.methodsId
            };
          });
          // 自定义部位删除id
          return params.map((item) => {
            const temp = { ...item };
            if (temp.monitorId === temp.monitorName) {
              delete temp.monitorId;
            }
            return temp;
          });
        } else {
          return false;
        }
      },

      resetForm() {
        this.num = 0;
        this.form.monitorList = [];
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    margin-bottom: 10px;
  }

  /deep/ {
    .el-table__cell {
      padding: 0;

      .el-form-item__content {
        width: calc(100% - 12px);
        padding-right: 24px;

        .el-select,
        .el-input {
          width: 100%;
        }
      }
    }

    .el-input__inner {
      padding-right: 10px !important;
    }

    .el-form-item--small {
      margin-top: 16px;
    }

    .el-table .warning-row {
      background: #d3dcecff !important;
    }

    .el-table__header {
      line-height: 50px !important;
    }
  }

  thead {
    .required {
      &::after {
        color: #f56c6c;
        content: ' *';
      }
    }
  }
</style>
