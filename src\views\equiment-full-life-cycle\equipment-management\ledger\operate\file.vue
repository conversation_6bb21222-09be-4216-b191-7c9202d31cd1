<template>
  <div class="top-info">
    <upload-file
      accept=".jpg, .png, .jpeg, .xlsx, .pdf, .docx, .md"
      v-model="attachList"
      url="/api/szyk-system/attach/put-file-attach-for-simas"
      :showFile="false"
      @input="handleSuccess"
      ref="file"
      :limit="100"
    ></upload-file>
    <el-form :model="form" inline label-suffix="：" ref="listForm" size="small">
      <el-table
        class="table"
        :data="form.list"
        row-key="id"
        size="small"
        border
        ref="table"
        :header-cell-style="{ background: '#fafafa' }"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="originalName"
          label="文件名称"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="category"
          label="资料类型"
          align="center"
          show-overflow-tooltip
          width="200px"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.attachType'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="[
                {
                  required: true,
                  message: '请选择资料类型',
                  trigger: 'change'
                }
              ]"
            >
              <el-select
                size="small"
                v-model="scope.row.attachType"
                style="width: 150px"
                placeholder="资料类型"
                clearable
              >
                <el-option
                  v-for="item in serviceDicts.type['attach_type']"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="type"
          label="文件类别"
          align="center"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="num"
          label="数量"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-view"
              type="text"
              size="small"
              @click="download(scope.row)"
              >下载</el-button
            >

            <el-popconfirm
              title="确定删除吗？"
              @confirm="() => handleDelete(scope)"
            >
              <el-button
                icon="el-icon-delete"
                slot="reference"
                type="text"
                size="small"
                style="margin-left: 10px"
                >删除</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script>
  import UploadFile from '@/components/upload-file.vue';
  export default {
    serviceDicts: ['attach_type'],
    name: 'DeviceBasicList',
    components: { UploadFile },
    props: {
      initData: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        form: {
          list: []
        },
        list: [],
        attachList: []
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData.attachInfoList);
          }
        }
      }
    },
    mounted() {},

    methods: {
      download(row) {
        console.log(row);
        // data: { id: row.id, domain: row.domain }
        this.$refs['file'].handleDownLoad({
          data: { id: row.id, originalName: row.originalName }
        });
      },
      setData(attachInfoList) {
        if (attachInfoList) {
          // this.attachList = attachInfoList.attach;
          let data = attachInfoList.map((i) => {
            let item = {
              ...i.attach,
              num: 1,
              type: this.getString(i.attach.originalName),
              attachType: i.attachType
            };
            this.attachList.push(i.attach);
            return item;
          });
          // this.list = [...this.list, ...data];
          this.form.list = data;
        } else {
          this.form.list = [];
        }
      },
      //  上传成功
      handleSuccess(file) {
        console.log('file。。。。', file);
        let data = file.map((i) => {
          return {
            data: {
              ...i
            },
            id: i.id,
            originalName: i.originalName,
            num: 1,
            type: this.getString(i.originalName),
            attachType: undefined
          };
        });
        this.form.list = data; // [...this.list, ...data];
      },
      getString(str) {
        const lastDotIndex = str.lastIndexOf('.');
        let result;
        if (lastDotIndex !== -1) {
          // 截取点后面的部分
          result = str.slice(lastDotIndex + 1);
        } else {
          // 如果没有找到点，则返回原字符串
          result = str;
        }
        return result;
      },
      async validForm() {
        let list;
        if (this.form.list.length > 0) {
          let valid = await this.$refs['listForm'].validate();
          if (valid) {
            list = this.form.list.map((i) => {
              return {
                attachId: i.id,
                attachType: i.attachType
              };
            });
          }
        } else {
          list = [];
        }
        return list;
      },
      resetForm() {
        this.attachList = [];
        this.form.list = [];
      },
      async handleDelete(scope) {
        this.form.list.splice(scope.$index, 1);
        this.$refs['file'].handleRemove(scope.row, this.form.list);
      }
    }
  };
</script>

<style scoped lang="scss"></style>
