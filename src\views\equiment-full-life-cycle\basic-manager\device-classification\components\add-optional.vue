<template>
  <Dialog
    v-if="visible"
    :visible="visible"
    :title="title"
    width="1100px"
    @closed="hide"
  >
    <el-form
      ref="form"
      :rules="rules"
      :model="form"
      size="small"
      label-width="200px"
      label-position="right"
      label-suffix="："
    >
      <el-table
        :data="form.memberList"
        border
        stripe
        :header-cell-style="{ backgroundColor: '#fafafa' }"
        style="width: 100%"
      >
        <el-table-column label="序号" type="index" align="center" width="50">
        </el-table-column>
        <el-table-column prop="label" align="center">
          <template slot="header">
            <span style="color: #f56c6c">*</span>
            待选值名称
          </template>
          <template slot-scope="scope">
            <el-form-item
              label-width="0"
              :prop="'memberList.' + scope.$index + '.label'"
              :rules="rules.label"
            >
              <avue-input
                :maxlength="20"
                v-if="isEdit && !editSelected.includes(scope.row.label)"
                v-model="scope.row.label"
                placeholder="请输入"
              ></avue-input>
              <template v-else>{{ scope.row.label || '--' }}</template>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column width="120" prop="isDefault" align="center">
          <template slot="header"> 是否默认值 </template>
          <template slot-scope="scope">
            <el-form-item
              label-width="0"
              :prop="'memberList.' + scope.$index + '.isDefault'"
              :rules="rules.isDefault"
            >
              <el-switch
                v-if="isEdit && !editSelected.includes(scope.row.label)"
                @change="handleSwitch(scope.$index)"
                v-model="scope.row.isDefault"
                active-color="#13ce66"
                inactive-value="0"
                active-value="1"
              >
              </el-switch>
              <template v-else>{{
                scope.row.isDefault === '1' ? '是' : '否'
              }}</template>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" v-if="isEdit" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              :disabled="
                form.memberList.length == 1 ||
                editSelected.includes(scope.row.label)
              "
              type="text"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-button
        v-if="isEdit"
        size="small"
        class="dynamic-add-btn"
        @click="handleAdd"
        >新 增</el-button
      >
    </el-form>
    <div slot="footer">
      <el-button
        type="primary"
        class="el-icon-circle-plus-outline"
        size="small"
        v-if="isEdit"
        @click="onSubmit"
      >
        保存
      </el-button>
      <el-button
        type="primary"
        class="el-icon-circle-close"
        size="small"
        plain
        @click="hide"
      >
        取消</el-button
      >
    </div>
  </Dialog>
</template>
<script>
  import Dialog from '@/components/basic-dialog';
  import { deepClone } from '@/util/util';
  const form = {
    memberList: []
  };
  export default {
    components: { Dialog },
    props: {
      indicatorList: { type: Array, require: () => [] },
      isEdit: { type: Boolean, default: true },
      id: { type: String, default: '' }
    },
    data() {
      return {
        visible: false,
        loading: false,
        title: '待选值',
        form: { ...form },
        rules: {
          label: [{ required: true, message: '请输入' }]
        },
        // ------------
        currentIndex: undefined, // 当前编辑行的行号
        editSelected: [] // 用户被选中值
      };
    },
    methods: {
      handleSwitch(index) {
        let arr = this.form.memberList;
        let isDefault = arr[index].isDefault;
        arr.forEach((item) => {
          item.isDefault = '0';
        });
        arr[index].isDefault = isDefault === '1' ? '1' : '0';
      },
      async handleDelete(index) {
        this.form.memberList.splice(index, 1);
      },
      handleAdd() {
        // 默认值
        let row = {
          label: '',
          isDefault: '0'
        };
        let arr = this.form.memberList;
        arr.push(row);
      },
      validForm(flag) {
        let bool = false;
        this.$refs[flag].validate((valid) => {
          bool = valid;
        });
        // 唯一名称校验
        let arr = this.form.memberList;
        const obj = {};
        let uniqueName = arr.every((item) => {
          return obj[item.label] ? false : (obj[item.label] = true);
        });
        if (!uniqueName) {
          this.$message.warning('待选值名称不能重复');
        }
        return bool && uniqueName;
      },
      // 提交
      onSubmit() {
        let bool = this.validForm('form');
        if (!bool) return;
        this.$message.success('保存成功');

        let arr = [...this.form.memberList];
        this.$emit('save-success', arr, this.currentIndex);
        this.hide();
      },
      async show(row, index) {
        this.visible = true;
        this.currentIndex = index;
        let obj = deepClone(row);
        this.editSelected = obj.selectList || [];
        this.setForm(obj);
      },
      setForm(row) {
        console.log('row', row);
        let historyList = row.selectData || [];
        if (!historyList.length) {
          return this.handleAdd();
        }
        this.form.memberList = row.selectData || [];
      },
      resetPage() {
        this.form.memberList = [];
        form.memberList = [];
      },
      reset() {
        this.resetPage();
        this.queryText();
      },
      hide() {
        this.resetPage();
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .dynamic-add-btn {
    width: 100%;
    border: 1px dashed #dcdfe6;
  }

  .query-form {
    /deep/.el-form-item--mini.el-form-item {
      display: flex;
    }

    /deep/.el-form-item--mini .el-form-item__content {
      flex: 1;
    }
  }
</style>
