<template>
  <div class="maintenance-standards-item">
    <el-form
      :model="standardItem"
      label-suffix="："
      :ref="`baseForm`"
      label-width="120px"
      :label-position="'right'"
      size="small"
    >
      <div class="standard-box">
        <span class="el-base-title"
          >{{ standardItem.serialNumber }}、{{
            standardItem.monitorName || '-'
          }}</span
        >
        <el-row>
          <el-col :span="8">
            <el-form-item label="检修标准">
              {{ standardItem.standard || '-' }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检修方式">
              {{ standardItem.methodsName || '-' }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="检修结果"
              :prop="`isAbnormal`"
              :rules="[
                { required: true, message: '请选择', trigger: 'change' }
              ]"
            >
              <el-select
                v-model="standardItem.isAbnormal"
                filterable
                style="width: 90%"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in resultsList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="standardItem.isAbnormal === 1">
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="故障缺陷名称"
                :prop="`faultName`"
                :rules="[
                  { required: true, message: '请输入', trigger: 'blur' }
                ]"
              >
                <el-input
                  placeholder="请输入"
                  style="width: 90%"
                  v-model.trim="standardItem.faultName"
                  maxlength="20"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障缺陷类型"
                :prop="`faultType`"
                :rules="[
                  { required: true, message: '请选择', trigger: 'change' }
                ]"
              >
                <el-select
                  v-model="standardItem.faultType"
                  filterable
                  style="width: 90%"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="dict in serviceDicts.type['repair_type']"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="standardItem.faultType === '1'">
              <el-form-item
                label="故障缺陷等级"
                :prop="`faultLevel`"
                :rules="[
                  { required: true, message: '请选择', trigger: 'change' }
                ]"
              >
                <el-select
                  v-model="standardItem.faultLevel"
                  filterable
                  style="width: 90%"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="dict in serviceDicts.type['defect_level']"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="异常描述"
                :prop="`faultRemark`"
                :rules="[
                  { required: true, message: '请输入', trigger: 'blur' }
                ]"
              >
                <el-input
                  placeholder="请输入 故障缺陷描述"
                  style="width: 97%"
                  type="textarea"
                  v-model.trim="standardItem.faultRemark"
                  maxlength="500"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="是否现场处理"
                :prop="`isReport`"
                :rules="[
                  { required: true, message: '请选择', trigger: 'change' }
                ]"
              >
                <el-select
                  v-model="standardItem.isReport"
                  filterable
                  style="width: 90%"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="dict in onSiteList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <template v-if="standardItem.isReport === 0">
              <el-col :span="8">
                <el-form-item
                  label="维修结果"
                  :prop="`result`"
                  :rules="[
                    { required: true, message: '请选择', trigger: 'change' }
                  ]"
                >
                  <el-select
                    v-model="standardItem.result"
                    filterable
                    style="width: 90%"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="dict in repairResultsList"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="结果确认方式"
                  :prop="`checkMethod`"
                  :rules="[
                    { required: true, message: '请输入', trigger: 'blur' }
                  ]"
                >
                  <el-input
                    placeholder="请输入"
                    style="width: 90%"
                    v-model.trim="standardItem.checkMethod"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="维修时长"
                  :prop="`duration`"
                  :rules="[
                    { required: true, message: '请输入', trigger: 'blur' }
                  ]"
                >
                  <el-input-number
                    v-model.trim="standardItem.duration"
                    style="width: 80%"
                    :step="0.5"
                    :min="0.5"
                    :max="1000"
                  >
                  </el-input-number>
                  <span style="padding-left: 10px; color: #606266">小时</span>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item
                  label="故障缺陷原因"
                  :prop="`faultReason`"
                  :rules="[
                    { required: true, message: '请输入', trigger: 'blur' }
                  ]"
                >
                  <el-input
                    placeholder="请输入"
                    style="width: 95.5%"
                    v-model.trim="standardItem.faultReason"
                    maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="解决方案"
                  :prop="`solution`"
                  :rules="[
                    { required: true, message: '请输入', trigger: 'blur' }
                  ]"
                >
                  <el-input
                    placeholder="请输入 解决方案"
                    style="width: 97%"
                    type="textarea"
                    v-model.trim="standardItem.solution"
                    maxlength="500"
                    clearable
                  />
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="24">
              <el-form-item label="更换部件">
                <SparePart
                  :standardItem="standardItem"
                  ref="sparePart"
                ></SparePart>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="上传图片"
                :prop="`attachList`"
                :rules="[
                  { required: true, message: '请选择', trigger: 'change' }
                ]"
              >
                <upload-img
                  v-model="standardItem.attachList"
                  placeholder="上传图片"
                  :limit="3"
                  :clearValid="true"
                  @input="handleFileChange"
                  formatLimit="jpeg,png,jpg"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </div>
    </el-form>
  </div>
</template>

<script>
  import UploadImg from '@/components/uploadImage.vue';
  import SparePart from './spare-part.vue';

  export default {
    components: { UploadImg, SparePart },
    serviceDicts: ['repair_type', 'defect_level'],
    props: {
      standardIndex: {
        type: Number,
        default: 0
      },
      standardItem: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        standardList: [],
        resultsList: [
          { label: '正常', value: 0 },
          { label: '异常', value: 1 }
        ],
        onSiteList: [
          { label: '现场已处理', value: 0 },
          { label: '现场未处理，需上报', value: 1 }
        ],
        repairResultsList: [
          { label: '已修复', value: 1 },
          { label: '未完全修复，可运行', value: 2 }
        ]
      };
    },
    mounted() {},
    methods: {
      handleFileChange(val) {
        this.standardItem.attachList = val;
        this.$refs.baseForm.validateField('attachList');
      },
      async validForm() {
        try {
          this.$refs.sparePart && this.$refs.sparePart.validForm();
          let valid = await this.$refs['baseForm'].validate();
          if (valid) {
            return true;
          } else {
            return false;
          }
        } catch (error) {
          return false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .maintenance-standards-wrap {
    padding: 0 20px;

    .el-base-title {
      color: #409eff;
    }
  }
</style>
