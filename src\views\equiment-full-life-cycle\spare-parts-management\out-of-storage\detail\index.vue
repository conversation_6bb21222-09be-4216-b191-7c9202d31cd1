<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @close="close"
    class="detail-drawer"
    size="80%"
  >
    <div class="details" v-loading="loading" v-if="visible">
      <!-- 基本信息 -->
      <section>
        <p class="el-base-title">基本信息</p>
        <base-info :details="details"></base-info>
      </section>

      <p class="el-base-title">备件列表</p>
      <sel-asset ref="asset" :detail="details" :isShow="false"></sel-asset>
    </div>
  </dialog-drawer>
</template>

<script>
  import SelAsset from '../operate/sel-asset.vue';
  import BaseInfo from './base-info.vue';
  import { getSparePartsOutViewApi } from '@/api/equiment-full-life-api/spare-parts';
  export default {
    name: 'RepairViewIndex',
    components: { BaseInfo, SelAsset },
    data() {
      return {
        id: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {} // 详情数据
      };
    },

    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getSparePartsOutViewApi(id);
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 点击展示
      async show(id) {
        this.visible = true;
        if (id) {
          await this.getDetail(id);
        }
      },

      // 关闭弹窗
      close() {
        this.visible = false;
        this.detail = {};
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
