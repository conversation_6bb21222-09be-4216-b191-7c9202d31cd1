<template>
  <basic-container class="table-content" :auto-height="true">
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="no"
        label="资料编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="资料名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.attachList[0].originalName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="typeName"
        label="归类"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="fileCategoryName"
        label="资料类型"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="equipmentCategory"
        label="适用设备类型"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentCategory || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="创建人员"
        align="center"
        show-overflow-tooltip
        width="90px"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column label="操作" align="center" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="preview(scope.row)"
            >下载</el-button
          >
          <el-button type="text" size="small" @click="detail(scope.row)"
            >查看</el-button
          >
          <el-button type="text" size="small" @click="operate(scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            title="确定删除吗？"
            @confirm="() => handleDelete(scope.row)"
          >
            <el-button
              slot="reference"
              type="text"
              size="small"
              style="margin-left: 10px"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex"></detail-index>
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import { convertFileUrl } from '@/util/util';
  import { getFileFullUrl } from '@/util/file';

  import {
    getPageList,
    userDeleteLogic
  } from '@/api/equiment-full-life-api/profile';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination
    },
    props: {},
    data() {
      return {
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      preview(row) {
        console.log('row......', row);
        let url = getFileFullUrl(row.attachList[0].id);
        window.open(url, '_self');
        // let type = row.attachList[0].extension;
        // if (type === 'pdf') {
        //   let url = convertFileUrl(row.attachList[0].domain);
        //   window.open(url);
        // } else {
        //   this.$message.warning(`该${type}文件暂不支持预览`);
        // }
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getPageList({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        let id = row ? row.id : undefined;
        this.$refs.add.show(id);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      },
      async handleDelete(row) {
        try {
          await userDeleteLogic({ ids: row.id });
          //  删除的时候，判断当前列表，是不是length 是1 ，如果是1，将current置成1
          if (this.list.length === 1) {
            this.searchParams.current = 1;
          }
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          this.setPage();
          await this.getList(true);
        } catch (e) {
          this.$message.warning(e.data.msg);
        }
      },
      setPage() {
        let rowsLength = (this.allPageSelect || []).length;
        let length = this.list.length;
        if (rowsLength && rowsLength >= length) {
          this.searchParams.current = 1;
        } else {
          if (length === 1) {
            this.searchParams.current = 1;
          }
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
