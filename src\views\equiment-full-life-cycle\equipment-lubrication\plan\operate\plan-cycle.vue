<template>
  <div>
    <el-form
      :model="form"
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="120px"
      label-position="right"
      size="small"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="计划名称" prop="name">
            <el-input
              placeholder="请输入计划名称"
              v-model.trim="form.name"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划开始时间" prop="startTime">
            <el-date-picker
              v-model="form.startTime"
              type="date"
              style="width: 100%"
              placeholder="请选择计划开始时间"
              clearable
              value-format="yyyy-MM-dd"
              :picker-options="startOptions"
              @change="clearEndTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划截止时间" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              type="date"
              style="width: 100%"
              placeholder="请选择计划截止时间"
              clearable
              value-format="yyyy-MM-dd"
              :picker-options="endOptions"
              @change="endTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="负责部门" prop="chargeDeptName">
            <el-input
              style="width: 100%"
              placeholder="请选择负责部门"
              v-model.trim="form.chargeDeptName"
              @focus.prevent="onSelectDeptClick"
              readonly
            >
              <template slot="append">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      form.chargeDept = undefined;
                      form.chargeDeptName = undefined;
                      form.chargeUser = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="责任人" prop="chargeUser">
            <el-select
              v-loading="userLoading"
              v-model="form.chargeUser"
              filterable
              style="width: 100%"
              placeholder="请选择责任人"
              clearable
            >
              <el-option
                v-for="item in executeUserList"
                :key="item.id"
                :label="item.realName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import { getMidnight } from '../../../equipment-inspection/util';
  import DeptDialog from '@/components/dept-dialog/index.vue';
  import { getUserListByDeptId } from '@/api/equiment-full-life-api/common';
  import { mapGetters } from 'vuex';
  export default {
    name: 'AddDeviceInfo',
    components: { DeptDialog },
    props: {
      initData: {
        type: Object,
        default: () => {}
      }
    },

    data() {
      return {
        executeUserList: [], // 责任人
        userLoading: false,
        form: {
          name: undefined,
          startTime: undefined,
          endTime: undefined,
          chargeDeptName: undefined,
          chargeDept: undefined,
          chargeUser: undefined
        },
        edit: false,
        startOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now();
            // return false;
          }
        },
        endOptions: {
          disabledDate(time) {
            return time.getTime() <= Date.now();
          }
        },
        rules: {
          name: [
            {
              required: true,
              message: '请输入计划名称',
              trigger: ['blur']
            }
          ],
          startTime: [
            {
              required: true,
              message: '请选择计划开始时间',
              trigger: ['change']
            }
          ],
          endTime: [
            {
              required: true,
              message: '请选择计划截止时间',
              trigger: ['change']
            }
          ],
          chargeDeptName: [
            {
              required: true,
              message: '请选择润滑部门',
              trigger: ['change']
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    created() {},
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods: {
      // 选择人员
      async getUser(deptId) {
        this.userLoading = true;
        try {
          let params = {
            // alias: 'lubricate_user',
            deptId: deptId
          };

          let res = await getUserListByDeptId(params);
          this.executeUserList = res.data.data;

          this.userLoading = false;
        } catch ({ message }) {
          this.userLoading = false;
          console.log(message);
        }
      },
      // change
      clearEndTime(val) {
        if (
          val &&
          this.form.endTime &&
          getMidnight(val) >= getMidnight(this.form.endTime)
        ) {
          this.form.endTime = val;
        } else {
          this.form.endTime = undefined;
        }

        // 更新 picker-options 的 disabledDate 函数
        this.endOptions.disabledDate = this.handleEndDateDisabled;
        this.$emit('changeTime', {
          startTime: val,
          endTime: this.form.endTime
        });
      },
      //  结束时间改变
      endTime(val) {
        this.$emit('changeTime', {
          startTime: this.form.startTime,
          endTime: val
        });
      },
      handleEndDateDisabled(date) {
        // 结束时间不能小于开始时间
        return (
          getMidnight(date) < getMidnight(this.form.startTime) ||
          getMidnight(date) <= getMidnight(new Date())
        );
      },

      // 选择部门
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.chargeDept = dept.id;
        this.form.chargeDeptName = dept.deptName;
        this.form.chargeUser = undefined;
        this.getUser(dept.id);
      },
      setData(initData) {
        if (initData.chargeDept) {
          this.getUser(initData.chargeDept);
        }
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
        this.$emit('changeTime', {
          startTime: this.form.startTime,
          endTime: this.form.endTime
        });
      },

      select(data) {
        this.form.name = data.name;
        this.form.id = data.id;
      },

      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return this.form;
        } else {
          return false;
        }
      },
      resetForm() {
        this.executeUserList = [];
        this.$refs['baseForm'].resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped></style>
