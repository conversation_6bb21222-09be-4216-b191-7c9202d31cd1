// 传感器电量数字转类名 颜色
import { powerColors } from '@/util/color';

export const power2Obj = (power) => {
  power = Number(power);
  const obj = {};
  if (power >= 85) {
    obj.color = powerColors[0];
    obj.colorClass = 'power-0';
  } else if (power > 30) {
    obj.color = powerColors[1];
    obj.colorClass = 'power-1';
  } else {
    obj.color = powerColors[2];
    obj.colorClass = 'power-2';
  }
  return obj;
};

export const onlinePercent2Obj = (percent) => {
  percent = Number(percent);
  const obj = {};
  if (percent === 100) {
    obj.color = powerColors[0];
  } else if (percent <= 50) {
    obj.color = powerColors[2];
  } else {
    obj.color = powerColors[1];
  }
  return obj;
};
