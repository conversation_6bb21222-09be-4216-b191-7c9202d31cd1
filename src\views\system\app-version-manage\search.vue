<template>
  <div>
    <el-form
      ref="form"
      :model="form"
      :inline="true"
      label-suffix="："
      size="small"
    >
      <el-form-item label="迭代内容" prop="content">
        <el-input v-model="form.content" placeholder="请输入迭代内容">
        </el-input>
      </el-form-item>

      <el-form-item>
        <btn type="search" @click="query"></btn>
        <btn type="reset" @click="reset"></btn>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'app-version-search',
    data() {
      return {
        form: {
          content: undefined
        }
      };
    },
    methods: {
      query() {
        this.$emit('search', this.form);
      },
      reset() {
        this.$refs.form.resetFields();
        this.query();
      }
    }
  };
</script>

<style scoped></style>
