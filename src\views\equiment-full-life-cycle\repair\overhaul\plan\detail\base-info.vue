<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '140px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="计划名称：">{{
        details.name || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="计划编号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="负责部门：">{{
        details.executeDeptName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="责任人：">{{
        details.executeUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="计划周期：">{{
        details.cycleTypeName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="重复执行间隔：">{{
        details.cycleInterval
      }}</el-descriptions-item>

      <el-descriptions-item label="计划开始时间：">{{
        details.startDate || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="计划截止时间：">{{
        details.endDate || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="执行浮动时间：">{{
        details.floatDate
      }}</el-descriptions-item>
      <el-descriptions-item label="工作时间段设置：">{{
        getField()
      }}</el-descriptions-item>
      <el-descriptions-item v-if="details.status === 1" label="驳回原因：">
        {{ details.rejectReason || '-' }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {
      getField() {
        const type = this.details.cycleType;
        if (type === 'DAY') {
          let arr = this.details['byDaySet'];
          let str = arr
            .map((item) => {
              return item.startTime + '~' + item.endTime;
            })
            .join('  ； ');
          return str;
        } else {
          return this.details['executeTimeStr'];
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
