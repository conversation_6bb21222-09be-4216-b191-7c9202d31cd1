<template>
  <div class="table_wrapper">
    <el-table
      :data="list"
      v-loading="loading"
      @selection-change="handleSelectionChange"
      style="width: 100%"
      size="small"
    >
      <!-- <el-table-column align="center" type="selection" width="55">
      </el-table-column> -->
      <el-table-column
        align="center"
        type="index"
        label="序号"
      ></el-table-column>
      <el-table-column align="center" prop="num" label="订单号" width="180">
      </el-table-column>
      <el-table-column align="center" prop="price" label="价格">
      </el-table-column>
      <el-table-column align="center" prop="payment" label="支付方式">
        <template slot-scope="{ row }">
          {{ row.paymentType | payment }}</template
        >
      </el-table-column>
      <el-table-column align="center" prop="orderTime" label="订单日期">
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="250">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="$emit('dispatch', 'view', scope.row)"
            >查看
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="$emit('dispatch', 'edit', scope.row)"
            >编辑
          </el-button>
          <el-button
            type="text"
            icon="el-icon-delete"
            size="small"
            @click="rowDel(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { cloneDeep } from 'lodash';
  import { postDel } from '@/api/demo/order';
  import { payType } from '@/constant/common';

  export default {
    name: 'OrderTableInfo',
    props: {
      loading: {
        type: Boolean,
        default: false
      },
      source: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    filters: {
      payment(type) {
        switch (type) {
          case payType.bank:
            return '银行卡';
          case payType.zfb:
            return '支付宝';
          case payType.wx:
            return '微信';
          case payType.other:
            return '其他';
          default:
            return '--';
        }
      }
    },
    watch: {
      source: {
        handler(arr) {
          this.list = cloneDeep(arr);
        },
        deep: true
      }
    },
    data() {
      return {
        list: [],
        visited: false
      };
    },
    methods: {
      // 删除
      rowDel({ id }) {
        this.$confirm('确定删除选择的数据吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const res = await postDel({ id });
          if (res.data.code === 200) {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.$emit('dispatch', 'refresh');
          }
        });
      },
      // 多选框
      handleSelectionChange(val) {
        const multipleSelection = val.map((item) => {
          return item.code;
        });
        this.$emit('dispatch', 'checked', multipleSelection);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
