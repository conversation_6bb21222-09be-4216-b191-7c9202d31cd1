import request from '@/router/axios';
//  报废相关接口联调

// 报废详情
export const userDetail = (params) => {
  return request({
    url: `/api/szyk-simas/scrap/detail`,
    method: 'get',
    params
  });
};
// 报废分页列表
export const getPageList = (params) => {
  return request({
    url: `/api/szyk-simas/scrap/page`,
    method: 'get',
    params
  });
};

// 新增或修改页面详情
export const userSubmit = (data) => {
  return request({
    url: `/api/szyk-simas/scrap/submit`,
    method: 'post',
    data
  });
};

// 逻辑删除
export const userDeleteLogic = (params) => {
  return request({
    url: `/api/szyk-simas/scrap/remove`,
    method: 'post',
    params
  });
};

// 审核
export const userAudit = (data) => {
  return request({
    url: `/api/szyk-simas/scrap/approve`,
    method: 'post',
    data
  });
};
//  撤销
export const userCancel = (params) => {
  return request({
    url: `/api/szyk-simas/scrap/cancel`,
    method: 'post',
    params
  });
};
