<template>
  <div class="maintenance-standards-wrap">
    <el-descriptions
      border
      :labelStyle="{ width: '133px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="维修结果：">{{
        detail.repairRecord.resultName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="结果确认方式：">{{
        detail.repairRecord.checkMethod || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="故障缺陷原因：">{{
        detail.repairRecord.faultReason || '-'
      }}</el-descriptions-item>
      <el-descriptions-item :span="3" label="维修时长："
        >{{ detail.repairRecord.duration || '-'
        }}<span style="padding-left: 10px; color: #606266">小时</span>
      </el-descriptions-item>
      <el-descriptions-item :span="3" label="解决方案：">{{
        detail.repairRecord.solution || '-'
      }}</el-descriptions-item>
      <el-descriptions-item :span="3" label="更换部件：">
        <SparePart :standardItem="detail.repairRecord" onlyView></SparePart>
      </el-descriptions-item>
      <el-descriptions-item :span="3" label="上传图片：">
        <template v-if="detail?.repairRecord?.attachList">
          <upload-img
            v-model="detail.repairRecord.attachList"
            placeholder="上传照片"
            :editable="false"
            formatLimit="jpeg,png,jpg"
          />
        </template>
        <template v-else>-</template>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
  import UploadImg from '@/components/uploadImage.vue';
  import SparePart from '@/views/equiment-full-life-cycle/repair/overhaul/work-order/perform-overhaul/spare-part.vue';

  export default {
    components: { UploadImg, SparePart },
    props: {
      detail: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped>
  .maintenance-standards-wrap {
    // padding: 0 20px;

    .el-base-title {
      color: #409eff;
    }
  }
</style>
