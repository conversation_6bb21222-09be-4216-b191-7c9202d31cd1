import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/szyk-system/dept/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getLazyList = (parentId, params) => {
  return request({
    url: '/api/szyk-system/dept/lazy-list',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  });
};

export const remove = (ids) => {
  return request({
    url: '/api/szyk-system/dept/check-remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const add = (row) => {
  return request({
    url: '/api/szyk-system/dept/submit',
    method: 'post',
    data: row
  });
};

export const update = (row) => {
  return request({
    url: '/api/szyk-system/dept/submit',
    method: 'post',
    data: row
  });
};

export const getDept = (id) => {
  return request({
    url: '/api/szyk-system/dept/detail',
    method: 'get',
    params: {
      id
    }
  });
};

export const getDeptTree = (tenantId) => {
  return request({
    url: '/api/szyk-system/dept/tree',
    method: 'get',
    params: {
      tenantId
    }
  });
};
export const getUserList = (params) => {
  return request({
    url: '/api/szyk-system/page',
    method: 'get',
    params
  });
};

// export const getUserList_new = (params) => {
//   return request({
//     url: '/szyk-user/page-oauth',
//     method: 'get',
//     params
//   });
// };
// export const getDeptLazyTree_new = (parentId) => {
//   return request({
//     url: '/szyk-system/dept/lazy-tree-oauth',
//     method: 'get',
//     params: {
//       parentId
//     }
//   });
// };
export const getDeptLazyTree = (parentId) => {
  return request({
    url: '/api/szyk-system/dept/lazy-tree',
    method: 'get',
    params: {
      parentId
    }
  });
};

export const getDeptLazyTreeByParent = (parentId, tenantId) => {
  return request({
    url: '/api/szyk-system/dept/lazy-tree-by-parent',
    method: 'get',
    params: {
      parentId,
      tenantId
    }
  });
};

// 获取所有部门
export const getDeptAll = (tenantId) => {
  return request({
    url: '/api/szyk-system/dept/all',
    method: 'get',
    params: {
      tenantId
    }
  });
};

// 获取部门的所有父级部门id列表(查名字)
export const getDeptNameList = (deptName, tenantId) => {
  return request({
    url: '/api/szyk-system/dept/select-ancestor-id-list',
    method: 'get',
    params: {
      deptName,
      tenantId
    }
  });
};

// 获取部门的所有父级部门id列表(查ids)
export const getDeptIdList = (ids, tenantId) => {
  return request({
    url: '/api/szyk-system/dept/select-ancestor-id-list-by-ids',
    method: 'get',
    params: {
      ids,
      tenantId
    }
  });
};
