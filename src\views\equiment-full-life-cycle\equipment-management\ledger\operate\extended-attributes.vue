<template>
  <div>
    <!-- v-if="form.constructionUnitId" -->
    <el-form
      ref="form"
      size="small"
      :model="form"
      :class="isEdit ? '' : 'form-view'"
      label-width="130px"
      label-position="right"
      label-suffix="："
    >
      <el-row class="extend-row" :gutter="20">
        <el-col
          v-for="(item, index) in form.memberList"
          :key="item.id"
          :span="12"
        >
          <!-- 多选 :label-width="item.attrName.length >= 9 ? '230px' : '170px'" -->
          <el-form-item
            v-if="['0', '1'].includes(item.attrType)"
            :label="item.attrName"
            :prop="'memberList.' + index + '.attributeValue'"
            class="form-item-wrap"
            :rules="
              isEdit
                ? [
                    {
                      required: !!item.isRequire,
                      message: '请选择',
                      trigger: 'change'
                    }
                  ]
                : []
            "
          >
            <el-select
              v-model="item.attributeValue"
              filterable
              v-if="isEdit"
              :clearable="!item.isRequire"
              :multiple="['0'].includes(item.attrType)"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="dict in item.selectData"
                :key="dict.label"
                :label="dict.label"
                :value="dict.label"
              ></el-option>
            </el-select>
            <template v-else>
              <template v-if="['0'].includes(item.attrType)">
                {{ item.attributeValue.join() || '--' }}
              </template>
              <template v-if="['1'].includes(item.attrType)">{{
                item.attributeValue || '--'
              }}</template>
            </template>
          </el-form-item>
          <!-- 精度 -->
          <el-form-item
            v-if="['2'].includes(item.attrType)"
            :label="item.attrName"
            :prop="'memberList.' + index + '.attributeValue'"
            class="form-item-wrap"
            :rules="
              isEdit
                ? [
                    {
                      required: !!item.isRequire,
                      message: '请输入',
                      trigger: 'blur'
                    },
                    {
                      validator: (rule, value, callback) =>
                        validatePercision(rule, value, callback, item),
                      trigger: 'blur'
                    }
                  ]
                : []
            "
          >
            <el-input
              v-model.trim="item.attributeValue"
              style="width: 100%"
              placeholder="请输入"
              v-if="isEdit"
              :clearable="!item.isRequire"
              :maxlength="15"
            />
            <template v-else>{{ item.attributeValue || '--' }}</template>
          </el-form-item>
          <!-- 日期 -->
          <el-form-item
            v-if="['3'].includes(item.attrType)"
            :label="item.attrName"
            :prop="'memberList.' + index + '.attributeValue'"
            class="form-item-wrap"
            :rules="
              isEdit
                ? [
                    {
                      required: !!item.isRequire,
                      message: '请选择',
                      trigger: 'blur'
                    }
                  ]
                : []
            "
          >
            <el-date-picker
              v-model="item.attributeValue"
              style="width: 100%"
              :type="['YYYY-MM-DD'].includes(item.dataType) ? 'date' : 'month'"
              v-if="isEdit"
              :clearable="!item.isRequire"
              :value-format="
                ['YYYY-MM-DD'].includes(item.dataType)
                  ? 'yyyy-MM-dd'
                  : 'yyyy-MM'
              "
              placeholder="请选择"
              autocomplete="off"
            >
            </el-date-picker>
            <template v-else>{{ item.attributeValue || '--' }}</template>
          </el-form-item>
          <!-- 文本 -->
          <el-form-item
            v-if="['4'].includes(item.attrType)"
            :label="item.attrName"
            :prop="'memberList.' + index + '.attributeValue'"
            class="form-item-wrap"
            :rules="
              isEdit
                ? [
                    {
                      required: !!item.isRequire,
                      message: '请输入',
                      trigger: 'blur'
                    }
                  ]
                : []
            "
          >
            <!-- :disabled="!isEdit" -->
            <el-input
              v-model.trim="item.attributeValue"
              placeholder="请输入"
              v-if="isEdit"
              autosize
              style="width: 100%"
              type="textarea"
              :clearable="!item.isRequire"
              :maxlength="item.fieldLength"
            />
            <div style="margin-bottom: 9px; line-height: 17px" v-else>
              {{ item.attributeValue || '--' }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
  import reg from '@/util/regexp';
  export default {
    name: 'extendedAttribute',
    props: {
      form: {
        type: Object,
        default: () => {}
      },
      isEdit: {
        type: Boolean,
        default: false
      },
      id: { type: String, default: '' }
    },
    data() {
      return {
        constructionList: [],
        memberTypeDic: []
      };
    },
    methods: {
      // 验证精度
      validatePercision(rule, value, callback, row) {
        let precision = row.precision;
        // let isRequire = !!row.isRequire;
        // 精度为0
        if (!precision && !reg.number.test(value)) {
          return callback(new Error('请输入整数'));
        } else if (precision) {
          // 精度不为0
          let regexp = new RegExp(
            `^[+-]?\\d{0,20}(?:\\.\\d{1,${precision}})?$`
          );
          if (!regexp.test(value)) {
            return callback(new Error(`请输入精度${precision}以内的数`));
          }
        }
        callback();
      },
      async validForm() {
        let valid = await this.$refs['form'].validate();

        if (valid) {
          let memberList = this.form.memberList;
          let list = memberList.map((item) => {
            let attrValue = Array.isArray(item.attributeValue)
              ? item.attributeValue.join()
              : item.attrType === '2'
              ? item.attributeValue
                ? Number(item.attributeValue).toFixed(item.precision)
                : item.attributeValue
              : item.attributeValue;
            return {
              attrId: item.attrId || item.id,
              attrValue
            };
          });
          return list;
        } else {
          return false;
        }
      }
    }
  };
</script>
<style lang="scss">
  .dynamic-add-btn {
    width: 100%;
    border: 1px dashed #dcdfe6;
  }
</style>
<style lang="scss" scoped>
  .extend-row {
    display: flex;
    flex-wrap: wrap;
  }

  /deep/ .form-view {
    .el-form-item {
      margin-bottom: 0 !important;
    }

    // .el-form-item__label {
    //   color: #98b6d5;
    // }
  }

  /deep/ .el-descriptions .el-descriptions-item {
    padding: 0 10px;
  }

  /deep/ .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
    width: 33.33%;
    padding-bottom: 0;
  }

  /deep/ .description-content {
    // width: 33.333%;
    flex: 1;

    .form-item-wrap {
      display: flex;
      align-items: flex-end;
    }

    .el-form-item__content {
      flex: 1;
      margin-left: 0 !important;
    }
  }
</style>
