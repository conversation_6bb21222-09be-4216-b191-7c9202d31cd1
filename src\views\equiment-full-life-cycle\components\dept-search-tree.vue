<template>
  <el-row style="height: 98%">
    <el-col :span="show ? 4 : 0">
      <transition name="el-fade-in">
        <basic-container
          :autoHeight="true"
          no-scrollbar
          v-loading="deptLoading"
        >
          <el-input
            size="small"
            clearable
            placeholder="输入关键字进行过滤"
            v-model="filterText"
          >
          </el-input>
          <el-tree
            style="margin-top: 10px"
            :expand-on-click-node="false"
            :data="treeData"
            :props="defaultProps"
            default-expand-all
            :filter-node-method="filterNode"
            node-key="id"
            ref="deptTree"
            @node-click="nodeClick"
          >
            <template v-slot="{ node, data }">
              <span class="title" :title="data.title">{{ data.title }}</span>
            </template>
          </el-tree>
        </basic-container>
      </transition>
    </el-col>
    <el-col
      :span="show ? 20 : 24"
      style="position: relative; padding-left: 19px"
    >
      <span
        :class="`show ${
          show ? 'el-icon-d-arrow-left' : 'el-icon-d-arrow-right'
        }`"
        @click="visibleClick"
      ></span>
      <slot></slot>
    </el-col>
  </el-row>
  <!--  <div style="position: relative; padding-right: 10px">-->
  <!--    <basic-container :autoHeight="true" no-scrollbar>-->
  <!--      <el-input-->
  <!--        size="small"-->
  <!--        clearable-->
  <!--        placeholder="输入关键字进行过滤"-->
  <!--        v-model="filterText"-->
  <!--      >-->
  <!--      </el-input>-->
  <!--      <el-tree-->
  <!--        style="margin-top: 10px"-->
  <!--        :expand-on-click-node="false"-->
  <!--        :data="treeData"-->
  <!--        :props="defaultProps"-->
  <!--        default-expand-all-->
  <!--        :filter-node-method="filterNode"-->
  <!--        node-key="id"-->
  <!--        ref="deptTree"-->
  <!--        @node-click="nodeClick"-->
  <!--      />-->
  <!--    </basic-container>-->
  <!--    <span-->
  <!--      :class="`show ${show ? 'el-icon-d-arrow-left' : 'el-icon-d-arrow-right'}`"-->
  <!--      @click="visibleClick"-->
  <!--    ></span>-->
  <!--  </div>-->
</template>

<script>
  import 'element-ui/lib/theme-chalk/base.css';
  import { getDeptTree } from '@/api/system/dept';
  export default {
    name: 'DeviceBasicList',
    components: {},
    props: {},
    data() {
      return {
        filterText: '',
        treeData: [],
        defaultProps: {
          children: 'children',
          label: 'title'
        },
        show: true,
        deptLoading: false,
        defaultExpandedKeys: []
      };
    },
    mounted() {
      this.$nextTick(async () => {
        await this.getDeptTree();
      });
      //    监听浏览器窗口变化事件，改变树形结构宽度
      window.addEventListener('resize', this.handleResize);
    },
    watch: {
      filterText(val) {
        this.$refs.deptTree.filter(val);
      }
    },
    beforeDestroy() {
      // 移除事件监听器
      window.removeEventListener('resize', this.handleResize);
    },
    methods: {
      handleResize() {
        //    实时获取浏览器窗口的宽度
        let width = document.body.clientWidth;
        if (width <= 992) {
          this.show = false;
        } else {
          this.show = true;
        }
      },
      visibleClick() {
        this.show = !this.show;
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.title.indexOf(value) !== -1;
      },
      async getDeptTree() {
        try {
          this.deptLoading = true;
          const res = await getDeptTree();
          this.treeData = res.data.data || [];
          if (res.data.data[0]?.id) {
            this.defaultExpandedKeys = [res.data.data[0]?.id];
            this.$nextTick(() => {
              this.$refs.deptTree.setCurrentKey(res.data.data[0].id);
              this.nodeClick(res.data.data[0]);
            });
          }
          this.deptLoading = false;
        } catch (e) {
          this.deptLoading = false;
        }
      },
      nodeClick(node) {
        this.$emit('node-click', node);
      }
    }
  };
</script>

<style scoped lang="scss">
  .show {
    position: absolute;
    top: 2px;
    left: 0px;
    display: block;
    padding: 10px 2px;
    background: #409eff;
    color: #fff;
    cursor: pointer;
  }
  .title {
    white-space: nowrap; // 防止文本换行
    overflow: hidden; // 隐藏溢出的内容
    text-overflow: ellipsis; // 显示省略号
  }
</style>
