/**
 * 全局配置文件
 */
export default {
  title: '设备全生命周期',
  logo: '',
  key: 'device-mine-simas', //配置主键,目前用于存储
  indexTitle: '设备全生命周期',
  clientId: 'saber', // 客户端id
  clientSecret: 'saber_secret', // 客户端密钥
  tenantMode: false, // 是否开启租户模式
  tenantId: '000000', // 管理组租户编号
  captchaMode: true, // 是否开启验证码模式
  switchMode: false, // 是否开启部门切换模式
  lockPage: '/lock',
  tokenTime: 3000,
  tokenHeader: 'Szyk-Auth',
  //http的status默认放行列表
  statusWhiteList: [],
  //配置首页不可关闭
  isFirstPage: false,
  // 请求菜单和按钮时的services
  services: 'simas',
  fistPage: {
    label: 'wel',
    value: '/wel/index',
    title: '首页',
    params: {},
    query: {},
    props: false,
    meta: {
      title: '首页',
      i18n: 'dashboard'
    },
    group: [],
    close: false
  },
  //配置菜单的属性
  menu: {
    iconDefault: 'iconfont icon-caidan',
    props: {
      name: 'componentName',
      label: 'name',
      path: 'path',
      icon: 'source',
      children: 'children',
      componentPath: 'componentPath',
      meta: 'meta',
      isHide: 'isHide',
      activeMenu: 'activeMenu',
      isProps: 'isProps',
      isKeepAlive: 'isKeepAlive'
    }
  },
  // websocket配置
  WS: {
    enable: false, // 是否启用
    lockReconnect: false, // 连接失败不进行重连
    maxReconnect: 5 // 最大重连次数，若连接失败
  },
  // 第三方系统授权地址
  authUrl: 'http://localhost/szyk-auth/oauth/render',
  // 流程设计器地址
  flowDesignUrl: 'http://localhost:9999',
  // 报表设计器地址(cloud端口为8108,boot端口为80)
  reportUrl: 'http://localhost:8108/ureport',
  // 部门组件全部数据根数据
  deptRootSync: {
    id: '0',
    deptName: '顶级',
    deptCategory: 1,
    hasChildren: true,
    parentId: 'ROOT'
  },
  // 部门组件懒加载根数据
  deptRoot: { deptName: '顶级', id: '0', deptCategory: 1, hasChildren: true }
};
