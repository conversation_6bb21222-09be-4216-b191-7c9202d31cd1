<template>
  <dialog-drawer
    :title="type === 'ADD' ? '新增' : '编辑'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <section>
        <span class="el-base-title">基础信息</span>
        <base-info :details="details"></base-info>
      </section>
      <!--  资料内容 -->
      <span class="el-base-title">资料内容</span>
      <upload-file
        ref="file"
        style="padding-bottom: 80px"
        :initData="details"
        :cateGoryList="cateGoryList"
        :childrenList="childrenList"
        :typeId="typeId"
      ></upload-file>
    </div>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"></btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>
<script>
  import BaseInfo from './base-info.vue'; // 设备基本信息
  import UploadFile from './file.vue';
  import {
    getDeviceFileDetail,
    addOrEditDeviceFileSubmit,
    getDeviceTypeLazyList,
    getChildrenList
  } from '@/api/equiment-full-life-api/profile';

  export default {
    name: 'RepairViewIndex',
    components: {
      BaseInfo,
      UploadFile
    },
    data() {
      return {
        eqId: '',
        type: 'ADD',
        visible: false,
        loading: false,
        typeId: undefined,
        cateGoryList: [],
        childrenList: [],
        details: {
          equipmentAccount: {},
          equipmentFileInfoList: []
        }, //
        standList: [] //
      };
    },
    methods: {
      //  获取归类列表
      getCategoryList() {
        this.loading = true;
        getDeviceTypeLazyList({ parentId: '0' })
          .then((res) => {
            this.cateGoryList = res.data.data;
            let id = this.cateGoryList.find((it) => {
              if (this.activeName === 'PRE') {
                return it.name === '设备前期资料';
              } else {
                return it.name === '注册登记资料';
              }
            }).id;
            this.typeId = id;
            this.getChildrenList(id);
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
      },
      //  获取子列表
      async getChildrenList(id) {
        try {
          const res = await getChildrenList({ parentId: id });
          let list = res.data.data || [];
          this.childrenList = list;
        } catch (e) {
          console.log(e);
        }
      },
      async submit() {
        try {
          this.loading = true;
          const standList = await this.$refs.file.validForm();
          if (standList.length === 0 && this.type === 'EDIT') {
            this.$confirm(
              `是否确定清空${this.details.sn}设备的所有资料？`,
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
              .then(async () => {
                try {
                  if (standList) {
                    await addOrEditDeviceFileSubmit({
                      equipmentId: this.eqId,
                      fileList: standList,
                      module: this.activeName
                    });
                    this.$emit('success');
                    this.visible = false;
                    this.$message.success('操作成功');
                  }
                } catch (e) {
                  this.$message.warning(e.data.msg);
                }
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                });
              });
          } else if (standList.length === 0 && this.type === 'ADD') {
            this.$confirm(`您还没有添加任何文件，是否关闭页面？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(async () => {
                try {
                  if (standList) {
                    await addOrEditDeviceFileSubmit({
                      equipmentId: this.eqId,
                      fileList: standList,
                      module: this.activeName
                    });
                    this.$emit('success');
                    this.visible = false;
                    this.$message.success('操作成功');
                  }
                } catch (e) {
                  this.$message.warning(e.data.msg);
                }
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                });
              });
          } else {
            await addOrEditDeviceFileSubmit({
              equipmentId: this.eqId,
              fileList: standList,
              module: this.activeName
            });
            this.$emit('success');
            this.visible = false;
            this.$message.success('操作成功');
          }
          this.loading = false;
        } catch (e) {
          console.log('e..', e);
          this.loading = false;
        }
      },
      closed() {
        this.details = { equipmentAccount: {}, monitorStandardList: [] };
        this.standList = [];
        this.visible = false;
        this.$refs['file'].resetForm();
      },
      add() {
        this.$refs.standard.show(this.standList);
      },
      // 点击展示
      async show(id, type, activeName) {
        // 获取归类类型
        await this.getCategoryList();
        this.activeName = activeName;
        this.visible = true;
        // this.type = type;
        if (id) {
          this.eqId = id;
          await this.getDetail(id);
        }
      },
      // 获取详情接口
      async getDetail(id) {
        try {
          this.loading = true;
          const res = await getDeviceFileDetail({
            id: id,
            module: this.activeName
          });

          this.details = res.data.data;
          this.type = this.details.equipmentFileInfoList ? 'EDIT' : 'ADD';
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }
</style>
