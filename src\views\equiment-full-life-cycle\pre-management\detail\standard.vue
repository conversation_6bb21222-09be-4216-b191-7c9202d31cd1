<template>
  <div>
    <el-table size="small" :data="list" border>
      <!-- 表格列保持不变 -->
      <el-table-column type="index" width="50" align="center"></el-table-column>
      <el-table-column prop="name" label="资料名称" align="center">
        <template v-slot="{ row }"> {{ getFileName(row.name) }}</template>
      </el-table-column>
      <el-table-column
        prop="typeName"
        label="归类"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="fileCategoryName"
        label="资料类型"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.fileCategoryName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="extension"
        label="文件类型"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="添加时间"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createUserName"
        label="添加人"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-view"
            type="text"
            size="small"
            @click="handlePreview(scope.row)"
            >预览</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="img_fullScreen" v-show="false">
      <el-image style="height: 100%" ref="image" :preview-src-list="[imageUrl]">
        <span slot="placeholder" class="loading">加载中...</span>
        <span slot="error">图片加载失败!</span>
      </el-image>
    </div>
  </div>
</template>

<script>
  import { getFileName } from '@/views/equiment-full-life-cycle/equipment-management/util';
  import { previewFile } from '@/util/preview';
  import { getFileFullUrl } from '@/util/file';

  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        getFileName,
        imageUrl: ''
      };
    },
    methods: {
      // 预览文件
      handlePreview(file) {
        const imageExtensions = ['jpg', 'jpeg', 'png'];
        const extension = (file.extension || '').toLowerCase();
        if (imageExtensions.includes(extension)) {
          this.imageUrl = getFileFullUrl(file.attach.id);
          // 调用预览方法
          this.$nextTick(() => {
            this.$refs.image.clickHandler();
          });
        } else if (extension === 'md') {
          this.$message.warning('Markdown文件无法预览');
        } else {
          if (!file.originalName) file.originalName = file.name;
          previewFile(file);
        }
      }
    },
    watch: {}
  };
</script>

<style lang="scss" scoped></style>
