<template>
  <el-dialog
    title="选择部门"
    :visible.sync="visible"
    width="60%"
    custom-class="_dialogStyle"
    @closed="closed"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
  >
    <el-form :model="form" inline size="small" class="search-form">
      <el-form-item label="部门名称">
        <el-input
          v-model="form.deptName"
          placeholder="请输入部门名称"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="getList" />
        <btn type="reset" @click="onFormReset" />
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      style="width: 100%"
      border
      row-key="id"
      lazy
      :load="load"
      size="small"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :header-cell-style="{ background: '#fafafa' }"
      height="500px"
    >
      <el-table-column type="index" label="#"></el-table-column>
      <el-table-column prop="deptName" label="部门名称" show-overflow-tooltip>
      </el-table-column>

      <el-table-column prop="fullName" label="部门全称" show-overflow-tooltip>
      </el-table-column>
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <el-button type="text" size="mini" @click="handleSelect(scope.row)">
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
  import { getLazyList } from '@/api/system/dept';
  export default {
    name: 'dept-dialog',
    components: {},
    props: {},
    data() {
      return {
        form: {
          deptName: ''
        },
        loading: false,
        total: 0,
        current: undefined,
        list: [],
        visible: false
      };
    },
    mounted() {},
    methods: {
      show(searchParams = {}) {
        this.visible = true;
        this.$nextTick(() => {
          this.form = { ...this.form, ...searchParams };
          this.getList();
        });
      },
      onFormReset() {
        this.form.deptName = '';
        this.getList();
      },
      handleSelect(row) {
        this.visible = false;
        this.$emit('select', row);
      },
      async load(tree, treeNode, resolve) {
        const parentId = tree.id;
        getLazyList(parentId).then((res) => {
          resolve(res.data.data);
        });
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getLazyList(this.form.deptName ? '' : 0, this.form);
          this.list = res.data.data;
        } catch (e) {
          console.log(e);
        }
        this.loading = false;
      },
      closed() {
        this.form.deptName = undefined;
      }
    }
  };
</script>

<style scoped lang="scss"></style>
