<template>
  <dialog-drawer
    title="点巡检工单"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <span class="el-base-title">设备信息</span>
      <base-info :details="details"></base-info>
      <span class="el-base-title">点巡检标准</span>
      <standard ref="standard" :detail="details"></standard>
      <span class="el-base-title">备注信息</span>
      <el-input
        v-model="remark"
        type="textarea"
        placeholder="请输入备注信息"
        clearable
        show-word-limit
        :maxlength="200"
        autosize
      >
      </el-input>
    </div>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"></btn>
      <btn type="close" @click="closed" :loading="loading"></btn>
    </div>
  </dialog-drawer>
</template>
<script>
  import BaseInfo from '@/views/equiment-full-life-cycle/equipment-inspection/work-order/detail/base-info.vue';
  import Standard from '@/views/equiment-full-life-cycle/equipment-inspection/work-order/operate/standard.vue';
  import {
    simasInspectOrderApi,
    getOrderDetail
  } from '@/api/equiment-full-life-api/inspect';

  export default {
    name: 'RepairViewIndex',
    components: {
      BaseInfo,
      Standard
    },
    data() {
      return {
        remark: '',
        eqId: '',
        visible: false,
        loading: false,
        details: {} // 详情数据
      };
    },
    methods: {
      async show(no) {
        this.visible = true;
        if (no) await this.getDetail(no);
      },
      //  点巡检接口
      async inspectOperate(standList) {
        try {
          this.loading = true;
          let params = {
            orderId: this.details.id,
            equipmentId: this.details.equipmentAccount.id,
            remark: this.remark,
            inspectRecordList: standList
          };
          await simasInspectOrderApi(params);
          this.visible = false;
          this.$message.success('操作成功');
          this.$emit('success');
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      async submit() {
        this.loading = true;
        try {
          const standList = await this.$refs.standard.validForm();
          // 判断返回的数据， 检查结果都是正常，询问是否提交
          let normal = standList.every((item) => {
            return item.isAbnormal === 0;
          });
          if (normal) {
            this.$confirm(`检查结果均为正常，是否提交？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(async () => {
                try {
                  await this.inspectOperate(standList);
                } catch (e) {
                  this.$message.warning(e.data.msg);
                }
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                });
              });
          } else {
            await this.inspectOperate(standList);
          }

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      closed() {
        this.visible = false;
        this.details = {};
        this.$refs.standard.resetForm();
        this.visible = false;
        this.remark = undefined;
      },

      // 获取详情接口
      async getDetail(no) {
        try {
          this.loading = true;
          const res = await getOrderDetail({ no: no });
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }

  .details {
    padding-bottom: 60px;
  }
</style>
