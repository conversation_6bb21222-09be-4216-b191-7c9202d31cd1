<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-if="permission['spare-ledger-add-edit']"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate({})"
        >新增</el-button
      >
      <el-button
        v-if="permission['spare-ledger-add-delete']"
        icon="el-icon-delete"
        type="danger"
        size="small"
        @click="handleDelete()"
        >删除</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      stripe
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column align="center" type="selection" width="55">
      </el-table-column>
      <el-table-column
        prop="no"
        label="备品备件编号"
        align="center"
        width="120"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="name"
        align="center"
        label="备品备件名称"
        width="120"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column
        align="center"
        prop="model"
        label="规格型号"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.model || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="measureUnitName"
        align="center"
        label="计量单位"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.measureUnitName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="defaultWarehouseName"
        align="center"
        label="默认库房"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.defaultWarehouseName || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="safeStockAmount"
        align="center"
        label="安全库存数量"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.safeStockAmount || 0 }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        prop="statusName"
        label="状态"
        show-overflow-tooltip
      >
        <!--        1 是停用  0 启用-->
        <template v-slot="{ row }">
          <i
            :style="`color: ${
              row.status === 1 ? '#E23F3F' : '#00BB7D'
            };font-size:18px`"
            >●</i
          >
          {{ row.statusName }}
        </template>
      </el-table-column>

      <el-table-column
        prop="createUserName"
        align="center"
        label="创建人"
        width="100"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="createTime"
        align="center"
        label="创建时间"
        width="150"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        align="center"
        label="更新人"
        width="100"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="updateTime"
        align="center"
        label="更新时间"
        width="150"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column align="center" label="操作" width="200" fixed="right">
        <template v-slot="{ row }">
          <section class="cell-operate-class">
            <el-button
              v-if="permission['spare-ledger-add-edit']"
              type="text"
              size="small"
              @click="operate(row)"
              >编辑</el-button
            >

            <el-popconfirm
              v-if="permission['spare-ledger-add-delete']"
              :title="`确定要删除该备品备件吗？`"
              @confirm="() => handleDelete(row)"
            >
              <el-button
                slot="reference"
                type="text"
                size="small"
                class="danger-btn"
                >删除</el-button
              >
            </el-popconfirm>
            <!--            停用之后可以临时盘点-->
            <!--            <el-button-->
            <!--              v-if="-->
            <!--                row.status === 0 &&-->
            <!--                permission['spare-ledger-temporary-inventory']-->
            <!--              "-->
            <!--              type="text"-->
            <!--              size="small"-->
            <!--              :class="row.warehouseLockStock == 1 ? 'text-gray' : ''"-->
            <!--              @click="tempInventory(row)"-->
            <!--              >临时盘点</el-button-->
            <!--            >-->
            <el-popconfirm
              v-if="permission['spare-ledger-enable-disable']"
              :title="`确定${Number(row.status) === 1 ? '启用' : '停用'}吗？`"
              @confirm="() => handleEnable(row)"
            >
              <el-button
                slot="reference"
                type="text"
                size="small"
                :class="row.status === 1 ? 'green-btn' : 'danger-btn'"
                >{{ Number(row.status) === 1 ? '启用' : '停用' }}</el-button
              >
            </el-popconfirm>
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!-- 新增 -->
    <add-base-info ref="add" @success="getList"></add-base-info>
    <!-- 临时盘点 -->
    <temp-inventory
      :visible="tempInventoryVisible"
      :accountId="accountId"
      :currentAmount="currentAmount"
      @close="tempInventoryVisible = false"
      @refresh="onsubmit"
    />
  </basic-container>
</template>

<script>
  import Search from './search';
  import AddBaseInfo from './operate/index.vue';
  import TempInventory from './temp-inventory.vue';
  import Pagination from '@/components/pagination';
  import {
    getSparePartsPageListApi,
    startStopSparePartsApi,
    delSparePartsApi
  } from '@/api/equiment-full-life-api/spare-parts';
  import { mapGetters } from 'vuex';

  export default {
    name: 'DeviceBasicList',
    computed: {
      ...mapGetters(['permission'])
    },
    components: {
      Search,
      AddBaseInfo,
      TempInventory,
      Pagination
    },
    props: {},
    filters: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {},
        // 临时盘点弹窗
        tempInventoryVisible: false,
        accountId: undefined,
        currentAmount: undefined,
        mul: []
      };
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      // 勾选删除
      handleSelectionChange(val) {
        this.mul = val;
      },
      // 删除备件
      handleDelete(row) {
        console.log(row, !row);
        if (!row) {
          if (this.mul.length === 0) {
            this.$message.warning('请选择备品备件！');
            return;
          }
          this.$confirm('确定删除所选备品备件吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            let ids = this.mul
              .map((item) => {
                return item.id;
              })
              .join(',');
            await this.deleteSubmit({ ids: ids });
          });
        } else {
          this.deleteSubmit({ ids: row.id });
        }
      },
      async deleteSubmit(params) {
        this.loading = true;
        try {
          await delSparePartsApi(params);
          this.$message.success('删除成功');
          await this.getList();
        } catch (e) {
          this.loading = false;
          await this.getList();
          console.log(e);
        }
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getSparePartsPageListApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        this.$refs.add.show(row);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      },
      // 停用/启用
      async handleEnable(row) {
        let statusEnum = row.status === 1 ? 'ENABLED' : 'DISABLED';
        try {
          await startStopSparePartsApi(row.id, { statusEnum: statusEnum });
          this.$message({
            type: 'success',
            message: row.status === 1 ? '启用成功' : '停用成功'
          });
          await this.getList(true);
        } catch (e) {
          this.$message.warning(e.data.msg);
        }
      },
      // 临时盘点
      tempInventory(row) {
        if (row.warehouseLockStock == 1) {
          return this.$message.warning('该仓库备品备件正在执行盘点');
        }
        this.tempInventoryVisible = true;
        this.accountId = row.id;
        this.currentAmount = row.currentAmount;
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }

  .text-gray {
    color: #999;
  }
</style>
