<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="设备编号" prop="code">
        <el-input
          v-model.trim="form.code"
          placeholder="请输入设备编号"
          style="width: 150px"
          clearable
          maxlength="50"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="SN编号" prop="sn">
        <el-input
          v-model.trim="form.sn"
          placeholder="请输入SN编号"
          style="width: 150px"
          clearable
          maxlength="50"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="设备状态" prop="status">
        <el-select
          v-model="form.status"
          style="width: 150px"
          placeholder="请选择设备状态"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['equipment_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    serviceDicts: ['equipment_status'],
    props: {
      activeName: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        statusList: [
          { label: '未到期', value: '0' },
          { label: '已到期', value: '1' }
        ],
        form: {
          code: undefined,
          sn: undefined,
          status: undefined
        }
      };
    },
    methods: {
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.chargeDept = dept.id;
        this.form.chargeDeptName = dept.deptName;
      },
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = { ...this.form };
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
