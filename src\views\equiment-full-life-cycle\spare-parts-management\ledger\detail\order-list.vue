<template>
  <div style="height: 100%">
    <div class="top-info">
      <el-form
        label-suffix="："
        label-width="90px"
        :inline="true"
        ref="search"
        :model="form"
        size="small"
      >
        <el-form-item label="工单编号" prop="bizNo">
          <el-input
            v-model.trim="form.bizNo"
            placeholder="请输入工单编号"
            clearable
            :maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="工单类型" prop="module">
          <el-select
            v-model="form.module"
            placeholder="请选择工单类型"
            clearable
          >
            <el-option
              v-for="dict in serviceDicts.type['system_module'].filter((it) =>
                filterDict.includes(it.value)
              )"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="submit"
            >搜索</el-button
          >
          <el-button icon="el-icon-delete" @click="reset">清空</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="500px"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="bizNo"
        label="工单编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="moduleName"
        label="工单类型"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.moduleName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="工单创建时间"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="endTime"
        label="工单结束时间"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.endTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="责任人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.executeUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="bizStatusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="90px"
      ></el-table-column>

      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-view"
            type="text"
            size="small"
            @click="detail(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!--    保养工单-->
    <detail-index ref="maintainOrder"></detail-index>
    <!--    点检工单-->
    <inspect-work-order ref="inspectWorkOrder"></inspect-work-order>
    <!--    维修工单 -->
    <repair-internal-dispatch
      ref="repairInternalDispatch"
    ></repair-internal-dispatch>
    <repair-external-dispatch
      ref="repairExternalDispatch"
    ></repair-external-dispatch>
  </div>
</template>

<script>
  import Pagination from '@/components/pagination';
  import repairInternalDispatch from '@/views/equiment-full-life-cycle/repair/internal/detail/index.vue'; // 内部维修
  import repairExternalDispatch from '@/views/equiment-full-life-cycle/repair/external/detail/index.vue'; // 外部维修
  import { getEquipmentOrderPage } from '@/api/equiment-full-life-api/ledger';
  import DetailIndex from '@/views/equiment-full-life-cycle/equipment-maintenance/work-order/detail/index.vue';
  import InspectWorkOrder from '@/views/equiment-full-life-cycle/equipment-inspection/work-order/detail/index.vue';
  export default {
    serviceDicts: ['system_module'],
    name: 'DeviceBasicList',
    components: {
      DetailIndex,
      Pagination,
      InspectWorkOrder,
      repairInternalDispatch,
      repairExternalDispatch
    },
    props: {
      equipmentId: {
        type: String,
        default: () => {
          return undefined;
        }
      }
    },
    watch: {
      equipmentId: {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.eqId = val;
            this.getList();
          } else {
            this.eqId = undefined;
          }
        }
      }
    },
    data() {
      return {
        filterDict,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        eqId: undefined,
        form: {
          bizNo: undefined,
          module: undefined
        }
      };
    },

    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      resetForm() {
        this.list = [];
        this.searchParams = {
          current: 1,
          size: 10
        };
        this.form = { bizNo: undefined, module: undefined };
      },
      submit() {
        this.searchParams = { ...this.searchParams, ...this.form };
        this.getList();
      },
      detail(row) {
        console.log('查看详情信息......', row);
        let refName = getRouterName(row.module);
        if (refName === 'INTERNAL_REPAIR' || refName === 'EXTERNAL_REPAIR') {
          this.$refs[refName].show(row.bizNo, refName);
        } else {
          this.$refs[refName].show(row.bizNo);
        }
      },

      async getList() {
        this.loading = true;
        try {
          let res = await getEquipmentOrderPage({
            ...this.searchParams,
            equipmentId: this.eqId
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },

      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
