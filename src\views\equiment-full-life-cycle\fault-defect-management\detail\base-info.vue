<template>
  <!-- 详情基本信息 -->
  <div>
    <el-form
      ref="form"
      size="small"
      inline
      label-width="140px"
      label-suffix="："
    >
      <el-row :gutter="15">
        <el-col :span="8">
          <el-form-item label="设备编号" prop="equipmentCode">
            <div class="content-wrapper">
              {{ details.equipmentAccount.code || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="SN编号" prop="equipmentSn">
            <div class="content-wrapper">
              {{ details.equipmentAccount.sn || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="设备名称" prop="name">
            <div class="content-wrapper" :title="details.equipmentAccount.name">
              {{ details.equipmentAccount.name || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="规格型号" prop="model">
            <div class="content-wrapper">
              {{ details.equipmentAccount.model || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="设备位置" prop="locationName">
            <div
              class="content-wrapper"
              :title="details.equipmentAccount.locationPath"
            >
              {{ details.equipmentAccount.locationPath || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="使用部门" prop="useDeptName">
            <div
              class="content-wrapper"
              :title="details.equipmentAccount.useDeptName"
            >
              {{ details.equipmentAccount.useDeptName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="使用人" prop="userName">
            <div class="content-wrapper">
              {{ details.equipmentAccount.userName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="缺陷来源" prop="reportUser">
            <div class="content-wrapper">
              {{ details.sourceName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源单号" prop="reportUser">
            <el-button
              v-if="details.sourceNo"
              type="text"
              size="medium"
              @click="$emit('detail')"
              >{{ details.sourceNo }}</el-button
            >
            <span v-else>-</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报修人" prop="reportUser">
            <div class="content-wrapper">
              {{ details.reportUserName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报修人所属部门" prop="reportUser">
            <div class="content-wrapper">
              {{ details.reportDeptName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上报时间" prop="reportUser">
            <div class="content-wrapper">
              {{ details.reportTime || '-' }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {};
    },
    methods: {}
  };
</script>

<style lang="scss" scoped>
  ::v-deep .el-form-item {
    margin-bottom: 4px;
  }
</style>
