import request from '@/router/axios';
//  保养相关接口

// 分页列表

// 保养标准详情
export const maintainStandardDetail = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-standard/detail`,
    method: 'get',
    params
  });
};
// 保养标准新增或修改该
export const userAddOrUpdate = (data) => {
  return request({
    url: `/api/szyk-simas/maintain-standard/submit`,
    method: 'post',
    data
  });
};
// 保养标准清空标准
export const userClear = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-standard/clear`,
    method: 'post',
    params
  });
};

// 选择部位下拉列表
export const getPartList = (params) => {
  return request({
    url: `/api/szyk-common/equipment-account/monitor-select`,
    method: 'get',
    params
  });
};

//保养计划表相关接口
export const getPlanList = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/page`,
    method: 'get',
    params
  });
};

// 保养计划详情列表
export const getPlanDetail = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/detail`,
    method: 'get',
    params
  });
};
// 保养计划详情 新接口
export const getPlanDetailNew = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/view`,
    method: 'get',
    params
  });
};

// 保养计划新增接口
export const addPlan = (data) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/save`,
    method: 'post',
    data
  });
};

// 保养计划修改接口
export const updatePlan = (data) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/update`,
    method: 'post',
    data
  });
};
// 保养计划逻辑删除接口
export const deletePlan = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/remove`,
    method: 'post',
    params
  });
};

// 点击按计划手动开始
export const startPlan = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/manual-start`,
    method: 'post',
    params
  });
};

// 点检计划手动停止
export const stopPlan = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/manual-stop`,
    method: 'post',
    params
  });
};

// 保养计划手动执行生成当天工单
export const manualPlan = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/manual-generate-orders`,
    method: 'post',
    params
  });
};

// 保养工单相关接口 -----
export const getOrderList = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-order/page`,
    method: 'get',
    params
  });
};

// 保养工单详情
export const getOrderDetail = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-order/detail`,
    method: 'get',
    params
  });
};

// 工单审核确认
export const auditOrder = (data) => {
  return request({
    url: `/api/szyk-simas/maintain-order/confirm`,
    method: 'post',
    data
  });
};

// 工单 - 批量审核
export const batchAuditOrder = (data) => {
  return request({
    url: `/api/szyk-simas/maintain-order/confirmBatch`,
    method: 'post',
    data
  });
};

//  保养统计相关接口 ---------------------
//  保养工单完成情况
//  工单完成情况
export const getStatistics = (params) => {
  return request({
    url: `/api/szyk-simas/statistics/maintain-order`,
    method: 'get',
    params
  });
};
// 保养异常统计
export const getStatisticsException = (params) => {
  return request({
    url: `/api/szyk-simas/statistics/maintain-abnormal`,
    method: 'get',
    params
  });
};
// 保养任务统计
export const getStatisticsTask = (params) => {
  return request({
    url: `/api/szyk-simas/statistics/maintain-task`,
    method: 'get',
    params
  });
};
// 保养计划统计分页
export const getStatisticsPlan = (params) => {
  return request({
    url: `/api/szyk-simas/statistics/maintain-plan`,
    method: 'get',
    params
  });
};

//保养计划 - 选择标准接口
export const getStandardList = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/selectPage`,
    method: 'get',
    params
  });
};

// 保养计划 - 详情中获取设备列表接口
export const getMaintainDeviceList = (params) => {
  return request({
    url: `/api/szyk-simas/maintain-plan/planDevicePage`,
    method: 'get',
    params
  });
};

// 保养工单 - 保养操作按钮权限
export const getMaintainOrderButton = (data) => {
  return request({
    url: `/api/szyk-simas/maintain-order/maintain`,
    method: 'post',
    data
  });
};
