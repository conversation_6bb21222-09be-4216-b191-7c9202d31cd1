<template>
  <div class="img-block">
    <div
      v-for="(file, i) in fileList"
      v-bind:key="i"
      class="avatar"
      v-loading="loading"
    >
      <el-image
        v-if="fileType === 'IMAGE'"
        :style="imgStyle"
        :src="file.status === 'ready' ? getFileUrl(file) : file.filePath"
        class="img"
      ></el-image>
      <div v-if="fileType === 'AUDIO'" class="icon">
        <i class="el-icon-headset"></i>
      </div>
      <video
        v-if="fileType === 'VIDEO'"
        width="100%"
        :src="file.status === 'ready' ? getFileUrl(file) : file.filePath"
        class="img"
      ></video>
      <div class="mask">
        <i @click="onPreview(file)" class="el-icon-zoom-in"></i>
        <i @click="onDelete(file)" class="el-icon-delete" v-if="editable"></i>
      </div>
    </div>
    <el-upload
      :style="imgStyle"
      v-if="fileList.length < limit && editable"
      ref="upload"
      accept="image/*"
      :multiple="false"
      :action="baseUrl + url"
      class="avatar-uploader"
      :show-file-list="false"
      :headers="header"
      name="file"
      :on-change="handleChange"
      :auto-upload="!cutImage"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
    >
      <i
        class="el-icon-plus avatar-uploader-icon"
        :style="{
          width:
            option.fixedNumber[0] > 100
              ? option.fixedNumber[0]
              : option.fixedNumber[0] * 10,
          height:
            option.fixedNumber[1] > 100
              ? option.fixedNumber[1]
              : option.fixedNumber[1] * 10
        }"
      >
        <div :v-if="placeholder">{{ placeholder }}</div>
      </i>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible" title="图片预览" append-to-body>
      <img
        v-if="fileType === 'IMAGE'"
        width="100%"
        :src="dialogImageUrl"
        alt=""
      />
      <audio
        v-if="fileType === 'AUDIO'"
        width="100%"
        :src="dialogImageUrl"
        alt=""
        controls
        preload
      ></audio>
      <video
        v-if="fileType === 'VIDEO'"
        width="100%"
        :src="dialogImageUrl"
        alt=""
        controls
        preload
      ></video>
    </el-dialog>

    <el-dialog title="图片剪裁" :visible.sync="cropperVisible" append-to-body>
      <div class="cropper-content">
        <div class="cropper" style="text-align: center">
          <vueCropper
            ref="cropper"
            :img="option.img"
            :outputSize="option.size"
            :outputType="option.outputType"
            :info="true"
            :full="option.full"
            :canMove="option.canMove"
            :canMoveBox="option.canMoveBox"
            :original="option.original"
            :autoCrop="option.autoCrop"
            :fixed="option.fixed"
            :fixedNumber="option.fixedNumber"
            :centerBox="option.centerBox"
            :infoTrue="option.infoTrue"
            :fixedBox="option.fixedBox"
          ></vueCropper>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cropperVisible = false" size="small"
          >取 消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="finish"
          :loading="loading"
          >确认</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { baseUrl } from '@/config/env';
  import { uploadFileSiams } from '@/api/base/region';
  import { Base64 } from 'js-base64';
  import website from '@/config/website';
  import { getToken } from '@/util/auth';
  import { getFileFullUrl } from '@/util/file';

  export default {
    props: {
      clearValid: {
        type: Boolean,
        default: false
      },
      value: {
        type: [Array, String],
        default: () => []
      },
      limit: {
        type: Number,
        default: 5
      },
      editable: {
        type: Boolean,
        default: true
      },
      // 文件格式
      formatLimit: {
        type: String,
        default: 'jpeg,jpg,png'
      },
      imgStyle: {},
      placeholder: {
        type: String,
        default: undefined
      },
      url: {
        type: String,
        default: '/api/szyk-system/attach/put-file-attach-for-simas'
      },
      fixedNumber: {
        type: Array,
        default: () => [10, 7]
      },
      // 是否裁剪图片
      cutImage: {
        type: Boolean,
        default: false
      },
      // 图片裁剪配置信息 详见data。option
      cropOption: {
        type: Object,
        default: () => {}
      },
      // 限制上传大小
      limitSize: {
        type: Number,
        default: 20
      },
      // 文件类型
      // AUDIO 音频
      // VIDEO 视频
      // IMAGE 图片
      fileType: {
        type: String,
        default: 'IMAGE'
      },
      isDeleteValid: { type: Boolean, default: false }
    },
    data() {
      return {
        file: {},
        baseUrl,
        fileList: [],
        dialogImageUrl: '',
        dialogVisible: false,
        disabled: false,
        header: {
          Authorization: `Basic ${Base64.encode(
            `${website.clientId}:${website.clientSecret}`
          )}`,
          [website.tokenHeader]: 'bearer ' + getToken()
        },
        cropperVisible: false,
        loading: false,
        duration: 0,
        option: {
          img: '', // 裁剪图片的地址
          info: true, // 裁剪框的大小信息
          outputSize: 1, // 裁剪生成图片的质量
          outputType: 'png', //'jpeg', // 裁剪生成图片的格式
          canScale: false, // 图片是否允许滚轮缩放
          autoCrop: true, // 是否默认生成截图框
          // autoCropWidth: 300, // 默认生成截图框宽度
          // autoCropHeight: 200, // 默认生成截图框高度
          fixedBox: false, // 固定截图框大小 不允许改变
          fixed: true, // 是否开启截图框宽高固定比例
          fixedNumber: this.fixedNumber, // 截图框的宽高比例
          full: true, // 是否输出原图比例的截图
          canMoveBox: false, // 截图框能否拖动
          original: false, // 上传图片按照原始比例渲染
          centerBox: false, // 截图框是否被限制在图片里面
          infoTrue: true // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
        }
      };
    },
    methods: {
      onPreview(file) {
        console.log('onPreview', file);
        // 修复设备台账查看图片bug - 由于el-upload会自动给文件添加uid和status导致
        this.dialogImageUrl = file.filePath;

        this.dialogVisible = true;
      },
      getFileUrl(file) {
        this.file = file;
        let reader = new FileReader();
        reader.readAsDataURL(file.raw);
        reader.onload = (e) => {
          // e.target.result 就是文件读取后的结果
          const imgURL = e.target.result;

          file.raw
            ? (this.option.img = imgURL)
            : `${baseUrl}/api/szyk-system/attach/download/${file.attachId}`;
          // 而这里拿到的imgURL就可以传给vue-cropper中的img参数了
        };
      },
      onDelete(file) {
        if (this.isDeleteValid) {
          // 验证是不是需要删除
          this.$confirm('删除该图片将会清空舞台所有设置，是否继续？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              this.fileList = this.fileList.filter((a) => {
                return a !== file;
              });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除'
              });
            });
        } else {
          this.fileList = this.fileList.filter((a) => {
            return a !== file;
          });
        }
      },
      async handleSuccess(response) {
        console.log('response', response);
        this.loading = false;
        if (response.code === 200) {
          this.$emit('success', response);
        } else {
          this.handleError(response);
        }
      },
      handleError(error) {
        this.loading = false;
        this.$message.error('上传失败');
        this.$emit('error', error);
      },
      async handleChange(file, list) {
        const flag = await this.beforeUpload(file);
        if (!flag) return;

        console.log('handleChange', file, list);
        if (this.cutImage) {
          const isLt5M = file.size / 1024 / 1024 < this.limitSize;
          if (!isLt5M) {
            this.$message.error('上传文件大小不能超过 2MB!');
            return false;
          }

          // 上传成功后将图片地址赋值给裁剪框显示图片
          this.$nextTick(() => {
            this.getFileUrl(file);
            // this.option.img = this.getFileUrl(file)
            this.cropperVisible = true;
          });
        } else {
          const attachId = file.response.data.attachId;
          this.fileList.push({
            filePath: getFileFullUrl(attachId),
            id: attachId
          });
        }
      },
      // 点击裁剪，这一步是可以拿到处理后的地址
      async finish() {
        let fileName = this.file ? this.file.name : this.fileList[0].name;
        this.$refs.cropper.getCropBlob((data) => {
          let formData = new FormData();
          formData.append('file', data, fileName);
          this.loading = true;
          uploadFileSiams(formData).then(
            (res) => {
              let data = res.data.data;
              this.loading = false;
              this.fileList = [
                ...this.fileList,
                {
                  filePath: getFileFullUrl(res.data.data.attachId), // `${baseUrl}/api/szyk-system/attach/download/${data.attachId}`,
                  id: data.attachId
                }
              ];
              this.cropperVisible = false;
              if (this.clearValid) {
                this.$emit('handleSuccess');
              }
              this.$emit('success', data);
            },
            () => {
              this.loading = false;
            }
          );
        });
      },
      async beforeUpload(file) {
        let fileExt = file.name.replace(/.+\./, '');
        let formatLimit = this.formatLimit.split(',');
        let isTrueFile = formatLimit.indexOf(fileExt.toLowerCase()) !== -1;
        if (!isTrueFile) {
          this.$message.error(`上传文件限${this.formatLimit}格式!`);
        }
        const isLt2M = file.size / 1024 / 1024 < this.limitSize;
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 2MB!');
        }
        // this.duration = await getAudioTime(file);
        // this.loading = true;
        return isLt2M && isTrueFile;
      },
      onSubmit() {
        this.$refs.upload.submit();
      }
    },
    watch: {
      value: {
        immediate: true,
        handler(newValue) {
          if (typeof newValue !== 'string') {
            this.fileList = newValue;
            if (!this.editable) {
              let arr = newValue || [];
              arr.forEach((item) => {
                item.filePath = getFileFullUrl(item.attachId || item.id);
              });
              this.fileList = arr;
            }
          } else {
            this.fileList = [
              {
                filePath: `${baseUrl}/api/szyk-system/attach/download/${newValue}`,
                id: newValue
              }
            ];
          }
        }
      },
      fileList(newValue) {
        this.$emit('input', newValue);
      },
      cropOption: {
        handler(nv) {
          Object.assign(this.option, nv);
        },
        deep: true,
        immediate: true
      }
    }
  };
</script>

<style lang="scss">
  .cropper-content {
    .cropper {
      width: auto;
      height: 500px;
    }
  }

  .img-block {
    display: flex;
    flex-wrap: wrap;
    margin-right: 10px;
  }

  .avatar-uploader .el-upload {
    position: relative;
    overflow: hidden;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100px;
    height: 100px;
    color: #8c939d;
    font-size: 28px;

    div {
      margin-top: 5px;
      font-size: 10px;
    }
  }

  .avatar {
    position: relative;
    width: 100px;
    height: 100px;
    margin-right: 10px;
    margin-bottom: 10px;
    overflow: hidden;
    border-radius: 4px;

    .img {
      width: 100px;
      height: 100px;
    }

    .icon {
      width: 100px;
      height: 100px;
      font-size: 50px;
      line-height: 100px;
      text-align: center;
    }

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 999;
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 100%;
      height: 100%;
      color: #fff;
      font-size: 20px;
      text-align: center;
      background-color: rgba(0, 0, 0, 50%);
      cursor: default;
      opacity: 0;
      transition: opacity 0.3s;

      i {
        cursor: pointer;
      }
    }

    &:hover {
      .mask {
        opacity: 1;
      }
    }
  }
</style>
