<template>
  <div style="height: 100%">
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100%)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="no"
        label="工单号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.no || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentCategoryName"
        label="设备类型"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{
          row.equipmentCategoryName || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="monitorName"
        label="维修部位"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.monitorName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="faultName"
        label="故障缺陷名称"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.faultName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="bizTypeName"
        label="维修方式"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.bizTypeName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="receiveUserName"
        label="维修人/跟进人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{
          (row.bizType === 'INTERNAL'
            ? row.receiveUserName
            : row.followUserName) || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="工单生成时间"
        align="center"
        show-overflow-tooltip
        width="110px"
      ></el-table-column>
      <el-table-column
        prop="actualCompleteTime"
        label="完成时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{
          row.actualCompleteTime || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="操作"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="view(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <in-view ref="inView"></in-view>
    <ex-view ref="exView"></ex-view>
  </div>
</template>

<script>
  import Pagination from '@/components/pagination/index.vue';
  import { getRepairStatistics } from '@/api/equiment-full-life-api/statement-statistics';
  import InView from '@/views/equiment-full-life-cycle/repair/internal/detail/index.vue';
  import ExView from '@/views/equiment-full-life-cycle/repair/external/detail/index.vue';

  export default {
    name: 'DeviceBasicList',
    components: {
      Pagination,
      InView,
      ExView
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },
    mounted() {},
    methods: {
      view(row) {
        //   根据类型判断是内部维修还是外部维修，然后打开不同的详情
        if (row.bizType === 'INTERNAL') {
          this.$refs.inView.show(row.no);
        } else {
          this.$refs.exView.show(row.no);
        }
      },
      getData(search) {
        this.searchParams.current = 1;
        this.searchParams = { ...this.searchParams, ...search };
        this.getList();
      },
      //  点巡检统计 按工单
      async getList() {
        this.loading = true;
        try {
          let res = await getRepairStatistics({
            ...this.searchParams
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      }
    }
  };
</script>

<style scoped lang="scss"></style>
