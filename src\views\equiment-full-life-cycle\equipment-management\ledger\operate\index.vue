<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <info
        ref="info"
        @getAttribute="getAttribute"
        :initData="detail"
        @getCategoryId="getCategoryId"
      ></info>
      <p v-if="form.memberList.length" class="el-base-title">拓展属性</p>
      <ExtendedAttribute
        v-if="form.memberList.length"
        ref="extend"
        :isEdit="true"
        :form="form"
      ></ExtendedAttribute>
      <!--      上传文件资料-->
      <p class="el-base-title">文件资料</p>
      <file-upload
        ref="file"
        :initData="detail"
        :fileData="fileData"
        :limit="100"
        source="ledger"
      >
      </file-upload>
      <!--      上传设备图片-->
      <p class="el-base-title">设备图片</p>
      <el-form
        :model="form"
        inline
        label-suffix="："
        ref="baseForm"
        label-width="110px"
        :label-position="'right'"
        size="small"
      >
        <el-row class="add-info">
          <el-col :span="12">
            <el-form-item prop="image" label="">
              <upload-img
                v-model="form.image"
                placeholder="上传图片"
                :limit="1"
                formatLimit="jpeg,png,jpg"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <div class="oper_btn">
      <el-button
        class="el-icon-circle-plus-outline"
        size="small"
        type="primary"
        @click="submit()"
        :loading="loading"
      >
        提交</el-button
      >
      <btn type="close" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import Info from './base-info.vue';
  import ExtendedAttribute from './extended-attributes.vue';
  import FileUpload from '@/views/equiment-full-life-cycle/components/profile.vue';
  import { ledgerExpandList } from '@/api/equiment-full-life-api/classification';
  import {
    addOrEditAccount,
    getAccountDetail
  } from '@/api/equiment-full-life-api/ledger';
  import UploadImg from '@/components/uploadImage.vue';
  import { convertFileUrl } from '@/util/util';

  export default {
    name: 'AddDevice',
    components: {
      UploadImg,
      Info,
      FileUpload,
      ExtendedAttribute
    },
    props: {},
    data() {
      return {
        categoryId: undefined,
        visible: false,
        detail: { categoryId: undefined },
        extendedDetail: {}, // 拓展属性详情
        fileData: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {
          image: [],
          memberList: []
        }
      };
    },
    watch: {
      'detail.categoryId': {
        immediate: true,
        deep: true,
        async handler(val) {
          if (val) {
            await this.getAttribute(val);
          } else {
            this.form.memberList = [];
          }
        }
      }
    },
    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getAccountDetail({ id: id });
          this.detail = res.data.data;
          this.setForm();
          setTimeout(() => {
            this.setAttribute();
            this.loading = false;
          }, 800);
        } catch (e) {
          this.loading = false;
        }
      },
      // 初始化
      setForm() {
        let file =
          this.detail.equipmentFileInfoList ||
          [].map((i) => {
            return {
              ...i.attach,
              name: i.name,
              fileCategoryId: i.fileCategoryId, // 资料类型id,
              extension: this.getString(i.name), // 文件后缀类型
              type: i.type
            };
          });
        this.fileData = { id: this.detail.id, fileData: file };
        this.form.image = (this.detail.imageList || []).map((it) => {
          return {
            id: it.id,
            fileName: it.originalName,
            filePath: convertFileUrl(it.domain)
          };
        });
      },
      //  根据设备分类，获取的文件列表
      getCategoryId(id) {
        // this.categoryId = id;
        this.$refs['file'].changeFile(id);
      },
      getString(str) {
        if (str) {
          const lastDotIndex = str.lastIndexOf('.');
          let result;
          if (lastDotIndex !== -1) {
            // 截取点后面的部分
            result = str.slice(lastDotIndex + 1);
          } else {
            // 如果没有找到点，则返回原字符串
            result = str;
          }
          return result;
        } else {
          this.$message.error('文件名称返回有错误，请检查');
        }
      },
      setAttribute() {
        // 属性初始化
        let list = this.form.memberList || [];
        let valueList = this.detail.attrList || [];
        if (!valueList.length) return;
        const result = valueList.reduce((acc, item) => {
          acc[item.attrId] = item;
          return acc;
        }, {});
        list.forEach((item) => {
          let defaultV = result[item.id].attrValue;
          // 设置初始值
          if (['0', '1'].includes(item.attrType)) {
            if (['0'].includes(item.attrType)) {
              item.attributeValue = defaultV ? defaultV.split(',') : [];
            } else {
              item.attributeValue = defaultV;
            }
          } else {
            item.attributeValue = defaultV || '';
          }
        });
      },
      // 设置初始化
      setInitializationAttribute(data) {
        // 属性初始化
        let list = data || [];
        list.forEach((item) => {
          let selectData = JSON.parse(item.selectData) || [];
          item.selectData = selectData;
          // 设置初始值
          if (['0', '1'].includes(item.attrType)) {
            let defaultV = selectData.reduce((accumulator, currentValue) => {
              if (currentValue.isDefault === '1') {
                return accumulator.concat(currentValue.label);
              } else {
                return accumulator;
              }
            }, []);
            if (['0'].includes(item.attrType)) {
              item.attributeValue = defaultV;
            } else {
              item.attributeValue = defaultV.join();
            }
          } else {
            item.attributeValue = '';
          }
        });
        this.form.memberList = list;
      },
      // 获取自定义属性
      async getAttribute(id) {
        try {
          if (!id) return;
          this.loading = true;
          let {
            data: { data }
          } = await ledgerExpandList({ id });
          this.setInitializationAttribute(data);
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
      show(id) {
        this.visible = true;
        this.edit = !!id;
        this.eqId = id;
        if (id) {
          this.getDetail(id);
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.form.image = [];
        this.$refs['info'].resetForm();
        this.$refs['file'].resetForm();
        this.detail = {};
      },
      async submit() {
        const imageId = this.form.image
          .map((it) => {
            return it.id;
          })
          .join(',');
        let file = await this.$refs['file'].validForm();
        let params = await this.$refs['info'].validForm();
        let attrList = [];
        if (this.form.memberList.length) {
          attrList = await this.$refs['extend'].validForm();
        }
        await this.save({
          id: this.edit ? this.eqId : undefined,
          nfc: this.edit ? this.detail.nfc : undefined,
          ...params,
          imageId: imageId,
          equipmentFileList: file,
          attrList
        });
      },

      // 提交设备
      async save(params) {
        this.loading = true;
        try {
          await addOrEditAccount(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
