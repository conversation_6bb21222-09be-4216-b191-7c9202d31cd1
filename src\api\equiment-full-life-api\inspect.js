import request from '@/router/axios';
//  点巡检相关接口

// 分页列表

// 点检标准详情
export const userDetail = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-standard/detail`,
    method: 'get',
    params
  });
};
// 点检标准新增或修改该
export const userAddOrUpdate = (data) => {
  return request({
    url: `/api/szyk-simas/inspect-standard/submit`,
    method: 'post',
    data
  });
};
// 点检标准清空标准
export const userClear = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-standard/clear`,
    method: 'post',
    params
  });
};

// 点检标准列表接口
export const getStandardList = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-standard/page`,
    method: 'get',
    params
  });
};
// 选择部位下拉列表
export const getPartList = (params) => {
  return request({
    url: `/api/szyk-common/equipment-account/monitor-select`,
    method: 'get',
    params
  });
};

//设备点检计划表相关接口
export const getPlanList = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/page`,
    method: 'get',
    params
  });
};

// 点检计划详情列表
export const getPlanDetail = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/detail`,
    method: 'get',
    params
  });
};

//  点检计划详情 - 接口 新出
export const getPlanDetailNew = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/view`,
    method: 'get',
    params
  });
};
// 点检计划新增接口
export const addPlan = (data) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/save`,
    method: 'post',
    data
  });
};
// 点检计划修改接口
export const updatePlan = (data) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/update`,
    method: 'post',
    data
  });
};
// 点检计划逻辑删除接口
export const deletePlan = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/remove`,
    method: 'post',
    params
  });
};

// 点击按计划手动开始
export const startPlan = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/manual-start`,
    method: 'post',
    params
  });
};

// 点检计划手动停止
export const stopPlan = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/manual-stop`,
    method: 'post',
    params
  });
};

// 点检计划手动执行生成当天工单
export const manualPlan = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/manual-generate-orders`,
    method: 'post',
    params
  });
};

// 点检工单相关接口 -----
export const getOrderList = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-order/page`,
    method: 'get',
    params
  });
};

// 点检工单详情
export const getOrderDetail = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-order/detail`,
    method: 'get',
    params
  });
};

// 点检统计相关接口
//  工单完成情况
export const getStatistics = (params) => {
  return request({
    url: `/api/szyk-simas/statistics/inspect-order`,
    method: 'get',
    params
  });
};
//  点巡检异常统计
export const getStatisticsException = (params) => {
  return request({
    url: `/api/szyk-simas/statistics/inspect-abnormal`,
    method: 'get',
    params
  });
};
// 点巡检任务统计
export const getStatisticsTask = (params) => {
  return request({
    url: `/api/szyk-simas/statistics/inspect-task`,
    method: 'get',
    params
  });
};
// 点检计划统计分页
export const getStatisticsPlan = (params) => {
  return request({
    url: `/api/szyk-simas/statistics/inspect-plan`,
    method: 'get',
    params
  });
};
// 工单中审核确认
export const auditOrder = (data) => {
  return request({
    url: `/api/szyk-simas/inspect-order/confirm`,
    method: 'post',
    data
  });
};
// 工单 - 批量审核
export const batchAuditOrder = (data) => {
  return request({
    url: `/api/szyk-simas/inspect-order/confirmBatch`,
    method: 'post',
    data
  });
};
export const getInspectDeviceList = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-plan/planDevicePage`,
    method: 'get',
    params
  });
};
export const getInspectOrderList = (params) => {
  return request({
    url: `/api/szyk-simas/inspect-order/page`,
    method: 'get',
    params
  });
};

// 检修 - 提交检修相关
export const simasInspectOrderApi = (data) => {
  return request({
    url: `/api/szyk-simas/inspect-order/inspect`,
    method: 'post',
    data
  });
};
