<template>
  <basic-container>
    <search :show-search="showSearch" @search="search" />
    <el-row :gutter="15">
      <el-col :span="1.5">
        <el-button
          v-if="permission.menu_add"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-if="permission.menu_delete"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="delDisabled"
          @click="checkDelete"
          >删除</el-button
        >
      </el-col>
      <yk-right-tool
        :show-search.sync="showSearch"
        :columns="columns"
        @queryTable="request"
      ></yk-right-tool>
    </el-row>
    <table-list
      v-loading="loading"
      type="pc"
      :columns="columns"
      :list="list"
      @dispatch="dispatch"
    />
    <model
      :title="title"
      :visited="visited"
      :is-edit="isEdit"
      :id="rowId"
      :child-id="childId"
      @close="handleClose"
      @refresh="request"
    />
  </basic-container>
</template>

<script>
  import { Search, TableList } from '../components';
  import Model from './components/model';
  import { getLazyList, remove, removeCheck } from '@/api/system/menu';
  import { mapGetters } from 'vuex';

  const columns = [
    { key: 0, label: `菜单名称`, visible: true },
    { key: 1, label: `路由地址`, visible: true },
    { key: 2, label: `菜单图标`, visible: true },
    { key: 3, label: `菜单类型`, visible: true },
    { key: 4, label: `菜单状态`, visible: true },
    { key: 5, label: `是否缓存`, visible: true },
    { key: 6, label: `菜单排序`, visible: true },
    { key: 7, label: `权限字符`, visible: true }
  ];

  export default {
    name: 'pcMenu',
    components: {
      Search,
      TableList,
      Model
    },
    data() {
      return {
        columns,
        queryParams: {},
        showSearch: true,
        loading: false,
        list: [],
        title: '添加菜单',
        visited: false,
        isEdit: false,
        delDisabled: true,
        rowIds: [],
        rowId: '',
        childId: ''
      };
    },
    watch: {
      rowIds: {
        handler(newVal) {
          this.delDisabled = !newVal.length;
        },
        deep: true
      }
    },
    computed: {
      ...mapGetters(['permission'])
    },
    methods: {
      // 查询操作
      search(params = {}) {
        this.queryParams = Object.assign({}, params);
        this.request();
      },
      // 列表api查询
      async request() {
        try {
          this.loading = true;
          const res = await getLazyList('', this.queryParams, 'PC');
          this.list = res.data.data;
        } catch (err) {
          console.error(err);
        } finally {
          this.loading = false;
        }
      },
      // 新增
      handleAdd() {
        this.title = '添加菜单';
        this.visited = true;
        this.isEdit = false;
      },
      // 关闭弹框
      handleClose() {
        this.visited = false;
        this.rowId = '';
        this.childId = '';
      },
      // 列表事件
      dispatch(type, row) {
        switch (type) {
          case 'add_child': // 新增子项
            return this.handleAddChild(row);
          case 'edit': // 编辑
            return this.handleEdit(row);
          case 'delete': // 列表单个删除
            return this.handleDelete(row);
          case 'selections': // 批量删除
            return (this.rowIds = row);
        }
      },
      // 新增子项
      handleAddChild(row) {
        this.title = '新增子项';
        this.visited = true;
        this.isEdit = false;
        this.childId = row.id;
      },
      // 编辑
      handleEdit(row) {
        this.title = '编辑菜单';
        this.visited = true;
        this.isEdit = true;
        this.rowId = row.id;
      },
      // 删除
      handleDelete(row) {
        // 检验是否有下级菜单
        let msg;
        if (row.hasChildren) {
          msg = '当前菜单存在下级菜单，是否继续删除？';
        } else {
          msg = '确定删除选择的数据吗？';
        }
        this.delCommon(msg, row.id);
      },
      // 批量删除
      async checkDelete() {
        // 检验是否有下级菜单
        let msg;
        try {
          const { data } = await removeCheck(this.rowIds.toString());
          if (!data.success) {
            msg = '当前菜单存在下级菜单，是否继续删除？';
          } else {
            msg = '确定删除选择的数据吗？';
          }
          this.delCommon(msg, this.rowIds.toString());
        } catch (e) {
          console.error(e);
        }
      },
      // 删除的通用操作
      delCommon(msg, value) {
        this.$confirm(msg, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return remove(value);
          })
          .then(() => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.request();
          })
          .catch(() => {});
      }
    }
  };
</script>
