<template>
  <el-table size="small" :data="list" border>
    <el-table-column
      label="#"
      type="index"
      width="50"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="name"
      label="耗材名称"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="model"
      label="耗材类型"
      align="center"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{ row.model || '-' }}</template>
    </el-table-column>
    <el-table-column
      prop="count"
      label="耗材数量"
      align="center"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{ row.count }}</template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
