export const getRouterName = (module) => {
  switch (module) {
    case 'INSPECT_ORDER': //点巡检工单
      return 'inspectWorkOrder';
    case 'MAINTAIN_ORDER': //保养工单
      return 'maintainOrder';
    case 'INTERNAL_REPAIR': //内部维修
      return 'repairInternalDispatch';
    case 'EXTERNAL_REPAIR': //外委维修
      return 'repairExternalDispatch';
    case 'LUBRICATE_ORDER': //润滑工单
      return 'lubricationDetail';
    case 'OVERHAUL_ORDER': //检修工单
      return 'overhaulDetail';
    default:
      return '';
  }
};
// 字典过滤 后期添加润滑工单(暂时废弃, 字典不便于租户同步)
export const filterDict = [
  'INSPECT_ORDER',
  'MAINTAIN_ORDER',
  'INTERNAL_REPAIR',
  'EXTERNAL_REPAIR',
  'OVERHAUL_ORDER',
  'LUBRICATE_ORDER'
];

// 详情-工单类型
export const orderTypeDict = [
  { label: '点巡检工单', value: 'INSPECT_ORDER' },
  { label: '保养工单', value: 'MAINTAIN_ORDER' },
  { label: '内部维修', value: 'INTERNAL_REPAIR' },
  { label: '外委维修', value: 'EXTERNAL_REPAIR' },
  { label: '润滑工单', value: 'LUBRICATE_ORDER' },
  { label: '计划性检修工单', value: 'OVERHAUL_ORDER' }
];
export const getFileName = (name) => {
  if (name.indexOf('.') === -1) {
    return '-';
  }
  const match = name.match(/^(.*?)(\.[^.]*$|$)/);
  return match ? match[1] : name;
};
