<template>
  <div>
    <el-dialog
      title="用户角色配置"
      append-to-body
      :visible.sync="roleBox"
      width="345px"
    >
      <el-tree
        :data="roleGrantList"
        show-checkbox
        check-strictly
        default-expand-all
        node-key="id"
        ref="treeRole"
        :default-checked-keys="roleTreeObj"
        :props="props"
      >
      </el-tree>

      <span slot="footer" class="dialog-footer">
        <el-button @click="roleBox = false">取 消</el-button>
        <el-button type="primary" @click="submitRole">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
  import { grant } from '@/api/system/user';

  export default {
    name: 'UserRoleSet',
    props: {
      ids: String,
      roleBoxVisible: {
        type: Boolean,
        default: false
      },
      roleGrantList: {
        type: Array,
        default() {
          return [];
        }
      },
      roleTreeObj: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    data() {
      return {
        props: {
          label: 'title',
          value: 'key'
        }
      };
    },
    methods: {
      submitRole() {
        const roleList = this.$refs.treeRole.getCheckedKeys().join(',');
        grant(this.ids, roleList).then(() => {
          this.roleBox = false;
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.$emit('dispatch', 'refresh');
        });
      }
    },
    computed: {
      roleBox: {
        get() {
          return this.roleBoxVisible;
        },
        set() {
          this.$emit('close');
        }
      }
    }
  };
</script>
<style lang="scss" scoped></style>
