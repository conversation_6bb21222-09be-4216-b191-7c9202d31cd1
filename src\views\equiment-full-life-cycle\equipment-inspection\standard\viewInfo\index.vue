<template>
  <basic-container :autoHeight="true">
    <div class="top-info">
      <search ref="search" @query="search"></search>
      <!-- <el-button
        v-if="permission['inspect-standard-add-edit-export']"
        icon="el-icon-download"
        type="primary"
        size="small"
        @click="importExcel"
        >导入</el-button
      > -->
    </div>
    <el-table
      ref="table"
      size="small"
      :data="list"
      border
      stripe
      v-loading="loading"
      height="calc(100% - 180px)"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        prop="code"
        label="设备编号"
        align="center"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="sn"
        label="SN编号"
        align="center"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="model"
        label="规格型号"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.model || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="belongDeptName"
        label="归属部门"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="responsiblePersonName"
        label="负责人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.responsiblePersonName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.useDeptName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="userName"
        label="使用人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.userName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="categoryName"
        label="设备类型"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.categoryName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.createUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.updateUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        width="150"
        label="更新时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.updateTime || '-' }} </template>
      </el-table-column>

      <el-table-column
        prop=""
        label="操作"
        align="center"
        show-overflow-tooltip
        width="180px"
        fixed="right"
      >
        <template slot-scope="{ row }">
          <section class="cell-operate-class">
            <el-button size="mini" type="text" @click="view(row)"
              >查看</el-button
            >
            <el-button
              v-if="
                row.standardCount === 0 &&
                permission['inspect-standard-add-edit-export']
              "
              size="mini"
              type="text"
              @click="configuration(row, 'ADD')"
              >新增</el-button
            >
            <el-button
              v-if="
                row.standardCount > 0 &&
                permission['inspect-standard-add-edit-export']
              "
              size="mini"
              type="text"
              @click="configuration(row, 'EDIT')"
              >编辑</el-button
            >
            <el-button
              class="danger-btn"
              @click="handleDelete(row)"
              v-if="
                row.standardCount > 0 && permission['inspect-standard-clear']
              "
              slot="reference"
              type="text"
              size="small"
              >清空标准</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <import-files
      ref="file"
      :templateUrl="'/szyk-simas/inspect-standard/export-template'"
      action="/szyk-simas/inspect-standard/import-data"
      downLoadFileName="点检标准模板"
      :isCovered="true"
      :isInstanceExport="true"
      @refresh="getList"
    ></import-files>
    <point-dialog ref="pointDialog" @success="getList"></point-dialog>
    <!--     查看详情-->
    <view-info ref="viewInfo"></view-info>
  </basic-container>
</template>

<script>
  import Search from './search';
  import PointDialog from '../operate/index.vue';
  import ViewInfo from '../detail/index.vue';
  import ImportFiles from '@/components/import-files';

  import {
    userClear,
    getStandardList
  } from '@/api/equiment-full-life-api/inspect';
  import { getToken } from '@/util/auth';
  import { downloadFileBlob } from '@/util/util';
  import { mapGetters } from 'vuex';

  export default {
    name: 'bearingLibraryIndex',
    components: { PointDialog, Search, ViewInfo, ImportFiles },
    props: {
      categoryId: {
        // 设备id
        type: String,
        default: () => {
          return '';
        }
      }
    },
    computed: {
      ...mapGetters(['permission'])
    },
    data() {
      return {
        searchParams: {
          size: 10,
          current: 1
        },
        total: 0,
        loading: false,
        list: [],
        num: 0
      };
    },
    watch: {
      categoryId: {
        handler(val) {
          if (val) {
            this.getList();
          }
        }
      }
    },
    mounted() {
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/equipment-account/export-standard-template?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/equipment-account/export-standard-template?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '点检标准模板.xlsx'
        );
      },
      importExcel() {
        this.$refs.file.show();
      },

      view(row) {
        this.$refs['viewInfo'].show(row.id);
      },
      //  点击删除
      async handleDelete(row) {
        this.$confirm(
          `是否确定清空${row.code}-${row.name}的点巡检标准？清空不影响已生成的工单，但是此设备将不再生成新的工单！`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(async () => {
            try {
              await userClear({ equipmentId: row.id });
              this.$message({
                type: 'success',
                message: '清除成功'
              });
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },
      // 點擊搜索
      search(params) {
        this.searchParams = { ...this.searchParams, ...params };
        this.getList();
      },
      configuration(row, type) {
        this.$refs['pointDialog'].show(row.id, type);
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await getStandardList({
            categoryId: this.categoryId,
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
          this.loading = false;
        } catch (message) {
          this.loading = false;
          console.log('err', message);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-scrollbar__view {
      height: 98% !important;
    }
  }

  .top-info {
    margin-bottom: 10px;
  }
</style>
