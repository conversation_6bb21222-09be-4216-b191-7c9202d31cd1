<template>
  <el-row>
    <el-col :span="5" style="margin-bottom: 0">
      <basic-container :autoHeight="true">
        <simas-position-tree
          ref="categoryTree"
          :hiddenButtons="true"
          @category-change="categoryChange"
        />
      </basic-container>
    </el-col>
    <el-col :span="19" style="margin-bottom: 0">
      <view-info
        :locationId="deviceId"
        @dispatch="dispatch"
        ref="viewInfo"
        where="position-bearing"
      ></view-info>
    </el-col>
    <AddContent ref="content" @save-success="refresh"></AddContent>
  </el-row>
</template>

<script>
  import ViewInfo from './viewInfo/index.vue';
  import SimasPositionTree from './simas-position-tree.vue';
  import AddContent from './components/add-content.vue';
  import { ledgerExpandDel } from '@/api/equiment-full-life-api/classification';
  export default {
    name: 'DeviceBasic',
    components: {
      SimasPositionTree,
      ViewInfo,
      AddContent
    },
    data() {
      return {
        loading: false,
        list: [],
        deviceId: undefined, // 部位id
        treeNode: {} // 项
      };
    },

    mounted() {},
    methods: {
      dispatch(type, row) {
        switch (type) {
          case 'add':
            this.handleAdd(row);
            break;
          case 'edit':
            this.handleEdit(row);
            break;
          case 'del':
            this.handleDel(row);
            break;
          default:
            break;
        }
      },
      async handleDel(row) {
        await ledgerExpandDel({ ids: row.id });
        this.refresh();
        this.$message.success('操作成功');
      },
      handleEdit(row) {
        this.$refs.content.show(this.treeNode, row);
      },
      handleAdd() {
        this.$refs.content.show(this.treeNode);
      },
      refresh() {
        this.$refs.viewInfo.getList();
      },
      categoryChange(node) {
        console.log('node', node);
        this.treeNode = node.data;
        this.deviceId = node.data.id;
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    margin-bottom: 10px;
  }

  ::v-deep {
    .el-tabs__content {
      flex: 1;

      // overflow: auto;
    }

    .el-tabs__header {
      margin: 0 0 10px;
    }
  }
</style>
