<template>
  <el-table size="small" :data="list" border stripe>
    <el-table-column label="#" type="index" width="50"></el-table-column>
    <el-table-column prop="monitorName" label="润滑部位">
      <template v-slot="{ row }">{{
        row.equipmentMonitorName || '-'
      }}</template>
    </el-table-column>
    <el-table-column prop="methodsName" label="润滑方式" show-overflow-tooltip>
      <template v-slot="{ row }">{{ row.methodsName || '-' }}</template>
    </el-table-column>
    <el-table-column
      prop="oilTypeName"
      label="油品类型"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="lubricateCycle"
      label="润滑周期"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{ row.lubricateCycle || '-' }}天</template>
    </el-table-column>

    <el-table-column
      prop="abnormalComment"
      label="异常描述"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{ row.abnormalComment || '-' }}</template>
    </el-table-column>
    <el-table-column
      prop="abnormalLevelName"
      label="异常等级"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{ row.abnormalLevelName || '-' }} </template>
    </el-table-column>
    <el-table-column
      prop="isHandledName"
      label="是否现场处理"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{ row.isHandledName || '-' }}</template>
    </el-table-column>
    <el-table-column prop="attachList" label="异常图片" width="80">
      <template v-slot="{ row }">
        <el-image
          v-if="row.attachList"
          v-for="img in row.attachList"
          :key="img.id"
          style="width: 50px; height: 50px; margin: 10px"
          :src="getFileFullUrl(img.id)"
          fit="cover"
          :preview-src-list="[getFileFullUrl(img.id)]"
        ></el-image>
      </template>
    </el-table-column>
    <el-table-column prop="lastTime" label="上次润滑时间" show-overflow-tooltip>
      <template v-slot="{ row }">{{ row.lastTime || '-' }}</template>
    </el-table-column>
    <el-table-column prop="planTime" label="计划润滑时间" show-overflow-tooltip>
      <template v-slot="{ row }">{{ row.planTime || '-' }}</template>
    </el-table-column>
    <el-table-column
      prop="executeTime"
      label="本次润滑时间"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{ row.executeTime || '-' }}</template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { getFileFullUrl } from '@/util/file';

  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return { getFileFullUrl };
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
