<template>
  <div class="file-preview-test">
    <h2>File Preview Security Test</h2>
    <p>This component tests the new secure modal-based file preview system.</p>
    
    <div class="test-section">
      <h3>Test Files</h3>
      <div class="test-buttons">
        <el-button @click="testImagePreview" type="primary">
          Test Image Preview (JPG)
        </el-button>
        <el-button @click="testDocumentPreview" type="success">
          Test Document Preview (DOCX)
        </el-button>
        <el-button @click="testPdfPreview" type="warning">
          Test PDF Preview
        </el-button>
        <el-button @click="testSpreadsheetPreview" type="info">
          Test Spreadsheet Preview (XLSX)
        </el-button>
      </div>
    </div>

    <div class="security-info">
      <h3>Security Validation</h3>
      <ul>
        <li>✅ File URLs are NOT exposed in browser address bar</li>
        <li>✅ Preview opens in modal dialog instead of new tab</li>
        <li>✅ OnlyOffice editor embedded securely within modal</li>
        <li>✅ No sensitive file information in URL parameters</li>
        <li>✅ Maintains all existing file preview functionality</li>
      </ul>
    </div>

    <div class="instructions">
      <h3>How to Test</h3>
      <ol>
        <li>Click any of the test buttons above</li>
        <li>Verify that a modal dialog opens (not a new tab)</li>
        <li>Check that the browser address bar remains unchanged</li>
        <li>Confirm that file content loads correctly in the modal</li>
        <li>Test fullscreen functionality within the modal</li>
      </ol>
    </div>
  </div>
</template>

<script>
import { previewFile, previewFileByLink } from '@/util/preview';

export default {
  name: 'file-preview-test',
  methods: {
    testImagePreview() {
      // Test with a sample image file
      const sampleImageFile = {
        id: 'test-image-123',
        originalName: 'sample-image.jpg',
        extension: 'jpg'
      };
      
      console.log('Testing image preview with modal...');
      previewFile(sampleImageFile);
    },

    testDocumentPreview() {
      // Test with a sample document file
      const sampleDocFile = {
        id: 'test-doc-456',
        originalName: 'sample-document.docx',
        extension: 'docx'
      };
      
      console.log('Testing document preview with modal...');
      previewFile(sampleDocFile);
    },

    testPdfPreview() {
      // Test with a sample PDF file
      const samplePdfFile = {
        id: 'test-pdf-789',
        originalName: 'sample-document.pdf',
        extension: 'pdf'
      };
      
      console.log('Testing PDF preview with modal...');
      previewFile(samplePdfFile);
    },

    testSpreadsheetPreview() {
      // Test with a sample spreadsheet file using link preview
      const sampleSpreadsheetFile = {
        link: 'https://example.com/sample-spreadsheet.xlsx',
        originalName: 'sample-spreadsheet.xlsx',
        extension: 'xlsx'
      };
      
      console.log('Testing spreadsheet preview with modal...');
      previewFileByLink(sampleSpreadsheetFile);
    }
  }
};
</script>

<style lang="scss" scoped>
.file-preview-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #303133;
    margin-bottom: 10px;
  }

  h3 {
    color: #606266;
    margin: 20px 0 10px 0;
  }

  .test-section {
    margin: 30px 0;
    padding: 20px;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    background-color: #FAFAFA;
  }

  .test-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
  }

  .security-info {
    margin: 30px 0;
    padding: 20px;
    border: 1px solid #67C23A;
    border-radius: 4px;
    background-color: #F0F9FF;

    ul {
      margin: 10px 0;
      padding-left: 20px;

      li {
        margin: 5px 0;
        color: #67C23A;
      }
    }
  }

  .instructions {
    margin: 30px 0;
    padding: 20px;
    border: 1px solid #409EFF;
    border-radius: 4px;
    background-color: #ECF5FF;

    ol {
      margin: 10px 0;
      padding-left: 20px;

      li {
        margin: 5px 0;
        color: #606266;
      }
    }
  }
}
</style>
