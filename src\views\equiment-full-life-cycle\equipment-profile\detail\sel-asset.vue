<template>
  <div>
    <el-table
      class="table"
      :data="detail.categoryList"
      border
      :header-cell-style="{ background: '#fafafa' }"
      size="small"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="categoryName"
        label="设备类型名称"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="pathName"
        label="路径"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.pathName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="remark"
        label="类型编码"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.remark || '-' }}</template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: 'DeviceBasicList',
    props: {
      detail: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.list = this.detail.categoryList;
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: []
      };
    },

    mounted() {},
    methods: {}
  };
</script>

<style scoped lang="scss"></style>
