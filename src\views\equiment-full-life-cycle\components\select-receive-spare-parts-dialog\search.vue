<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      label-width="110px"
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      label-position="left"
      class="search-form"
    >
      <el-form-item label="库房" prop="warehouseId" label-width="60px">
        <el-select
          v-model="form.warehouseId"
          placeholder="请选择库房"
          clearable
          filterable
          :disabled="!!warehouseId"
        >
          <el-option
            v-for="item in whorehouseList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备品备件名称" prop="dictName">
        <el-input
          v-model.trim="form.dictName"
          placeholder="请输入备品备件名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="备品备件编号" prop="dictNo">
        <el-input
          style="width: 100%"
          v-model.trim="form.dictNo"
          placeholder="请输入备品备件编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    props: {
      whorehouseList: {
        type: Array,
        default: () => []
      },
      warehouseId: {
        type: String,
        default: () => {
          return undefined;
        }
      }
    },
    watch: {
      warehouseId: {
        immediate: true,
        handler(val) {
          this.$nextTick(() => {
            this.form.warehouseId = val;
            this.submit();
          });
        }
      }
    },
    data() {
      return {
        form: {
          warehouseId: undefined,
          dictName: undefined,
          dictNo: undefined
        }
      };
    },
    created() {
      // 如果没有值
      // if (!this.warehouseId) {
      //   this.$nextTick(() => {
      //     this.submit();
      //   });
      // }
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        if (this.warehouseId) {
          this.form.warehouseId = this.warehouseId;
        }
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
