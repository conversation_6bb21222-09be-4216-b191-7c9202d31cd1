<template>
  <dialog-popup
    title="选择备品备件"
    ref="dialogs"
    :visible="visible"
    @closed="closed"
    width="85%"
    class="selectSensor"
    v-if="visible"
  >
    <search
      ref="search"
      :whorehouseList="whorehouseList"
      @search="onSubmit"
      :warehouseId="warehouseId"
    ></search>
    <el-row>
      <el-row>
        <el-col :span="24" style="height: 460px; overflow-y: scroll">
          <el-table
            ref="multipleTable"
            v-loading="loading"
            :data="dataSource"
            border
            stripe
            size="small"
            :key="key"
            row-key="id"
            :headerCellStyle="{ background: '#fafafa' }"
            :reserve-selection="true"
            @select="handleCheckBox"
            @select-all="handleSelectAll"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              property="dictNo"
              label="备品备件编号"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              property="dictName"
              label="备品备件名称"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              property="model"
              label="设备型号"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.model || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              property="measureUnitName"
              label="计量单位"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.measureUnitName || '- ' }}
              </template>
            </el-table-column>
            <el-table-column
              property="warehouseName"
              label="库房名称"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              property="currentQuantity"
              label="当前数量"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.currentQuantity || 0 }}
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" style="height: 100%">
          <pagination
            :page-size.sync="searchParams.size"
            :page-no.sync="searchParams.current"
            :total="total"
            @pagination="getList"
          />
        </el-col>
        <el-col :span="12" style="height: 100%">
          <div
            style="
              display: flex;
              align-items: flex-end;
              justify-content: flex-end;
            "
          >
            <btn type="confirm" @click="confirm" :loading="loading"></btn>
            <btn type="cancel" @click="closed"></btn>
          </div>
        </el-col>
      </el-row>
    </el-row>
  </dialog-popup>
</template>
<script>
  import {
    getCheckRepairTypeListApi,
    getSparePartsStockListApi
  } from '@/api/equiment-full-life-api/spare-parts';
  import Search from '@/views/equiment-full-life-cycle/components/select-receive-spare-parts-dialog/search.vue';

  export default {
    components: { Search },
    props: {
      // 是否展示库房搜索
      showWarehouseSearch: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        warehouseId: '',
        visible: false,
        loading: false,
        dataSource: [],
        total: 0,
        searchParams: {
          size: 10,
          current: 1
        },
        key: 0,
        // 库房列表
        whorehouseList: [],
        // 所有页上多选的数据之和
        allPageSelect: [],
        originLength: 0 // 已存在的长度
      };
    },
    watch: {},
    methods: {
      onSubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      getKey() {
        return Date.now() + '';
      },
      handleCheckBox(rows, row) {
        if (rows.includes(row)) {
          // 新勾选
          this.$set(row, 'num', 1);
          this.allPageSelect.push(row);
        } else {
          // 取消勾选
          this.$set(row, 'num', undefined);
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item.id !== row.id
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        if (rows.length) {
          rows.forEach((row) => {
            this.$set(row, 'num', 1);
            if (!this.allPageSelect.find((item) => item.id === row.id)) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.dataSource.forEach((row) => {
            this.$set(row, 'num', undefined);
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item.id !== row.id
            );
          });
        }
      },

      handleClose(row) {
        this.$set(row, 'num', undefined);
        this.$refs.multipleTable.toggleRowSelection(row);
        let index = this.allPageSelect.findIndex((it) => it.id === row.id);
        index !== -1 && this.allPageSelect.splice(index, 1);
      },
      search() {
        this.searchParams.current = 1;
        this.getList();
      },
      //  获取库房列表
      async getStoreHouseList() {
        let res = await getCheckRepairTypeListApi();
        this.whorehouseList = res.data.data;
      },
      async show(originList, warehouseId) {
        this.resetFrom();
        this.warehouseId = warehouseId;
        this.allPageSelect = originList; // deepClone(originList);
        this.visible = true;
        this.searchParams.current = 1;
        this.showWarehouseSearch && (await this.getStoreHouseList());
        // await this.getList(); // 部位列表
        this.key++;
        if (originList && originList.length) {
          originList.forEach((item) => {
            const row = this.dataSource.find((m) => item.id === m.id);
            if (row) {
              this.$nextTick(() => {
                this.$refs.multipleTable.toggleRowSelection(row, true);
              });
            }
          });
        }
      },

      resetFrom() {
        this.searchParams = {
          size: 10,
          current: 1
        };
        this.allPageSelect = [];
      },
      closed() {
        this.visible = false;
        this.warehouseId = '';
        this.allPageSelect = [];
      },
      // 点击clearable时触发
      clear() {
        this.searchParams.current = 1;
        this.getList();
      },
      //
      async getList() {
        this.loading = true;
        try {
          let params = {
            ...this.searchParams
          };
          let res = await getSparePartsStockListApi(params);
          let data = res.data.data.records || [];
          // console.log('this.allPageSelect', this.allPageSelect);
          this.dataSource = data.map((item) => {
            const mathingObj = this.allPageSelect.find((m) => item.id === m.id);
            if (mathingObj) {
              return { ...mathingObj, ...item };
            } else {
              return item;
            }
          });
          this.$nextTick(() => {
            this.dataSource.map((item) => {
              if (item.num) {
                this.$refs.multipleTable.toggleRowSelection(item, true);
              }
            });
            this.total = res.data.data.total;
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      confirm() {
        if (this.allPageSelect.length === 0) {
          this.$message.warning('请选择备品备件！');
          return;
        }
        this.$emit('getAssetList', this.allPageSelect);
        this.visible = false;
      },
      onOneChoose(row) {
        this.$emit('on-choose', row);
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .el-pagination__sizes {
    display: none !important;
  }

  .search {
    margin-bottom: 10px;
  }

  /deep/ .pagination-container {
    text-align: left !important;
  }

  .clear {
    float: right;
  }

  /deep/ .el-pagination__jump {
    display: none !important;
  }

  .right {
    margin-left: 20px;
  }

  /deep/ {
    .el-form-item--small {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      padding: 10px 0;
    }
  }

  .table {
    :deep(.el-table__cell) {
      padding: 8px 0 !important;
    }
  }

  .selectSensor {
    :deep(.el-dialog__body) {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  ::v-deep {
    .el-input-number--mini {
      width: 100px !important;
    }
  }
</style>
