<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-if="permission['spare-in-add-edit']"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      stripe
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        align="center"
        prop="no"
        label="入库单号"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.no || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="inboundTypeName"
        label="入库类型"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="supplierName"
        label="供应商"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.supplierName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="warehouseName"
        label="入库库房"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="inboundDate"
        label="入库日期"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="inboundUserName"
        label="入库人"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.createUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.updateUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        width="150"
        align="center"
        label="更新时间"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.updateTime || '-' }} </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="120" fixed="right">
        <template v-slot="{ row }">
          <section class="cell-operate-class">
            <el-button
              v-if="permission['spare-in-remark']"
              type="text"
              size="small"
              @click="remark(row)"
              >备注</el-button
            >
            <el-button
              v-if="permission['spare-in-view']"
              type="text"
              size="small"
              @click="detail(row)"
              >查看</el-button
            >
            <!--          status = 1 已完成   2 已撤销-->
            <!--            <el-button-->
            <!--              type="text"-->
            <!--              size="small"-->
            <!--              @click="operate(row)"-->
            <!--              v-if="row.status === 2 && permission['spare-in-add-edit']"-->
            <!--              >编辑</el-button-->
            <!--            >-->

            <!--            <el-popconfirm-->
            <!--              title="确定删除吗？"-->
            <!--              @confirm="() => handleDelete(row)"-->
            <!--              v-if="row.status === 2 && permission['spare-in-delete']"-->
            <!--            >-->
            <!--              <el-button-->
            <!--                class="danger-btn"-->
            <!--                slot="reference"-->
            <!--                type="text"-->
            <!--                size="small"-->
            <!--                style="margin-left: 10px"-->
            <!--                >删除</el-button-->
            <!--              >-->
            <!--            </el-popconfirm>-->
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex"></detail-index>
    <!-- 备注 -->
    <el-dialog
      title="备注"
      append-to-body
      width="35%"
      :visible.sync="remarkVisible"
    >
      <el-form
        class="remark-form"
        inline
        ref="form"
        :model="remarkForm"
        :rules="rules"
        size="small"
        label-width="90px"
        :label-position="'right'"
      >
        <el-form-item label="备注：" prop="remark">
          <el-input
            type="textarea"
            v-model="remarkForm.remark"
            placeholder="请输入备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="saveRemark">确 定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import { convertFileUrl } from '@/util/util';
  import {
    getSparePartsInListApi,
    cancelSparePartsInOutApi,
    delSparePartsInOutApi,
    addSparePartsRemarkApi
  } from '@/api/equiment-full-life-api/spare-parts';

  import { mapGetters } from 'vuex';
  export default {
    name: 'PutInStorage',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination
    },
    props: {},
    computed: {
      ...mapGetters(['permission', 'userInfo'])
    },
    data() {
      return {
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        // 备注
        currentRow: undefined,
        remarkVisible: false,
        remarkForm: {
          remark: ''
        },
        rules: {
          remark: [
            {
              required: true,
              message: '请输入备注',
              trigger: 'blur'
            }
          ]
        }
      };
    },

    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      //  撤销
      examine(row) {
        this.$confirm(`确定撤销此入库单吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await cancelSparePartsInOutApi({
                no: row.no
              });
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getSparePartsInListApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      // 查看
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      },
      async handleDelete(row) {
        this.loading = true;
        try {
          await delSparePartsInOutApi({ id: row.id });
          if (this.list.length === 1) {
            this.searchParams.current = 1;
          }
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          await this.getList();
        } catch (e) {
          this.loading = false;
        }
      },
      // 备注
      remark(row) {
        this.currentRow = row;
        this.remarkVisible = true;
      },
      saveRemark() {
        this.$refs.form.validate(async (valid) => {
          if (!valid) return;
          try {
            const { data } = await addSparePartsRemarkApi({
              id: this.currentRow.id,
              completionRemark: this.remarkForm.remark
            });
            if (data.code === 200) {
              this.$message({
                type: 'success',
                message: '操作成功'
              });
              this.cancel();
            }
          } catch (e) {
            console.error(e);
          }
        });
      },
      cancel() {
        this.$refs.form.resetFields();
        this.currentRow = undefined;
        this.remarkVisible = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }

  .remark-form ::v-deep {
    .el-form-item {
      width: 100%;
    }

    .el-form-item__content {
      width: calc(100% - 90px);
    }
  }

  .btn-wrapper {
    text-align: center;
  }
</style>
