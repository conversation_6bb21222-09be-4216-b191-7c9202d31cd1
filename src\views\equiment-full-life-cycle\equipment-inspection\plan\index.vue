<template>
  <basic-container class="table-content" :auto-height="true">
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-if="permission['inspect-plan-add-edit']"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
      <!-- <el-button
        v-if="permission['inspect-plan-export']"
        icon="el-icon-upload2"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      stripe
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>

      <el-table-column
        prop="name"
        align="center"
        label="计划名称"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeDeptName"
        label="点巡检部门"
        align="center"
        show-overflow-tooltip
        width="110px"
      >
        <template slot-scope="scope">
          {{ scope.row.executeDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="责任人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="equipmentCount"
        label="设备数量"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentCount || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="cycleTypeName"
        label="计划周期"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.cycleTypeName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="startDate"
        label="开始执行日期"
        align="center"
        show-overflow-tooltip
        width="110px"
      >
        <template v-slot="{ row }">{{ row.startDate || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="endDate"
        label="结束执行日期"
        align="center"
        show-overflow-tooltip
        width="110px"
      >
        <template v-slot="{ row }">{{ row.endDate || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="状态"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }">
          <i
            :style="`color:${inspectPlanStatusColor(
              row.status
            )};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.createUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.updateUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        width="150"
        align="center"
        label="更新时间"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.updateTime || '-' }} </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" fixed="right">
        <!--         0 =  未开始  1= 执行中 2 = 已完成 3=已终止   -->
        <template v-slot="{ row }">
          <section class="cell-operate-class">
            <el-button type="text" size="small" @click="detail(row)"
              >查看</el-button
            >
            <!--    未开始  = 编辑、查看、开始执行、删除    执行中 = 查看、停止执行-->
            <template v-if="permission['inspect-plan-start-stop-manual']">
              <el-button
                v-if="row.status === 0"
                class="green-btn"
                type="text"
                size="small"
                @click="execution(row, 'start')"
                >开始执行
              </el-button>
              <el-button
                v-if="row.status === 1"
                class="danger-btn"
                type="text"
                size="small"
                @click="execution(row, 'stop')"
                >停止执行</el-button
              >
              <!-- 执行中的计划可以手动执行 -->
              <el-button
                v-if="row.status === 1"
                type="text"
                size="small"
                @click="execution(row, 'manual')"
                >手动执行</el-button
              >
            </template>
            <!--           未开始-->
            <el-button
              v-if="row.status === 0 && permission['inspect-plan-add-edit']"
              type="text"
              size="small"
              @click="operate(row)"
              >编辑</el-button
            >
            <el-popconfirm
              title="确定删除吗？"
              @confirm="() => handleDelete(row)"
              v-if="row.status === 0 && permission['inspect-plan-delete']"
            >
              <el-button
                class="danger-btn"
                slot="reference"
                type="text"
                size="small"
                style="margin-left: 10px"
                >删除</el-button
              >
            </el-popconfirm>
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex"></detail-index>
  </basic-container>
</template>

<script>
  import ImportFiles from '@/components/import-files.vue';
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import { convertFileUrl, downloadFileBlob } from '@/util/util';
  import {
    getPlanList,
    deletePlan,
    startPlan,
    stopPlan,
    manualPlan
  } from '@/api/equiment-full-life-api/inspect';
  import { getToken } from '@/util/auth';
  import { mapGetters } from 'vuex';
  import { inspectPlanStatusColor } from '@/views/equiment-full-life-cycle/equipment-inspection/util';
  import dayjs from 'dayjs';

  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination,
      ImportFiles
    },
    props: {},

    data() {
      return {
        inspectPlanStatusColor,
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      //  执行方法
      //0 =  未开始  1= 执行中 2 = 已完成 3=已终止
      execution(row, type) {
        if (!type) return;
        const date = dayjs().format('YYYY-MM-DD');
        const typeObj = {
          start: {
            tips: `该计划将提前至${date}执行，是否确定执行该计划？`,
            api: startPlan
          },
          stop: {
            tips: `该计划将在${date}停止执行，是否确定停止该计划？`,
            api: stopPlan
          },
          manual: {
            tips: `是否确定手动执行生成当天的工单?`,
            api: manualPlan
          }
        };
        const { tips, api } = typeObj[type];
        this.$confirm(tips, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.handleApi(api, row.id);
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },
      // 请求接口
      async handleApi(api, id) {
        try {
          this.loading = true;
          const { data } = await api({ id });
          if (data.code === 200) {
            this.$message.success('操作成功');
            this.getList();
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/inspect-plan/export-plan?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/inspect-plan/export-plan?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '点检计划.xlsx'
        );
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getPlanList({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      },
      async handleDelete(row) {
        try {
          await deletePlan({ ids: row.id });
          //  删除的时候，判断当前列表，是不是length 是1 ，如果是1，将current置成1
          if (this.list.length === 1) {
            this.searchParams.current = 1;
          }

          this.$message({
            type: 'success',
            message: '删除成功'
          });
          await this.getList(true);
          // 发信号 通知树更新
        } catch (e) {
          this.$message.warning(e.data.msg);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
