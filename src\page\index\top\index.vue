<template>
  <div class="avue-top">
    <div class="top-bar__left">
      <div
        class="avue-breadcrumb"
        :class="[{ 'avue-breadcrumb--active': isCollapse }]"
        v-if="showCollapse"
      >
        <i class="icon-navicon" @click="setCollapse"></i>
      </div>
    </div>
    <div class="top-bar__title">
      <div class="top-bar__item top-bar__item--show" v-if="showMenu">
        <top-menu ref="topMenu"></top-menu>
      </div>
      <span class="top-bar__item" v-if="showSearch">
        <top-search></top-search>
      </span>
    </div>
    <div class="top-bar__right">
      <el-button
        type="primary"
        size="mini"
        class="top-btn"
        icon="el-icon-s-home"
        @click="backToMain"
        >返回主界面</el-button
      >
      <el-button
        type="primary"
        size="mini"
        class="top-btn"
        icon="el-icon-s-help"
        @click="jumpToAITools"
        >AI工具箱</el-button
      >
      <ai-btn v-if="configShowAssistant" />
      <el-tooltip
        v-if="showColor"
        effect="dark"
        :content="$t('navbar.color')"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-color></top-color>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showDebug"
        effect="dark"
        :content="logsFlag ? $t('navbar.bug') : logsLen + $t('navbar.bugs')"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-logs></top-logs>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showLock"
        effect="dark"
        :content="$t('navbar.lock')"
        placement="bottom"
      >
        <div class="top-bar__item">
          <top-lock></top-lock>
        </div>
      </el-tooltip>
      <el-tooltip
        v-if="showTheme"
        effect="dark"
        :content="$t('navbar.theme')"
        placement="bottom"
      >
        <div class="top-bar__item top-bar__item--show">
          <top-theme></top-theme>
        </div>
      </el-tooltip>
      <!-- <el-tooltip
        effect="dark"
        :content="$t('navbar.notice')"
        placement="bottom"
      >
        <div class="top-bar__item top-bar__item--show">
          <top-notice></top-notice>
        </div>
      </el-tooltip> -->
      <el-tooltip
        v-if="showLanguage"
        effect="dark"
        :content="$t('navbar.language')"
        placement="bottom"
      >
        <div class="top-bar__item top-bar__item--show">
          <top-lang></top-lang>
        </div>
      </el-tooltip>
      <div v-if="showFullScren" class="top-bar__item" @click="handleScreen">
        <i
          class="iconfont"
          :class="isFullScren ? 'icon-tuichuzhuanhuan' : 'icon-quanping'"
        ></i>
        {{ isFullScren ? $t('navbar.screenfullF') : $t('navbar.screenfull') }}
        <el-divider direction="vertical"></el-divider>
      </div>
      <div class="top-bar__item">
        <top-notice></top-notice>
      </div>
      <!-- <img class="top-bar__img"
           :src="userInfo.avatar"> -->
      <el-dropdown>
        <span class="el-dropdown-link">
          {{ userInfo.account }}
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown" class="custom-userInfo-name">
          <el-dropdown-item @click.native="$router.push('/')">
            {{ $t('navbar.dashboard') }}
          </el-dropdown-item>
          <el-dropdown-item @click.native="$router.push('/info/index')">
            {{ $t('navbar.userinfo') }}
          </el-dropdown-item>
          <el-dropdown-item
            v-if="this.website.switchMode"
            @click.native="switchDept"
            >{{ $t('navbar.switchDept') }}
          </el-dropdown-item>
          <el-dropdown-item @click.native="logout" divided
            >{{ $t('navbar.logOut') }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dialog
        title="用户信息选择"
        append-to-body
        :visible.sync="userBox"
        width="350px"
      >
        <avue-form
          ref="form"
          :option="userOption"
          v-model="userForm"
          @submit="submitSwitch"
        />
      </el-dialog>
    </div>
  </div>
</template>
<script>
  import { resetRouter } from '@/router/router';
  import { mapGetters, mapState } from 'vuex';
  import { fullscreenToggel, listenfullscreen } from '@/util/util';
  import AiBtn from './components/ai-btn.vue';
  import topLock from './top-lock';
  import topMenu from './top-menu';
  import topSearch from './top-search';
  import topTheme from './top-theme';
  import topLogs from './top-logs';
  import topColor from './top-color';
  import topNotice from './top-notice';
  import topLang from './top-lang';
  import { getParameterValue } from '@/api/common';

  export default {
    components: {
      topLock,
      topMenu,
      topSearch,
      topTheme,
      topLogs,
      topColor,
      topNotice,
      topLang,
      AiBtn
    },
    name: 'top',
    data() {
      return {
        userBox: false,
        userForm: {
          deptId: '',
          roleId: ''
        },
        userOption: {
          labelWidth: 70,
          submitBtn: true,
          emptyBtn: false,
          submitText: '切换',
          column: [
            {
              label: '部门',
              prop: 'deptId',
              type: 'select',
              props: {
                label: 'deptName',
                value: 'id'
              },
              dicUrl: '/api/szyk-system/dept/select',
              span: 24,
              display: false,
              rules: [
                {
                  required: true,
                  message: '请选择部门',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '角色',
              prop: 'roleId',
              type: 'select',
              props: {
                label: 'roleName',
                value: 'id'
              },
              dicUrl: '/api/szyk-system/role/select',
              span: 24,
              display: false,
              rules: [
                {
                  required: true,
                  message: '请选择角色',
                  trigger: 'blur'
                }
              ]
            }
          ]
        },
        // 系统配置是否显示智能助手
        configShowAssistant: false
      };
    },
    filters: {},
    created() {
      this.getAssistantStatus();
    },
    mounted() {
      listenfullscreen(this.setScreen);
    },
    computed: {
      ...mapState({
        showDebug: (state) => state.common.showDebug,
        showTheme: (state) => state.common.showTheme,
        showLock: (state) => state.common.showLock,
        showFullScren: (state) => state.common.showFullScren,
        showCollapse: (state) => state.common.showCollapse,
        showSearch: (state) => state.common.showSearch,
        showMenu: (state) => state.common.showMenu,
        showColor: (state) => state.common.showColor,
        showLanguage: (state) => state.common.showLanguage
      }),
      ...mapGetters([
        'userInfo',
        'isFullScren',
        'tagWel',
        'tagList',
        'isCollapse',
        'tag',
        'logsLen',
        'logsFlag'
      ])
    },
    methods: {
      // 获取智能助手状态
      async getAssistantStatus() {
        try {
          const { data } = (await getParameterValue('open_ai_assistant')) || {};
          if (data.data) {
            this.configShowAssistant = data.data.paramValue === '1';
          }
        } catch (e) {
          console.error(e);
        }
      },
      // 跳转到AI工具
      jumpToAITools() {
        this.$router.push('/aitools/index');
      },
      // 返回主应用
      backToMain() {
        // history.pushState({}, '', '/');
        window.location.href = `${location.origin}?toMain=1`;
      },
      handleScreen() {
        fullscreenToggel();
      },
      setCollapse() {
        this.$store.commit('SET_COLLAPSE');
      },
      setScreen() {
        this.$store.commit('SET_FULLSCREN');
      },
      switchDept() {
        const userId = this.userInfo.user_id;
        const deptColumn = this.findObject(this.userOption.column, 'deptId');
        deptColumn.dicUrl = `/api/szyk-system/dept/select?userId=${userId}`;
        deptColumn.display = true;
        const roleColumn = this.findObject(this.userOption.column, 'roleId');
        roleColumn.dicUrl = `/api/szyk-system/role/select?userId=${userId}`;
        roleColumn.display = true;
        this.userBox = true;
      },
      submitSwitch(form, done) {
        this.$store.dispatch('refreshToken', form).then(() => {
          this.userBox = false;
          this.$router.push({ path: '/' });
        });
        done();
      },
      logout() {
        this.$confirm(this.$t('logoutTip'), this.$t('tip'), {
          confirmButtonText: this.$t('submitText'),
          cancelButtonText: this.$t('cancelText'),
          type: 'warning'
        }).then(() => {
          this.$store.dispatch('LogOut').then(() => {
            resetRouter();
            // history.pushState({}, '', '/login');
            window.location.href = `${location.origin}/login`;
          });
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .custom-userInfo-name {
    margin-top: -10px;
  }

  .top-btn {
    margin-right: 8px;

    ::v-deep [class^='el-icon-'] {
      line-height: normal;
    }
  }
</style>
