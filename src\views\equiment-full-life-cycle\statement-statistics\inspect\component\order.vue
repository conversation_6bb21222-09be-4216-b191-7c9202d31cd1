<template>
  <div style="height: 100%">
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 50px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="no"
        label="工单号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.no || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentCategoryName"
        label="设备类型"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{
          row.equipmentCategoryName || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="inspectResult"
        label="点巡检结果"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.inspectResult || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="startTimeStr"
        label="开始时间"
        align="center"
        show-overflow-tooltip
        width="110px"
      ></el-table-column>
      <el-table-column
        prop="endTimeStr"
        label="截止时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.endTimeStr || '-' }}</template>
      </el-table-column>
      <!-- <el-table-column
        prop="executeDeptName"
        label="负责部门"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeDeptName || '-' }} </template>
      </el-table-column> -->
      <el-table-column
        prop="executeUserName"
        label="责任人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="submitTime"
        label="完成时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.submitTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop=""
        label="操作"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <el-button size="small" type="text" @click="view(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <view-info ref="view"></view-info>
  </div>
</template>

<script>
  import Pagination from '@/components/pagination/index.vue';
  import { getDeviceStatistics } from '@/api/equiment-full-life-api/statement-statistics';
  import ViewInfo from '@/views/equiment-full-life-cycle/equipment-inspection/work-order/detail/index.vue';
  export default {
    name: 'DeviceBasicList',
    components: {
      Pagination,
      ViewInfo
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },
    mounted() {},
    methods: {
      view(row) {
        this.$refs['view'].show(row.no);
      },
      getData(search) {
        this.searchParams.current = 1;
        this.searchParams = { ...this.searchParams, ...search };
        this.getList();
      },
      //  点巡检统计 按工单
      async getList() {
        this.loading = true;
        try {
          let res = await getDeviceStatistics({
            ...this.searchParams
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
