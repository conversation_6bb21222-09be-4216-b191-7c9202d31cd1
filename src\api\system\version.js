import request from '@/router/axios';
// const prefix = 'device'
// =============================版本管理===============================

// 版本列表
export function versionList(data) {
  return request({
    method: 'get',
    url: `/api/szyk-system/version/page`,
    params: data
  });
}
// 新增
export function versionAdd(data) {
  return request({
    method: 'post',
    url: `/api/szyk-system/version/save`,
    data
  });
}
// 修改
export function versionEdit(data) {
  return request({
    method: 'post',
    url: `/api/szyk-system/version/update`,
    data
  });
}
// 详情
export function versionDetail(id) {
  return request({
    method: 'get',
    url: `/api/szyk-system/version/${id}`
  });
}
// 删除
export function versionDel(data) {
  return request({
    method: 'post',
    url: `/api/szyk-system/version/remove`,
    params: data
  });
}
