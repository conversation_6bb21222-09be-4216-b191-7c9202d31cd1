<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      label-width="90px"
      :inline="true"
      ref="search"
      :model="form"
      size="small"
    >
      <el-form-item label="设备等级" prop="importantLevel">
        <el-select
          v-model="form.importantLevel"
          placeholder="请选择设备等级"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['important_level']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    serviceDicts: ['important_level'],
    data() {
      return {
        form: {
          importantLevel: undefined
        }
      };
    },
    methods: {
      reset() {
        this.form.useDept = undefined;
        this.form.categoryId = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
