export default {
  install(Vue) {
    Vue.prototype.success = function (msg) {
      this.$message({
        type: 'success',
        message: msg
      });
    };
    Vue.prototype.warning = function (msg) {
      this.$message({
        message: msg,
        type: 'warning'
      });
    };
    Vue.prototype.error = function (msg) {
      this.$message({
        message: msg,
        type: 'error'
      });
    };
  }
};
