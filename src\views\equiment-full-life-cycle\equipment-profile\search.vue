<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="资料编号" prop="no">
        <el-input
          v-model.trim="form.no"
          placeholder="请输入资料编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="资料名称" prop="name">
        <el-input
          v-model.trim="form.name"
          placeholder="请输入资料名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item
        label-width="110px"
        label="适用设备类型"
        prop="categoryName"
      >
        <el-input
          placeholder="请选择适用设备类型"
          v-model.trim="form.categoryName"
          @focus.prevent="onSelectClick"
          readonly
          clearable
        >
          <template slot="append">
            <i
              class="el-icon-circle-close"
              @click="
                () => {
                  form.categoryId = undefined;
                  form.categoryName = undefined;
                  selected = [];
                }
              "
            ></i>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择设备分类-->
    <select-asset-category-dialog
      ref="assetDialog"
      @success="getSuccess"
    ></select-asset-category-dialog>
  </div>
</template>

<script>
  import SelectAssetCategoryDialog from '@/views/equiment-full-life-cycle/components/select-asset-category-dialog/index.vue';
  // import { deepClone } from '@/util/util';
  export default {
    name: 'DeviceListSearch',
    components: { SelectAssetCategoryDialog },
    data() {
      return {
        loading: false,
        form: {
          no: undefined,
          name: undefined,
          categoryName: undefined,
          categoryId: undefined
        },
        selected: []
      };
    },
    methods: {
      //  选择适用设备
      onSelectClick() {
        let list = this.selected.map((item) => {
          return {
            ...item,
            num: 1
          };
        });
        this.$refs['assetDialog'].show(list);
      },
      //  成功获取选择设备的数据
      getSuccess(row) {
        if (row) {
          this.selected = row;
          let name = [],
            id = [];
          row.forEach((item) => {
            name.push(item.categoryName);
            id.push(item.id);
          });
          this.$set(this.form, 'categoryName', name.join(','));
          this.$set(this.form, 'categoryId', id.join(','));
        }
      },
      reset() {
        this.selected = [];
        this.form.categoryId = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
