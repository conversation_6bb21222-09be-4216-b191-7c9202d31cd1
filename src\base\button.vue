<template>
  <span class="btn">
    <!-- 新增 查询，重置 -->
    <el-button
      v-if="type !== 'dele'"
      :icon="icon.icon"
      :type="icon.btnType"
      @click="handleClick"
      size="small"
      :plain="icon.plain"
      :loading="loading"
      >{{ icon.labelName }}</el-button
    >
    <el-popconfirm
      v-else
      title="你确定删除此条信息吗？"
      @confirm="handleDel()"
      class="slotDelete"
    >
      <el-button
        slot="reference"
        :type="icon.btnType"
        :icon="icon.icon"
        size="small"
        >{{ icon.labelName }}</el-button
      >
    </el-popconfirm>
  </span>
</template>

<script>
  export default {
    name: 'btn',
    props: {
      type: {
        type: String,
        default: () => {
          return 'create';
        }
      },
      loading: {
        type: Boolean,
        default: () => {
          return false;
        }
      }
    },
    computed: {
      icon() {
        switch (this.type) {
          case 'add':
            return {
              icon: 'el-icon-plus',
              labelName: '新增',
              btnType: 'primary'
            };
          case 'search':
            return {
              // icon: 'el-icon-search',
              labelName: '查 询',
              btnType: 'primary',
              plain: 'plain'
            };
          case 'export':
            return {
              icon: 'el-icon-download',
              labelName: '导出',
              btnType: 'warning',
              plain: 'plain'
            };
          case 'import':
            return {
              icon: 'el-icon-upload2',
              labelName: '导入',
              btnType: 'success',
              plain: 'plain'
            };
          case 'batchDel':
            return {
              icon: 'el-icon-delete',
              labelName: '删除',
              btnType: 'danger',
              plain: 'plain'
            };
          case 'reset':
            return {
              // icon: 'el-icon-delete',
              labelName: '重 置',
              btnType: 'plain'
            };
          case 'save':
            return {
              // icon: 'el-icon-position',
              labelName: '暂 存',
              btnType: 'primary'
            };
          case 'submit':
            return {
              // icon: 'el-icon-circle-plus-outline',
              labelName: '提 交',
              btnType: 'primary'
            };
          case 'confirm':
            return {
              // icon: 'el-icon-circle-check',
              labelName: '确 定',
              btnType: 'primary'
            };
          case 'close':
            return {
              // icon: 'el-icon-circle-close',
              labelName: '关 闭',
              btnType: 'plain'
            };
          case 'cancel':
            return {
              // icon: 'el-icon-circle-close',
              labelName: '取 消',
              btnType: 'plain'
            };
          case 'lookup':
            return { icon: 'el-icon-view', labelName: '查看', btnType: 'text' };
          case 'edit':
            return { icon: 'el-icon-edit', labelName: '编辑', btnType: 'text' };
          case 'dele':
            return {
              icon: 'el-icon-delete',
              labelName: '删除',
              btnType: 'text'
            };
          case 'reject':
            return {
              icon: 'el-icon-circle-close',
              labelName: '驳回',
              btnType: 'warning'
            };
          case 'agree':
            return {
              icon: 'el-icon-check',
              labelName: '通过',
              btnType: 'primary'
            };
          case 'enable':
            return {
              icon: 'el-icon-circle-check',
              labelName: '启用',
              btnType: 'text'
            };
          case 'disable':
            return {
              icon: 'el-icon-circle-close',
              labelName: '禁用',
              btnType: 'text'
            };
          case 'copy':
            return {
              icon: 'el-icon-document-copy',
              labelName: '复制',
              btnType: 'text'
            };
          default:
            return { icon: '', labelName: '' };
        }
      }
    },
    data() {
      return {};
    },
    methods: {
      handleClick() {
        this.$emit('click');
      },
      handleDel() {
        this.$emit('deleted');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .btn {
    display: inline-block;
    padding: 0 5px;
  }
</style>
