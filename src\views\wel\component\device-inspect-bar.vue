<template>
  <div class="alarmTrend">
    <span class="title">
      <section>
        <span style="color: #333; font-size: 20px"> 本年度设备运行情况</span>
      </section>
    </span>
    <div
      id="bar_year_chart"
      class="alarmTrendChart"
      v-if="flag"
      v-loading="loading"
    ></div>
    <custom-empty :size="70" v-else></custom-empty>
  </div>
</template>
<script>
  import * as echarts from 'echarts/core';
  import { curYearEquipmentStatus } from '@/api/home';
  import {
    TooltipComponent,
    LegendComponent,
    GridComponent,
    TitleComponent
  } from 'echarts/components';
  import { Bar<PERSON>hart, LineChart } from 'echarts/charts';
  import { CanvasRenderer } from 'echarts/renderers';
  import CustomEmpty from '@/components/custom-empty.vue';
  // import { markRaw } from 'vue';
  import Search from './search.vue';
  echarts.use([
    TooltipComponent,
    TitleComponent,
    Bar<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>po<PERSON>,
    Grid<PERSON>omponent
  ]);

  export default {
    name: 'alarmTrend',
    props: {},
    components: { Search, CustomEmpty },

    data() {
      return {
        dateList: [],
        abnormalCountList: [],
        normalCountList: [],
        loading: false,
        flag: true
      };
    },
    computed: {
      uniqueId() {
        return `alarmTrendChart_${Date.now()}_${Math.floor(
          Math.random() * 1000
        )}`;
      }
    },
    mounted() {
      this.$nextTick(async () => {
        await this.getCategoryAsset();
      });
      window.addEventListener('resize', this.resizeChart);
    },
    unmounted() {
      window.removeEventListener('resize', this.resizeChart);
    },
    methods: {
      async getCategoryAsset() {
        this.loading = true;
        try {
          const res = await curYearEquipmentStatus();
          let arr = res.data.data || [];
          if (arr.length) {
            this.flag = true;
            let dateList = [];
            let abnormalCountList = [];
            let normalCountList = [];
            arr.forEach((item) => {
              dateList.push(item.month + '月');
              abnormalCountList.push({
                value: item.defaultNum,
                repairNum: item.repairNum
              });
              normalCountList.push(item.repairNum);
            });
            this.abnormalCountList = abnormalCountList;
            this.normalCountList = normalCountList;
            this.dateList = dateList;
          } else {
            this.flag = false;
          }
          await this.init();
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },

      resizeChart() {
        this.status_chart &&
          this.status_chart.resize({ width: 'auto', height: 'auto' });
      },
      init() {
        let chartDom = document.getElementById('bar_year_chart');
        this.status_chart = echarts.init(chartDom);

        let option = {
          legend: {
            orient: 'horizontal',
            bottom: '30',
            data: ['异常故障数量', '维修数量'],
            itemGap: 20,
            itemWidth: 14,
            itemHeight: 7,
            formatter: function (name) {
              // 根据图例名称设置不同的颜色
              if (name === '异常故障数量') {
                return `{a|${name}}`; // 使用富文本标签设置颜色
              } else if (name === '维修数量') {
                return `{b|${name}}`;
              }
              return name;
            },
            textStyle: {
              rich: {
                a: {
                  color: '#2566F6' // 红色
                },
                b: {
                  color: '#FD8424' // 绿色
                }
              }
            }
          },
          grid: { top: '5%', bottom: '25%', left: '8%', right: '10' },
          xAxis: {
            type: 'category',
            data: this.dateList,
            axisLine: { lineStyle: { color: '#ccc' } },
            axisTick: { length: 3 },
            axisLabel: { color: '#999' }
          },
          yAxis: {
            type: 'value',
            axisLine: { show: true, lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#999' },
            splitLine: {
              lineStyle: { color: ['#CEEDFF'], type: [5, 8], dashOffset: 3 }
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            textStyle: { color: '#424242' },
            renderMode: 'html',
            className: 'tooltip',
            order: 'seriesDesc'
          },
          series: [
            {
              name: '维修数量',
              type: 'line',
              color: '#FF8B17',
              avoidLabelOverlap: true,
              showAllSymbol: true,
              symbolSize: 0,
              smooth: true,
              label: {
                show: true,
                position: 'top'
              },
              areaStyle: {
                //区域填充样式
                normal: {
                  //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: 'rgba(255,221,189,1)'
                      },
                      {
                        offset: 1,
                        color: 'rgba(255,244,229, 1)'
                      }
                    ],
                    false
                  ),
                  shadowColor: 'rgba(255,139,23, 0.5)', //阴影颜色
                  shadowBlur: 10 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
                }
              },
              data: this.normalCountList
            },
            {
              name: '异常故障数量',
              type: 'bar',
              color: '#52A8FF',
              avoidLabelOverlap: true,
              zIndex: 10,
              label: {
                show: true,
                position: 'top', // insideTopLeft
                formatter: function (data) {
                  let { value, repairNum } = data.data;
                  return `${value === repairNum ? '' : value}`;
                }
              },
              barWidth: '25',
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(30,97,250,1)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(255,244,229,1)'
                    }
                  ])
                },
                //  鼠标移到柱子上
                emphasis: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(30,97,250,1)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(30,97,250,1)'
                    }
                  ])
                }
              },
              data: this.abnormalCountList
            }
          ]
        };

        this.status_chart && this.status_chart.setOption(option);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .alarmTrend {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: #fff;
    border-radius: 5px;
  }

  .alarmTrendChart {
    width: 100%;
    height: calc(100% - 50px);
    border-radius: 5px;
  }

  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    section {
      width: calc(100% - 220px);
      overflow: hidden;
      text-wrap: nowrap;
      text-overflow: ellipsis;
    }

    em {
      display: inline-block;
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-style: normal;
      line-height: 10%;
    }
  }

  ::v-deep {
    .el-tabs__nav-wrap::after {
      background-color: unset;
    }

    .el-tabs__nav-wrap {
      top: -20px;
    }
  }
</style>
