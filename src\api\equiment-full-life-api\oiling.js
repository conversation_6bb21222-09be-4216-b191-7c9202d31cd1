import request from '@/router/axios';
//  润滑相关接口
// 润滑方式---------------------------------------------------------------------------------------------------------------------
// 润滑方式分页列表
export const getOilingWayList = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/methods/page`,
    method: 'get',
    params
  });
};
//  润滑方式提交或修改
export const getAddOrUpdateWay = (data) => {
  return request({
    url: `/api/szyk-simas/lubricate/methods/submit`,
    method: 'post',
    data
  });
};
//  润滑方式详情接口
export const getOilingWayDetail = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/methods/detail`,
    method: 'get',
    params
  });
};
//  手段下拉列表接口
export const getOilingWaySelect = () => {
  return request({
    url: `/api/szyk-simas/lubricate/methods/list`,
    method: 'get'
  });
};
//油品类型-------------------------------------------------------------------------------------------------------------------
export const getOilingTypeList = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/oil-type/page`,
    method: 'get',
    params
  });
};
//  油品类型列表
export const oilingTypeList = () => {
  return request({
    url: `/api/szyk-simas/lubricate/oil-type/list`,
    method: 'get'
  });
};
// 油品类型删除接口
export const oilingTypeDelete = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/oil-type/remove`,
    method: 'post',
    params
  });
};
//  油品类型提交或修改
export const typeAddOrUpdateType = (data) => {
  return request({
    url: `/api/szyk-simas/lubricate/oil-type/submit`,
    method: 'post',
    data
  });
};
export const getOilingTypeDetail = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/oil-type/detail`,
    method: 'get',
    params
  });
};

// 标准----------------------------------------------------------------------------------------------------------------------
// 润滑标准列表 和 点检标准用的是同一个列表

// 润滑标准新增或修改
export const userAddOrUpdate = (data) => {
  return request({
    url: `/api/szyk-simas/lubricate/standards/submit`,
    method: 'post',
    data
  });
};

// 润滑标准清空标准
export const userClear = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/standards/clear`,
    method: 'post',
    params
  });
};
// 润滑标准详情
export const userDetail = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/standards/detail`,
    method: 'get',
    params
  });
};
// 选择部位下拉列表
export const getPartList = (params) => {
  return request({
    url: `/api/szyk-common/equipment-account/monitor-select`,
    method: 'get',
    params
  });
};

//  计划相关 ----------------------------------------------------------------------------------------------------------------
//设备润滑计划表相关接口
export const getPlanList = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/plan/page`,
    method: 'get',
    params
  });
};

// 润滑详情列表
export const getPlanDetail = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/plan/detail`,
    method: 'get',
    params
  });
};

// 润滑计划新增接口
export const addPlan = (data) => {
  return request({
    url: `/api/szyk-simas/lubricate/plan/submit`,
    method: 'post',
    data
  });
};
// 润滑计划修改接口

// 润滑计划逻辑删除接口
export const deletePlan = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/plan/delete`,
    method: 'post',
    params
  });
};

// 点击按计划手动开始
export const startPlan = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/plan/start`,
    method: 'post',
    params
  });
};

// 点检计划手动停止
export const stopPlan = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/plan/stop`,
    method: 'post',
    params
  });
};

// 润滑计划手动执行生成当天工单
export const manualPlan = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/plan/manual-generate-orders`,
    method: 'post',
    params
  });
};

// 点检工单相关接口 -------------------------------------------------------------------------------------------------------------------------
export const getOrderList = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/order/page`,
    method: 'get',
    params
  });
};

// 润滑工单详情
export const getOrderDetail = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/order/detail`,
    method: 'get',
    params
  });
};
//  润滑标准部位列表
export const getPartListByType = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/standards/list`,
    method: 'post',
    params
  });
};
// 工单中审核确认
export const auditOrder = (data) => {
  return request({
    url: `/api/szyk-simas/lubricate/order/confirm`,
    method: 'post',
    data
  });
};

// 工单 - 批量审核
export const batchAuditOrder = (data) => {
  return request({
    url: `/api/szyk-simas/lubricate/order/confirmBatch`,
    method: 'post',
    data
  });
};

// 润滑标准列表接口
export const getStandardList = (params) => {
  return request({
    url: `/api/szyk-simas/lubricate/standards/devicePage`,
    method: 'get',
    params
  });
};
//  提交润滑接口
export const oilingOrderButton = (data) => {
  return request({
    url: `/api/szyk-simas/lubricate/order/submit`,
    method: 'post',
    data
  });
};
