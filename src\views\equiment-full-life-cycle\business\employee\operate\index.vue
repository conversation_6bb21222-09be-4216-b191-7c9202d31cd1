<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <info ref="info" :initData="detail" :postList="postList"></info>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import Info from './base-info.vue';
  import {
    userSave,
    userDetail,
    postList
  } from '@/api/equiment-full-life-api/employee';
  export default {
    name: 'AddDevice',
    components: {
      Info
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        loading: false,
        edit: false,
        form: {},
        postList: [],
        id: ''
      };
    },
    watch: {},
    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await userDetail({ id: id });
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 所属岗位
      async getPostList() {
        try {
          const res = await postList();
          this.postList = res.data.data;
        } catch (e) {
          this.loading = false;
        }
      },
      show(id) {
        this.visible = true;
        this.getPostList();
        this.edit = !!id;
        this.id = id;
        if (id) {
          this.getDetail(id);
        }
      },

      closed() {
        this.edit = false;
        this.id = undefined;
        this.detail = {};
        this.$refs['info'].resetForm();
        this.visible = false;
      },
      async submit() {
        let params = await this.$refs['info'].validForm();
        if (params) {
          await this.save({ id: this.edit ? this.id : undefined, ...params });
        }
      },
      //
      async save(params) {
        this.loading = true;
        try {
          await userSave(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
