<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <info
        ref="info"
        :initData="detail"
        :whorehouse="whorehouse"
        :unitArr="unitArr"
      ></info>
    </section>
    <div class="oper_btn">
      <el-button
        class="el-icon-circle-plus-outline"
        size="small"
        type="primary"
        @click="submit()"
        :loading="loading"
      >
        提交</el-button
      >
      <btn type="close" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import Info from './base-info.vue';
  import {
    addOrEditSparePartsApi,
    getCheckRepairTypeListApi,
    getSparePartsViewApi
  } from '@/api/equiment-full-life-api/spare-parts';
  //  获取计量单位
  import { getUnitList } from '@/api/equiment-full-life-api/common';
  export default {
    name: 'AddDevice',
    components: {
      Info
    },
    filters: {
      contentFilter(text) {
        // 使用正则表达式匹配日期时间部分和剩余部分
        const regex = /^(\d{4})-(\d{2})-(\d{2}) (\d{2}:\d{2}:\d{2})(.+)$/;
        const matches = text.match(regex);

        if (matches) {
          // 提取匹配的结果
          const year = matches[1]; // 年
          const month = matches[2]; // 月
          const day = matches[3]; // 日
          const time = matches[4]; // 时间
          const rest = matches[5]; // 剩余部分

          // 拼接转换后的日期时间
          const formattedDateTime = `${year}年${month}月${day}日 ${time}`;

          // 拼接最终结果
          return `${formattedDateTime}   ${rest};`;
        }

        // 如果没有匹配到，返回原始文本
        return text;
      }
    },
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {},
        unitArr: [], // 存放计量单位
        whorehouse: [] // 存放库房
      };
    },
    watch: {},
    methods: {
      //  获取仓库列表
      async getStoreHouseList() {
        let res = await getCheckRepairTypeListApi();
        this.whorehouse = res.data.data;
      },
      async getUnitList() {
        let res = await getUnitList({ current: 1, size: -1 });
        this.unitArr = res.data.data.records || [];
      },
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getSparePartsViewApi(id);
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      show(row) {
        this.visible = true;
        this.edit = !!row.id;
        this.eqId = row.id;
        this.getStoreHouseList(); // 获取仓库列表
        this.getUnitList(); //获取计量的单位列表
        if (row.id) {
          this.getDetail(row.id);
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.$refs['info'].resetForm();
        this.detail = {};
      },
      async submit() {
        let params = await this.$refs['info'].validForm();
        await this.save({
          id: this.edit ? this.eqId : undefined,
          ...params
        });
      },

      // 提交台账
      async save(params) {
        this.loading = true;
        try {
          await addOrEditSparePartsApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }

    .log-wrapper {
      .log-item {
        margin-bottom: 10px;
      }
    }
  }
</style>
