<template>
  <basic-container class="table-content" :auto-height="true">
    <div class="top-info">
      <search ref="search" @search="onsubmit" />

      <el-button
        icon="el-icon-upload2"
        type="success"
        size="small"
        @click="exportExcel"
        >导出</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>

      <el-table-column
        prop="moduleName"
        label="系统模块"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="typeName"
        label="操作类型"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="request"
        label="请求方式"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="operateUserName"
        label="操作人员"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="host"
        label="主机"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="netName"
        label="操作地点"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="statusName"
        label="操作状态"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="operateTime"
        label="操作时间"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
  </basic-container>
</template>

<script>
  import Search from './search';
  import Pagination from '@/components/pagination';
  import { convertFileUrl } from '@/util/util';
  import { logListPage } from '@/api/equiment-full-life-api/log';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      Pagination
    },
    props: {},
    data() {
      return {
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      print() {},
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/operate-log/export-log?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/operate-log/export-log?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '日志.xlsx'
        );
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await logListPage({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
