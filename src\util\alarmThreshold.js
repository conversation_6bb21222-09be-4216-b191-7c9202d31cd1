/**
 * 水平超限比对
 * 如果值超过设定的阈值，则返回相应的报警级别
 * @param {Object} alarmThreshold - 报警阈值对象
 * @param {number} value - 当前的振动有效值
 * @return {string|null} 报警级别
 */
export function horizontalOverrunCompare(alarmThreshold, value) {
  if (
    alarmThreshold.fourthThresholdUpper !== null &&
    value >= alarmThreshold.fourthThresholdUpper
  ) {
    return 4;
  } else if (
    alarmThreshold.thirdThresholdUpper !== null &&
    value >= alarmThreshold.thirdThresholdUpper
  ) {
    return 3;
  } else if (
    alarmThreshold.secondThresholdUpper !== null &&
    value >= alarmThreshold.secondThresholdUpper
  ) {
    return 2;
  } else if (
    alarmThreshold.firstThresholdUpper !== null &&
    value >= alarmThreshold.firstThresholdUpper
  ) {
    return 1;
  }
  return null;
}

/**
 * 水平低限比对
 * 如果值低于设定的阈值，则返回相应的报警级别
 * @param {Object} alarmThreshold - 报警阈值对象
 * @param {number} value - 当前的振动有效值
 * @return {string|null} 报警级别
 */
export function horizontalLowerLimitCompare(alarmThreshold, value) {
  if (
    alarmThreshold.fourthThresholdLower !== null &&
    value < alarmThreshold.fourthThresholdLower
  ) {
    return 4;
  } else if (
    alarmThreshold.thirdThresholdLower !== null &&
    value < alarmThreshold.thirdThresholdLower
  ) {
    return 3;
  } else if (
    alarmThreshold.secondThresholdLower !== null &&
    value < alarmThreshold.secondThresholdLower
  ) {
    return 2;
  } else if (
    alarmThreshold.firstThresholdLower !== null &&
    value < alarmThreshold.firstThresholdLower
  ) {
    return 1;
  }
  return null;
}

/**
 * 窗内比对
 * 如果值位于设定的阈值区间内，则返回相应的报警级别
 * @param {Object} alarmThreshold - 报警阈值对象
 * @param {number} value - 当前的振动有效值
 * @return {string|null} 报警级别
 */
export function insideWindowCompare(alarmThreshold, value) {
  if (
    alarmThreshold.fourthThresholdLower !== null &&
    alarmThreshold.fourthThresholdUpper !== null &&
    value >= alarmThreshold.fourthThresholdLower &&
    value <= alarmThreshold.fourthThresholdUpper
  ) {
    return 4;
  }
  if (
    alarmThreshold.thirdThresholdLower !== null &&
    alarmThreshold.thirdThresholdUpper !== null &&
    value >= alarmThreshold.thirdThresholdLower &&
    value <= alarmThreshold.thirdThresholdUpper
  ) {
    return 3;
  }
  if (
    alarmThreshold.secondThresholdLower !== null &&
    alarmThreshold.secondThresholdUpper !== null &&
    value >= alarmThreshold.secondThresholdLower &&
    value <= alarmThreshold.secondThresholdUpper
  ) {
    return 2;
  }
  if (
    alarmThreshold.firstThresholdLower !== null &&
    alarmThreshold.firstThresholdUpper !== null &&
    value >= alarmThreshold.firstThresholdLower &&
    value <= alarmThreshold.firstThresholdUpper
  ) {
    return 1;
  }
  return null;
}

/**
 * 窗外比对
 * 如果值位于设定的阈值区间外，则返回相应的报警级别
 * @param {Object} alarmThreshold - 报警阈值对象
 * @param {number} value - 当前的振动有效值
 * @return {string|null} 报警级别
 */
export function outsideWindowCompare(alarmThreshold, value) {
  if (
    alarmThreshold.fourthThresholdLower !== null &&
    alarmThreshold.fourthThresholdUpper !== null &&
    (value < alarmThreshold.fourthThresholdLower ||
      value > alarmThreshold.fourthThresholdUpper)
  ) {
    return 4;
  }
  if (
    alarmThreshold.thirdThresholdLower !== null &&
    alarmThreshold.thirdThresholdUpper !== null &&
    (value < alarmThreshold.thirdThresholdLower ||
      value > alarmThreshold.thirdThresholdUpper)
  ) {
    return 3;
  }
  if (
    alarmThreshold.secondThresholdLower !== null &&
    alarmThreshold.secondThresholdUpper !== null &&
    (value < alarmThreshold.secondThresholdLower ||
      value > alarmThreshold.secondThresholdUpper)
  ) {
    return 2;
  }
  if (
    alarmThreshold.firstThresholdLower !== null &&
    alarmThreshold.firstThresholdUpper !== null &&
    (value < alarmThreshold.firstThresholdLower ||
      value > alarmThreshold.firstThresholdUpper)
  ) {
    return 1;
  }
  return null;
}

/**
 * 根据阈值类型决定调用哪个比对函数
 * @param {Object} alarmThreshold - 报警阈值对象
 * @param {number} value - 当前的振动有效值
 * @return {string|null} 报警级别
 */
export function determineAlarmLevel(alarmThreshold, value) {
  value = Number(value);
  const newAlarmThreshold = { ...alarmThreshold };
  newAlarmThreshold.firstThresholdLower = parseFloat(
    alarmThreshold.firstThresholdLower
  );
  newAlarmThreshold.firstThresholdUpper = Number(
    alarmThreshold.firstThresholdLower
  );
  newAlarmThreshold.secondThresholdLower = Number(
    alarmThreshold.secondThresholdLower
  );
  newAlarmThreshold.secondThresholdUpper = Number(
    alarmThreshold.secondThresholdUpper
  );
  newAlarmThreshold.thirdThresholdLower = Number(
    alarmThreshold.thirdThresholdLower
  );
  newAlarmThreshold.thirdThresholdUpper = Number(
    alarmThreshold.thirdThresholdUpper
  );
  newAlarmThreshold.fourthThresholdLower = Number(
    alarmThreshold.fourthThresholdLower
  );
  newAlarmThreshold.fourthThresholdUpper = Number(
    alarmThreshold.fourthThresholdUpper
  );
  switch (`${alarmThreshold.alarmType}`) {
    case '0':
      console.log('水平超限');
      return horizontalOverrunCompare(alarmThreshold, value);
    case '1':
      console.log('水平低限');
      return horizontalLowerLimitCompare(alarmThreshold, value);
    case '2':
      console.log('窗内');
      return insideWindowCompare(alarmThreshold, value);
    case '3':
      console.log('窗外');
      return outsideWindowCompare(alarmThreshold, value);
    default:
      return null;
  }
}

export function validateThresholds(thresholds) {
  thresholds.alarmType = `${thresholds.alarmType}`;
  const {
    firstThresholdLower,
    firstThresholdUpper,
    secondThresholdLower,
    secondThresholdUpper,
    thirdThresholdLower,
    thirdThresholdUpper,
    fourthThresholdLower,
    fourthThresholdUpper,
    alarmType
  } = thresholds;
  // 将门限值转换为数字，如果是null或无法转换，则为NaN
  const upperThresholds = [
    firstThresholdUpper,
    secondThresholdUpper,
    thirdThresholdUpper,
    fourthThresholdUpper
  ].map((threshold) => parseFloat(threshold));
  const lowerThresholds = [
    firstThresholdLower,
    secondThresholdLower,
    thirdThresholdLower,
    fourthThresholdLower
  ].map((threshold) => parseFloat(threshold));
  // 提取已填写的阈值
  const filledUpperThresholds = upperThresholds.filter(
    (threshold) => !isNaN(threshold)
  );
  const filledLowerThresholds = lowerThresholds.filter(
    (threshold) => !isNaN(threshold)
  );
  // 超限和低限验证
  if (alarmType === '0') {
    // 超限
    for (let i = 0; i < filledUpperThresholds.length - 1; i++) {
      if (
        !isNaN(filledUpperThresholds[i]) &&
        !isNaN(filledUpperThresholds[i + 1]) &&
        filledUpperThresholds[i] >= filledUpperThresholds[i + 1]
      ) {
        return {
          isValid: false,
          msg: `水平超限 高级门限值必须大于低级门限值`
        };
      }
    }
  } else if (alarmType === '1') {
    // 低限
    for (let i = 0; i < filledLowerThresholds.length - 1; i++) {
      if (
        !isNaN(filledLowerThresholds[i]) &&
        !isNaN(filledLowerThresholds[i + 1]) &&
        filledLowerThresholds[i] <= filledLowerThresholds[i + 1]
      ) {
        return {
          isValid: false,
          msg: `水平超限 低级门限值必须大于高级门限值`
        };
      }
    }
  }

  // 窗内和窗外验证
  if (alarmType === '2' || alarmType === '3') {
    // 窗内或窗外
    for (let i = 0; i < upperThresholds.length; i++) {
      if (
        !isNaN(lowerThresholds[i]) &&
        !isNaN(upperThresholds[i]) &&
        lowerThresholds[i] >= upperThresholds[i]
      ) {
        return {
          isValid: false,
          msg: `${alarmType === '2' ? '窗内' : '窗外'}：第${
            i + 1
          }级门限的上限值应大于下限值`
        };
      }
    }
  }

  // 如果所有验证都通过
  return { isValid: true };
}
