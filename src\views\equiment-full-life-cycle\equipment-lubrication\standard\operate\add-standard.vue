<template>
  <div class="table-content">
    <div class="top-info">
      <el-button icon="el-icon-plus" type="primary" size="small" @click="add"
        >添加标准</el-button
      >
      <el-button
        v-if="form.monitorList.length > 0"
        icon="el-icon-delete"
        type="danger"
        size="small"
        @click="delAll"
        >删除所选</el-button
      >
    </div>
    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
      label-width="10px"
    >
      <el-table
        ref="point-table"
        class="table table-vertical-top"
        :data="form.monitorList"
        style="width: 100%"
        size="mini"
        border
        :header-cell-style="{ background: '#fafafa' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column
          prop="equipmentMonitorId"
          label="润滑部位"
          align="center"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.equipmentMonitorId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="repeatRuleMonitor"
              label=" "
            >
              <el-select
                v-model="scope.row.equipmentMonitorId"
                placeholder="请选择润滑部位"
                clearable
              >
                <!--                 禁用已经选择的润滑部位-->
                <el-option
                  v-for="dict in monitorList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                  :disabled="isItemSelected(dict.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="oilTypeId" label="油品类型" align="center">
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.oilTypeId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="repeatRuleOilingType"
              label=" "
            >
              <el-select
                v-model="scope.row.oilTypeId"
                placeholder="请选择油品类型"
                clearable
              >
                <el-option
                  v-for="dict in oilType"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="lubricateMethodsId"
          label="润滑方式"
          align="center"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.lubricateMethodsId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="repeatRuleOilingWay"
              label=" "
            >
              <el-select
                v-model="scope.row.lubricateMethodsId"
                placeholder="请选择润滑方式"
                clearable
              >
                <el-option
                  v-for="dict in wayList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="lubricateCycle"
          label="润滑周期(天)"
          align="center"
          width="300px"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.lubricateCycle'"
              style="width: 100%"
              :rules="repeatRuleCycle"
              label=" "
            >
              <el-input
                placeholder="请输入润滑周期"
                v-model.trim="scope.row.lubricateCycle"
                clearable
                @blur="cycleBlur(scope)"
              >
                <template slot="prepend">每</template>
                <template slot="append">天执行一次</template>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="floatTime"
          label="执行浮动时间（天）"
          align="center"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.floatTime'"
              style="width: 100%"
              :rules="repeatRuleFloatTime"
              label=" "
            >
              <el-input
                placeholder="请输入执行浮动时间"
                v-model.trim="scope.row.floatTime"
                clearable
              />
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column prop="code" label="操作" align="center" width="70px">
          <template v-slot="scope">
            <el-button size="small" type="text" @click="del(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>
<script>
  import { isLiftNum, isLiftNum0 } from '@/util/func';
  import { getPartList } from '@/api/equiment-full-life-api/inspect';

  export default {
    name: 'PointList',
    components: {},
    props: {
      details: {
        type: Object,
        default: () => {
          return {
            lubricateStandardsList: []
          };
        }
      },
      // //  部位列表
      // monitorList: {
      //   type: Array,
      //   default: () => {
      //     return [];
      //   }
      // },
      wayList: {
        type: Array,
        default: () => {
          return [];
        }
      },
      oilType: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    // watch: {
    //   'details.equipmentId': {
    //     immediate: true,
    //     handler(val) {
    //       if (val) {
    //         this.$nextTick(() => {
    //           let list = this.details.lubricateStandardsList || [];
    //           this.setList(list);
    //         });
    //       }
    //     }
    //   }
    // },
    watch: {
      'details.equipmentId': {
        immediate: true,
        handler(val) {
          if (val) {
            this.$nextTick(async () => {
              await this.getMonitorList(val);
              let list = this.details.lubricateStandardsList || [];
              if (list.length > 0) {
                this.edit = true;
                // 如果已经添加了标准
                await this.setList(list);
              } else {
                this.edit = false;
                // 没有添加标准
                await this.setList(this.monitorList);
              }
            });
          }
        }
      }
    },
    data() {
      const validateCode = (rule, value, callback) => {
        let index = Number(rule.fullField.split('.')[1]);
        let cyc = this.form.monitorList[index].lubricateCycle;
        let max = cyc ? Number(cyc) : undefined;
        // 如果是null 空字符串
        if (!value) {
          callback();
        } else if (isLiftNum0(value, max ? max : 9999)) {
          callback();
        } else {
          callback(new Error(`请输入0~${max}的整数`));
        }
      };
      return {
        num: 0,
        //  润滑部位
        repeatRuleMonitor: [
          {
            required: true,
            message: '请选择润滑部位',
            trigger: 'change'
          }
        ],
        //  油品类型
        repeatRuleOilingType: [
          {
            required: true,
            message: '请选择油品类型',
            trigger: 'change'
          }
        ],
        //  润滑方式
        repeatRuleOilingWay: [
          {
            required: true,
            message: '请选择润滑方式',
            trigger: 'change'
          }
        ],

        //  润滑周期
        repeatRuleCycle: [
          {
            required: true,
            message: '请输入润滑周期',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              // 如果是null 空字符串
              if (!value) {
                callback();
              } else if (isLiftNum(value, 9999)) {
                callback();
              } else {
                callback(new Error('请输入大于0且小于9999的整数'));
              }
            },
            trigger: 'blur'
          }
        ],
        //  执行浮动时间
        repeatRuleFloatTime: [
          {
            required: true,
            message: '请输入执行浮动时间',
            trigger: 'blur'
          },
          {
            validator: validateCode
          }
        ],
        form: {
          monitorList: []
        },
        edit: false,
        currentIdx: undefined, // 当前的要匹配的索引
        // 某个部位已选择的传感器列表
        initSelectList: [],
        multipleSelection: [],
        repeatCode: [], // 接口返回的 重复的编码
        monitorList: []
      };
    },
    methods: {
      //  获取所有的测点
      async getMonitorList(id) {
        try {
          this.loading = true;
          const res = await getPartList({ equipmentId: id });
          let data = res.data.data || [];
          this.monitorList = data.map((item) => {
            return {
              name: item.name,
              id: item.id,
              equipmentMonitorId: item.id,
              monitorName: item.name,
              monitorId: item.id
            };
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  部位选择的直接禁用 不能继续选择
      isItemSelected(id) {
        return this.form.monitorList.some(
          (item) => item.equipmentMonitorId === id
        );
      },
      //  润滑周期改变的时候，计算浮动时间
      cycleBlur(scope) {
        if (scope.row.lubricateCycle) {
          this.$set(
            scope.row,
            'floatTime',
            Math.ceil(scope.row.lubricateCycle * 0.1)
          );
        }
      },

      delAll() {
        if (this.multipleSelection.length === 0) {
          this.$message.warning('请先选择要删除的标准');
          return;
        }
        const filteredArray = this.form.monitorList.filter(
          (item) =>
            !this.multipleSelection.some(
              (deleteItem) => deleteItem.num === item.num
            )
        );

        this.form.monitorList = [...filteredArray];
      },

      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      del(scope) {
        console.log(scope.$index);
        this.form.monitorList.splice(scope.$index, 1);
      },
      // 初始化设置列表（编辑时）
      setList(list) {
        const listNum = list.map((it) => {
          return {
            ...it,
            num: this.num++
          };
        });
        this.form.monitorList = listNum;
      },

      // 点击添加部位 在列表新增一行列表
      add() {
        this.num++;
        //1.0选择设备 1.1输入设备；
        let listData = [
          {
            num: this.num,
            lubricateCycle: undefined,
            equipmentMonitorId: undefined,
            floatTime: undefined,
            lubricateMethodsId: undefined,
            oilTypeId: undefined
          }
        ];
        // 点击新增的时候，如果部位超过20个（最多20个），那么就新增，如果超过20个
        if (this.form.monitorList.length >= 50) {
          this.$message.warning('最多能增加50个标准');
          return;
        }
        this.form.monitorList = [...this.form.monitorList, ...listData];
      },

      async validForm() {
        // if (this.form.monitorList.length === 0) {
        //   return [];
        // }
        //
        // let valid = await this.$refs['listForm'].validate();
        // if (valid) {
        //   console.log('this.form.monitorList', this.form.monitorList);
        //   let p = this.form.monitorList.map((i) => {
        //     i.image =
        //       i.imageObj && i.imageObj.length > 0
        //         ? i.imageObj[0].id
        //         : undefined;
        //     delete i.imageObj;
        //     return i;
        //   });
        //
        //   return p;
        // } else {
        //   return false;
        // }
        if (this.form.monitorList.length === 0) {
          return [];
        }
        let valid = await this.$refs['listForm'].validate();
        if (valid) {
          let params;
          if (this.edit) {
            params = this.form.monitorList;
          } else {
            params = this.form.monitorList.map((i) => {
              return {
                lubricateCycle: i.lubricateCycle,
                equipmentMonitorId: i.equipmentMonitorId,
                floatTime: i.floatTime,
                lubricateMethodsId: i.lubricateMethodsId,
                oilTypeId: i.oilTypeId
              };
            });
          }
          return params;
        } else {
          return false;
        }
      },

      resetForm() {
        this.num = 0;
        this.form.monitorList = [];
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    margin-bottom: 10px;
  }

  .el-form {
    padding-bottom: 50px;
  }

  /deep/ {
    .el-table__cell {
      padding: 0;
    }

    .el-input__inner {
      padding-right: 10px !important;
    }

    .el-form-item--small {
      margin-top: 16px;
    }

    .el-table .warning-row {
      background: #d3dcecff !important;
    }

    .el-table__header {
      line-height: 50px !important;
    }

    .el-form-item__content {
      width: calc(100% - 25px) !important;
    }
  }
</style>
