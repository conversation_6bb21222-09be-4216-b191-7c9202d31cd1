<template>
  <div>
    <el-form
      :model="form"
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="110px"
      size="small"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="油品类型" prop="name">
            <el-input
              placeholder="请输入油品类型"
              v-model.trim="form.name"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              placeholder="请输入备注"
              v-model.trim="form.remark"
              maxlength="200"
              clearable
              show-word-limit
              autosize
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'AddTypeInfo',
    props: {
      initData: {
        type: Object,
        default: () => {}
      }
    },

    data() {
      return {
        form: {
          name: undefined,
          remark: undefined
        },
        edit: false,
        rules: {
          name: [
            {
              required: true,
              message: '请输入油品类型',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    created() {},
    computed: {},
    methods: {
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return {
            ...this.form
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs['baseForm'].resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-input-number {
        width: 100%;

        .el-input__inner {
          text-align: left;
        }
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
