<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '130px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
      size="small"
    >
      <el-descriptions-item label="故障缺陷名称：">{{
        details.faultName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="故障缺陷类型：">{{
        details.faultTypeName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="异常等级：">{{
        details.faultLevelName || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        default: () => {
          return {};
        }
      },
      //  上报的生成维修工单
      repairNo: {
        type: String,
        default: undefined
      }
    },
    components: {},
    data() {
      return {};
    },
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
