<template>
  <view-info where="position-bearing"></view-info>
</template>

<script>
  import ViewInfo from './viewInfo/index.vue';
  export default {
    name: 'DeviceBasic',
    components: {
      ViewInfo
    },
    data() {
      return {
        loading: false,
        list: []
      };
    },

    mounted() {},
    methods: {}
  };
</script>

<style scoped lang="scss">
  .top-info {
    margin-bottom: 10px;
  }

  ::v-deep {
    .el-tabs__content {
      flex: 1;
    }

    .el-tabs__header {
      margin: 0 0 10px;
    }
  }
</style>
