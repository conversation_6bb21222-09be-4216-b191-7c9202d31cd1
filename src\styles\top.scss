$height: 64px;

.avue-top {
    position: relative;
    box-sizing: border-box;
    height: $height;
    padding: 0 20px;
    color: rgba(0, 0, 0, 65%);
    font-size: 28px;
    line-height: $height;
    white-space: nowrap;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 15%);

    .el-menu-item{
        i,span{
            font-size: 13px;
        }
    }

    .el-menu--horizontal>.el-menu-item {
      height: $height;
      line-height: $height;
    }
}

.avue-breadcrumb {
    height: 100%;

    i{
        font-size: 30px !important;
    }

    &--active {
        transform: rotate(90deg);
    }
}

.top-menu {
    box-sizing: border-box;

    .el-menu-item {
        padding: 0 10px;

        //border: none !important;
    }
}

.top-search {
    position: absolute !important;
    top: 0;
    left: 20px;
    width: 400px !important;
    line-height: $height;

    .el-input__inner {
        font-size: 13px;
        background-color: transparent;
        border: none;
    }
}

.top-bar__img {
    box-sizing: border-box;
    width: 30px;
    height: 30px;
    margin: 0 8px 0 5px;
    padding: 2px;
    vertical-align: middle;
    border: 1px solid #eee;
    border-radius: 100%;
}

.top-bar__left,
.top-bar__right {
    position: absolute;
    top: 0;
    height: $height;

    i{
        line-height: $height;
    }
}

.top-bar__left {
    left: 20px;
}

.top-bar__right {
    right: 20px;
    display: flex;
    align-items: center;
}

.top-bar__item {
    position: relative;
    display: inline-block;
    height: $height;
    margin: 0 10px;
    font-size: 14px;

    &--show {
        display: inline-block !important;
    }

    .el-badge__content.is-fixed{
        top: 12px;
        right: 5px;
    }
}

.top-bar__title {
    box-sizing: border-box;
    height: 100%;
    padding: 0 40px;
    overflow: hidden;
    font-weight: 400;
    font-size: inherit;
    white-space: nowrap;
    text-overflow: ellipsis;
}
