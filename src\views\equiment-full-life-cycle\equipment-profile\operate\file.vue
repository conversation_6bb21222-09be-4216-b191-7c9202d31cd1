<template>
  <div class="top-info">
    <upload-file
      accept=".jpg, .png, .jpeg, .xlsx, .pdf, .docx, .md"
      v-model="attachList"
      url="/api/szyk-system/attach/put-file-attach-for-simas"
      :showFile="false"
      @input="handleSuccess"
      ref="file"
      :limit="1"
    ></upload-file>
    <el-table
      class="table"
      :data="list"
      row-key="id"
      size="small"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="originalName"
        label="文件名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="type"
        label="文件类别"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-view"
            type="text"
            size="small"
            @click="download(scope.row)"
            >下载</el-button
          >

          <el-popconfirm
            title="确定删除吗？"
            @confirm="() => handleDelete(scope)"
          >
            <el-button
              icon="el-icon-delete"
              slot="reference"
              type="text"
              size="small"
              style="margin-left: 10px"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import UploadFile from '@/components/upload-file.vue';
  export default {
    name: 'DeviceBasicList',
    components: { UploadFile },
    props: {
      initData: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        list: [],
        attachList: []
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData.equipmentFileInfoList);
          }
        }
      }
    },
    mounted() {},

    methods: {
      download(row) {
        this.$refs['file'].handleDownLoad({
          data: { id: row.id, originalName: row.originalName }
        });
      },
      setData(attachInfoList) {
        if (attachInfoList) {
          let data = attachInfoList.map((i) => {
            let item = {
              ...i,
              num: 1,
              type: this.getString(i.originalName)
            };
            this.attachList.push(i);
            return item;
          });
          this.list = data;
        } else {
          this.list = [];
        }
      },
      //  上传成功
      handleSuccess(file) {
        let data = file.map((i) => {
          return {
            data: {
              ...i
            },
            id: i.id,
            originalName: i.originalName,
            num: 1,
            type: this.getString(i.originalName)
          };
        });
        this.list = data; // [...this.list, ...data];
      },
      getString(str) {
        const lastDotIndex = str.lastIndexOf('.');
        let result;
        if (lastDotIndex !== -1) {
          // 截取点后面的部分
          result = str.slice(lastDotIndex + 1);
        } else {
          // 如果没有找到点，则返回原字符串
          result = str;
        }
        return result;
      },
      validForm() {
        if (this.list.length === 0) {
          this.$message.warning('请上传文件');
          return;
        }
        return this.list;
      },
      resetForm() {
        this.attachList = [];
        this.list = [];
      },
      async handleDelete(scope) {
        this.list.splice(scope.$index, 1);
        this.$refs['file'].handleRemove(scope.row, this.list);
      }
    }
  };
</script>

<style scoped lang="scss"></style>
