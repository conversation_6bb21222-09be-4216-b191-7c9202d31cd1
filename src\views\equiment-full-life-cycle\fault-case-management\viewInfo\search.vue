<template>
  <el-form
    class="_form search-form"
    ref="form"
    :model="form"
    size="small"
    :inline="true"
    label-suffix="："
  >
    <el-form-item label="故障缺陷编号" prop="no">
      <el-input
        v-model.trim="form.no"
        size="small"
        placeholder="请输入故障缺陷编号"
        style="margin-right: 10px"
        :maxlength="20"
        clearable
      >
      </el-input>
    </el-form-item>
    <el-form-item label="故障缺陷名称" prop="faultName">
      <el-input
        v-model.trim="form.faultName"
        size="small"
        placeholder="请输入故障缺陷名称"
        style="margin-right: 10px"
        :maxlength="20"
        clearable
      >
      </el-input>
    </el-form-item>
    <el-form-item label="故障缺陷类型" prop="faultType">
      <el-select
        v-model="form.faultType"
        placeholder="请选择故障缺陷类型"
        clearable
      >
        <el-option
          v-for="dict in serviceDicts.type['repair_type']"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
    </el-form-item>

    <el-form-item>
      <btn type="search" @click="onSubmit" />
      <btn type="reset" @click="reset" />
    </el-form-item>
  </el-form>
</template>

<script>
  import 'element-ui/lib/theme-chalk/display.css';
  export default {
    name: 'alarmSearch',
    components: {},
    serviceDicts: ['repair_type'],
    data() {
      return {
        form: {
          no: undefined,
          faultName: undefined,
          faultType: undefined
        }
      };
    },
    created() {},
    methods: {
      onSubmit() {
        let searchParams = {
          ...this.form
        };
        this.$emit('query', searchParams);
      },
      reset() {
        this.$refs['form'].resetFields();
        this.$emit('query', this.form);
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep {
    .el-form {
      margin-bottom: 15px;
    }
  }
</style>
