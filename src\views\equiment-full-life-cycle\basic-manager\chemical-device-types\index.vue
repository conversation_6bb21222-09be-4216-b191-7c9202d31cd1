<template>
  <basic-container :auto-height="true">
    <el-table
      v-loading="loading"
      :data="tableData"
      ref="treeTable"
      border
      size="small"
      :header-cell-style="{ background: '#fafafa' }"
      height="calc(100% - 50px)"
    >
      <el-table-column prop="#" type="index" label="#" align="center">
      </el-table-column>
      <el-table-column prop="name" label="标准名称" align="left">
      </el-table-column>

      <el-table-column prop="address" label="操作" align="center" width="250">
        <template v-slot="scope">
          <el-button
            v-if="permission.standard_preview_btn"
            type="text"
            size="small"
            link
            @click="preview(scope.row)"
            >预览</el-button
          >
          <el-button type="text" size="small" link @click="quote(scope.row)"
            >引用</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!--     引用弹窗-->
    <quote-dialog ref="quoteDialog"></quote-dialog>
  </basic-container>
</template>

<script>
  import Pagination from '@/components/pagination';
  import QuoteDialog from './component/quote.vue';
  import { getDeviceTypeTemplatePageList } from '@/api/equiment-full-life-api/device-type';
  import { mapGetters } from 'vuex';
  export default {
    name: 'DeviceBasicList',
    components: {
      Pagination,
      QuoteDialog
    },
    props: {},
    data() {
      return {
        maps: new Map(),
        loading: false,
        total: 0,
        tableData: [{ name: '标准' }],
        searchParams: {
          size: 10,
          current: 1
        }
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    mounted() {
      this.getList();
    },
    methods: {
      async getList() {
        try {
          let res = await getDeviceTypeTemplatePageList({
            parentId: 0,
            ...this.searchParams
          });
          this.tableData = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          console.log(e);
        }
      },
      // 分类标准引用
      preview(row) {
        this.$router.push({
          path: '/equiment-full-life-cycle/basic-manager/chemical-device-types/standard-preview',
          query: {
            templateId: row.id
          }
        });
      },
      //  点击引用
      quote(row) {
        this.$refs.quoteDialog.show(row.id);
      }
    }
  };
</script>

<style scoped lang="scss"></style>
