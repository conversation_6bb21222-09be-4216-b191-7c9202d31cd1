<template>
  <basic-container class="table-content" :auto-height="true" no-scrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        icon="el-icon-plus"
        type="primary"
        v-if="permission['exterior-repairs-add-repair-export']"
        size="small"
        @click="onOperateClick('add')"
        >新增</el-button
      >
      <!-- <el-button
        icon="el-icon-upload2"
        v-if="permission['exterior-repairs-add-repair-export']"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column prop="no" label="工单编号" width="120" align="center">
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.no || '-' }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="sourceName"
        label="工单来源"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.sourceName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="repairTypeName"
        label="报修类型"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.repairTypeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentModel"
        label="设备型号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentModel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="monitorName"
        label="维修部位"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.monitorName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="reportUserName"
        label="报修人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.reportUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="tel"
        label="报修人电话"
        align="center"
        width="94px"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.tel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="receiveUserName"
        label="跟进人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.followUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="followUserTel"
        label="跟进人电话"
        align="center"
        width="94px"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.followUserTel || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="是否需审核" align="center" show-overflow-tooltip>
        <template v-slot="{ row }">{{
          row.isNeedApproval ? '是' : '否'
        }}</template>
      </el-table-column>
      <el-table-column label="审核人员" align="center" show-overflow-tooltip>
        <template v-slot="{ row }">{{
          (row.isNeedApproval && row.approvalUserName) || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="reportTime"
        label="报修时间"
        align="center"
        width="90px"
      >
        <template v-slot="{ row }">
          <template v-if="!row.reportTime">-</template>
          <template v-else>
            <div v-for="item in row.reportTime.split(' ')" :key="item">
              {{ item }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="completeTime"
        width="95px"
        label="预计完成时间"
        align="center"
      >
        <template v-slot="{ row }">
          <template v-if="!row.completeTime">-</template>
          <template v-else>
            <div v-for="item in row.completeTime.split(' ')" :key="item">
              {{ item }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="handleTime"
        label="处理时间"
        align="center"
        width="90px"
      >
        <template v-slot="{ row }">
          <template v-if="!row.handleTime">-</template>
          <template v-else>
            <div v-for="item in row.handleTime.split(' ')" :key="item">
              {{ item }}
            </div>
          </template>
        </template></el-table-column
      >
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="110px"
      >
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.statusName || '-' }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        key="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="createTime"
        label="创建时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="updateTime"
        label="更新时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateTime || '-' }}</template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="166" fixed="right">
        <template slot-scope="scope">
          <section class="cell-operate-class">
            <el-button type="text" size="small" @click="detail(scope.row)"
              >查看</el-button
            >
            <el-button
              v-if="
                permission['exterior-repairs-order-close-verify'] &&
                scope.row.status === 0
              "
              type="text"
              size="small"
              @click="onDispatchClick(scope.row)"
              >派单</el-button
            >
            <el-button
              v-if="
                permission['exterior-repairs-order-close-verify'] &&
                [0, 1, 3, 5, 6].includes(scope.row.status)
              "
              type="text"
              size="small"
              class="danger-btn"
              @click="handleClose(scope.row)"
              >关闭</el-button
            >
            <el-button
              v-if="
                permission['exterior-repairs-order-close-verify'] &&
                scope.row.status === 5
              "
              type="text"
              size="small"
              @click="handleVerify(scope.row, 'verify')"
              >验证</el-button
            >
            <el-button
              v-if="
                permission['exterior-repairs-add-repair-export'] &&
                [1, 6].includes(scope.row.status)
              "
              type="text"
              size="small"
              @click="overHaulHandle(scope.row)"
              >维修</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <detail-index ref="detailIndex" @success="getList"></detail-index>
    <repair-internal-operate
      ref="operate"
      @success="getList"
    ></repair-internal-operate>
    <repair-internal-dispatch
      ref="dispatch"
      @success="getList"
    ></repair-internal-dispatch>
    <Maintenance ref="maintenance" @success="getList"></Maintenance>
    <VerifyApproval ref="verify" @success="getList"></VerifyApproval>
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import Pagination from '@/components/pagination';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  import {
    closeRepairApi,
    getExternalPageApi
  } from '@/api/equiment-full-life-api/repair';
  import RepairInternalOperate from '@/views/equiment-full-life-cycle/repair/external/operate/index.vue';
  import Maintenance from '@/views/equiment-full-life-cycle/repair/external/perform-overhaul/index.vue';
  import VerifyApproval from '@/views/equiment-full-life-cycle/repair/external/perform-overhaul/verify.vue';
  import RepairInternalDispatch from '@/views/equiment-full-life-cycle/repair/external/components/dispatch.vue';
  import dayjs from 'dayjs';
  import { mapGetters } from 'vuex';
  export default {
    name: 'DeviceBasicList',
    components: {
      RepairInternalDispatch,
      RepairInternalOperate,
      Search,
      DetailIndex,
      Pagination,
      Maintenance,
      VerifyApproval
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    activated() {
      const { fromAssist, status } = this.$route.params;
      if (fromAssist) {
        let fields = { field: 'status', value: status };
        this.$refs.search.setFields(fields);
      } else {
        this.getList();
      }
    },
    computed: {
      ...mapGetters(['permission'])
    },
    mounted() {
      // this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      handleVerify(row) {
        this.$refs.verify.show(row.no);
      },
      // 关闭
      handleClose(row) {
        this.$confirm(`确定关闭该维修单吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            await this.onCloseRepair(row);
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },
      overHaulHandle(row) {
        this.$refs.maintenance.show(row.no);
      },
      onOperateClick() {
        this.$refs.operate.show();
      },
      onDispatchClick(row) {
        this.$refs.dispatch.show(row);
      },
      async exportExcel() {
        this.exportParams.bizType = 'external';
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/repair/export-repair?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/repair/export-repair?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          `外委维修工单${dayjs().format('YYYY-MM-DD HH:mm:ss')}.xlsx`
        );
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },
      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getExternalPageApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      detail(row, type) {
        this.$refs['detailIndex'].show(row.no, type);
      },
      async onCloseRepair(row) {
        try {
          await closeRepairApi(row.id);
          this.$message.success('操作成功');
          this.getList();
        } catch (e) {
          console.log('error closeRepairApi', e);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
