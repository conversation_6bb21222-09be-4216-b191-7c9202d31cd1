import request from '@/router/axios';
// 设备盘点相关接口
// 盘点计划分页列表
export const getDevicePartsCheckListApi = (params) =>
  request({
    url: '/api/szyk-simas/equipmentinventoryplan/page',
    method: 'get',
    params
  });

// 提交
export const submitDevicePartsInventoryApi = (data) =>
  request({
    url: `/api/szyk-simas/equipmentinventoryplan/save`,
    method: 'post',
    data
  });

// 获取详情
export const getDevicePartsInventoryViewApi = (id) =>
  request({
    url: `/api/szyk-simas/equipmentinventoryplan/detail/${id}`,
    method: 'get'
  });

//  启动
export const startDevicePartsCheckApi = (id) =>
  request({
    url: `/api/szyk-simas/equipmentinventoryplan/start/${id}`,
    method: 'post'
  });

// 终止
export const stopDevicePartsCheckApi = (id) =>
  request({
    url: `/api/szyk-simas/equipmentinventoryplan/stop/${id}`,
    method: 'put'
  });
// 删除
export const delDevicePartsCheckApi = (id) =>
  request({
    url: `/api/szyk-simas/equipmentinventoryplan/removeById/${id}`,
    method: 'delete'
  });

// 盘点工单分页列表
export const getDevicePartsCheckListStaticApi = (params) =>
  request({
    url: '/api/szyk-simas/equipmentinventoryorder/page',
    method: 'get',
    params
  });

// 盘点工单完成
export const completeDevicePartsInventoryApi = (id) =>
  request({
    url: `/api/szyk-simas/equipmentinventoryorder/complete/${id}`,
    method: 'put'
  });

// 盘点工单获取详情
export const getDeviceInventoryOrderViewApi = (id) =>
  request({
    url: `/api/szyk-simas/equipmentinventoryorder/detail/${id}`,
    method: 'get'
  });

// 盘点工单明细
export const getDeviceInventoryOrderListApi = (params) =>
  request({
    url: '/api/szyk-simas/equipmentinventoryitem/page',
    method: 'get',
    params
  });

// 统计
export const getDeviceInventoryOrderStatisticsApi = (params) =>
  request({
    url: '/api/szyk-simas/equipmentinventoryitem/statistics',
    method: 'get',
    params
  });

// 盘点工单暂存接口
export const saveDeviceInventoryOrderApi = (data) =>
  request({
    url: `/api/szyk-simas/equipmentinventoryitem/draft`,
    method: 'put',
    data
  });
//  盘点提交
export const submitDevicePartsInventoryOrderApi = (planId, data) =>
  request({
    url: `/api/szyk-simas/equipmentinventoryitem/submit/${planId}`,
    method: 'put',
    data
  });
