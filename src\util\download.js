import { Message } from 'element-ui';
import { getToken } from './auth';
import { Base64 } from 'js-base64';
import website from '@/config/website';

const env = '/api';
const downloadApi = {
  bigFileDownLoad: '/minio/tasks/download/', // 断点续传下载接口
  download: '/szyk-resource/oss/endpoint/download/'
};

export const downloadFile = (url, name = '', method = 'GET') => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.open(method, url, true);
    xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
    xhr.setRequestHeader('Szyk-Auth', `bearer ${getToken()}`);
    xhr.setRequestHeader(
      'Authorization',
      `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`
    );
    xhr.send();
    xhr.responseType = 'blob';
    xhr.onload = function () {
      if (this.status === 200) {
        const that = this;
        const blob = this.response;
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = function (e) {
          const a = document.createElement('a');
          const contentDis = that.getResponseHeader('content-disposition');
          if (contentDis) {
            // 解密
            let contentDis_ = decodeURIComponent(escape(contentDis));
            const fileName = contentDis_.substring(
              contentDis_.lastIndexOf('=') + 1
            );
            console.log(fileName);
            const fileType = fileName.substring(fileName.lastIndexOf('.') + 1);
            a.download = name ? name + '.' + fileType : fileName;
            a.href = e.target.result;
            a.target = '__blank';
            a.style.display = 'none';
            const body = document.querySelector('body');
            body.append(a);
            a.click();
            body.removeChild(a);
            resolve();
            reader.onerror = function () {
              reject(new Error('文件读取错误'));
              Message.warning('文件读取错误');
            };
          } else {
            Message.warning('数据为空，文件下载失败');
          }
        };
      } else {
        reject(new Error());
        Message.warning('下载错误');
      }
    };
  });
};

// 下载文件
export const downloadUrl = (type, fileId = '', params = {}) => {
  if (downloadApi[type]) {
    let url = env + downloadApi[type] + fileId;
    // 如果params参数不为空, 则将每一项拆分拼入url
    if (JSON.stringify(params) !== '{}') {
      return getJointUrl(url, params);
    }
    return url;
  }
};

// 将参数每一项的key和value放入url中(不考虑value是对象情况)
function getJointUrl(url, params) {
  let adds = url + '?';

  for (const key in params) {
    const value = params[key];
    if (value) {
      adds += `${encodeURIComponent(key)}=${encodeURIComponent(value)}&`;
    }
  }
  return adds.slice(0, -1);
}
