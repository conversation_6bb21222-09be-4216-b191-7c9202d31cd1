import request from '@/router/axios';

export const getDeviceTypeLazyList = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_category/lazy-list',
    method: 'get',
    params: {
      ...params
    }
  });
};

// 获取有设备数量的懒加载树
export const getDeviceTypeLazyListWithCount = (params) => {
  return request({
    url: '/api/szyk-common/equipment_category/lazy-list-with-device-count',
    method: 'get',
    params: {
      ...params
    }
  });
};

// 分页page 搜索的时候使用
export const getDeviceTypePageList = (params) => {
  return request({
    url: '/api/szyk-common/equipment_category/page',
    method: 'get',
    params: {
      ...params
    }
  });
};
// 详情接口
export const getDeviceTypeDetail = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_category/detail',
    method: 'get',
    params
  });
};

//删除
export const removeDeviceType = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_category/check-remove',
    method: 'post',
    params
  });
};

// 更新添加
export const addOrEditDeviceType = (data) => {
  return request({
    url: '/api/szyk-simas/equipment_category/submit',
    method: 'post',
    data
  });
};

// 搜索类型全数据
export const getDeviceTypeAllList = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_category/searchCategory',
    method: 'get',
    params
  });
};

//  设备分类模板分页
export const getDeviceTypeTemplatePageList = (params) => {
  return request({
    url: '/api/szyk-simas/equipmentcategorytemplate/page',
    method: 'get',
    params
  });
};
//  点击预览的时候的接口
export const getDeviceTypeTemplateDetail = (params) => {
  return request({
    url: '/api/szyk-simas/equipmentcategorytemplatedetail/tree',
    method: 'get',
    params
  });
};
// 引用接口
export const getDeviceTypeReference = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_category/reference',
    method: 'post',
    params
  });
};

// 设备类型树列表
export const getDeviceTypeTreeList = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_category/tree',
    method: 'get',
    params
  });
};
