<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @close="close"
    class="detail-drawer"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <section>
        <span class="el-base-title">基础信息</span>
        <base-info :details="details"></base-info>
      </section>
      <section>
        <span class="el-base-title">报废设备列表</span>
        <file-name :details="details"></file-name>
      </section>
      <div style="padding-bottom: 50px">
        <logs ref="log"></logs>
      </div>
    </div>
    <div class="oper_btn" v-if="type === 'examine'">
      <el-button size="small" type="success" @click="operate(2)"
        >同意报废</el-button
      >
      <el-button size="small" type="danger" @click="operate(3)"
        >驳回申请</el-button
      >
      <btn type="close" @click="close"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from './base-info.vue';
  import FileName from './file.vue';
  import { userDetail, userAudit } from '@/api/equiment-full-life-api/scrap';
  import Logs from '@/views/equiment-full-life-cycle/components/logs/index.vue';
  export default {
    name: 'RepairViewIndex',
    components: { Logs, BaseInfo, FileName },
    data() {
      return {
        id: undefined,
        type: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {} // 详情数据
      };
    },

    methods: {
      operate(type) {
        this.getUserAudit(type, this.details.id);
      },
      //  同意
      async getUserAudit(type, id) {
        this.loading = true;
        try {
          await userAudit({ status: type, id: id });
          this.visible = false;
          this.$emit('success');
          this.$message({
            type: 'success',
            message: '操作成功'
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      async getDetail(id) {
        this.loading = true;
        try {
          const res = await userDetail({ id: id });
          this.details = res.data.data;
          this.loading = false;
          await this.$refs['log'].getLogs(this.details.id, 'EQUIPMENT_SCRAP');
        } catch (e) {
          this.loading = false;
        }
      },

      // 点击展示
      async show(id, type) {
        console.log(type);
        this.type = type;
        this.visible = true;
        if (id) {
          this.id = id;
          await this.getDetail(id);
        }
      },

      // 关闭弹窗
      close() {
        this.visible = false;
        this.detail = {};
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
