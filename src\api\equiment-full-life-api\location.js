import request from '@/router/axios';

export const getLocationPage = (params) => {
  return request({
    url: '/api/szyk-simas/location/page',
    method: 'get',
    params: {
      ...params
    }
  });
};
export const getLocationLazyList = (params) => {
  return request({
    url: '/api/szyk-simas/location/lazy-list',
    method: 'get',
    params: {
      ...params
    }
  });
};

export const getLocationTree = (params) => {
  return request({
    url: '/api/szyk-common/location/lazy-tree',
    method: 'get',
    params: {
      ...params
    }
  });
};
export const getLocationDetail = ({ id }) => {
  return request({
    url: '/api/szyk-simas/location/detail',
    method: 'get',
    params: {
      id
    }
  });
};

export const removeLocation = (ids) => {
  return request({
    url: '/api/szyk-simas/location/check-remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const addOrEditLocation = (data) => {
  return request({
    url: '/api/szyk-simas/location/submit',
    method: 'post',
    data
  });
};
//  树形结构
export const getLocationTreeList = (params) => {
  return request({
    url: '/api/szyk-common/location/tree',
    method: 'get',
    params
  });
};

// 地点搜索树 2024-8-23 测试点检标准的时候，左边的树搜索 用的接口
export const getLocationTreeSearch = (params) => {
  return request({
    url: '/api/szyk-common/location/searchTree',
    method: 'get',
    params
  });
};

// 设备定位 - 移动记录 - 新增
export const postEquipmentMoveLogAdd = (data) => {
  return request({
    url: '/api/szyk-simas/device-move-record/submit',
    method: 'post',
    data
  });
};

// 设备定位 - 移动记录 - 列表
export const getEquipmentMoveLogList = (params) => {
  return request({
    url: '/api/szyk-simas/device-move-record/page',
    method: 'get',
    params
  });
};
