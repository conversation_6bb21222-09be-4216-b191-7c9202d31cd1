<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="100px"
      label-position="right"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="领用名称" prop="name">
            <el-input
              placeholder="请输入领用名称"
              v-model.trim="form.name"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="领用部门" prop="receiveDeptName">
            <el-input
              style="width: 100%"
              placeholder="请选择领用部门"
              v-model.trim="form.receiveDeptName"
              @focus.prevent="onSelectDeptClick()"
              readonly
              clearable
            >
              <template slot="append">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      form.receiveDeptName = undefined;
                      form.receiveDeptId = undefined;
                      form.receiveUserId = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领用人" prop="receiveUserId">
            <el-select
              v-loading="userLoading"
              v-model="form.receiveUserId"
              filterable
              placeholder="请选择领用人"
              clearable
            >
              <el-option
                v-for="item in userList"
                :key="item.id"
                :label="item.realName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="领用原因" prop="remark">
            <el-input
              placeholder="请输入领用原因"
              v-model.trim="form.remark"
              :maxlength="200"
              clearable
              autosize
              show-word-limit
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import { getUserListByDeptId } from '@/api/equiment-full-life-api/common';
  import DeptDialog from '@/components/dept-dialog/index.vue';

  export default {
    name: 'AddDeviceInfo',
    props: {
      initData: {
        type: Object,
        default: () => {}
      }
    },
    components: {
      DeptDialog
    },

    data() {
      return {
        userList: [], // 领用人员
        userLoading: false,
        form: {
          name: undefined,
          receiveDeptName: undefined,
          receiveDeptId: undefined,
          receiveUserId: undefined,
          remark: undefined
        },
        warehouse_old: undefined,
        edit: false,

        rules: {
          name: [
            {
              required: true,
              message: '请输入领用名称',
              trigger: ['change']
            }
          ],
          receiveDeptName: [
            {
              required: true,
              message: '请选择领用部门',
              trigger: 'change'
            }
          ],
          receiveUserId: [
            {
              required: true,
              message: '请选择领用人',
              trigger: 'change'
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    created() {},
    computed: {},
    methods: {
      // 选择部门
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.receiveDeptId = dept.id;
        this.form.receiveDeptName = dept.deptName;
        this.form.receiveUserId = undefined;

        this.getUser(dept.id);
      },
      // 选择人员
      async getUser(deptId) {
        this.userLoading = true;
        try {
          let params = {
            deptId: deptId
            // includeSubDepartments: true
          };
          let res = await getUserListByDeptId(params);
          this.userList = res.data.data;
          this.userLoading = false;
        } catch ({ message }) {
          this.userLoading = false;
          console.log(message);
        }
      },
      setData(initData) {
        if (initData.receiveDeptId) {
          this.getUser(initData.receiveDeptId);
        }
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },

      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return this.form;
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs['baseForm'].resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-input-number {
        width: 100%;

        .el-input__inner {
          text-align: left;
        }
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
