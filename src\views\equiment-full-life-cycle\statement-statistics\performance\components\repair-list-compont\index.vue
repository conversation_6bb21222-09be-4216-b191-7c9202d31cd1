<template>
  <div class="comp-wrapper">
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <section style="display: flex; justify-content: flex-end">
        <el-button
          icon="el-icon-arrow-left"
          type="primary"
          plain
          size="small"
          @click="back"
          >返回</el-button
        >
      </section>
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      border
      height="100%"
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column prop="no" label="工单编号" width="150" align="center">
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.no || '-' }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="sourceName"
        label="工单来源"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.sourceName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="bizTypeName"
        label="工单类型"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="repairTypeName"
        label="报修类型"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.repairTypeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="equipmentName" label="设备名称" align="center">
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="equipmentModel" label="设备型号" align="center">
        <template slot-scope="scope">
          {{ scope.row.equipmentModel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="monitorName"
        label="维修部位"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.monitorName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="reportUserName"
        label="报修人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.reportUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="tel"
        label="报修人电话"
        align="center"
        width="94px"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.tel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="receiveUserOrFollowUserName"
        label="维修人/跟进人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.receiveUserOrFollowUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="receiveUserTelOrFollowUserTel"
        label="维修人/跟进人电话"
        align="center"
        width="94px"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.receiveUserTelOrFollowUserTel || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="是否需审核" align="center" show-overflow-tooltip>
        <template v-slot="{ row }">{{
          row.isNeedApproval ? '是' : '否'
        }}</template>
      </el-table-column>
      <el-table-column label="审核人员" align="center" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.approvalUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="reportTime"
        label="报修时间"
        align="center"
        width="90px"
      >
        <template v-slot="{ row }">
          <template v-if="!row.reportTime">-</template>
          <template v-else>
            <div v-for="item in row.reportTime.split(' ')" :key="item">
              {{ item }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        width="93px"
        prop="completeTime"
        label="预计完成时间"
        align="center"
      >
        <template v-slot="{ row }">
          <template v-if="!row.completeTime">-</template>
          <template v-else>
            <div v-for="item in row.completeTime.split(' ')" :key="item">
              {{ item }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="handleTime"
        label="处理时间"
        align="center"
        width="90px"
      >
        <template v-slot="{ row }">
          <template v-if="!row.handleTime">-</template>
          <template v-else>
            <div v-for="item in row.handleTime.split(' ')" :key="item">
              {{ item }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="90px"
      >
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.statusName || '-' }}</span
          >
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            style="margin-right: 10px"
            type="text"
            size="small"
            @click="detail(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      class="page"
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />

    <internal-detail ref="internal"></internal-detail>
    <external-detail ref="external"></external-detail>
  </div>
</template>

<script>
  import InternalDetail from '@/views/equiment-full-life-cycle/repair/internal/detail/index.vue'; // 查看详情页面
  import ExternalDetail from '@/views/equiment-full-life-cycle/repair/external/detail/index.vue'; // 查看详情页面
  import Search from './search';
  import Pagination from '@/components/pagination';

  import { getImplementDetailList } from '@/api/equiment-full-life-api/statement-statistics';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      InternalDetail,
      ExternalDetail,
      Pagination
    },
    props: {
      //  这个工单的id 是 点击设备维修率 第一级页面，中的列表： 查看必须要传递参数。
      orderId: {
        type: String,
        default: undefined
      }
    },
    watch: {
      orderId: {
        immediate: true,
        handler(val) {
          if (val) {
            this.searchParams.current = 1;
            this.getList(val);
          } else {
            this.$message.error('设备id不能为空');
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    mounted() {
      // this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      back() {
        this.$emit('compView', { comp: 'repair' });
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },
      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getImplementDetailList({
            ...this.searchParams,
            equipmentId: this.orderId
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 点击查看的时候,查看的时候，会根据类型来判断 打开哪一个详情页面
      detail(row, type) {
        let ref = row.bizType === 'INTERNAL' ? 'internal' : 'external';
        this.$refs[ref].show(row.no, type);
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }

  .comp-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%; /* 使容器高度充满整个视口 */
  }
</style>
