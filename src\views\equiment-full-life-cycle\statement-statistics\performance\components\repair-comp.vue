<template>
  <div class="comp-wrapper">
    <!-- 搜索 -->
    <div class="search-wrapper">
      <el-form
        label-suffix="："
        :inline="true"
        ref="search"
        :model="searchParams"
        size="small"
      >
        <el-form-item label="设备名称" prop="equipmentName">
          <el-input
            style="width: 100%"
            v-model.trim="searchParams.equipmentName"
            placeholder="请输入设备名称"
            clearable
            :maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="设备类型" prop="categoryName">
          <el-input
            placeholder="请选择设备类型"
            type="text"
            v-model="searchParams.categoryName"
            readonly
            @focus.prevent="selectAssetCategory"
          >
            <template slot="append">
              <i
                class="el-icon-circle-close"
                @click="
                  () => {
                    searchParams.categoryId = undefined;
                    searchParams.categoryName = undefined;
                  }
                "
              ></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="统计部门" prop="deptId">
          <InputTree
            v-model="searchParams.deptId"
            lazy
            clearable
            :form="searchParams"
            :dic="deptData"
            style="width: 100%"
            :props="treeProps"
            :load="lazyLoad"
            :lazyLoading="lazyLoading"
            @search="lazySearch"
          ></InputTree>
        </el-form-item>
        <el-form-item label="统计时间" prop="timeType">
          <el-select
            v-model="searchParams.timeType"
            placeholder="请选择计划周期"
          >
            <el-option
              v-for="item in timeTypeOpts"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择日期" prop="time" class="_label">
          <el-date-picker
            v-model="searchParams.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <btn type="search" @click="query" />
          <btn type="reset" @click="reset" />
        </el-form-item>
      </el-form>
    </div>
    <!-- 总结 -->
    <div class="summary-wrapper">
      <div class="left">
        <div class="font-bold">设备状态统计</div>
        <div class="num">平均维修率: {{ sumVal }}</div>
      </div>
      <!-- <el-button
        icon="el-icon-upload2"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <!-- 表格 -->
    <el-table
      v-loading="loading"
      ref="table"
      class="table"
      :data="listData"
      row-key="id"
      size="small"
      height="calc(100% - 190px)"
      border
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="model"
        label="设备型号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.model || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="locationPath"
        label="设备位置"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.locationPath || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="设备状态"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${equipmentStatusColor(row.status)};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="repairDuration"
        label="总计维修时间"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="totalDuration"
        label="总时长"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="repairRate"
        label="维修率"
        align="center"
        show-overflow-tooltip
        sortable
      >
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="{ row }">
          <el-button size="small" type="text" @click="repairView(row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!--     选择设备类型-->
    <device-type-dialog
      ref="device-type"
      @success="getSingleRow"
    ></device-type-dialog>
  </div>
</template>

<script>
  import Pagination from '@/components/pagination';
  import DeviceTypeDialog from '@/views/equiment-full-life-cycle/components/select-asset-category-dialog/single-index.vue';
  import { getLazyList } from '@/api/system/dept';
  import { InputTree } from '@/components/yk-select-tree';
  import { getToken } from '@/util/auth';
  import { downloadFileBlob } from '@/util/util';
  import {
    getRepairVal,
    getRepairList
  } from '@/api/equiment-full-life-api/statement-statistics.js';

  export default {
    components: { Pagination, DeviceTypeDialog, InputTree },
    data() {
      return {
        loading: false,
        treeProps: {
          label: 'deptName',
          value: 'id',
          isLeaf: (row) => !row.hasChildren,
          formLabel: 'deptName',
          formValue: 'deptId'
        },
        timeTypeOpts: [
          { label: '今日', value: 'TODAY' },
          { label: '近7天', value: 'SEVEN_DAYS' },
          { label: '近30天', value: 'THIRTY_DAYS' },
          { label: '近一年', value: 'ONE_YEAR' }
        ],
        deptData: [],
        lazyLoading: false,
        searchParams: {
          equipmentName: undefined,
          categoryId: undefined,
          categoryName: undefined,
          deptName: undefined,
          deptId: undefined,
          timeType: 'THIRTY_DAYS', // 近30天
          time: [],
          current: 1,
          size: 10
        },
        sumVal: undefined,
        // 列表
        listData: [],
        total: 0
      };
    },
    mounted() {
      this.refresh();
    },
    methods: {
      // 设备台账 设备状态状态颜色
      equipmentStatusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#35C24B'; // 在用
          case 1:
            return '#155CFF'; // 备用
          case 3:
            return '#EE7C11'; // 维修
          case 4:
            return '#E23F3F'; // 报废
        }
      },
      async lazySearch(title) {
        this.lazyLoad(null, null, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node || {};
        const { id } = data || {};
        let params = {
          deptName: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyList(title ? '' : id || 0, params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          if (title) {
            this.deptData = arr;
          } else {
            resolve && resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      //  点击查看，展示维修列表
      repairView(row) {
        this.$emit('compView', {
          comp: 'RepairList',
          paramsObj: row
        });
      },
      //  选择类型
      getSingleRow(row) {
        this.searchParams.categoryId = row.id;
        this.searchParams.categoryName = row.categoryName;
      },
      selectAssetCategory() {
        this.$refs['device-type'].show();
      },
      query() {
        const [startDate, endDate] = this.searchParams.time || [];
        this.searchParams.startDate = startDate;
        this.searchParams.endDate = endDate;
        this.searchParams.current = 1;
        this.getList();
        this.getSumVal();
      },
      reset() {
        this.searchParams.categoryId = undefined;
        this.searchParams.categoryName = undefined;
        this.searchParams.deptName = undefined;
        this.searchParams.deptId = undefined;
        this.$refs['search'].resetFields();
        this.query();
      },
      refresh() {
        this.getSumVal();
        this.getList();
      },
      // 获取统计数据
      async getSumVal() {
        try {
          const params = {
            ...this.searchParams,
            time: undefined,
            current: undefined,
            size: undefined
          };
          if (params.startDate && params.endDate) {
            params.timeType = undefined;
          }
          const { data } = await getRepairVal(params);
          this.sumVal = data.data;
        } catch (e) {
          console.error(e);
        }
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          const params = {
            ...this.searchParams,
            time: undefined
          };
          if (params.startDate && params.endDate) {
            params.timeType = undefined;
          }
          let res = await getRepairList(params);
          this.listData = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 导出
      exportExcel() {
        let path =
          '/api/szyk-simas/statistical-report/export-repair-percentage?';
        const params = {
          ...this.searchParams
        };
        delete params.categoryName;
        delete params.deptName;
        delete params.time;
        delete params.current;
        delete params.size;
        if (params.startDate && params.endDate) {
          params.timeType = undefined;
        }

        for (let key in params) {
          params[key] && (path += `${key}=${params[key]}&`);
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '设备维修率统计.xlsx'
        );
      }
    }
  };
</script>

<style lang="scss" scoped>
  .comp-wrapper {
    height: 100%;

    .search-wrapper {
      ::v-deep .el-form-item {
        margin-bottom: 10px;
      }
    }

    .summary-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      .left {
        display: flex;
        align-items: center;

        .font-bold {
          font-weight: bold;
        }

        .num {
          margin-left: 12px;
          color: #409eff;
        }
      }
    }
  }
</style>
