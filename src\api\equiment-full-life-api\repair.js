import request from '@/router/axios';
//  维修相关接口

// 内部分页列表
export const getInternalPageApi = (params) => {
  return request({
    url: `/api/szyk-simas/repair/internalPage`,
    method: 'get',
    params
  });
};

// 外委分页列表
export const getExternalPageApi = (params) => {
  return request({
    url: `/api/szyk-simas/repair/externalPage`,
    method: 'get',
    params
  });
};
// 维修详情
export const getInternalViewApi = (params) => {
  return request({
    url: `/api/szyk-simas/repair/view`,
    method: 'get',
    params
  });
};
// 派单调用获取备品备件详情
export const getDispatchViewApi = (params) => {
  return request({
    url: `/api/szyk-simas/repair/detail`,
    method: 'get',
    params
  });
};

// 新增编辑维修
export const addOrEditRepairApi = (data) => {
  return request({
    url: `/api/szyk-simas/repair/submit`,
    method: 'post',
    data
  });
};

// 维修验证
export const verifyRepairApi = (data) => {
  return request({
    url: `/api/szyk-simas/repair/verify`,
    method: 'post',
    data
  });
};

// 派单
export const dispatchRepairApi = (data) => {
  return request({
    url: `/api/szyk-simas/repair/dispatch`,
    method: 'post',
    data
  });
};

// 派单转外委
export const repairToExternalApi = (data) => {
  return request({
    url: `/api/szyk-simas/repair/toExternal`,
    method: 'post',
    data
  });
};

// 关闭
export const closeRepairApi = (id) => {
  return request({
    url: `/api/szyk-simas/repair/close?id=${id}`,
    method: 'post'
  });
};

// 检修模块接口 - -----------------------------------------------------------------------------
// 检修方式标接口
export const getCheckRepairTypeListPageApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul/methods/page`,
    method: 'get',
    params
  });
};
//  检修方式列表
export const getCheckRepairTypeListApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul/methods/list`,
    method: 'get',
    params
  });
};
// 检修编辑
export const addOrEditCheckRepairTypeApi = (data) => {
  return request({
    url: `/api/szyk-simas/overhaul/methods/submit`,
    method: 'post',
    data
  });
};

// 检修详情
export const getCheckRepairTypeViewApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul/methods/detail`,
    method: 'get',
    params
  });
};

// 设备检修标准----------------------------------------------------------------
// 检修详情
export const getCheckRepairDetailViewApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-standard/detail`,
    method: 'get',
    params
  });
};
// 检修操作
export const addOrEditCheckRepairDetailApi = (data) => {
  return request({
    url: `/api/szyk-simas/overhaul-standard/submit`,
    method: 'post',
    data
  });
};
// 检修清空标准
export const clearCheckRepairDetailApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-standard/clear`,
    method: 'post',
    params
  });
};

//  检修计划表接口----------------------------------------------------------------
// 分页列表
export const getCheckRepairPlanPageApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/page`,
    method: 'get',
    params
  });
};
// 新增接口
export const addCheckRepairPlanApi = (data) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/save`,
    method: 'post',
    data
  });
};
//  修改
export const editCheckRepairPlanApi = (data) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/update`,
    method: 'post',
    data
  });
};

// 详情
export const getCheckRepairPlanViewApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/detail`,
    method: 'get',
    params
  });
};
//  检修计划点击查看详情
export const getCheckRepairPlanCheckViewApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/view`,
    method: 'get',
    params
  });
};

// 逻辑删除
export const delCheckRepairPlanApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/remove`,
    method: 'post',
    params
  });
};
//  手动开始
export const startCheckRepairPlanApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/manual-start`,
    method: 'post',
    params
  });
};

// 手动停止
export const stopCheckRepairPlanApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/manual-stop`,
    method: 'post',
    params
  });
};
// 检修计划手动执行生成当天工单
export const manualPlan = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/manual-generate-orders`,
    method: 'post',
    params
  });
};
//  审核
export const checkCheckRepairPlanApi = (data) => {
  return request({
    url: `/api/szyk-simas/overhaul-plan/audit`,
    method: 'post',
    data
  });
};

// 检维修工单---------------------------------------------------------------------
// 分页列表
export const getCheckRepairOrderPageApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-order/page`,
    method: 'get',
    params
  });
};

// 详情
export const getCheckRepairOrderViewApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-order/detail`,
    method: 'get',
    params
  });
};

// 提交检修
export const submitCheckRepairOrderApi = (data) => {
  return request({
    url: `/api/szyk-simas/overhaul-order/overhaul`,
    method: 'post',
    data
  });
};
// 确认

export const confirmCheckRepairOrderApi = (data) => {
  return request({
    url: `/api/szyk-simas/overhaul-order/confirm`,
    method: 'post',
    data
  });
};

// 工单 - 批量审核
export const batchAuditOrder = (data) => {
  return request({
    url: `/api/szyk-simas/overhaul-order/confirmBatch`,
    method: 'post',
    data
  });
};

// 超时提醒时间性情
export const getCheckRepairOrderOverTimeApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-order/timeoutRemindDetail`,
    method: 'get',
    params
  });
};
// 设置超时提醒时间
export const setCheckRepairOrderOverTimeApi = (data) => {
  return request({
    url: `/api/szyk-simas/overhaul-order/setTimeoutRemind`,
    method: 'post',
    data
  });
};
// 即将超时分页
export const getCheckRepairOrderOverTimePageApi = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-order/timeoutPage`,
    method: 'get',
    params
  });
};

// 一键催办功能
export const getCheckRepairOrderUrgeApi = (data) => {
  return request({
    url: `/api/szyk-simas/app-home/urge`,
    method: 'post',
    data
  });
};
// 设备标准的分页分页
export const overhaulStandardPage = (params) => {
  return request({
    url: `/api/szyk-simas/overhaul-standard/devicePage`,
    method: 'get',
    params
  });
};
// 提交维修
export const repairApi = (data) => {
  return request({
    url: `/api/szyk-simas/repair/repair`,
    method: 'post',
    data
  });
};
