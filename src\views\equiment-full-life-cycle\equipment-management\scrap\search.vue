<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      label-position="left"
      class="search-form"
    >
      <el-form-item label="报废单号" prop="no">
        <el-input
          v-model.trim="form.no"
          placeholder="请输入报废单号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="报废名称" prop="name">
        <el-input
          v-model.trim="form.name"
          placeholder="请输入报废名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item label="报废单状态" prop="status">
        <el-select
          v-model="form.status"
          placeholder="请选择报废单状态"
          clearable
        >
          <el-option
            v-for="item in scrapStatus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择日期" prop="time" class="_label">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { scrapStatus } from './util';
  export default {
    name: 'DeviceListSearch',
    data() {
      return {
        scrapStatus,
        form: {
          no: undefined,
          status: undefined,
          name: undefined,
          time: []
        }
      };
    },
    methods: {
      reset() {
        this.form.executeDept = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          startDate: this.form.time ? this.form.time[0] : undefined,
          endDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
