export default {
  install(Vue) {
    const Count = {
      name: 'Count',
      // props用于接收传递进组件的参数
      props: {
        // 需要监听的数字，由组件外部传入
        num: {
          type: Number,
          default: 0
        },
        time: {
          type: Number,
          default: 800
        }
      },
      data() {
        return {
          // 当前显示的数字
          showVal: this.num
        };
      },
      methods: {
        // 核心函数，start为开始数字，end为结束数字，duration为动画持续时间
        animateValue(start, end, duration) {
          let startTimestamp = null;

          // 动画核心代码 递归调用requestAnimationFrame实现数字跳动
          const step = (timestamp) => {
            // 获取初始时间
            if (!startTimestamp) startTimestamp = timestamp;

            // 结束时间大于开始时间 duration 毫秒时动画结束
            const progress = Math.min(
              (timestamp - startTimestamp) / duration,
              1
            );

            this.showVal = Math.floor(progress * (end - start) + start);

            // 动画未结束时调用
            if (progress < 1) {
              window.requestAnimationFrame(step);
            }
          };

          // 开始递归
          window.requestAnimationFrame(step);
        }
      },
      watch: {
        // 监听数字
        num: {
          handler(newNum, oldNum) {
            // 监听num num改变时触发动画
            const time = this.time;
            let beforeNum = oldNum ? oldNum : 0;
            this.animateValue(
              beforeNum,
              newNum,
              Math.abs(newNum - beforeNum) >= 10 ? time : 380
            );
          },
          immediate: true // 立即执行
        }
      },

      // 使用render创建虚拟dom
      render: function (createElement) {
        return createElement('div', {}, this.showVal);
      }
    };

    Vue.component('Count', Count);
  }
};
