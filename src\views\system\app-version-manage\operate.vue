<template>
  <dialog-drawer
    :title="isEdit ? '编辑更新' : '新增更新'"
    :visible="visible"
    :spanVal.sync="spanVal"
    :before-close="closed"
    @closed="closed"
    ref="drawer"
    size="80%"
  >
    <section>
      <el-form
        :model="form"
        label-suffix="："
        :rules="rules"
        label-width="150px"
        label-position="right"
        ref="baseForm"
        size="small"
      >
        <el-row
          ><el-col :span="12">
            <el-form-item label="版本号" prop="latestVersion">
              <el-input
                v-model="form.latestVersion"
                placeholder="请输入版本号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="兼容最低版本" prop="lowestVersion">
              <el-input
                v-model="form.lowestVersion"
                placeholder="请输入最低版本"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Android下载地址" prop="androidDownload">
              <el-input
                placeholder="请输入Android下载地址"
                v-model="form.androidDownload"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="iOS下载地址" prop="iosDownload">
              <el-input
                placeholder="请输入iOS下载地址"
                v-model="form.iosDownload"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="升级时间" prop="latestPublishTime">
              <el-date-picker
                v-model="form.latestPublishTime"
                type="datetime"
                placeholder="选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isMustUpdate">
            <el-form-item label="热更包" prop="hotUpdate">
              <upload-file
                class="upload"
                v-model="form.hotUpdate"
                :limit="1"
                formatLimit="wgt"
                btnSize="small"
                style="width: 100%"
                @success="handleSuc"
                url="/api/szyk-resource/attach/put-wgt-attach"
              ></upload-file>
              <!-- <el-upload
                :headers="header"
                :action="baseUrl + url"
                multiple
                :limit="3"
                :file-list="form.hotUpdate"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload> -->
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="迭代内容" prop="content">
              <el-input
                :maxlength="500"
                :autosize="{ minRows: 5 }"
                placeholder="请输入迭代内容"
                v-model="form.content"
                type="textarea"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit()" :loading="loading"></btn>
      <btn
        type="close"
        @click="
          () => {
            this.visible = false;
          }
        "
      ></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import { versionDetail, versionAdd, versionEdit } from '@/api/system/version';
  import UploadFile from '@/components/upload-file';
  import { baseUrl } from '@/config/env';
  import website from '@/config/website';
  import { getToken } from '@/util/auth';
  import { Base64 } from 'js-base64';
  export default {
    name: 'dialog-operate',
    props: {
      serviceDicts: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    components: { UploadFile },
    data() {
      return {
        url: '/api/szyk-resource/oss/endpoint/put-file-attach',

        header: {
          Authorization: `Basic ${Base64.encode(
            `${website.clientId}:${website.clientSecret}`
          )}`,
          [website.tokenHeader]: 'bearer ' + getToken()
        },
        baseUrl,
        spanVal: 12, // 默认占行数
        visible: false,
        loading: false,
        isEdit: false,
        form: {
          latestVersion: '',
          lowestVersion: '',
          androidDownload: 'https://www.pgyer.com/ruOTBn',
          iosDownload: '',
          hotUpdate: [],
          content: '',
          latestPublishTime: undefined
        },

        id: undefined,
        rules: {
          latestVersion: [
            {
              required: true,
              message: '请输入版本号！',
              trigger: ['change', 'blur']
            },
            {
              validator: (rule, value, callback) => {
                if (/^(\d\.)+\d+$/.test(value)) {
                  callback();
                } else {
                  callback(new Error('请输入正常版本号！'));
                }
              },
              trigger: ['change', 'blur']
            }
          ],
          lowestVersion: [
            {
              required: true,
              message: '请输入最低兼容版本！',
              trigger: ['change', 'blur']
            },
            {
              validator: (rule, value, callback) => {
                if (/^(\d\.)+\d+$/.test(value)) {
                  callback();
                } else {
                  callback(new Error('请输入正常版本号！'));
                }
              },
              trigger: ['change', 'blur']
            }
          ],
          hotUpdate: [
            {
              required: true,
              message: '请上传热更包！',
              trigger: ['change', 'blur']
            },
            {
              validator: (rule, value, callback) => {
                if (value.length > 0) {
                  callback();
                } else {
                  callback(new Error('请上传热更新包！'));
                }
              },
              message: '请上传热更新包！',
              trigger: ['change']
            }
          ],
          latestPublishTime: [
            { required: true, message: '请选择发布时间！', trigger: ['change'] }
          ],
          content: [
            {
              required: true,
              message: '请输入迭代内容！',
              trigger: ['change', 'blur']
            }
          ]
        }
      };
    },
    watch: {
      hotUpdate(val) {
        console.log('hotUpdate 发生变化 ', val);
      },
      latestVersion(val) {
        console.log('latestVersion 变化', val);
      }
    },
    computed: {
      isMustUpdate() {
        let flag = false;
        if (
          /^(\d\.)+\d+$/.test(this.form.latestVersion) &&
          /^(\d\.)+\d+$/.test(this.form.lowestVersion)
        ) {
          // 如果 版本号等于 最低兼容版本 则为强更
          if (this.form.latestVersion !== this.form.lowestVersion) {
            flag = true;
          }
        }
        return flag;
      }
    },
    methods: {
      handleSuc() {
        this.$refs['baseForm'].clearValidate('hotUpdate');
      },
      // 关闭调用
      closed() {
        console.log('关闭');
        this.id = undefined;
        this.isEdit = false;
        this.form = this.$options.data().form;
        this.$refs['baseForm'].resetFields();
        this.visible = false;
      },
      // 打开抽屉
      drawShow(id) {
        if (id) {
          this.id = id;
          this.getDetail().then(() => {
            this.visible = true;
            this.isEdit = true;
          });
        } else {
          this.visible = true;
        }
      },
      // 点击提交
      async submit() {
        try {
          this.$refs.baseForm.validate(async (valid) => {
            if (valid) {
              let param = {
                ...this.form,
                hotUpdate: this.form.hotUpdate[0].attachId,
                id: this.id
              };
              const res = this.isEdit
                ? await versionEdit(param)
                : await versionAdd(param);
              console.log(res);
              if (res.data.success) {
                this.$message.success('操作成功');
                this.visible = false;
                this.$emit('success');
                this.loading = false;
              }
            } else {
              this.loading = false;
            }
          });
        } catch (e) {
          console.log(e);
          this.loading = false;
          // this.visible = false
        }
      },
      // 获取详情接口
      async getDetail() {
        try {
          let res = await versionDetail(this.id);
          this.form = {
            ...res.data.data,
            hotUpdate: [res.data.data.attach]
          };
        } catch (e) {
          console.log(e);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  // @import './common/common.scss';
  // ::v-deep .el-form-item__content {
  //   margin-left: -0px !important;
  //   flex: 1;
  //   margin-right: 20px;
  // }
  ::v-deep .el-drawer__body {
    padding-bottom: 60px;
  }

  // ::v-deep .el-form-item {
  //   display: flex;
  // }
</style>
