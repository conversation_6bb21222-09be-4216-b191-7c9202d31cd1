<template>
  <div>
    <el-dialog
      append-to-body
      :title="isEdit ? '编辑' : '新增'"
      :visible.sync="appendFormDialogVisible"
      width="50%"
      class="add-tree-item"
      @closed="closed"
      :close-on-click-modal="false"
      @opened="open"
    >
      <el-row>
        <el-form
          :model="appendForm"
          :rules="appendFormRules"
          style="margin-top: 10px"
          label-position="right"
          label-width="90px"
          label-suffix="："
          ref="appendForm"
          size="small"
        >
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="上级厂区" prop="codePath">
                <!--                <el-cascader-->
                <!--                  :disabled="isEdit"-->
                <!--                  v-loading="cascaderLoading"-->
                <!--                  v-model="appendForm.codePath"-->
                <!--                  style="width: 100%"-->
                <!--                  ref="cascader"-->
                <!--                  :props="props"-->
                <!--                ></el-cascader>-->

                {{ (currentRow && currentRow.path) || '-' }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="name" label="厂区名称">
                <el-input
                  v-model.trim="appendForm.name"
                  placeholder="请输入厂区名称"
                  maxlength="50"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="remark" label="备注">
                <el-input
                  v-model.trim="appendForm.remark"
                  placeholder="请输入备注"
                  maxlength="50"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="appendFormDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="appendFormSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {
    addOrEditLocation,
    getLocationLazyList
  } from '@/api/equiment-full-life-api/location';

  export default {
    name: 'AddTreeItem',
    components: {},
    data() {
      return {
        isEdit: false,
        loading: false,
        cascaderLoading: false,
        currentRow: { path: '' },
        appendForm: {
          name: '',
          remark: ''
        },
        appendFormDialogVisible: false,
        appendFormRules: {
          name: { required: true, message: '请输入厂区名称', trigger: 'blur' }
        },
        props: {
          checkStrictly: true,
          lazy: true,
          label: 'name',
          value: 'id',
          children: 'children',
          async lazyLoad(node, resolve) {
            const res = await getLocationLazyList({
              parentId: node.data ? node.data.id : '0'
            });
            const nodes = res.data.data.map((item) => {
              return {
                ...item,
                leaf: !item.hasChildren
              };
            });
            resolve(nodes);
          }
        }
      };
    },

    methods: {
      open() {
        this.$nextTick(() => {
          if (this.isEdit && this.currentRow) {
            this.appendForm.name = this.currentRow.name;
            this.appendForm.remark = this.currentRow.remark;
            // const codePath = this.currentRow.codePath.split(',');
            // codePath.pop();
            // this.appendForm.codePath = codePath;
          } else if (this.currentRow) {
            // this.appendForm.codePath = this.currentRow.codePath.split(',');
          }
        });
      },
      show(type, row) {
        console.log(this.currentRow);

        this.isEdit = type === 'edit';
        this.appendFormDialogVisible = true;
        this.currentRow = row;
      },

      closed() {
        this.appendFormDialogVisible = false;
        this.root = false;
        this.locationShow = false;
        this.isEdit = false;
        this.$refs['appendForm'].resetFields();
        this.currentRow = { path: '' };
      },
      appendFormSubmit() {
        this.$refs.appendForm.validate(async (validate) => {
          if (validate) {
            this.loading = true;
            console.log(this.currentRow);
            const form = {
              name: this.appendForm.name,
              remark: this.appendForm.remark,
              parentId: this.currentRow.id
            };
            if (this.isEdit) {
              form.id = this.currentRow.id;
              delete form.parentId;
            }

            await addOrEditLocation(form);
            this.$message.success('操作成功');
            this.loading = false;
            this.appendFormDialogVisible = false;
            this.$emit('success', {
              parentId: this.currentRow.id
              // parentCode:
              //   this.appendForm.codePath[this.appendForm.codePath.length - 1] ||
              //   '0'
            });
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-tree-item {
    ::v-deep .el-dialog__body {
      padding: 5px 20px;
    }
    ::v-deep .el-input-number {
      width: 100%;
    }

    .tool {
      display: flex;
      align-items: center;
      width: 100%;
    }
  }
</style>
