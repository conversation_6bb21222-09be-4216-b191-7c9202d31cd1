<template>
  <div>
    <el-dialog
      top="5vh"
      title="用户平台配置"
      append-to-body
      :visible.sync="platformBox"
    >
      <!-- 搜索栏 -->
      <search size="mini" v-show="showSearch" @search="handleSearch" />
      <!-- 表格 -->
      <el-row>
        <el-col>
          <yk-right-tool
            :show-search.sync="showSearch"
            :columns="columns"
            @queryTable="onLoad(page)"
          ></yk-right-tool>
        </el-col>
      </el-row>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        size="mini"
        border
        @selection-change="platformSelectionChange"
      >
        <el-table-column align="center" type="selection" width="55" />
        <el-table-column align="center" type="index" label="#" />
        <el-table-column
          v-if="columns[0].visible"
          prop="account"
          label="登录账号"
        >
        </el-table-column>
        <el-table-column
          v-if="columns[1].visible"
          prop="tenantName"
          label="所属租户"
        >
          <template slot-scope="{ row }">
            <el-tag>{{ row.tenantName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="columns[2].visible"
          prop="realName"
          label="用户姓名"
        >
        </el-table-column>
        <el-table-column
          v-if="columns[3].visible"
          prop="userTypeName"
          label="用户平台"
        >
          <template slot-scope="{ row }">
            <el-tag>{{ row.userTypeName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="text"
              icon="el-icon-edit"
              v-if="permission.user_edit"
              @click="handleConfig(scope.row)"
              >配置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <yk-pagination
        small
        v-show="page.total > 0"
        :total="page.total"
        :page.sync="page.currentPage"
        :limit.sync="page.pageSize"
        @pagination="
          ({ page, limit }) => onLoad({ currentPage: page, pageSize: limit })
        "
      />
      <!-- 配置弹窗 -->
      <el-dialog title="配 置" append-to-body :visible.sync="configBox">
        <el-form
          v-loading="configLoading"
          ref="configForm"
          :model="form"
          label-width="90px"
        >
          <el-form-item label="用户拓展: " prop="userExt">
            <el-input
              type="textarea"
              placeholder="请输入用户拓展"
              v-model="form.userExt"
              :rows="8"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            :disabled="btnLoading"
            size="small"
            type="primary"
            @click="handleSubmit"
            >修 改</el-button
          >
          <el-button
            @click="configBox = false"
            size="small"
            :disabled="btnLoading"
            >取 消</el-button
          >
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>
<script>
  import { getList, getUserPlatform, updatePlatform } from '@/api/system/user';
  import Search from '../search';
  import { mapGetters } from 'vuex';

  const columns = [
    { key: 0, label: `登录账号`, visible: true },
    { key: 1, label: `所属租户`, visible: true },
    { key: 2, label: `用户姓名`, visible: true },
    { key: 3, label: `用户平台`, visible: true }
  ];

  export default {
    name: 'UserPlatform',
    components: { Search },
    props: {
      platformBoxVisible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        columns,
        showSearch: true,
        loading: false,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        tableData: [],
        selectionList: [],
        // 配置弹窗
        configBox: false,
        configLoading: false,
        form: {},
        btnLoading: false
      };
    },
    methods: {
      platformSelectionChange(list) {
        this.selectionList = list;
      },
      handleSearch(params) {
        this.queryParams = params;
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      selectionClear() {
        this.selectionList = [];
      },
      // 请求列表数据
      async onLoad(page) {
        try {
          this.loading = true;
          const res = await getList(
            page.currentPage,
            page.pageSize,
            this.queryParams,
            this.treeDeptId
          );
          const data = res.data.data;
          this.page.total = data.total;
          this.tableData = data.records;
          this.selectionClear();
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 配置
      async handleConfig({ id }) {
        try {
          this.configBox = true;
          this.configLoading = true;
          const res = await getUserPlatform(id);
          this.form = res.data.data;
        } catch (e) {
          console.error(e);
        } finally {
          this.configLoading = false;
        }
      },
      // 配置修改
      async handleSubmit() {
        try {
          this.btnLoading = true;
          const { data } = await updatePlatform(
            this.form.id,
            this.form.userType,
            this.form.userExt
          );
          if (data.code === 200) {
            this.configBox = false;
            this.onLoad(this.page);
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.btnLoading = false;
        }
      }
    },
    computed: {
      ...mapGetters(['permission']),
      platformBox: {
        get() {
          return this.platformBoxVisible;
        },
        set() {
          this.$emit('close');
          this.$refs.configForm.resetFields();
        }
      }
    }
  };
</script>
<style lang=""></style>
