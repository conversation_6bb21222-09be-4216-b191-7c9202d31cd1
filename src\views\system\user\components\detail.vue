<template>
  <el-form
    size="mini"
    label-suffix="："
    label-position="right"
    label-width="100px"
  >
    <el-collapse v-model="collapse" style="border: 0">
      <el-collapse-item name="1" style="padding: 0 20px 0 0">
        <template slot="title">
          <div class="collapse_title">
            <i
              class="el-icon-user-solid"
              style="margin-right: 10px; font-size: 20px"
            ></i
            >基础信息
          </div>
        </template>
        <el-row :gutter="15" style="margin-top: 20px">
          <el-col :span="24" v-if="website.tenantMode">
            <el-form-item label="所属租户">
              {{ formData.tenantName || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录账号">
              {{ formData.account || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户平台">
              {{ formData.userTypeName || '--' }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item name="2" style="padding: 0 20px 0 0">
        <template slot="title">
          <div class="collapse_title">
            <i
              class="el-icon-s-order"
              style="margin-right: 10px; font-size: 20px"
            ></i
            >详细信息
          </div>
        </template>
        <el-row :gutter="15" style="margin-top: 20px">
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="name">
              {{ formData.name || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户姓名" prop="realName">
              {{ formData.realName || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phone">
              {{ formData.phone || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电子邮箱" prop="email">
              {{ formData.email || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户性别" prop="sex">
              {{ formData.sexName || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户生日" prop="birthday">
              {{ formData.birthday || '--' }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item name="3" style="padding: 0 20px 0 0">
        <template slot="title">
          <div class="collapse_title">
            <i
              class="el-icon-s-custom"
              style="margin-right: 10px; font-size: 20px"
            ></i
            >职责信息
          </div>
        </template>
        <el-row :gutter="15" style="margin-top: 20px">
          <el-col :span="12">
            <el-form-item label="用户编号">
              {{ formData.code || '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属角色" prop="roleName">
              <el-tag
                type="info"
                size="small"
                v-for="(item, index) in roleArr"
                :key="index"
                >{{ item }}</el-tag
              >
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属部门" prop="deptName">
              <el-tag
                type="info"
                size="small"
                v-for="(item, index) in deptArr"
                :key="index"
                >{{ item }}</el-tag
              >
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属岗位" prop="postName">
              <el-tag
                type="info"
                size="small"
                v-for="(item, index) in postArr"
                :key="index"
                >{{ item }}</el-tag
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
  </el-form>
</template>
<script>
  import website from '@/config/website';

  export default {
    name: 'user-detail',
    props: {
      formData: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    mounted() {
      // console.log('', sdfadfathis.formData);
    },
    data() {
      return {
        website,
        collapse: ['1', '2', '3']
      };
    },
    computed: {
      roleArr() {
        return this.formData.roleName ? this.formData.roleName.split(',') : [];
      },
      postArr() {
        return this.formData.postName ? this.formData.postName.split(',') : [];
      },
      deptArr() {
        return this.formData.roleName ? this.formData.deptName.split(',') : [];
      }
    }
  };
</script>
<style lang="scss" scoped>
  .collapse_title {
    font-size: 16px;
  }

  ::v-deep(.el-tag) {
    margin: 2px 6px 2px 0;
  }
</style>
