<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="110px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="报废名称" prop="name">
            <el-input
              placeholder="请输入报废名称"
              v-model.trim="form.name"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="操作人员" prop="operateUserName">
            <el-input
              style="width: 100%"
              placeholder="请选择操作人员"
              v-model.trim="form.operateUserName"
              readonly
              :disabled="true"
              @focus.prevent="receiveUsers"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="报废日期" prop="scrapDate">
            <el-date-picker
              v-model="form.scrapDate"
              type="date"
              placeholder="请选择报废日期"
              clearable
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info">

        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              placeholder="请输入备注"
              v-model.trim="form.remark"
              clearable
              maxlength="200"
              :rows="1"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row class="add-info">
        <el-col :span="24">
          <el-form-item prop="image" label="审批材料">
            <upload-img
              v-model="form.image"
              placeholder="上传图片"
              :limit="5"
              formatLimit="jpeg,png,jpg"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--     选择人员-->
    <recipient-dialog
      ref="recipient"
      @selected="onUserSelect"
    ></recipient-dialog>
  </div>
</template>

<script>
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import { mapGetters } from 'vuex';
  import store from '@/store';
  import { dateFormat } from '@/util/date';
  import UploadImg from '@/components/uploadImage.vue';
  import { getFileFullUrl } from '@/util/file';
  export default {
    serviceDicts: [],
    name: 'AddDeviceInfo',
    components: {
      UploadImg,
      RecipientDialog
    },
    props: {
      initData: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        form: {
          image: [],
          name: undefined,
          scrapDate: dateFormat(new Date(), 'yyyy-MM-dd'),
          operateUser: store.state.user.userInfo.user_id,
          operateUserName: store.state.user.userInfo.real_name,
          remark: undefined
        },
        rules: {
          image: [
            {
              required: true,
              message: '请上传审批材料',
              trigger: 'change'
            }
          ],
          name: [
            {
              required: true,
              message: '请输入报废名称',
              trigger: 'blur'
            }
          ],
          operateUserName: [
            {
              required: true,
              message: '请选择操作人员',
              trigger: 'change'
            }
          ],
          scrapDate: [
            {
              required: true,
              message: '请选择报废日期',
              trigger: 'change'
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods: {
      //  选择人员
      receiveUsers() {
        this.$refs['recipient'].show();
      },
      //  选择人员回调
      onUserSelect(row) {
        this.form.operateUserName = row.realName;
        this.form.operateUser = row.id;
      },

      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
        this.form.image = initData.attachList.map((it) => {
          return {
            id: it.id,
            fileName: it.originalName,
            filePath: getFileFullUrl(it.id)
          };
        });
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          const imageId = this.form.image
            .map((it) => {
              return it.id;
            })
            .join(',');
          return {
            attachId: imageId,
            ...this.form
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.edit = false;
        this.form.types = undefined;
        this.$refs['baseForm'].resetFields();
      },

      select(data) {
        this.form.name = data.name;
        this.form.id = data.id;
      }
    },
    created() {}
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-select {
        margin-left: 0 !important;
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
