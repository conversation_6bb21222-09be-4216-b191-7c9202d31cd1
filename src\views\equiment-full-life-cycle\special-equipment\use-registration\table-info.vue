<template>
  <el-table
    v-loading="loading"
    class="table"
    :data="tableData"
    row-key="id"
    size="small"
    height="calc(100% - 130px)"
    border
    ref="table"
    :header-cell-style="{ background: '#fafafa' }"
  >
    <el-table-column align="center" type="index" label="序号"></el-table-column>
    <el-table-column
      prop="code"
      label="设备编号"
      align="center"
      width="116"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.code || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="name"
      label="设备名称"
      align="center"
      show-overflow-tooltip
      width="100px"
    >
      <template slot-scope="scope">
        {{ scope.row.name || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="model"
      label="规格型号"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.model || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="categoryName"
      label="设备类型"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      align="center"
      prop="model"
      label="业务域"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.businessDomainName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="importantLevelName"
      label="设备等级"
      align="center"
      show-overflow-tooltip
      width="96px"
    >
      <template slot-scope="{ row }">
        <el-tag
          :type="getTagType(row.importantLevel)"
          size="small"
          class="table-custom-tag"
        >
          {{ row.importantLevelName || '-' }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      label="特种设备"
      show-overflow-tooltip
      width="90px"
    >
      <template slot-scope="scope">
        {{ scope.row.isSpecialName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      label="特种设备检查周期(天)"
      align="center"
      show-overflow-tooltip
      width="85px"
    >
      <template slot-scope="scope">
        {{ scope.row.specialInspectPeriod || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      label="租赁设备"
      show-overflow-tooltip
      width="90px"
    >
      <template slot-scope="scope">
        {{ scope.row.isLeaseName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="measureUnitName"
      label="计量单位"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      align="center"
      prop="statusName"
      label="设备状态"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">
        <i :style="`color:${equipmentStatusColor(row.status)};font-size:18px`"
          >●</i
        >
        {{ row.statusName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      label="归属部门"
      show-overflow-tooltip
      width="90px"
    >
      <template slot-scope="scope">
        {{ scope.row.belongDeptName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      label="负责人"
      show-overflow-tooltip
      width="90px"
    >
      <template slot-scope="scope">
        {{ scope.row.responsiblePersonName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="useDeptName"
      label="使用部门"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.useDeptName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="userName"
      label="使用人员"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.userName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="sn"
      label="SN编号"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      align="center"
      prop="productDate"
      label="投产日期"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.productDate || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="locationPath"
      label="设备位置"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.locationPath || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="supplier"
      label="生产厂家"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.supplierName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="contact"
      label="联系人"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.contact || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="tel"
      label="联系电话"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.tel || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="createUserName"
      label="创建人"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.createUserName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="createTime"
      label="创建时间"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.createTime || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="contact"
      label="更新人"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.updateUserName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="contact"
      label="更新人所属部门"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.updateDeptName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="contact"
      label="更新时间"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.updateTime || '-' }}
      </template>
    </el-table-column>
    <el-table-column label="操作" align="center" width="110" fixed="right">
      <template slot-scope="{ row }">
        <el-button
          type="text"
          size="small"
          @click="$emit('dispatch', 'view', row)"
          >查看</el-button
        >
        <el-button
          type="text"
          v-if="permission['special-equipment-register']"
          size="small"
          @click="$emit('dispatch', 'register', row)"
          >检验登记</el-button
        >
        <!-- <el-button
          icon="el-icon-folder"
          type="text"
          v-if="permission['special-equipment-register']"
          size="small"
          @click="$emit('dispatch', 'register', row)"
          >使用登记</el-button
        > -->
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { convertFileUrl } from '@/util/util';
  import { mapGetters } from 'vuex';

  export default {
    props: {
      loading: Boolean,
      tableData: {
        type: Array,
        default: () => []
      }
    },
    computed: {
      ...mapGetters(['permission'])
    },
    methods: {
      convertFileUrl,
      // 设备登记标签颜色
      getTagType(val) {
        const typeObj = {
          1: 'success',
          2: 'primary',
          3: 'warning'
        };
        return typeObj[val];
      },
      // 设备台账 设备状态状态颜色
      equipmentStatusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#35C24B'; // 在用
          case 1:
            return '#155CFF'; // 备用
          case 3:
            return '#EE7C11'; // 维修
          case 4:
            return '#E23F3F'; // 报废
        }
      }
    }
  };
</script>

<style lang="scss" scoped></style>
