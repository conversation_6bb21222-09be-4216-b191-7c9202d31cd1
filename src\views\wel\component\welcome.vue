<template>
  <div class="welcome_alarmTrend">
    <div class="welcome_info">
      欢迎您，进入<span class="time"
        >{{ time }}&nbsp;&nbsp;&nbsp;{{ week }}</span
      >
    </div>
    <div class="welcome_total">
      <template v-if="!noFinishOrderObj.allNum"
        >你还有
        <span class="emphasize">{{ noFinishOrderObj.allNum || 0 }}</span>
        个未完成工单，太棒了，您今天工作全部完成了，祝您愉快！</template
      >
      <template v-else-if="noFinishOrderObj.allNum <= 10"
        >你还有
        <span class="emphasize">{{ noFinishOrderObj.allNum || 0 }}</span>
        个未完成工单， 继续努力！</template
      >
      <template v-else
        >你还有
        <span class="emphasize">{{ noFinishOrderObj.allNum || 0 }}</span>
        个未完成工单, 您今天的工作还不少呢，加油吧！</template
      >
    </div>
    <div class="welcome_tickets">
      <div
        class="tickets_item"
        v-for="item in computedTicketsList"
        :key="item.title"
        @click="handleGo(item)"
      >
        <div class="tickets_item_wrap">
          <div class="tickets_item_title">{{ item.title }}</div>
          <div class="tickets_item_num">
            {{ item.num }}<span class="unit">单</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import dayjs from 'dayjs';
  import { getWeek } from '@/util/date';
  import { mapGetters } from 'vuex';

  export default {
    props: {
      noFinishOrderObj: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        dayjs,
        time: undefined,
        week: '',
        dateList: [],
        abnormalCountList: [],
        normalCountList: [],
        loading: false,
        flag: true,
        ticketsList: [
          {
            title: '点巡检工单',
            num: 8,

            color: '#1E62FB',
            field: 'inspectNum',
            path: '/equiment-full-life-cycle/equipment-inspection/work-order',
            code: 'e-i-work-order'
          },
          {
            title: '润滑工单',
            num: 8,
            color: '#00A385',
            field: 'lubricateNum',
            path: '/equiment-full-life-cycle/equipment-lubrication/work-order',
            code: 'lubrication-work-order'
          },
          {
            title: '保养工单',
            num: 8,

            color: '#155BD4',
            field: 'maintainNum',
            path: '/equiment-full-life-cycle/equipment-maintenance/work-order',
            code: 'maintenance-work-order'
          },
          {
            title: '内部维修工单',
            num: 8,

            color: '#07B274',
            field: 'repairInnerNum',
            path: '/repair/internal',
            code: 'repair-internal'
          },
          {
            title: '计划性检修工单',
            num: 8,

            color: '#155BD4',
            field: 'overhaulNum',
            path: '/equiment-full-life-cycle/repair/overhaul/work-order',
            code: 'overhaul-work-order'
          },
          {
            title: '外委维修工单',
            num: 8,

            color: '#DA2626',
            field: 'repairExternalNum',
            path: '/repair/external',
            code: 'repair-external'
          }
        ],
        menuCodes: []
      };
    },
    computed: {
      ...mapGetters(['menu']),
      computedTicketsList() {
        return this.ticketsList.map((item) => {
          return {
            ...item,
            num: this.noFinishOrderObj[item.field] || 0
          };
        });
      }
    },
    mounted() {
      this.getAllMenuCodes();

      this.week = getWeek(dayjs().day());
      setInterval(() => {
        this.time = dayjs(new Date()).format('YYYY年MM月DD日 HH:mm:ss');
      }, 1000);
    },
    beforeDestroy() {
      clearInterval(this.time);
    },
    methods: {
      // 获取所有菜单编码
      getAllMenuCodes() {
        let arr = [];
        this.menu.forEach((item) => {
          item.code && arr.push(item.code);
          if (item.children && item.children.length) {
            const temp = item.children.reduce((acc, cur) => {
              cur.code && acc.push(cur.code);
              return acc;
            }, []);
            arr.push(...temp);
          }
        });
        this.menuCodes = arr;
      },
      handleGo(item) {
        if (!this.menuCodes.includes(item.code))
          return this.$message.warning('您没有权限访问此菜单, 请联系管理员');
        this.$router.push(item.path);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .welcome_alarmTrend {
    width: 100%;
    height: 100%;
    padding: 20px;
    color: #222;
    background: url('../../../asset/home/<USER>') no-repeat 100%;
    border-radius: 5px;

    .welcome_info {
      margin-bottom: 20px;
      font-size: 30px;
    }

    .welcome_total {
      margin-bottom: 30px;
      color: #666;
      font-size: 16px;
      line-height: 20px;

      .emphasize {
        color: #f72b28;
        font-weight: 600;
        font-size: 20px;

        // font-family: electronicFont;
      }
    }

    .welcome_tickets {
      display: flex;
      justify-content: space-between;
      padding-top: 20px;
      border-top: 1px solid #e2effb;

      .tickets_item {
        cursor: pointer;

        .tickets_item_wrap {
          // display: flex;
          margin-bottom: 5px;
        }

        .tickets_item_num {
          display: inline-block;
          margin-top: 10px;
          color: #1e62fb;
          font-weight: 600;
          font-size: 32px;
          text-align: left;
        }

        .unit {
          margin-left: 5px;
          color: #000;
          font-size: 16px;
        }

        .tickets_item_icon {
          display: inline-block;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background-color: #fff;
          border-radius: 50%;
        }

        .tickets_item_title {
          font-size: 18px;
        }
      }
    }
  }

  .time {
    margin-left: 30px;
    padding: 5px 15px;
    color: #4974fb;
    font-size: 14px;
    background: #e9f0ff;
    border-radius: 15px;
  }
</style>
