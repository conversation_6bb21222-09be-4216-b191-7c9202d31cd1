<template>
  <el-row>
    <el-col :span="5" style="margin-bottom: 0">
      <basic-container :autoHeight="true">
        <simas-position-tree
          ref="categoryTree"
          :hiddenButtons="true"
          @category-change="categoryChange"
        />
      </basic-container>
    </el-col>
    <el-col :span="19" style="margin-bottom: 0">
      <view-info :locationId="deviceId" where="position-bearing"></view-info>
    </el-col>
  </el-row>
</template>

<script>
  import ViewInfo from './viewInfo/index.vue';
  import SimasPositionTree from '@/views/equiment-full-life-cycle/components/simas-position-tree.vue';
  export default {
    name: 'DeviceBasic',
    components: {
      SimasPositionTree,
      ViewInfo
    },
    data() {
      return {
        loading: false,
        list: [],
        deviceId: undefined // 部位id
      };
    },

    mounted() {},
    methods: {
      categoryChange(node) {
        console.log('node', node);
        this.deviceId = node.data.id;
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    margin-bottom: 10px;
  }

  .table {
    //height: 695px;
    //max-height: 695px;
  }

  ::v-deep {
    .el-tabs__content {
      flex: 1;

      // overflow: auto;
    }

    .el-tabs__header {
      margin: 0 0 10px;
    }
  }
</style>
