<template>
  <dialog-popup
    title="查看原始文本"
    ref="dialog"
    :visible="visible"
    @closed="closed"
    width="80%"
    class="selectSensor"
  >
    <!-- <div
      v-for="(item, index) in monitorSourceList"
      :key="index"
      class="monitor-source-item"
    >
      <div class="monitor-source-title">{{ item.title }}</div>
      <div class="monitor-source-content">{{ item.content }}</div>
    </div> -->
    <el-collapse v-model="activeName" accordion>
      <el-collapse-item
        v-for="({ title, content }, index) in monitorSourceList"
        :key="index"
        :title="title"
        :name="index"
      >
        {{ content }}
      </el-collapse-item>
    </el-collapse>
    <div class="btn-wrapper">
      <btn type="close" @click="closed"></btn>
    </div>
  </dialog-popup>
</template>

<script>
  export default {
    data() {
      return {
        visible: false,
        monitorSourceList: [],
        activeName: 0
      };
    },
    methods: {
      show(data) {
        this.visible = true;
        this.monitorSourceList = data;
      },
      closed() {
        this.visible = false;
        this.activeName = 0;
        this.monitorSourceList = [];
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .el-dialog__body {
    padding-bottom: 5px;
  }

  .btn-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .el-collapse-item ::v-deep {
    .el-collapse-item__header {
      font-weight: bold;
    }

    .el-collapse-item__content {
      white-space: pre-wrap;
    }
  }
</style>
