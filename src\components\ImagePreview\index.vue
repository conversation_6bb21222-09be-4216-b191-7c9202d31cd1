<template>
  <el-image
    :src="`${realSrc}`"
    fit="cover"
    :style="`width:${realWidth};height:${realHeight};`"
    :preview-src-list="realSrcList"
  >
    <div slot="error" class="image-slot">
      <i class="el-icon-picture-outline"></i>
    </div>
  </el-image>
</template>

<script>
  import { isExternal } from '@/util/validate';
  import { imgUrl } from '@/config/env';

  export default {
    name: 'ImagePreview',
    props: {
      src: {
        type: String,
        required: true
      },
      width: {
        type: [Number, String],
        default: ''
      },
      height: {
        type: [Number, String],
        default: ''
      }
    },
    computed: {
      realSrc() {
        let real_src = this.src.split(',')[0];
        if (isExternal(real_src)) {
          return real_src;
        }
        return imgUrl + real_src;
      },
      realSrcList() {
        let real_src_list = this.src.split(',');
        let srcList = [];
        real_src_list.forEach((item) => {
          if (isExternal(item)) {
            return srcList.push(item);
          }
          return srcList.push(imgUrl + item);
        });
        return srcList;
      },
      realWidth() {
        return typeof this.width == 'string' ? this.width : `${this.width}px`;
      },
      realHeight() {
        return typeof this.height == 'string'
          ? this.height
          : `${this.height}px`;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-image {
    background-color: #ebeef5;
    border-radius: 5px;
    box-shadow: 0 0 5px 1px #ccc;

    ::v-deep .el-image__inner {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.2);
      }
    }

    ::v-deep .image-slot {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: #909399;
      font-size: 30px;
    }
  }
</style>
