import request from '@/router/axios';
// 通用资料相关接口-------------------------------------------------------------------------
// 设备资料详情接口
export const userDetail = (params) => {
  return request({
    url: `/api/szyk-simas/equipment-file/detail`,
    method: 'get',
    params
  });
};
//  设备分页列表
export const getPageList = (params) => {
  return request({
    url: `/api/szyk-simas/equipment-file/page`,
    method: 'get',
    params
  });
};

// 设备新增或修改
export const userAddOrUpdate = (data) => {
  return request({
    url: `/api/szyk-simas/equipment-file/submit`,
    method: 'post',
    data
  });
};
//  设备资料删除接口
export const userDeleteLogic = (params) => {
  return request({
    url: `/api/szyk-simas/equipment-file/remove`,
    method: 'post',
    params
  });
};
//  设备资料类型相关接口------------------------------------------------------------------------
export const getDeviceTypeLazyList = (params) => {
  return request({
    url: '/api/szyk-common/equipment_file_category/lazy-list',
    method: 'get',
    params: {
      ...params
    }
  });
};

// 分页page 搜索的时候使用
export const getDeviceTypePageList = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_file_category/page',
    method: 'get',
    params: {
      ...params
    }
  });
};
// 详情接口
export const getDeviceTypeDetail = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_file_category/detail',
    method: 'get',
    params
  });
};

//删除
export const removeDeviceType = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_file_category/check-remove',
    method: 'post',
    params
  });
};

// 更新添加
export const addOrEditDeviceType = (data) => {
  return request({
    url: '/api/szyk-simas/equipment_file_category/submit',
    method: 'post',
    data
  });
};

// 搜索类型全数据
export const getDeviceTypeAllList = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_file_category/searchCategory',
    method: 'get',
    params
  });
};

// 属性结构
export const getDeviceTypeTreeList = () => {
  return request({
    url: '/api/szyk-simas/equipment_file_category/tree',
    method: 'get'
  });
};

// 前期资料管理---------------------------------------------------------------------
// 详情接口
export const getDeviceFileDetail = (params) => {
  return request({
    url: '/api/szyk-common/equipment-account/pre-detail',
    method: 'get',
    params
  });
};

// 前期资料管理提价
export const addOrEditDeviceFileSubmit = (data) => {
  return request({
    url: '/api/szyk-simas/equipment-file/submit-pre-file',
    method: 'post',
    data
  });
};

// 子节点数接口 根据父节点id ，获取子节点下拉列表

export const getChildrenList = (params) => {
  return request({
    url: '/api/szyk-common/equipment_file_category/node-tree',
    method: 'get',
    params
  });
};
// 设备标签和前期资料分页列表
export const accountListPage = (params) => {
  return request({
    url: '/api/szyk-common/equipment-account/allDevicePage',
    method: 'get',
    params
  });
};
// 通用设备资料新增 编辑
export const equipmentBatchUpload = (data) => {
  return request({
    url: '/api/szyk-simas/equipment-file/batch-upload',
    method: 'post',
    data
  });
};
