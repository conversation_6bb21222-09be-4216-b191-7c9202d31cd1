<template>
  <dialog-drawer
    title="审核"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="detail" v-loading="loading">
      <changeInfoDetail :detail="detail"></changeInfoDetail>
    </div>
    <div style="padding-bottom: 50px">
      <logs ref="log" title="审核日志"></logs>
    </div>

    <div class="oper_btn">
      <el-button
        size="small"
        type="success"
        @click="onVerify('1')"
        :loading="loading"
      >
        通 过</el-button
      >
      <el-button
        size="small"
        type="danger"
        @click="onVerify('2')"
        :loading="loading"
      >
        驳 回</el-button
      >
      <btn type="close" @click="closed"></btn>
    </div>
    <Overrule ref="overrule" @success="submit"></Overrule>
  </dialog-drawer>
</template>
<script>
  import {
    getChangeViewApi,
    changeAudit
  } from '@/api/equiment-full-life-api/change';
  import Logs from '@/views/equiment-full-life-cycle/components/logs/index.vue';
  import changeInfoDetail from './change-info-detail';
  import Overrule from './overrule';

  export default {
    name: 'RepairViewIndex',
    components: {
      changeInfoDetail,
      Overrule,
      Logs
    },
    data() {
      return {
        eqId: '',
        visible: false,
        loading: false,
        detail: {} // 详情数据
      };
    },
    methods: {
      show(id) {
        this.visible = true;
        if (id) {
          this.getDetail(id);
        }
      },
      closed() {
        this.visible = false;
        this.detail = {};
      },
      // 获取详情接口
      async getDetail(id) {
        try {
          this.loading = true;
          const res = await getChangeViewApi({ id });
          this.detail = res.data.data;
          await this.$refs['log'].getLogs(this.detail.id, 'EQUIPMENT_CHANGE');

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      async onVerify(type) {
        if (type === '2') {
          this.$refs.overrule.show(type);
        } else {
          await this.confirm('确认通过吗？')
            .then(() => {
              this.submit(type);
            })
            .catch(() => {});
        }
      },
      async submit(status, form = {}) {
        try {
          this.loading = true;
          await changeAudit({
            id: this.detail.id,
            status,
            ...form
          });
          this.$message.success('审核成功');
          this.$emit('success');
          this.visible = false;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  ::v-deep .el-card__body {
    padding-top: 0;
  }
</style>
