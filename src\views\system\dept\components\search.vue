<template>
  <el-form
    v-show="showSearch"
    ref="queryForm"
    :model="queryParams"
    :inline="true"
    label-suffix=": "
    label-position="left"
    size="small"
  >
    <el-form-item label="机构名称" prop="deptName" style="margin-bottom: 5px">
      <el-input
        v-model="queryParams.deptName"
        placeholder="请输入机构名称"
        clearable
        @keyup.enter.native="handleQuery"
      />
    </el-form-item>
    <el-form-item
      label="所属租户"
      prop="tenantId"
      style="margin-bottom: 5px"
      v-if="website.tenantMode"
    >
      <avue-input-tree
        v-model="queryParams.tenantId"
        type="tree"
        placeholder="请选择所属租户"
        style="width: 100%"
        :dic="tenantData"
      ></avue-input-tree>
    </el-form-item>
    <el-form-item label="机构全称" prop="fullName" style="margin-bottom: 5px">
      <el-input
        v-model="queryParams.fullName"
        placeholder="请输入机构全称"
        clearable
        @keyup.enter.native="handleQuery"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="handleQuery"
        >搜 索</el-button
      >
      <el-button icon="el-icon-refresh" @click="resetQuery">清 空</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
  import website from '@/config/website';

  export default {
    props: {
      showSearch: {
        type: Boolean,
        default: true
      },
      tenantData: {
        type: Array,
        default() {
          return [];
        }
      }
    },
    data() {
      return {
        website,
        queryParams: {
          deptName: '',
          tenantId: '',
          fullName: ''
        }
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      handleQuery() {
        this.$emit('search', Object.assign({}, this.queryParams));
      },
      resetQuery() {
        this.$refs.queryForm.resetFields();
        this.handleQuery();
      }
    }
  };
</script>
