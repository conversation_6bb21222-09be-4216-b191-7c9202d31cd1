<template>
  <!--   现做一个备份，这个备份主要是 在懒加载的情况下，能一键展开懒加载表格-->
  <basic-container :auto-height="true">
    <!--    <el-input v-model="search"></el-input>-->
    <search ref="search" @search="onsubmit" />
    <el-button
      type="primary"
      size="small"
      class="el-icon-plus"
      @click="handleOperate('add', { path: '' })"
    >
      新 增</el-button
    >
    <el-button
      type="success"
      size="small"
      class="el-icon-thumb"
      @click="standardReference"
    >
      设备分类标准引用</el-button
    >
    <el-button
      :class="flag ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"
      type="warning"
      size="small"
      @click="oneClickFolding"
      >{{ flag ? ' 一键折叠' : ' 一键展开' }}</el-button
    >
    <!--    tableData.filter(-->
    <!--    (data) => !search || data.categoryName.includes(search)-->
    <!--    )-->
    <el-table
      v-loading="loading"
      :data="tableData"
      ref="treeTable"
      style="width: 100%; margin-top: 15px; margin-bottom: 20px"
      row-key="id"
      border
      lazy
      height="calc(100% - 170px)"
      :load="tableLoad"
      :tree-props="{
        children: 'children',
        hasChildren: 'hasChildren'
      }"
      size="small"
      highlight-current-row
      :expand-row-keys="expandRowkeys"
    >
      <el-table-column prop="categoryName" label="类型名称" align="center">
      </el-table-column>
      <el-table-column prop="remark" label="类型编码" align="center">
        <template v-slot="{ row }">{{ row.remark || '-' }}</template>
      </el-table-column>
      <el-table-column prop="address" label="操作" align="center" width="250">
        <template v-slot="scope">
          <el-button
            type="text"
            size="small"
            link
            @click="handleOperate('add', scope.row)"
            >新增子项</el-button
          >
          <el-button
            type="text"
            size="small"
            link
            @click="handleOperate('edit', scope.row)"
            >编辑</el-button
          >
          <!--          v-if="Number(scope.row.parentId) !== 0"-->
          <el-popconfirm
            title="确定删除吗？"
            @confirm="() => handleDelete(scope.row)"
          >
            <el-button
              link
              size="small"
              type="text"
              slot="reference"
              style="margin-left: 10px"
              >删除
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="searchParams.keywords"
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <handle-operate ref="view" @success="onOperateSuccess"></handle-operate>
  </basic-container>
</template>

<script>
  import Search from './search';
  import Pagination from '@/components/pagination';
  import HandleOperate from './operate/index.vue';
  import {
    getDeviceTypeLazyList,
    getDeviceTypePageList,
    removeDeviceType
  } from '@/api/equiment-full-life-api/device-type';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      Pagination,
      HandleOperate
    },
    props: {},
    data() {
      return {
        search: '',
        maps: new Map(),
        loading: false,
        total: 0,
        tableData: [],
        searchParams: {
          keywords: '',
          size: 10,
          current: 1
        },
        expandRowkeys: [],
        flag: false, // 默认是折叠的。
        node: undefined,
        nodeId: undefined // 点击左边的树结构，传递的id
      };
    },

    mounted() {
      this.getList();
    },
    // computed: {
    //   filteredTableData() {
    //     return this.filterTreeData(this.tableData, this.search);
    //   }
    // }
    methods: {
      // filterTreeData(data, search) {
      //   if (!search) {
      //     return data;
      //   }
      //
      //   return data.reduce((acc, node) => {
      //     const children = node.children ? this.filterTreeData(node.children, search) : [];
      //     if (node.categoryName.includes(search) || children.length > 0) {
      //       acc.push({
      //         ...node,
      //         children: children.length > 0 ? children : node.children
      //       });
      //     }
      //     return acc;
      //   }, []);
      // },
      // 分类标准引用
      standardReference() {
        this.$router.push({
          path: '/equiment-full-life-cycle/basic-manager/chemical-device-types'
        });
        // this.$router.push({
        //   name: 'chemical-device-types'
        // });
      },

      //  点击一键折叠
      async oneClickFolding() {
        this.flag = !this.flag;
        if (this.flag) {
          // 展开所有节点
          await this.expandAllNodes();
        } else {
          // 折叠所有节点
          this.collapseAllNodes();
        }
      },
      // 递归展开所有节点
      async expandAllNodes() {
        this.expandRowKeys = []; // 清空展开的 keys
        await this.loadAndExpandNodes(this.tableData);
      },

      // 递归加载并展开节点
      async loadAndExpandNodes(nodes) {
        for (const node of nodes) {
          // 如果节点有子节点但未加载，则加载子节点
          if (node.hasChildren && !node.children) {
            await new Promise((resolve) => {
              this.tableLoad(node, null, resolve);
            });
          }
          // 展开当前节点
          this.expandRowKeys.push(node.id);
          this.$refs.treeTable.toggleRowExpansion(node, true);
          // 递归处理子节点
          if (node.children) {
            await this.loadAndExpandNodes(node.children);
          }
        }
      },

      // 折叠所有节点
      collapseAllNodes() {
        this.expandRowKeys = [];
        this.tableData.forEach((row) => {
          this.$refs.treeTable.toggleRowExpansion(row, false);
        });
      },

      //  获取列表
      async getPage() {
        const res = await getDeviceTypePageList({
          ...this.searchParams
        });
        this.maps.clear();
        this.tableData = res.data.data.records;
        this.total = res.data.data.total;
      },
      onsubmit(form) {
        if (form.keywords) {
          this.searchParams.keywords = form.keywords;
          this.getPage();
        } else {
          this.searchParams.keywords = '';
          this.getList();
        }
      },

      async tableLoad(tree, treeNode, resolve) {
        this.loading = true;
        const res = await getDeviceTypeLazyList({
          parentId: tree.id
        });
        this.maps.set(tree.id, { tree, treeNode, resolve });
        let data = res.data.data;

        //  将获取的数据，放在children中
        if (this.flag) {
          // 如果是全部展开的话
          this.$set(tree, 'children', data); // 再次点击不重复调用接口
          this.$set(tree, 'hasChildren', false); // 很关键.如果不加这一句，就不会打开二级节点
        }
        data.forEach((item) => {
          this.maps.set(item.id, { ...item, tree, treeNode, resolve });
        });
        resolve(data);
        this.loading = false;
        this.$nextTick(() => {
          if (this.flag) {
            this.$refs.treeTable.toggleRowExpansion(tree, true);
          }
        });
      },
      async handleDelete(row) {
        let res = await removeDeviceType({ ids: row.id });
        if (Number(res.data.data.successNumber) === 0) {
          this.$message.error(res.data.data.detailVOList[0].message);
          return;
        }
        if (Number(row.parentId) !== 0) {
          this.refreshLoad(row.parentId);
        } else {
          this.getList();
        }
        this.$message.success('操作成功');
      },
      handleOperate(type, row) {
        this.$refs['view'].show(type, row);
      },
      onOperateSuccess(row) {
        this.getList();
        this.refreshLoad(row.parentId);
      },
      getList() {
        this.loading = true;
        getDeviceTypeLazyList({ parentId: '0' })
          .then((res) => {
            this.tableData = res.data.data;
            this.loading = false;
          })
          .catch((e) => {
            this.loading = false;
          });
      },
      // 获取map中的数据，重新加载
      refreshLoad(parentId) {
        // 根据父级ID取出对应节点数据
        let obj = this.maps.get(parentId);
        console.log(obj);
        if (obj) {
          const { tree, treeNode, resolve } = obj;
          // 根据父节点id更新子节点数据
          // -- 先给table标签添加一个ref="table1"
          // -- parentId: 就是父节点的ID
          // -- []：就是子节点的数据
          if (tree) {
            this.$set(
              this.$refs.treeTable.store.states.lazyTreeNodeMap,
              parentId,
              []
            );
          }
          // 将取出对应数据再传给load方法，重新加载数据。
          this.tableLoad(tree, treeNode, resolve);
        }
      }
    }
  };
</script>

<style scoped lang="scss"></style>
