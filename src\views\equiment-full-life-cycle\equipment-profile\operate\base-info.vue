<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="110px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="资料名称" prop="name">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入资料名称"
              clearable
              maxlength="50"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资料类型" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择资料类型"
              clearable
            >
              <el-option
                v-for="item in serviceDicts.type['attach_type']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="创建人员" prop="createUserName">
            <el-input
              placeholder="请输入创建人员"
              v-model.trim="form.createUserName"
              maxlength="50"
              clearable
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  import store from '@/store';
  export default {
    serviceDicts: ['attach_type'],
    name: 'AddDeviceInfo',
    components: {},
    props: {
      initData: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        form: {
          name: undefined,
          type: undefined,
          createUser: store.state.user.userInfo.user_id,
          createUserName: store.state.user.userInfo.real_name
        },
        rules: {
          name: [
            {
              required: true,
              message: '请输入资料名称',
              trigger: 'blur'
            }
          ],
          type: [
            {
              required: true,
              message: '请选择资料类型',
              trigger: 'change'
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    methods: {
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();

        if (valid) {
          return {
            ...this.form
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.edit = false;
        this.form.types = undefined;
        this.$refs['baseForm'].resetFields();
      },

      select(data) {
        this.form.name = data.name;
        this.form.id = data.id;
      }
    },
    computed: {},
    created() {}
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }
      .el-select {
        margin-left: 0 !important;
      }
      .el-form-item__content {
        flex: 1;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
