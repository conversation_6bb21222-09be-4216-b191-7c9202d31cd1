<template>
  <basic-container :autoHeight="true">
    <search ref="refs" @search="query"></search>
    <div class="table-content">
      <div style="margin-bottom: 12px">
        <btn type="add" @click="handleCreate"></btn>
        <btn type="batchDel" @click="batchDel"></btn>
      </div>
      <el-table
        class="table"
        :data="dataSource"
        v-loading="loading"
        :headerCellStyle="{ background: '#fafafa', color: 'rgba(0,0,0,.85)' }"
        border
        size="small"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column
          type="index"
          label="#"
          align="center"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="latestVersion"
          label="推送更新版本号"
          align="center"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          prop="lowestVersion"
          label="最低兼容版本"
          align="center"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          prop="content"
          label="迭代内容"
          align="center"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          prop="latestPublishTime"
          label="升级时间"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row }">{{
            row.latestPublishTime || '-'
          }}</template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" width="200" align="center">
          <template slot-scope="scope">
            <btn type="edit" @click="(val) => handleEdit(scope.row)"></btn>
            <btn
              type="dele"
              @deleted="(val) => handleDelete(scope.row.id)"
            ></btn>
          </template>
        </el-table-column>
      </el-table>
      <operate ref="operate" @success="fetchData"></operate>
      <pagination
        :page-size.sync="pagination.pageSize"
        :page-no.sync="pagination.current"
        :total="pagination.total"
        @pagination="fetchData"
      />
    </div>
  </basic-container>
</template>

<script>
  import { versionList, versionDel } from '@/api/system/version';
  import Operate from './operate.vue';
  import search from './search.vue';
  export default {
    name: 'app-version',
    components: {
      search,
      Operate
    },
    data() {
      return {
        dataSource: [],
        form: {},
        loading: false,
        pagination: {
          total: 0,
          pageSize: 10,
          current: 1
        },
        options: [],
        multipleSelection: []
      };
    },
    created() {
      this.fetchData();
    },
    methods: {
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },

      // 批量删除
      batchDel() {
        if (this.multipleSelection.length !== 0) {
          this.$confirm('确定删除选择的数据吗？', '', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const ids = this.multipleSelection
              .filter((item) => item.id)
              .map((item) => item.id)
              .join(',');
            this.deletes(ids);
          });
        } else {
          this.$message.warning('请选择要删除的列表');
        }
      },

      fetchData() {
        // this.loading = true
        let params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          ...this.form
        };
        versionList(params)
          .then((res) => {
            let result = res.data.data;
            console.log(result);
            this.dataSource = result.records;
            this.pagination.total = result.total;
            this.loading = false;
          })
          .catch((e) => {
            this.loading = false;
            console.log(e);
          });
      },
      // 操作成功，刷新页面
      successOperate() {
        this.fetchData(true);
      },
      query(searchForm) {
        this.pagination.current = 1;
        this.form = searchForm;
        this.fetchData();
      },
      handleCreate() {
        this.$refs.operate.drawShow();
      },
      handleView(record) {
        this.$refs.viewInfo.drawShow(record.id);
      },
      // 编辑
      handleEdit(record) {
        this.$refs.operate.drawShow(record.id);
      },

      // 删除
      async handleDelete(id) {
        try {
          await versionDel({ ids: id });
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          this.fetchData(true);
        } catch (e) {
          console.log(e);
        }
      },
      // 批量删除
      async deletes(ids) {
        try {
          await versionDel({ ids: ids });
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          this.fetchData(true);
        } catch (e) {
          console.log(e);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  /deep/.el-icon--right {
    margin-left: 5px;
    color: #409eff;
    cursor: pointer;
  }

  .container {
    display: grid;
    grid-template-columns: 1fr 3fr;
  }

  ::v-deep .el-drawer__body {
    padding-top: 0 !important;
  }

  .adds {
    padding-left: 5px;
  }
</style>
