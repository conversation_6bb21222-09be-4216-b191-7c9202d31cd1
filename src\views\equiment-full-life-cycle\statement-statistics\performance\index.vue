<template>
  <basic-container class="wrapper" :auto-height="true">
    <el-tabs v-model="activeName" @tab-click="changeActive">
      <el-tab-pane
        v-for="item in tabsArr"
        :key="item.name"
        :label="item.title"
        :name="item.name"
      >
        <component
          v-if="activeName === item.name"
          :is="item.component"
          :key="compKey"
          @compView="compView"
          :orderId="orderId"
          :orderType="orderType"
          :searchType="searchType"
          :searchTime="searchTime"
          :searchParams="searchParams"
        ></component>
      </el-tab-pane>
    </el-tabs>
  </basic-container>
</template>

<script>
  import {
    Intact,
    Repair,
    Implement,
    ImplementView,
    IntactView,
    RepairList,
    ImplementList,
    InspectList,
    MaintenanceList,
    LubricationList,
    OveralList,
    InternalList,
    EnternalList
  } from './components';
  export default {
    components: {
      Intact,
      Repair,
      Implement,
      ImplementView,
      IntactView,
      RepairList,
      ImplementList,
      InspectList,
      MaintenanceList,
      LubricationList,
      OveralList,
      InternalList,
      EnternalList
    },
    data() {
      return {
        activeName: 'intact',
        tabsArr: [
          {
            name: 'intact',
            title: '设备完好率',
            component: 'Intact'
          },
          {
            name: 'repair',
            title: '设备维修率',
            component: 'Repair'
          },
          {
            name: 'implement',
            title: '计划执行率',
            component: 'Implement'
          }
        ],
        orderId: undefined, // 返回的orderId
        orderType: undefined,
        searchTime: [], // 计划执行率用来记录时间
        searchType: undefined, // 计划执行率二级页面筛选条件的参数 - 类型tab
        searchParams: {}, // 计划执行率二级页面筛选条件的参数
        compKey: Math.random()
      };
    },
    activated() {
      if (this.$route.params.fromAssist) {
        // this.handleImplementParams();
        this.activeName = 'intact';
        // this.changeActive();
        this.compKey = Math.random();
      }
    },
    methods: {
      // 处理从智能助手跳转到计划执行率的参数
      handleImplementParams() {
        console.log('pp', this.$route.params);
        this.activeName = 'implement';
        const { orderType, tab, searchParams } = this.$route.params;
        const params = {
          comp: 'ImplementView',
          paramsObj: {
            orderType,
            searchParams
          }
        };
        // 判断切换哪个tab
        if (tab === '按用户统计') {
          params.paramsObj.sType = 'USER';
        } else if (tab === '按部门统计') {
          params.paramsObj.sType = 'DEPT';
        } else if (tab === '按设备统计') {
          params.paramsObj.sType = 'EQUIPMENT';
        }
        // 跳转到二级详情
        this.$nextTick(() => {
          this.compKey = Math.random();
          this.compView(params);
        });
      },
      changeActive() {
        this.tabsArr = [
          {
            name: 'intact',
            title: '设备完好率',
            component: 'Intact'
          },
          {
            name: 'repair',
            title: '设备维修率',
            component: 'Repair'
          },
          {
            name: 'implement',
            title: '计划执行率',
            component: 'Implement'
          }
        ];
      },
      //  展示详情
      compView(params) {
        console.log('传递的参数', params);
        let obj = this.tabsArr.find((item) => {
          return item.name === this.activeName;
        });
        if (params) {
          obj.component = params.comp;
          this.orderId = params.paramsObj ? params.paramsObj.id : undefined;
          //  计划执行率的参数
          this.orderType = params.paramsObj
            ? params.paramsObj.orderType
            : undefined;
          // 计划执行率 二级页面筛选条件的参数
          this.searchType = params.paramsObj
            ? params.paramsObj.sType
            : undefined;
          //   计划执行率 二级页面筛选条件的参数 时间
          this.searchTime = params.paramsObj
            ? [params.paramsObj.startDate, params.paramsObj.endDate]
            : [];
          // 计划执行率 二级页面筛选条件的参数
          this.searchParams = params.paramsObj
            ? params.paramsObj.searchParams
            : {};
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .el-tabs {
    height: 100%;

    ::v-deep .el-tabs__content {
      height: calc(100% - 55px);

      .el-tab-pane {
        height: 100%;
      }
    }
  }
</style>
