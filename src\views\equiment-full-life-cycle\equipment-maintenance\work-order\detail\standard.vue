<template>
  <el-table size="small" :data="list" border>
    <el-table-column
      label="#"
      type="index"
      width="50"
      align="center"
    ></el-table-column>
    <el-table-column prop="monitorName" label="检查部位" align="center">
      <template v-slot="{ row }">{{ row.monitorName || '-' }}</template>
    </el-table-column>
    <el-table-column
      prop="standard"
      label="保养标准"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="method"
      label="保养方法"
      align="center"
      show-overflow-tooltip
    ></el-table-column>

    <el-table-column
      prop="abnormalStatus"
      label="检查结果"
      align="center"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{ row.abnormalStatus || '-' }}</template>
    </el-table-column>
    <el-table-column
      prop="abnormalComment"
      label="异常描述"
      align="center"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{
        (row.maintainRecord && row.maintainRecord.abnormalComment) || '-'
      }}</template>
    </el-table-column>
    <el-table-column
      prop="abnormalLevelName"
      label="异常等级"
      align="center"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{
        (row.maintainRecord && row.maintainRecord.abnormalLevelName) || '-'
      }}</template>
    </el-table-column>
    <el-table-column
      prop="abnormalImageList"
      label="异常图片"
      align="center"
      width="80"
    >
      <template v-slot="{ row }">
        <span v-if="row.maintainRecord && row.maintainRecord.abnormalImage">
          <el-image
            v-for="img in row.maintainRecord.abnormalImageList"
            :key="img.id"
            style="width: 50px; height: 50px"
            :src="getFileFullUrl(img.id)"
            fit="cover"
            :preview-src-list="[getFileFullUrl(img.id)]"
          ></el-image
        ></span>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column
      prop="isHandledName"
      label="是否现场处理"
      align="center"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{
        (row.maintainRecord && row.maintainRecord.isHandledName) || '-'
      }}</template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { getFileFullUrl } from '@/util/file';

  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return { getFileFullUrl };
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
