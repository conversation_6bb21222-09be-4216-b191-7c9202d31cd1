<template>
  <div>
    <el-button
      size="small"
      type="primary"
      style="margin-bottom: 15px"
      @click="selAsset"
      v-if="outType === '2'"
      >+ 选择备品备件库存</el-button
    >

    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
    >
      <el-table
        v-loading="loading"
        class="table"
        :data="form.list"
        border
        :header-cell-style="{ background: '#fafafa' }"
        size="small"
        stripe
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column prop="name" label="备品备件名称" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="no" label="备品备件编号" show-overflow-tooltip>
          <template v-slot="{ row }">{{ row.no || '-' }}</template>
        </el-table-column>
        <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
          <template v-slot="{ row }">{{ row.model || '-' }}</template>
        </el-table-column>

        <el-table-column
          prop="measureUnitName"
          label="计量单位"
          show-overflow-tooltip
        >
          <template v-slot="{ row }">{{ row.measureUnitName || '-' }}</template>
        </el-table-column>
        <el-table-column
          v-if="outType === '2'"
          prop="currentQuantity"
          label="当前库存"
          show-overflow-tooltip
        >
          <template v-slot="{ row }" style="color: red">{{
            row.currentQuantity || '-'
          }}</template>
        </el-table-column>
        <el-table-column
          prop="outboundQuantity"
          label="出库数量"
          show-overflow-tooltip
          width="250px"
        >
          <template v-slot="scope">
            <el-form-item
              v-if="outType === '2'"
              :prop="'list.' + scope.$index + '.outboundQuantity'"
              :rules="[
                {
                  required: true,
                  message: '请输入出库数量',
                  trigger: 'blur'
                },
                {
                  validator: (rule, value, callback) =>
                    validateCode(rule, value, callback)
                }
              ]"
              label=" "
            >
              <!--              <el-input-number-->
              <!--                size="small"-->
              <!--                :controls="false"-->
              <!--                v-model="scope.row.outboundQuantity"-->
              <!--                :min="1"-->
              <!--                :max="Number(scope.row.currentQuantity)"-->
              <!--                :precision="scope.row.measureUnitPrecision"-->
              <!--                placeholder="请输入出库数量"-->
              <!--              ></el-input-number>-->
              <el-input
                style="width: 190px"
                size="small"
                placeholder="请输入出库数量"
                v-model.trim="scope.row.outboundQuantity"
                clearable
                maxlength="50"
              />
            </el-form-item>
            <span v-else>{{ scope.row.outboundQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="model"
          label="操作"
          align="center"
          v-if="outType === '2'"
        >
          <template v-slot="scope">
            <el-button
              type="text"
              style="color: red"
              size="mini"
              @click="del(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <asset-list
      ref="assetList"
      @getAssetList="getAssetList"
      showWarehouseSearch
    ></asset-list>
  </div>
</template>

<script>
  import AssetList from '@/views/equiment-full-life-cycle/components/select-receive-spare-parts-dialog';
  import { validateValueThen0 } from '@/util/func';
  export default {
    name: 'DeviceBasicList',
    components: { AssetList },
    props: {
      detail: {
        type: Object,
        default: () => {}
      },
      outType: {
        type: String,
        default: () => {
          return '';
        }
      },
      warehouseId: {
        type: String,
        default: () => {
          return undefined;
        }
      },
      //  领用明细
      receiveList: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              let origin = this.detail.itemList || [];
              let list = origin.map((it) => {
                return {
                  ...it,
                  no: it.dictNo,
                  name: it.dictName
                };
              });
              this.form.list = list;
            });
          }
        }
      },
      //  如果是请领出库，会掉详情接口直接展示备件信息
      'receiveList.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            let origin = this.receiveList.itemList || [];
            let list = origin.map((it) => {
              return {
                ...it,
                no: it.dictNo,
                name: it.dictName,
                outboundQuantity: it.issuanceQuantity // 这边的出库数量就是领用时候的数量
              };
            });
            this.form.list = list;
          } else {
            this.form.list = [];
          }
        }
      },
      //  类型改变，清空备件列表
      outType: {
        immediate: true,
        deep: true,
        handler(val) {
          this.form.list = [];
        }
      }
    },
    data() {
      const validateCode = (rule, value, callback) => {
        // 获取当前数组
        let arr = rule.field.split('.');
        let idx = Number(arr[1]);
        let max = this.form.list[idx].currentQuantity;
        let measureUnitPrecision = this.form.list[idx].measureUnitPrecision;
        let val = validateValueThen0(value, max, measureUnitPrecision);
        if (!value) {
          callback();
        } else if (val) {
          callback();
        } else {
          callback(
            new Error(
              `请输入大于0且小于${max}的值，精度${measureUnitPrecision}`
            )
          );
        }
      };
      return {
        validateCode,
        loading: false,
        total: 0,
        form: {
          list: []
        },
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },

    mounted() {},
    methods: {
      del(scope) {
        this.form.list.splice(scope.$index, 1);
      },
      async validForm() {
        if (this.form.list.length === 0) {
          this.$message.warning('请选择备品备件');
          return;
        }
        let valid = await this.$refs['listForm'].validate();
        if (valid) {
          let p = this.form.list.map((it) => {
            return {
              dictId: it.dictId,
              outboundQuantity: it.outboundQuantity,
              stockId: it.stockId,
              warehouseId: it.warehouseId
            };
          });
          return p;
        } else {
          return false;
        }
      },
      resetForm() {
        this.form.list = [];
      },
      //  获取设备
      //  备品备件回显
      getAssetList(list) {
        console.log(list);
        let arr = [...list];
        let arrList = arr.map((item) => {
          return {
            no: item.dictNo,
            dictId: item.dictId, // 这两个id必传
            dictName: item.dictName,
            dictNo: item.dictNo,
            dictModel: item.model,
            stockId: item.id, // 这两个id必传
            name: item.dictName,
            model: item.model,
            outboundQuantity: item.outboundQuantity,
            warehouseId: item.warehouseId,
            warehouseName: item.warehouseName,
            measureUnitName: item.measureUnitName,
            measureUnitId: item.measureUnitId,
            currentQuantity: item.currentQuantity,
            measureUnitPrecision: item.measureUnitPrecision
          };
        });
        this.form.list = [...arrList];
      },
      selAsset() {
        let list = this.form.list.map((it) => {
          return {
            ...it,
            id: it.stockId, // 这个是唯一匹配库存列表的数据
            num: 1
          };
        });
        this.$refs['assetList'].show(list, this.warehouseId);
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }
  .operateBtn {
    margin-bottom: 15px;
  }
  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
