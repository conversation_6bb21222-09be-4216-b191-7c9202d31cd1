<template>
  <div class="alarmTrend">
    <div
      id="bar_year_chart"
      class="alarmTrendChart"
      v-if="flag"
      v-loading="loading"
    ></div>
    <custom-empty :size="70" v-else></custom-empty>
  </div>
</template>
<script>
  import * as echarts from 'echarts/core';
  import {
    TooltipComponent,
    LegendComponent,
    GridComponent,
    TitleComponent
  } from 'echarts/components';
  import { LineChart } from 'echarts/charts';
  import { CanvasRenderer } from 'echarts/renderers';
  import CustomEmpty from '@/components/custom-empty.vue';
  import { getIntactList } from '@/api/equiment-full-life-api/statement-statistics';
  echarts.use([
    TooltipComponent,
    TitleComponent,
    LineChart,
    CanvasRenderer,
    LegendComponent,
    GridComponent
  ]);

  export default {
    name: 'alarmTrend',
    props: {
      searchParams: {
        type: Object,
        default: () => {
          return {
            timeType: 'THIRTY_DAYS'
          };
        }
      }
    },
    components: { CustomEmpty },

    data() {
      return {
        xData: [],
        yData: [],
        flag: true,
        loading: false
      };
    },
    computed: {},
    mounted() {
      this.$nextTick(async () => {
        await this.getCategoryAsset();
      });
      window.addEventListener('resize', this.resizeChart);
    },
    unmounted() {
      window.removeEventListener('resize', this.resizeChart);
    },
    methods: {
      async getCategoryAsset() {
        this.xData = [];
        this.yData = [];
        this.loading = true;
        try {
          const res = await getIntactList({
            ...this.searchParams
          });
          let arr = res.data.data || [];

          if (arr.length) {
            this.flag = true;
            arr.forEach((item) => {
              this.xData.push(item.key);
              this.yData.push(Number(item.value));
            });
            this.init();
          } else {
            this.flag = false;
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      resizeChart() {
        this.status_chart &&
          this.status_chart.resize({ width: 'auto', height: 'auto' });
      },

      init() {
        const _this = this;
        let chartDom = document.getElementById('bar_year_chart');
        this.status_chart = echarts.init(chartDom);

        let option = {
          title: {
            text: '完好率',
            top: '0%',
            left: '3%',
            textStyle: { color: '#3d446e', fontSize: 14, fontWeight: 'normal' },
            padding: [5, 0, 0, 0]
          },

          legend: {
            show: false,
            orient: 'horizontal',
            top: '10',
            right: '10'
          },
          grid: { top: '7%', bottom: '10%', left: '8%', right: '10' },
          xAxis: {
            type: 'category',
            data: this.xData,
            axisLine: { lineStyle: { color: '#ccc' } },
            axisTick: { length: 3 },
            axisLabel: { color: '#999' }
          },
          yAxis: {
            type: 'value',
            axisLine: { show: true, lineStyle: { color: '#ccc' } },
            axisLabel: {
              color: '#999',
              formatter: function (value) {
                return `${value}%`;
              }
            },
            splitLine: {
              lineStyle: { color: ['#CEEDFF'], type: [5, 8], dashOffset: 3 }
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            textStyle: { color: '#424242' },
            renderMode: 'html',
            className: 'tooltip',
            formatter: function (params) {
              let value = params[0].value;
              let xValue = params[0].axisValue;
              return `设备完好率:<br/>时间：${xValue}<br/>完好率：${value}%`;
            }
          },
          series: [
            {
              name: '完好率',
              type: 'line',
              color: '#52A8FF',
              avoidLabelOverlap: true,
              label: {
                show: true,
                position: 'top', // insideTopLeft
                formatter: function (params) {
                  // 近30 的标记第一个和最后一个，其他的全部标记
                  if (_this.searchParams.timeType === 'THIRTY_DAYS') {
                    if (params.dataIndex === 0 || params.dataIndex === 29) {
                      return params.value + '%';
                    }
                    return ''; // 其他点不显示标签
                  } else {
                    return params.value + '%';
                  }
                }
              },
              itemStyle: { borderRadius: [12, 12, 0, 0] },
              data: this.yData
            }
          ]
        };

        this.status_chart && this.status_chart.setOption(option);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .alarmTrend {
    width: 100%;
    height: calc(100% - 50px);
    padding: 15px;
    background: var(--el-bg-color-overlay);
    border-radius: 5px;
  }

  .alarmTrendChart {
    width: 100%;
    height: calc(100% - 30px);
  }

  .title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

    section {
      width: calc(100% - 220px);
      overflow: hidden;
      text-wrap: nowrap;
      text-overflow: ellipsis;
    }

    em {
      display: inline-block;
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-style: normal;
      line-height: 10%;
    }
  }

  ::v-deep {
    .el-tabs__nav-wrap::after {
      background-color: unset;
    }

    .el-tabs__nav-wrap {
      top: -20px;
    }
  }
</style>
