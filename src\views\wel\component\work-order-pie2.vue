<template>
  <div class="alarmTrend">
    <span class="title">
      <section>
        <span style="color: #333; font-size: 20px">设备统计</span>
      </section>
    </span>

    <div
      id="device_statistics_chart"
      class="pie"
      v-if="flag"
      v-loading="loading"
    ></div>
    <custom-empty :size="70" v-else></custom-empty>
  </div>
</template>

<script>
  import { equipmentSummary } from '@/api/home';
  import * as echarts from 'echarts/core';
  import {
    TooltipComponent,
    LegendComponent,
    TitleComponent
  } from 'echarts/components';
  import { PieChart } from 'echarts/charts';
  import { CanvasRenderer } from 'echarts/renderers';
  import CustomEmpty from '@/components/custom-empty.vue';

  echarts.use([
    TooltipComponent,
    TitleComponent,
    PieChart,
    CanvasRenderer,
    LegendComponent
  ]);

  export default {
    name: 'AlarmTrend',
    components: { CustomEmpty },
    data() {
      return {
        inspectOrders: [],
        loading: false,
        flag: true,
        total: 0,
        status_chart: null,
        colorMap: {
          在用: '#28C23B',
          备用: '#185EFF',
          维修中: '#FF7D00',
          报废: '#FA3D35'
        }
      };
    },
    mounted() {
      this.$nextTick(async () => {
        await this.getInspectOrders();
      });
      window.addEventListener('resize', this.resizeChart);
    },
    beforeUnmount() {
      window.removeEventListener('resize', this.resizeChart);
      if (this.status_chart) {
        this.status_chart.dispose();
      }
    },
    methods: {
      async getInspectOrders() {
        this.loading = true;
        try {
          const res = await equipmentSummary();
          const obj = res.data.data;
          this.total = obj.equipmentTotal || 0;
          this.inspectOrders = [
            { name: '在用', value: obj.inUseCount || 0 },
            { name: '备用', value: obj.idleCount || 0 },
            { name: '维修中', value: obj.inRepairCount || 0 },
            { name: '报废', value: obj.scrappedCount || 0 }
          ];
          this.initChart();
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },

      resizeChart() {
        this.status_chart && this.status_chart.resize();
      },

      getColor(name) {
        return this.colorMap[name];
      },

      initChart() {
        const chartDom = document.getElementById('device_statistics_chart');
        if (!chartDom) return;

        this.status_chart = echarts.init(chartDom);

        const option = {
          // 调整title配置，确保中央文字完全居中
          title: {
            text: this.total,
            subtext: '设备总数',
            left: 'center',
            top: '40%',
            textStyle: {
              fontSize: 28,
              color: '#333',
              fontWeight: 'bold'
            },
            subtextStyle: {
              fontSize: 14,
              color: '#999'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a}<br/>{b}: {c} ({d}%)'
          },
          legend: {
            show: true,
            orient: 'horizontal',
            bottom: 0,
            left: 'center',
            itemWidth: 12,
            itemHeight: 12,
            itemGap: 20,
            textStyle: {
              fontSize: 12,
              color: '#666'
            },
            data: this.inspectOrders.map((item) => ({
              name: item.name,
              icon: 'circle',
              itemStyle: {
                color: this.getColor(item.name)
              }
            }))
          },
          // 饼图系列配置（带箭头标签）
          series: [
            {
              name: '设备状态',
              type: 'pie',
              radius: ['40%', '65%'], // 调整半径留出空间
              center: ['50%', '45%'], // 调整中心位置
              avoidLabelOverlap: true,
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                // 修正富文本标签中的颜色引用
                formatter: (params) => {
                  return `{arrow|➤} {name|${params.name}}\n{value|${params.value} (${params.percent}%)}`;
                },
                rich: {
                  arrow: {
                    fontSize: 14,
                    padding: [0, 5, 0, 0]
                  },
                  name: {
                    fontSize: 12,
                    fontWeight: 'bold'
                  },
                  value: {
                    color: '#666',
                    fontSize: 11
                  }
                },
                position: 'outer',
                alignTo: 'labelLine',
                bleedMargin: 5,
                distanceToLabelLine: 2
              },
              labelLine: {
                show: true,
                length: 15,
                length2: 20,
                smooth: true,
                lineStyle: {
                  width: 1.5,
                  type: 'solid'
                }
              },
              data: this.inspectOrders,
              color: Object.values(this.colorMap)
            }
          ]
        };

        this.status_chart.setOption(option);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .alarmTrend {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    padding: 15px;
    background: #fff;
    border-radius: 5px;

    .pie {
      width: 100%;
      height: calc(100% - 40px);
    }

    .title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      color: #333;
      font-weight: bold;
      font-size: 16px;
    }
  }
</style>
