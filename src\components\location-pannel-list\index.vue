<template>
  <div>
    <el-cascader
      v-model="bindVal"
      :props="props"
      :options="list"
      :show-all-levels="false"
      @change="getVal"
      :disabled="disabled"
      :clearable="clearable"
      :size="size"
      placeholder="请选择地点"
      popper-class="location-popper"
    ></el-cascader>
  </div>
</template>

<script>
  import { getLocationTreeList } from '@/api/equiment-full-life-api/location';
  export default {
    name: 'yk-location-lazy-list',
    components: {},
    props: {
      disabled: {
        type: Boolean,
        default: false
      },
      value: {
        type: String,
        default: () => {
          return '';
        }
      },
      size: {
        type: String,
        default: 'small'
      },
      clearable: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      value: {
        immediate: true,
        handler(val) {
          this.bindVal = val;
        }
      }
    },
    data() {
      return {
        selectedValues: [], // 选中的值
        list: [],
        bindVal: undefined,
        props: {
          value: 'id',
          label: 'name',
          children: 'children',
          checkStrictly: true,
          expandTrigger: 'hover',
          emitPath: false
        }
      };
    },

    methods: {
      //  获取value值
      getVal(val) {
        this.$emit('getValue', val);
        console.log(val);
      },
      async getList() {
        try {
          const res = await getLocationTreeList();
          this.list = res.data.data;
        } catch (e) {
          console.log(e);
        }
      }
    },
    computed: {},
    created() {
      this.getList();
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep .location-popper .el-cascader-panel {
    .el-radio {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 10;
      width: 100%;
      height: 100%;
    }

    .el-checkbox {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 10;
      width: 100%;
      height: 100%;
    }

    .el-radio__input {
      margin-top: 10px;
      margin-left: 8px;
    }

    .el-checkbox__input {
      margin-top: 2px;
      margin-left: 8px;
    }

    .el-cascader-node__postfix {
      top: 10px;
    }
  }
</style>
