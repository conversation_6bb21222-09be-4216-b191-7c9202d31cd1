<template>
  <div>
    <el-cascader
      v-model="bindVal"
      :props="props"
      :options="list"
      :show-all-levels="false"
      @change="getVal"
      :disabled="disabled"
      :clearable="clearable"
      :size="size"
      placeholder="请选择地点"
    ></el-cascader>
  </div>
</template>

<script>
  import { getLocationTreeList } from '@/api/equiment-full-life-api/location';
  export default {
    name: 'yk-location-lazy-list',
    components: {},
    props: {
      disabled: {
        type: Boolean,
        default: false
      },
      value: {
        type: String,
        default: () => {
          return '';
        }
      },
      size: {
        type: String,
        default: 'small'
      },
      clearable: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      value: {
        immediate: true,
        handler(val) {
          this.bindVal = val;
        }
      }
    },
    data() {
      return {
        selectedValues: [], // 选中的值
        list: [],
        bindVal: undefined,
        props: {
          value: 'id',
          label: 'name',
          children: 'children',
          checkStrictly: true,
          expandTrigger: 'hover',
          emitPath: false
        }
      };
    },

    methods: {
      //  获取value值
      getVal(val) {
        this.$emit('getValue', val);
        console.log(val);
      },
      async getList() {
        try {
          const res = await getLocationTreeList();
          this.list = res.data.data;
        } catch (e) {
          console.log(e);
        }
      }
    },
    computed: {},
    created() {
      this.getList();
    }
  };
</script>

<style lang="scss" scoped></style>
