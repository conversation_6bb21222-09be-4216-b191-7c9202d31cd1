<template>
  <div class="add-position" style="padding-bottom: 100px">
    <div class="button">
      <el-button size="mini" type="primary" @click="add">
        + 添加部位
      </el-button>
    </div>
    <el-form
      style="margin-top: 10px"
      :model="form"
      inline
      ref="baseForm"
      label-position="right"
      size="small"
      label-width="90px"
      label-suffix="："
    >
      <el-row
        class="add-info"
        :gutter="24"
        v-for="(item, idx) in form.list"
        :key="item.id || idx"
      >
        <el-col :span="2">
          <el-form-item class="nums" label="序号" label-width="auto">
            {{ idx + 1 }}
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item
            class="nums"
            label="部位名称"
            :prop="'list.' + idx + '.name'"
            :rules="{
              required: true,
              message: '请输入部位名称',
              trigger: ['blur']
            }"
          >
            <el-input
              v-model.trim="item.name"
              placeholder="请输入部位名称"
              clearable
              maxlength="50"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item
            :prop="'list.' + idx + '.equipmentType'"
            style="width: 100%"
            label="部位类型"
          >
            <el-input
              v-model.trim="item.type"
              placeholder="请输入部位类型"
              clearable
              maxlength="50"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <i class="el-icon-remove" @click="del(idx)"> </i>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'bearingLibraryIndex',
    props: {
      monitorList: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return {
        rule: [
          {
            required: true,
            message: '请输入参数阈值',
            trigger: ['blur']
          }
        ],
        form: {
          list: []
        },
        obj: {
          name: undefined,
          type: undefined
        }
      };
    },
    watch: {
      monitorList(val) {
        this.form.list = val;
      }
    },
    mounted() {},
    methods: {
      add() {
        if (this.form.list.length > 19) {
          return this.$message.warning('最多添加20个部位');
        }
        this.form.list.push({ ...this.obj });
      },
      async validForm() {
        //  允许不存在
        if (this.form.list.length === 0) {
          return [];
        }
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          // 假设 this.form.list 是你的对象数组
          let list = this.form.list;

          // 用于统计每个 name 出现的次数
          let nameCount = {};

          // 遍历数组，统计 name 出现次数
          list.forEach((item) => {
            if (nameCount[item.name]) {
              nameCount[item.name]++;
            } else {
              nameCount[item.name] = 1;
            }
          });

          // 找出重复的 name，将其放入数组
          let duplicatedNames = Object.keys(nameCount).filter(
            (name) => nameCount[name] > 1
          );
          console.log(duplicatedNames);
          if (duplicatedNames.length > 0) {
            this.$message.error(
              '部位名称不能重复:' + duplicatedNames.join(',')
            );
            return false;
          } else {
            return this.form.list;
          }
        } else {
          return false;
        }
      },
      resetForm() {
        // this.form.list = [];
      },
      del(idx) {
        this.form.list.splice(idx, 1);
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-form-item__content {
      width: calc(100% - 90px);
    }

    .el-form-item,
    .el-select {
      width: 100%;
    }

    .el-icon-remove {
      margin-top: 7px;
      color: red;
      font-size: 22px;
      cursor: pointer;
    }

    .button {
      display: flex;
      //justify-content: center;
    }
  }
</style>
