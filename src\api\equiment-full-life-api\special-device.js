import request from '@/router/axios';
// 使用登记
export const postUseLog = (data) => {
  return request({
    url: '/api/szyk-simas/special-equipment-usage-log',
    method: 'post',
    data
  });
};
// 检验登记
export const postInspectLog = (data) => {
  return request({
    url: '/api/szyk-simas/special-equipment-inspect-register/submit',
    method: 'post',
    data
  });
};
// 检验等级记录详情
export const getInspectLogDetail = (params) => {
  return request({
    url: '/api/szyk-simas/special-equipment-inspect-register/detail',
    method: 'get',
    params
  });
};
// 检验等级记录分页
export const getInspectLogList = (params) => {
  return request({
    url: '/api/szyk-simas/special-equipment-inspect-register/page',
    method: 'get',
    params
  });
};
// 删除检验记录
export const removeInspectLog = (params) => {
  return request({
    url: '/api/szyk-simas/special-equipment-inspect-register/remove',
    method: 'post',
    params
  });
};
// 使用记录分页
export const getUseLogList = (params) => {
  return request({
    url: '/api/szyk-simas/special-equipment-usage-log/page',
    method: 'get',
    params
  });
};
// 导出使用记录
export const exportUseLog = (params) => {
  return request({
    url: '/api/szyk-simas/special-equipment-usage-log/export',
    method: 'get',
    params
  });
};
