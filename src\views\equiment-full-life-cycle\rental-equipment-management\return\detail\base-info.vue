<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :column="3"
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="设备编号：">{{
        details.code || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备名称：">{{
        details.name || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="规格型号：">{{
        details.model || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="计量单位：">{{
        details.measureUnitName || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
