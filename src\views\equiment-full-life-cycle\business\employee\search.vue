<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      label-width="90px"
      :inline="true"
      ref="search"
      :model="form"
      size="small"
    >
      <el-form-item label="人员姓名" prop="name">
        <el-input
          style="width: 100%"
          v-model.trim="form.name"
          placeholder="请输入人员姓名"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="所属岗位" prop="postId">
        <el-select
          v-model="form.postId"
          style="margin-left: 20px"
          placeholder="请选择所属岗位"
          clearable
        >
          <el-option
            v-for="item in postList"
            :key="item.id"
            :label="item.postName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="人员性别" prop="sex">
        <el-select v-model="form.sex" placeholder="请选择人员性别" clearable>
          <el-option
            v-for="dict in systemDicts.type['sex']"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { postList } from '@/api/equiment-full-life-api/employee';
  export default {
    systemDicts: ['sex'],
    name: 'DeviceListSearch',
    data() {
      return {
        form: {
          name: undefined,
          postId: undefined,
          sex: undefined
        },
        postList: []
      };
    },
    created() {
      this.getPostList();
    },
    methods: {
      // 所属岗位
      async getPostList() {
        try {
          const res = await postList();
          this.postList = res.data.data;
        } catch (e) {
          this.loading = false;
        }
      },
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
