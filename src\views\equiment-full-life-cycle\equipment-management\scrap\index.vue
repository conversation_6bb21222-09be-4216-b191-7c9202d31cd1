<template>
  <basic-container class="table-content" :auto-height="true">
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        v-if="permission['scrap-add-edit']"
        >新增</el-button
      >
      <!-- <el-button
        v-if="permission['scrap-add-export']"
        icon="el-icon-upload2"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 220px)"
      style="width: calc(100% - 10px)"
      border
      stripe
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        prop="no"
        label="报废单号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        align="center"
        prop="name"
        label="报废名称"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="scrapDate"
        label="报废日期"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="equipmentCount"
        label="设备数量"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentCount || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        label="备注"
        align="center"
        show-overflow-tooltip
        width="90px"
      >
        <template slot-scope="scope">
          {{ scope.row.remark || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        prop="statusName"
        label="状态"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${statusColor(row.status)};font-size:18px`">●</i>
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.createUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.updateUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        width="150"
        align="center"
        label="更新时间"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.updateTime || '-' }} </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="250" fixed="right">
        <template slot-scope="scope">
          <section class="cell-operate-class">
            <el-button
              v-if="permission['scrap-list']"
              type="text"
              size="small"
              @click="detail(scope.row, 'detail')"
              >查看</el-button
            >

            <!--           待审核1-->
            <el-button
              class="green-btn"
              type="text"
              size="small"
              @click="detail(scope.row, 'examine')"
              v-if="scope.row.status === 1 && permission['scrap-examine']"
              >审核</el-button
            >

            <!--          编辑：待审核1 驳回3 撤销4-->
            <el-button
              type="text"
              size="small"
              @click="operate(scope.row)"
              v-if="
                (scope.row.status === 1 ||
                  scope.row.status === 3 ||
                  scope.row.status === 4) &&
                permission['scrap-add-edit']
              "
              >编辑</el-button
            >
            <!--           待审核1、驳回3-->
            <el-popconfirm
              title="确定撤销吗？"
              @confirm="() => handleUndo(scope.row)"
              v-if="scope.row.status === 1 && permission['scrap-cancel']"
            >
              <el-button
                class="danger-btn"
                slot="reference"
                type="text"
                size="small"
                >撤销</el-button
              >
            </el-popconfirm>
            <el-popconfirm
              title="确定要删除吗？"
              @confirm="() => deleteApi(scope.row)"
              v-if="
                (scope.row.status === 3 || scope.row.status === 4) &&
                permission['scrap-delete']
              "
            >
              <el-button
                class="danger-btn"
                slot="reference"
                type="text"
                size="small"
                >删除</el-button
              >
            </el-popconfirm>
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex" @success="getList"></detail-index>
  </basic-container>
</template>
<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import { convertFileUrl } from '@/util/util';
  import {
    getPageList,
    userCancel,
    userDeleteLogic
  } from '@/api/equiment-full-life-api/scrap';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  import { mapGetters } from 'vuex';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination
    },
    props: {},
    data() {
      return {
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'permission'])
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      // 状态颜色
      statusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#35C24B'; // 已完成
          case 1:
            return '#EE7C11'; // 待审核
          case 3:
            return '#E23F3F'; // 已驳回
          case 4:
            return '#909399'; // 已撤销
        }
      },
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/scrap/export-scrap?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/scrap/export-scrap?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '设备报废.xlsx'
        );
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getPageList({
            ...this.searchParams
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        let id = row ? row.id : undefined;
        this.$refs.add.show(id);
      },
      detail(row, type) {
        this.$refs['detailIndex'].show(row.id, type);
      },
      async getUserCancel(id) {
        this.loading = true;
        try {
          await userCancel({ id: id });
          this.$message.success('操作成功');
          await this.getList();
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 删除
      async deleteApi(row) {
        this.loading = true;
        try {
          await userDeleteLogic({ ids: row.id });
          //  删除的时候，判断当前列表，是不是length 是1 ，如果是1，将current置成1
          if (this.list.length === 1) {
            this.searchParams.current = 1;
          }
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          await this.getList();
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      //    撤销
      async handleUndo(row) {
        try {
          await this.getUserCancel(row.id);
        } catch (e) {
          this.$message.warning(e.data.msg);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
