<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      label-width="90px"
      :inline="true"
      ref="search"
      :model="form"
      size="small"
    >
      <el-form-item label="设备SN" prop="sn">
        <el-input
          style="width: 100%"
          v-model.trim="form.sn"
          placeholder="请输入设备SN"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <!--      <el-form-item label="设备编号" prop="code">-->
      <!--        <el-input-->
      <!--          style="width: 100%"-->
      <!--          v-model.trim="form.code"-->
      <!--          placeholder="请输入设备编号"-->
      <!--          clearable-->
      <!--          :maxlength="50"-->
      <!--        ></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="设备名称" prop="name">
        <el-input
          style="width: 100%"
          v-model.trim="form.name"
          placeholder="请输入设备名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item label="使用部门" prop="useDeptName">
        <el-input
          style="width: 100%"
          placeholder="请选择使用部门"
          v-model.trim="form.useDeptName"
          @focus.prevent="onSelectDeptClick"
          readonly
          clearable
        >
          <template slot="append">
            <i
              class="el-icon-circle-close"
              @click="
                () => {
                  form.useDept = undefined;
                  form.useDeptName = undefined;
                }
              "
            ></i>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="设备状态" prop="status">
        <el-select
          v-model="form.status"
          style="margin-left: 20px"
          placeholder="请选择设备状态"
          clearable
        >
          <el-option
            v-for="item in returnOption()"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import DeptDialog from '@/components/dept-dialog/index.vue';
  export default {
    name: 'DeviceListSearch',
    components: { DeptDialog },
    props: {
      selType: {
        type: String,
        default: undefined
      }
    },
    serviceDicts: ['equipment_status'],
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        form: {
          sn: undefined,
          code: undefined,
          name: undefined,
          useDeptName: undefined,
          useDept: undefined,
          category: undefined,
          importantLevel: undefined,
          status: undefined
        }
      };
    },
    methods: {
      returnOption() {
        return this.selType === 'scrap'
          ? this.serviceDicts.type['equipment_status'].filter(
              (item) => item.value !== '4'
            )
          : this.serviceDicts.type['equipment_status'];
      },
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.useDept = dept.id;
        this.form.useDeptName = dept.deptName;
      },
      reset() {
        this.form.useDept = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
