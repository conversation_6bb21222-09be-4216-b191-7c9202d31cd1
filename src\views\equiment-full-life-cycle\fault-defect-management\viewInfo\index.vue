<template>
  <basic-container :autoHeight="true">
    <div class="top-info">
      <search ref="search" @query="search"></search>
      <el-button
        v-hasPermi="['fault_add']"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="handleDrawerShow({}, 'add')"
        >新增</el-button
      >
    </div>
    <el-table
      size="small"
      :data="list"
      border
      v-loading="loading"
      height="calc(100% - 150px)"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        prop="no"
        label="故障缺陷编号"
        align="center"
        width="120px"
      >
        <template v-slot="{ row }"> {{ row.no || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="equipmentCode"
        label="设备编号"
        align="center"
        width="120px"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="equipmentModel"
        label="规格型号"
        align="center"
        show-overflow-tooltip
        width="100px"
      >
        <template v-slot="{ row }"> {{ row.equipmentModel || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="monitorName"
        label="缺陷部位"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.monitorName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="levelName"
        label="缺陷等级"
        align="center"
        width="80px"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.levelName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="locationPath"
        label="设备位置"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.locationPath || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="sourceName"
        label="缺陷来源"
        align="center"
        show-overflow-tooltip
        width="100px"
      >
        <template v-slot="{ row }"> {{ row.sourceName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="reportUserName"
        label="报修人"
        align="center"
        show-overflow-tooltip
        width="100px"
      >
        <template v-slot="{ row }"> {{ row.reportUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="reportDeptName"
        label="报修人所属部门"
        align="center"
        show-overflow-tooltip
        width="110px"
      >
        <template v-slot="{ row }"> {{ row.reportDeptName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="reportTime"
        label="上报时间"
        align="center"
        show-overflow-tooltip
        width="130px"
      >
        <template v-slot="{ row }"> {{ row.reportTime || '-' }} </template>
      </el-table-column>
      <!--      <el-table-column-->
      <!--        prop="repairNo"-->
      <!--        label="维修工单号"-->
      <!--        align="center"-->
      <!--        show-overflow-tooltip-->
      <!--        width="150px"-->
      <!--      >-->
      <!--        <template v-slot="{ row }">-->
      <!--          <el-link type="primary" @click="repairNo(row.repairNo)">-->
      <!--            {{ row.repairNo || '-' }}</el-link-->
      <!--          >-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column
        prop="statusName"
        label="缺陷状态"
        align="center"
        show-overflow-tooltip
        width="90px"
      >
        <template v-slot="{ row }">
          <i :style="`color:${statusColor(row.status)};font-size:18px`">●</i>
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop=""
        label="操作"
        align="center"
        show-overflow-tooltip
        width="120px"
      >
        <template slot-scope="{ row }">
          <section class="cell-operate-class">
            <el-button
              size="small"
              type="text"
              @click="handleDrawerShow(row, 'view')"
              >查看</el-button
            >
            <el-button
              v-hasPermi="['fault_handle']"
              v-if="Number(row.status) === 1"
              size="small"
              type="text"
              @click="handleDrawerShow(row, 'handle')"
              >处理</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!--  抽屉 -->
    <component ref="drawer" :is="drawerComp" @success="getList"></component>
  </basic-container>
</template>

<script>
  import Search from './search.vue';
  import AddDrawer from '../add/index.vue';
  import DetailDrawer from '../detail/index.vue';
  import { getDefectList } from '@/api/equiment-full-life-api/defect';

  export default {
    name: 'bearingLibraryIndex',
    components: { Search, AddDrawer, DetailDrawer },
    props: {},
    data() {
      return {
        drawerComp: undefined,
        searchParams: {
          size: 10,
          current: 1
        },
        total: 0,
        loading: false,
        list: []
      };
    },
    watch: {},
    mounted() {
      this.getList();
    },
    methods: {
      // 状态颜色
      statusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#155CFF'; // 已关闭
          case 1:
            return '#909399'; // 待处理
          case 3:
            return '#EE7C11'; // 已报修
          case 4:
            return '#35C24B'; // 已处理
        }
      },
      handleDrawerShow(row, type) {
        this.drawerComp = type == 'add' ? 'AddDrawer' : 'DetailDrawer';
        this.$nextTick(() => {
          this.$refs.drawer.show(row.no, type);
        });
      },
      // 點擊搜索
      search(params) {
        this.searchParams = { ...this.searchParams, ...params };
        this.getList();
      },

      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await getDefectList({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
          this.loading = false;
        } catch (message) {
          this.loading = false;
          console.log('err', message);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
