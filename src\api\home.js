import request from '@/router/axios';
import website from '@/config/website';
// 未完成工单的数据统计
export const noFinishOrderStat = (params) => {
  return request({
    url: '/api/szyk-simas/pc-home/noFinishOrderStat',
    method: 'get',
    params
  });
};
// 本年度设备运行情况
export const curYearEquipmentStatus = (params) => {
  return request({
    url: '/api/szyk-simas/pc-home/curYearEquipmentStatus',
    method: 'get',
    params
  });
};
// 年度工作汇总
export const curYearWorkSummary = (params) => {
  return request({
    url: '/api/szyk-simas/pc-home/curYearWorkSummary',
    method: 'get',
    params
  });
};
// 设备概览
export const equipmentSummary = (params) => {
  return request({
    url: '/api/szyk-simas/dashboard/equipment-summary',
    method: 'get',
    params
  });
};
// 工单占比功能说明
export const allOrderStat = (params) => {
  return request({
    url: '/api/szyk-simas/pc-home/allOrderStat',
    method: 'get',
    params
  });
};
// 我的菜单 ------------------------
export const myUsualmenu = (params) => {
  return request({
    url: '/api/szyk-system/usualmenu/myList',
    method: 'get',
    params
  });
};
export const allMenuList = () => {
  return request({
    url: '/api/szyk-system/usualmenu/allList',
    method: 'get',
    params: { service: website.services }
  });
};
export const saveMenu = (data) => {
  return request({
    url: '/api/szyk-system/usualmenu/save',
    method: 'post',
    data
  });
};
export const removeMenu = (data) => {
  return request({
    url: '/api/szyk-system/usualmenu/removeByIds',
    method: 'post',
    data
  });
};
