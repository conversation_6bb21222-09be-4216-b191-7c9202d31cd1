<template>
  <dialog-popup
    title="选择设备类型"
    ref="dialogs"
    :visible="visible"
    @closed="closed"
    width="80%"
    class="selectSensor"
  >
    <search ref="search" @search="onSubmit"></search>

    <el-table
      ref="multipleTable"
      v-loading="loading"
      :data="dataSource"
      border
      :headerCellStyle="{ background: '#fafafa' }"
      height="400px"
      size="small"
    >
      <el-table-column
        property="categoryName"
        label="设备类型名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        property="pathName"
        label="路径"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        property="remark"
        label="类型编码"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.remark || '-' }}</template>
      </el-table-column>
      <el-table-column
        property="remark"
        label="操作"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <el-button type="text" size="mini" @click="handleClick(row)"
            >选择</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
  </dialog-popup>
</template>
<script>
  import { getDeviceTypePageList } from '@/api/equiment-full-life-api/device-type';
  import Search from './search.vue';

  export default {
    components: { Search },
    props: {},
    data() {
      return {
        visible: false,
        loading: false,
        dataSource: [],
        total: 0,
        searchParams: {
          size: 10,
          current: 1
        }
      };
    },
    watch: {},
    methods: {
      onSubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },

      search() {
        this.searchParams.current = 1;
        this.getList();
      },
      show() {
        this.visible = true;
        this.searchParams.current = 1;
        this.getList(); // 部位列表
      },

      closed() {
        this.searchParams.keywords = undefined;
        this.visible = false;
      },
      // 点击clearable时触发
      clear() {
        this.searchParams.current = 1;
        this.getList();
      },
      //
      async getList() {
        this.loading = true;
        try {
          let params;
          params = {
            ...this.searchParams
          };
          let res = await getDeviceTypePageList(params);
          this.dataSource = res.data.data.records || [];

          this.total = res.data.data.total;

          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      handleClick(row) {
        this.$emit('success', row);
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .el-pagination__sizes {
    display: none !important;
  }

  .search {
    margin-bottom: 10px;
  }

  /deep/ .pagination-container {
    text-align: left !important;
  }

  .clear {
    float: right;
  }

  /deep/ .el-pagination__jump {
    display: none !important;
  }

  .right {
    margin-left: 20px;
  }

  /deep/ {
    .el-form-item--small {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      padding: 10px 0;
    }
  }

  .table {
    :deep(.el-table__cell) {
      padding: 8px 0 !important;
    }
  }

  .selectSensor {
    :deep(.el-dialog__body) {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  ::v-deep {
    .el-input-number--mini {
      width: 100px !important;
    }
  }
</style>
