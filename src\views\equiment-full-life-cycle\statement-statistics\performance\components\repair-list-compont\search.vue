<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="工单编号" prop="no">
        <el-input
          style="width: 100%"
          v-model.trim="form.no"
          placeholder="请输入工单编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="工单类型" prop="bizType">
        <el-select
          style="width: 100%"
          v-model="form.bizType"
          placeholder="请选择工单类型"
          clearable
        >
          <el-option
            v-for="item in [
              { label: '内部维修', value: 'INTERNAL' },
              { label: '外委维修', value: 'EXTERNAL' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="报修类型" prop="repairType">
        <el-select
          style="width: 100%"
          v-model="form.repairType"
          placeholder="请选择报修类型"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['repair_type']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="工单状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择工单状态" clearable>
          <el-option
            v-for="item in serviceDicts.type['repair_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="报修日期" prop="time">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择部门-->
  </div>
</template>

<script>
  import { maintenanceWorkStatus } from '@/views/equiment-full-life-cycle/repair/internal/util';
  export default {
    name: 'DeviceListSearch',
    components: {},
    serviceDicts: ['repair_status', 'repair_type'],
    data() {
      return {
        maintenanceWorkStatus,
        form: {
          no: undefined,
          bizTypeName: undefined,
          bizType: undefined,
          repairType: undefined,
          status: undefined,
          time: undefined
        }
      };
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          startDate: this.form.time ? this.form.time[0] : undefined,
          endDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
