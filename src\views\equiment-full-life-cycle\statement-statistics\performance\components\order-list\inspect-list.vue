<template>
  <div class="table-content">
    <span style="margin-bottom: 10px">
      <search ref="search" @search="search"></search>
      <section style="display: flex; justify-content: flex-end">
        <el-button
          icon="el-icon-arrow-left"
          type="primary"
          plain
          size="small"
          @click="back"
          >返回</el-button
        >
      </section>
    </span>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="no"
        label="工单编号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.no || '-' }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="orderName"
        label="工单名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.orderName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeDeptName"
        label="点巡检部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.executeDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="负责人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="executeDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.executeDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="startTime"
        label="开始时间"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="endTime"
        label="结束时间"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="cycleTypeName"
        label="计划周期"
        align="center"
        show-overflow-tooltip
        width="90px"
      ></el-table-column>
      <el-table-column label="是否需审核" align="center" show-overflow-tooltip>
        <template v-slot="{ row }">{{
          row.isNeedApproval ? '是' : '否'
        }}</template>
      </el-table-column>
      <el-table-column label="审核人员" align="center" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.approvalUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="inspectResult"
        label="检查结果"
        align="center"
        show-overflow-tooltip
        width="90px"
      >
        <template v-slot="{ row }">{{ row.inspectResult || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="110px"
      >
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.statusName || '-' }}</span
          >
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="detail(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />

    <detail-index ref="detailIndex"></detail-index>
    <!--     驳回-->
  </div>
</template>

<script>
  import DetailIndex from '@/views/equiment-full-life-cycle/equipment-inspection/work-order/detail/index.vue'; // 查看详情页面
  import { getKeys } from '../index';
  import Pagination from '@/components/pagination';
  import { getOrderList } from '@/api/equiment-full-life-api/inspect';
  import Search from './search.vue';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      DetailIndex,
      Pagination
    },
    props: {
      orderId: { required: true },
      orderType: { required: true },
      searchType: { required: true },
      searchTime: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    computed: {},
    mounted() {
      this.$nextTick(() => {
        if (this.orderType) {
          let key = getKeys(this.orderType, this.searchType);
          let params = {
            [key]: this.orderId
          };
          this.searchParams = { ...this.searchParams, ...params };
          this.getList();
        }
      });
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      //  点击搜索
      search(form) {
        this.searchParams = { ...this.searchParams, ...form };
        this.getList();
      },
      // 返回上一页
      back() {
        this.$emit('compView', {
          comp: 'ImplementView',
          paramsObj: {
            orderType: this.orderType,
            orderId: this.orderId,
            sType: this.searchType
          }
        });
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      async getList() {
        this.loading = true;
        try {
          let res = await getOrderList({
            ...this.searchParams,
            neStatus: '7',
            startDate: this.searchTime[0],
            endDate: this.searchTime[1]
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },

      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
