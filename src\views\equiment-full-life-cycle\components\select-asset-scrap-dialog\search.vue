<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      label-position="left"
      class="search-form"
    >
      <el-form-item label="设备编号" prop="code">
        <el-input
          style="width: 100%"
          v-model.trim="form.code"
          placeholder="请输入设备编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <!--      <el-form-item label="设备编号" prop="code">-->
      <!--        <el-input-->
      <!--          style="width: 100%"-->
      <!--          v-model.trim="form.code"-->
      <!--          placeholder="请输入设备编号"-->
      <!--          clearable-->
      <!--          :maxlength="50"-->
      <!--        ></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="设备名称" prop="name">
        <el-input
          style="width: 100%"
          v-model.trim="form.name"
          placeholder="请输入设备名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item label="使用部门" prop="useDeptName">
        <!-- <el-input
          style="width: 100%"
          placeholder="请选择使用部门"
          v-model.trim="form.useDeptName"
          @focus.prevent="onSelectDeptClick"
          readonly
          clearable
        >
          <template slot="append">
            <i
              class="el-icon-circle-close"
              @click="
                () => {
                  form.useDept = undefined;
                  form.useDeptName = undefined;
                }
              "
            ></i>
          </template>
        </el-input> -->
        <InputTree
          v-model="form.useDept"
          lazy
          clearable
          :form="form"
          :dic="deptData"
          style="width: 190px"
          :props="{
            label: 'deptName',
            value: 'id',
            isLeaf: (row) => !row.hasChildren,
            formLabel: 'useDeptName',
            formValue: 'useDept'
          }"
          :load="lazyLoad"
          :lazyLoading="lazyLoading"
          @search="lazySearch"
        ></InputTree>
      </el-form-item>

      <el-form-item label="设备状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择设备状态" clearable>
          <el-option
            v-for="item in returnOption()"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import DeptDialog from '@/components/dept-dialog/index.vue';
  import { getLazyList } from '@/api/system/dept';
  import { InputTree } from '@/components/yk-select-tree';
  export default {
    name: 'DeviceListSearch',
    components: { DeptDialog, InputTree },
    props: {
      selType: {
        type: String,
        default: undefined
      }
    },
    serviceDicts: ['equipment_status'],
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        form: {
          code: undefined,
          name: undefined,
          useDeptName: undefined,
          useDept: undefined,
          category: undefined,
          importantLevel: undefined,
          status: undefined
        },
        deptData: [],
        lazyLoading: false
      };
    },
    methods: {
      async lazySearch(title) {
        this.lazyLoad(null, null, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node || {};
        const { id } = data || {};
        let params = {
          deptName: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyList(title ? '' : id || 0, params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          if (title) {
            this.deptData = arr;
          } else {
            resolve && resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      returnOption() {
        return this.selType === 'scrap'
          ? this.serviceDicts.type['equipment_status'].filter(
              (item) => item.value !== '4'
            )
          : this.serviceDicts.type['equipment_status'];
      },
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.useDept = dept.id;
        this.form.useDeptName = dept.deptName;
      },
      reset() {
        this.form.useDept = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
