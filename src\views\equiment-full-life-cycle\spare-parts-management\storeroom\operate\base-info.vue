<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="110px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="库房名称" prop="name">
            <el-input
              placeholder="请输入库房名称"
              v-model.trim="form.name"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员" prop="managerName">
            <el-input
              placeholder="请选择入库人"
              type="text"
              v-model="form.managerName"
              readonly
              @focus.prevent="onChooseUser"
            >
              <template slot="append">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      form.manager = undefined;
                      form.managerName = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系电话" prop="tel">
            <el-input
              placeholder="请输入联系电话"
              v-model.trim="form.tel"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              placeholder="请输入备注"
              v-model.trim="form.remark"
              maxlength="200"
              clearable
              show-word-limit
              autosize
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <recipient-dialog
      ref="recipient"
      @selected="onUserSelect"
    ></recipient-dialog>
  </div>
</template>

<script>
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import reg from '@/util/regexp';

  export default {
    name: 'AddDeviceInfo',
    components: { RecipientDialog },
    props: {
      initData: {
        type: Object,
        default: () => {
          return {};
        }
      },
      // 仓库管理 员
      managerList: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return {
        form: {
          name: undefined,
          manager: undefined,
          managerName: undefined,
          tel: undefined,
          remark: undefined
        },

        rules: {
          name: [
            {
              required: true,
              message: '请输入库房名称',
              trigger: 'blur'
            }
          ],
          tel: [
            {
              required: true,
              message: '请输入联系电话',
              trigger: ['blur', 'change']
            },
            {
              pattern: reg.landline,
              message: '请输入正确的电话号码',
              trigger: ['blur', 'change']
            }
          ],
          managerName: [
            {
              required: true,
              message: '请选择管理员',
              trigger: 'change'
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    methods: {
      //  选择人员
      onChooseUser() {
        this.$refs['recipient'].show();
      },
      onUserSelect(user) {
        this.form.manager = user.id;
        this.form.managerName = user.realName;
      },

      setData(initData) {
        if (initData.useDept) {
          this.getUser(initData.useDept);
        }
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();

        if (valid) {
          return {
            ...this.form
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.edit = false;
        this.$refs['baseForm'].resetFields();
      }
    },
    computed: {},
    created() {}
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-select {
        margin-left: 0 !important;
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
