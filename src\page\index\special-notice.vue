<template>
  <el-dialog
    title="重要提醒"
    :visible.sync="dialogVisible"
    top="10vh"
    width="60%"
    :closed="handleClose"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="summary-text">
      目前有 {{ total }} 个特种设备检修工单待完成，请尽快处理！
    </div>
    <div class="table-title">工单明细</div>
    <el-table
      class="table"
      :data="listData"
      row-key="id"
      size="small"
      max-height="500px"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="locationPath"
        label="设备位置"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.locationPath || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.executeDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="no"
        label="检修工单"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.no || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="责任人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.executeUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="90px"
      ></el-table-column>
    </el-table>
    <pagination
      :page-size.sync="queryParams.size"
      :page-no.sync="queryParams.current"
      :total="total"
      @pagination="getList"
    />
    <div slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" @click="handleClose"
        >知道啦</el-button
      >
      <!-- <el-button
        v-if="userInfo.role_name.includes('service_owner')"
        type="primary"
        @click="urge"
        :loading="loading"
        >一键催办</el-button
      > -->
    </div>
  </el-dialog>
</template>
<script>
  import {
    getCheckRepairOrderPageApi,
    getCheckRepairOrderUrgeApi
  } from '@/api/equiment-full-life-api/repair';
  import { mapGetters } from 'vuex';

  export default {
    data() {
      return {
        dialogVisible: false,
        queryParams: {
          current: 1,
          size: 10,
          onlyQuerySpecialType: true,
          statuses: '1,3,5,6'
        },
        loading: false,
        listData: [],
        total: 0,
        content: '尽快完成特种设备检修工单！'
      };
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods: {
      show(listData, total) {
        this.dialogVisible = true;
        this.listData = listData;
        this.total = total;
      },
      handleClose() {
        this.dialogVisible = false;
      },
      urge() {
        this.$confirm(
          '您将给所有责任人发送催办消息，消息内容为：尽快完成特种设备检修工单！是否继续',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(() => {
            this.OrderUrge();
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },
      async OrderUrge() {
        this.loading = true;
        try {
          await getCheckRepairOrderUrgeApi({ content: this.content });
          this.$message.success('操作成功');
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      async getList() {
        try {
          const { data } = await getCheckRepairOrderPageApi(this.queryParams);
          this.listData = data.data.records || [];
          this.total = data.data.total;
        } catch (e) {
          console.error(e);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .summary-text {
    font-weight: bold;
    font-size: 18px;
  }

  .table-title {
    margin: 12px 0;
    font-weight: bold;
    font-size: 16px;
  }

  .dialog-footer {
    text-align: center;
  }
</style>
