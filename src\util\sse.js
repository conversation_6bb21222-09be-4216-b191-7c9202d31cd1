import { Base64 } from 'js-base64';
import website from '@/config/website';
import { getToken } from '@/util/auth';
import { fetchEventSource } from '@microsoft/fetch-event-source';

export const getSseData = ({
  url,
  data,
  onOpen,
  onMessage,
  onError,
  onClose
}) => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort(); // 触发超时中止
  }, 1000 * 120); // 一分钟超时
  fetchEventSource(url, {
    method: 'POST',
    headers: {
      Authorization: `Basic ${Base64.encode(
        `${website.clientId}:${website.clientSecret}`
      )}`,
      [website.tokenHeader]: 'bearer ' + getToken(),
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data),
    signal: controller.signal, // 绑定中止信号
    onopen(response) {
      onOpen && onOpen();
      if (!response.ok || response.status !== 200) {
        onError && onError();
      }
    },
    onmessage(event) {
      onMessage && onMessage(event);
    },
    onerror(err) {
      // onError && onError();
      console.error('Error:', err);
    },
    onclose() {
      clearTimeout(timeoutId);
      onClose && onClose();
    }
  });
};
