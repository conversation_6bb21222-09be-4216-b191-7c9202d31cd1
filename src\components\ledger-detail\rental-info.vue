<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      v-if="details.lessorVo"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="出租方：">{{
        details.lessorVo.lessorName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="联系人：">{{
        details.lessorVo.contactPerson || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="联系电话：">{{
        details.lessorVo.contactPhone || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="租赁日期：">{{
        details.lessorVo.leaseStartDate
          ? `${details.lessorVo.leaseStartDate} 至 ${details.lessorVo.leaseEndDate}`
          : '-'
      }}</el-descriptions-item>
      <el-descriptions-item v-if="details.isLeaseBack" label="实际租赁日期：">{{
        details?.deviceBackDto?.actualStartLeaseDate
          ? `${details.deviceBackDto.actualStartLeaseDate} 至 ${details.deviceBackDto.actualEndLeaseDate}`
          : '-'
      }}</el-descriptions-item>
      <el-descriptions-item v-if="details.isLeaseBack" label="归还人：">{{
        details?.deviceBackDto?.backPersonName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item
        v-if="details.isLeaseBack"
        :span="3"
        label="归还资料："
      >
        <upload-file
          v-model="details.leaseBackAttachList"
          disable
          placeholder=""
          :limit="9"
          accept=".jpg, .png, .jpeg, .xlsx, .pdf, .docx, .md"
          :isDetailInfo="false"
        ></upload-file>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  import UploadFile from '@/components/upload-file.vue';
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: { UploadFile },

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
