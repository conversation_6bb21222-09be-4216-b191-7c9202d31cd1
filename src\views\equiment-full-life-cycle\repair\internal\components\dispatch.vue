<template>
  <dialog-drawer
    title="派单"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    size="80%"
    class="device-add"
  >
    <section v-loading="loading">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-suffix="："
        label-position="right"
        label-width="120px"
      >
        <p class="el-base-title">报修信息</p>
        <div style="margin-bottom: 12px">
          <el-button size="small" type="primary" @click="onConvertClick">{{
            isInternal ? '转为外部维修' : '转为内部维修'
          }}</el-button>
        </div>
        <el-row>
          <!-- 内部维修字段 -->
          <template v-if="isInternal">
            <el-col :span="8">
              <el-form-item label="维修人员" prop="receiveUser">
                <el-select
                  v-model="form.receiveUser"
                  filterable
                  style="width: 100%"
                  @change="(val) => handleUserChange(val, 'receiveUserTel')"
                >
                  <el-option
                    v-for="user in userList"
                    :key="user.id"
                    :label="user.realName"
                    :value="user.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="维修人电话" prop="receiveUserTel">
                <el-input
                  v-model="form.receiveUserTel"
                  maxlength="11"
                  placeholder="请输入维修人电话"
                ></el-input>
              </el-form-item>
            </el-col>
          </template>
          <!-- 外部维修字段 -->
          <template v-else>
            <el-col :span="8">
              <!-- <el-form-item label="承修单位" prop="externalOrg">
                <el-input
                  maxlength="50"
                  v-model="form.externalOrg"
                  placeholder="请输入承修单位"
                ></el-input>
              </el-form-item> -->
              <el-form-item label="承修单位" prop="supplierId">
                <el-select
                  v-model="form.supplierId"
                  filterable
                  style="width: 100%"
                  placeholder="请选择承修单位"
                  clearable
                >
                  <el-option
                    v-for="item in supplierList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="跟进人" prop="followUserName">
                <el-input
                  placeholder="请选择跟进人"
                  readonly
                  @focus="onChooseUser"
                  v-model="form.followUserName"
                  filterable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="跟进人电话" prop="followUserTel">
                <el-input
                  v-model="form.followUserTel"
                  maxlength="11"
                  placeholder="请输入跟进人电话"
                ></el-input>
              </el-form-item>
            </el-col>
          </template>
          <!-- 通用字段 -->
          <el-col :span="8">
            <el-form-item label="预计完成时间" prop="completeTime">
              <el-date-picker
                style="width: 100%"
                type="datetime"
                v-model="form.completeTime"
                value-format="yyyy-MM-dd HH:mm:00"
                placeholder="请选择预计完成时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备品备件" prop="materialList" label-position>
              <el-button type="primary" size="small" @click="onAddComponent"
                >添加</el-button
              >
            </el-form-item>
            <div style="padding: 0 50px">
              <el-table size="small" :data="form.materialList">
                <el-table-column label="备品备件名称">
                  <template slot-scope="scope">
                    {{ scope.row.name || scope.row.dictName || '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="规格型号" prop="model">
                  <template slot-scope="scope">
                    {{ scope.row.model || '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="消耗数量">
                  <template slot-scope="scope">
                    <el-form-item
                      :prop="'materialList.' + scope.$index + '.count'"
                      :rules="{
                        required: true,
                        message: '请输入消耗数量',
                        trigger: 'blur'
                      }"
                      label-width="0"
                      label=""
                    >
                      <el-input-number
                        size="small"
                        v-model="scope.row.count"
                        :min="0"
                        :max="9999"
                        :controls="false"
                        :precision="scope.row.measureUnitPrecision"
                        placeholder="请输入消耗数量"
                        style="width: 200px"
                      ></el-input-number>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template slot-scope="scope">
                    <!-- <el-button
                      type="danger"
                      size="small"
                      @click="form.materialList.splice(scope.$index, 1)"
                      >删除</el-button
                    > -->
                    <el-popconfirm
                      title="确定删除吗？"
                      @confirm="() => form.materialList.splice(scope.$index, 1)"
                    >
                      <el-button
                        style="color: red"
                        slot="reference"
                        type="text"
                        size="small"
                        >删除</el-button
                      >
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
          <el-col :span="24" class="mt-18">
            <el-form-item label="备注说明" prop="">
              <el-input
                style="width: 100%"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                v-model="form.remark"
                maxlength="200"
                placeholder="请输入备注说明"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="mt-18">
            <el-form-item label="异常描述" prop="">
              <el-input
                style="width: 75%"
                v-model="detail.problemComment"
                readonly
              >
              </el-input>
              <i
                class="el-icon-copy-document"
                title="复制异常描述"
                @click="handleCopy"
              ></i>
            </el-form-item>
          </el-col>
        </el-row>
        <p class="el-base-title ai-btn-wrapper">
          维修建议
          <el-button
            size="small"
            type="primary"
            @click="jumpToAI"
            class="ai-btn"
          >
            故障维修建议</el-button
          >
        </p>
        <el-form-item
          label="维修建议"
          prop="repairSuggest"
          class="textarea-wrapper"
        >
          <el-input
            v-model="form.repairSuggest"
            placeholder="请输入维修建议"
            style="width: 100%"
            type="textarea"
            :rows="6"
            maxlength="3000"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
    <!--     选择人员-->
    <recipient-dialog
      ref="recipient"
      @selected="onUserSelect"
    ></recipient-dialog>
    <!--    选择备品备件 -->
    <select-receive-spare-parts-dialog
      @getAssetList="getAssetList"
      ref="receive"
      showWarehouseSearch
    ></select-receive-spare-parts-dialog>
  </dialog-drawer>
</template>

<script>
  import { mapGetters } from 'vuex';
  import {
    dispatchRepairApi,
    repairToExternalApi,
    getDispatchViewApi
  } from '@/api/equiment-full-life-api/repair';
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import { getUserList } from '@/api/system/dept';
  // import SelectReceiveSparePartsDialog from '@/views/equiment-full-life-cycle/components/select-receive-spare-parts-dialog/index.vue';
  import SelectReceiveSparePartsDialog from '@/views/equiment-full-life-cycle/repair/select-receive-spare-parts-dialog';
  import reg from '@/util/regexp';
  import { copyText } from '@/util/util';
  import { cloneDeep } from 'lodash';
  import { getSupplierList } from '@/api/equiment-full-life-api/common';

  export default {
    name: 'repair-internal-dispatch',
    components: { RecipientDialog, SelectReceiveSparePartsDialog },
    data() {
      return {
        isInternal: true,
        visible: false,
        detail: {},
        loading: false,
        userListLoading: false,
        userList: [],
        edit: false,
        tableKey: Math.random(),
        // 初始备品备件列表
        originalList: [],
        form: {
          // 维修人
          receiveUser: '',
          receiveUserTel: '',
          // 预计完成时间
          completeTime: '',
          // 派单说明
          remark: '',
          // 承修单位
          // externalOrg: '',
          supplierId: '',
          // 承修单位联系电话
          externalTel: '',
          followUser: '',
          followUserName: '',
          followUserTel: '',
          materialList: [],
          repairSuggest: ''
        },
        supplierList: [],
        rules: {
          receiveUser: [
            { required: true, message: '请选择维修人员', trigger: 'change' }
          ],
          receiveUserTel: [
            {
              pattern: reg.phone,
              message: '请输入正确的手机号码',
              trigger: 'change'
            }
          ],
          completeTime: [
            { required: true, message: '请选择预计完成时间', trigger: 'blur' }
          ],
          supplierId: [
            { required: true, message: '请选择承修单位', trigger: 'blur' }
          ],
          followUserName: [
            {
              required: true,
              message: '请选择跟进人',
              trigger: 'change'
            }
          ],
          followUserTel: [
            {
              pattern: reg.phone,
              message: '请输入正确的手机号码',
              trigger: 'change'
            }
          ]
        }
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'permission'])
    },
    methods: {
      onConvertClick() {
        // 清空提示语句
        this.$refs['form'].clearValidate();
        this.isInternal = !this.isInternal;
        this.form = {
          // 维修人
          receiveUser: '',
          receiveUserTel: '',
          // 预计完成时间
          completeTime: '',
          // 派单说明
          remark: '',
          // 承修单位
          // externalOrg: '',
          supplierId: '',
          // 承修单位联系电话
          externalTel: '',
          followUser: '',
          followUserName: '',
          followUserTel: '',
          materialList: cloneDeep(this.originalList)
        };
      },
      async getSupplierList() {
        this.loading = true;
        try {
          let res = await getSupplierList({ current: 1, size: -1 });
          this.supplierList = res.data.data.records || [];
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      show(row) {
        if (row && row.id) {
          this.getSupplierList();
          row.source === '3' && this.getSparePartsList(row);
          this.visible = true;
          this.getUserList();
          this.detail = row;
          // const listData = JSON.parse(row.component);
          // if (listData && listData.length > 0) {
          //   this.form.materialList = listData;
          //   this.originalList = cloneDeep(listData);
          // }
        } else {
          this.$message.warning('该数据异常');
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.originalList = [];
        this.form.receiveUser = '';
        this.form.receiveUserTel = '';
        this.form.completeTime = '';
        this.form.materialList = [];
        this.form.followUser = '';
        this.form.followUserName = undefined;
        this.form.followUserTel = '';
        // this.form.externalOrg = undefined;
        this.form.supplierId = '';
        this.form.remark = '';
        this.form.repairSuggest = '';
        this.detail = {};
        this.isInternal = true;
        this.$nextTick(this.$refs.form.clearValidate);
        this.visible = false;
      },
      async submit() {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            try {
              if (this.isInternal) {
                this.loading = true;
                await dispatchRepairApi({
                  id: this.detail.id,
                  ...this.form
                });
              } else {
                const flag = this.form.materialList.every((item) => item.count);
                if (flag) {
                  this.loading = true;
                  await repairToExternalApi({
                    id: this.detail.id,
                    // externalOrg: this.form.externalOrg,
                    supplierId: this.form.supplierId,
                    followUser: this.form.followUser,
                    followUserTel: this.form.followUserTel,
                    completeTime: this.form.completeTime,
                    materialList: this.form.materialList,
                    repairSuggest: this.form.repairSuggest,
                    remark: this.form.remark
                  });
                } else {
                  this.$message.warning('请完善维修配件信息');
                  return;
                }
              }
              this.$message.success('操作成功');
              this.$emit('success');
              this.visible = false;
              this.loading = false;
            } catch (e) {
              this.loading = false;
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      async getSparePartsList(row) {
        try {
          this.userListLoading = true;
          const res = await getDispatchViewApi({
            no: row.no
          });
          const listData = res.data.data.repairComponentList;
          if (listData && listData.length > 0) {
            this.form.materialList = listData;
            this.originalList = cloneDeep(listData);
          }
          this.userListLoading = false;
        } catch (e) {
          this.userListLoading = false;
          this.form.materialList = [];
          this.originalList = [];
        }
      },
      async getUserList() {
        try {
          this.userListLoading = true;
          const res = await getUserList({
            size: -1
          });
          this.userList = res.data.data.records;
          this.userListLoading = false;
        } catch (e) {
          this.userListLoading = false;
          this.monitorList = [];
        }
      },
      getAssetList(list) {
        let arrList = list.map((item) => {
          return {
            ...item,
            no: item.no || item.dictNo,
            name: item.name || item.dictName,
            model: item.model,
            dictName: item.dictName,
            stockId: item.id, // 这两个id必传
            dictNo: item.dictNo,
            dictModel: item.model,
            measureUnitName: item.measureUnitName,
            measureUnitId: item.measureUnitId,
            count: item.count,
            measureUnitPrecision: item.measureUnitPrecision
          };
        });
        this.form.materialList = arrList;
      },
      onAddComponent() {
        this.$refs['receive'].show(this.form.materialList);
        // this.form.materialList.push({
        //   name: '',
        //   model: '',
        //   count: 0
        // });
      },
      //  选择人员
      onChooseUser() {
        this.$refs['recipient'].show();
      },
      onUserSelect(user) {
        this.form.followUser = user.id;
        this.form.followUserName = user.realName;
        this.form.followUserTel = user.phone;
      },
      // 人员变更时获取手机号
      handleUserChange(val, field) {
        const user = this.userList.find((item) => item.id === val) || {};
        this.form[field] = user.phone;
      },
      // 复制异常描述
      handleCopy() {
        this.$nextTick(async () => {
          copyText(this.detail.problemComment);
        });
      },
      // 跳转到故障维修建议
      jumpToAI() {
        const { href } = this.$router.resolve('/aitools/index');
        const { equipmentId, equipmentName, problemComment } = this.detail;
        const url = `${href}?deviceId=${equipmentId}&deviceName=${equipmentName}&defectDesc=${problemComment}&type=diagnose`;
        window.open(url, '_blank');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  .mt-18 {
    margin-top: 18px !important;
  }

  .el-icon-copy-document {
    margin-left: 10px;
    font-size: 22px;
    cursor: pointer;
  }

  .ai-btn-wrapper {
    position: relative;

    .ai-btn {
      position: absolute;
      top: -9px;
      right: 0;
    }
  }
</style>
