<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="110px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="人员姓名" prop="name">
            <el-input
              placeholder="请输入人员姓名"
              v-model.trim="form.name"
              maxlength="50"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="tel">
            <el-input
              placeholder="请输入联系电话"
              v-model.trim="form.tel"
              maxlength="50"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属部门" prop="deptName">
            <el-input
              style="width: 100%"
              placeholder="请输入所属部门"
              v-model.trim="form.deptName"
              @focus.prevent="onSelectDeptClick"
              readonly
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属岗位" prop="postId">
            <el-select
              v-model="form.postId"
              style="margin-left: 20px"
              placeholder="请选择所属岗位"
            >
              <el-option
                v-for="item in postList"
                :key="item.id"
                :label="item.postName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="人员性别" prop="sex">
            <el-select
              v-model="form.sex"
              style="margin-left: 20px"
              placeholder="请选择人员性别"
            >
              <el-option
                v-for="item in sexDicData"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="idNumber">
            <el-input
              style="width: 100%"
              placeholder="请输入身份证号"
              v-model.trim="form.idNumber"
              clearable
              @blur="validateIdNumber"
              :maxlength="50"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="年龄" prop="age">
            <el-input-number
              style="width: 100%"
              :controls="false"
              placeholder="请输入年龄"
              v-model.trim="form.age"
              :min="0"
              :max="999"
              :precision="0"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出生年月" prop="birthday">
            <el-date-picker
              v-model="form.birthday"
              type="date"
              placeholder="请选择出生年月"
              clearable
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="员工性质" prop="property">
            <el-select
              v-model="form.property"
              style="margin-left: 20px"
              placeholder="请选择员工性质"
            >
              <el-option
                v-for="item in serviceDicts.type['staff_property']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学历" prop="qualification">
            <el-select
              v-model="form.qualification"
              style="margin-left: 20px"
              placeholder="请选择学历"
            >
              <el-option
                v-for="item in serviceDicts.type['staff_qualification']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="职称" prop="title">
            <el-input
              v-model.trim="form.title"
              placeholder="请输入职称"
              :maxlength="50"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工作年限" prop="workYears">
            <el-input-number
              style="width: 100%"
              :controls="false"
              placeholder="请输入工作年限"
              v-model="form.workYears"
              :min="0"
              :max="999"
              :precision="0"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row class="add-info">
        <el-col :span="12">
          <el-form-item prop="attachList" label="资质证书">
            <upload-img
              v-model="form.attachList"
              placeholder="上传资质证书"
              :limit="1"
              formatLimit="jpeg,png,jpg"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import DeptDialog from '@/components/dept-dialog/index.vue';
  import UploadImg from '@/components/uploadImage.vue';
  import { convertFileUrl } from '@/util/util';
  import { isMobile, cardid, parseIdCard } from '@/util/validate';
  import { sexDicData } from '@/views/system/user/components/data';

  export default {
    serviceDicts: ['staff_property', 'staff_qualification'],
    name: 'AddDeviceInfo',
    components: {
      UploadImg,
      DeptDialog
    },
    props: {
      initData: {
        type: Object,
        default: () => {}
      },
      postList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        sexDicData,
        form: {
          name: undefined,
          tel: undefined,
          deptName: undefined,
          deptId: undefined,
          postId: undefined,
          sex: undefined,
          idNumber: undefined,
          age: undefined,
          birthday: undefined,
          property: undefined, //： staff_property
          qualification: undefined, // staff_qualification
          title: undefined,
          workYears: undefined,
          attachList: []
        },

        edit: false,
        rules: {
          name: [
            {
              required: true,
              message: '请输入人员姓名',
              trigger: 'blur'
            }
          ],
          idNumber: [
            {
              validator: (rule, value, callback) => {
                if (!value) {
                  callback();
                } else {
                  let list = cardid(value);
                  console.log(list);
                  if (list[0]) {
                    callback(new Error(list[1]));
                  } else {
                    this.getInfo();
                    callback();
                  }
                }
              },
              trigger: 'blur'
            }
          ],
          tel: [
            {
              required: true,
              message: '请输入联系电话',
              trigger: 'blur'
            },
            {
              validator: (rule, value, callback) => {
                // 如果是null 空字符串
                if (!value) {
                  callback();
                } else if (isMobile(value)) {
                  callback();
                } else {
                  callback(new Error('请输入正确的手机号'));
                }
              },
              trigger: 'blur'
            }
          ],
          deptName: [
            {
              required: true,
              message: '请选择所属部门',
              trigger: ['change']
            }
          ],
          postId: [
            {
              required: true,
              message: '请选择所属岗位',
              trigger: ['change']
            }
          ],
          sex: [
            {
              required: true,
              message: '请选择人员性别',
              trigger: 'change'
            }
          ],
          property: [
            {
              required: true,
              message: '请选择员工性质',
              trigger: ['change']
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    methods: {
      getInfo() {
        let getInfo = parseIdCard(this.form.idNumber);
        console.log('分析身份证......', getInfo);
        this.form.age = getInfo.age;
        this.form.birthday = getInfo.birthDate;
      },
      validateIdNumber() {
        console.log('身份证号失去焦点', this.form.idNumber);
        let idNumber = this.form.idNumber.trim();
        if (idNumber) {
          this.$refs.baseForm.validateField('idNumber');
        }
        // 触发表单项的验证
      },
      // 选择部门
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.deptId = dept.id;
        this.form.deptName = dept.deptName;
      },
      setData(initData) {
        for (const key in this.form) {
          this.form[key] = initData[key];
        }
        this.form.age = initData.age === null ? undefined : initData.age;

        this.form.workYears = initData.workYears || undefined;
        this.form.attachList = (initData.attachList || []).map((it) => {
          return {
            id: it.id,
            fileName: it.fileName,
            filePath: convertFileUrl(it.domain)
          };
        });
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return {
            ...this.form,
            attachId:
              this.form.attachList.length > 0 ? this.form.attachList[0].id : ''
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs['baseForm'].resetFields();
      }
    },
    computed: {},
    created() {}
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }
      .el-select {
        margin-left: 0 !important;
      }
      .el-form-item__content {
        flex: 1;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }
      .el-input__inner {
        text-align: left !important;
      }
      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
