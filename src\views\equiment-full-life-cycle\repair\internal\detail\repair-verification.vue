<template>
  <el-descriptions
    border
    size="small"
    v-if="detail.repairRecord"
    :labelStyle="{ width: '110px', textAlign: 'right' }"
    :contentStyle="{
      width: '300px',
      wordBreak: 'break-all',
      wordWrap: 'break-word'
    }"
    contentClassName="contentClassName"
  >
    <el-descriptions-item label="验证评论：">{{
      detail.repairRecord.verifyComment || '-'
    }}</el-descriptions-item>
  </el-descriptions>
</template>

<script>
  export default {
    props: {
      detail: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>
<style scoped lang="scss"></style>
