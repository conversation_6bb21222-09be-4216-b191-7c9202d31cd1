<template>
  <el-form
    v-show="showSearch"
    ref="queryForm"
    :model="queryParams"
    :inline="true"
    label-position="left"
    size="small"
  >
    <el-form-item label="菜单名称" prop="name" style="margin-bottom: 5px">
      <el-input
        v-model="queryParams.name"
        placeholder="请输入菜单名称"
        clearable
        @keyup.enter.native="handleQuery"
      />
    </el-form-item>
    <el-form-item label="菜单编号" prop="code" style="margin-bottom: 5px">
      <el-input
        v-model="queryParams.code"
        placeholder="请输入菜单编号"
        clearable
        @keyup.enter.native="handleQuery"
      />
    </el-form-item>
    <el-form-item label="菜单别名" prop="alias" style="margin-bottom: 5px">
      <el-input
        v-model="queryParams.alias"
        placeholder="请输入菜单别名"
        clearable
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" icon="el-icon-search" @click="handleQuery"
        >搜索</el-button
      >
      <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
  export default {
    props: {
      showSearch: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        queryParams: {
          name: undefined,
          code: undefined,
          alias: undefined
        }
      };
    },
    mounted() {
      this.handleQuery();
    },
    methods: {
      handleQuery() {
        this.$emit('search', Object.assign({}, this.queryParams));
      },
      resetQuery() {
        this.$refs.queryForm.resetFields();
        this.handleQuery();
      }
    }
  };
</script>
