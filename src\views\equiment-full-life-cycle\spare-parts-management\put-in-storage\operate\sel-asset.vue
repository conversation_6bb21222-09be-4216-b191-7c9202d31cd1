<template>
  <div>
    <el-button
      size="small"
      type="primary"
      style="margin-bottom: 15px"
      @click="selAsset"
      v-if="isShow"
      >+ 选择备品备件</el-button
    >
    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
    >
      <el-table
        v-loading="loading"
        class="table"
        :data="form.list"
        border
        stripe
        :header-cell-style="{ background: '#fafafa' }"
        size="small"
      >
        <el-table-column type="index" label="#"></el-table-column>
        <el-table-column
          prop="dictNo"
          label="备品备件编号"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="dictName"
          label="备品备件名称"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
          <template v-slot="{ row }">{{ row.model || '-' }}</template>
        </el-table-column>

        <el-table-column
          prop="measureUnitName"
          label="计量单位"
          show-overflow-tooltip
        >
          <template v-slot="{ row }">{{ row.measureUnitName || '-' }}</template>
        </el-table-column>

        <el-table-column prop="inboundQuantity	" label="入库数量" width="300px">
          <template v-slot="scope">
            <el-form-item
              v-if="isShow"
              :prop="'list.' + scope.$index + '.inboundQuantity'"
              :rules="[
                {
                  required: true,
                  message: '请输入入库数量',
                  trigger: 'blur'
                },
                {
                  validator: (rule, value, callback) =>
                    validateCode(rule, value, callback)
                }
              ]"
              label=" "
            >
              <!--              <el-input-number-->
              <!--                size="small"-->
              <!--                :controls="false"-->
              <!--                v-model="scope.row.inboundQuantity"-->
              <!--                :min="1"-->
              <!--                :max="9999"-->
              <!--                :precision="scope.row.measureUnitPrecision"-->
              <!--                placeholder="请输入入库数量"-->
              <!--              ></el-input-number>-->
              <el-input
                style="width: 240px"
                size="small"
                placeholder="请输入入库数量"
                v-model.trim="scope.row.inboundQuantity"
                clearable
                maxlength="50"
              />
            </el-form-item>
            <span v-else>{{ scope.row.inboundQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="model" label="操作" width="60" v-if="isShow">
          <template v-slot="scope">
            <el-button
              type="text"
              style="color: red"
              size="mini"
              @click="del(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <asset-list
      ref="assetList"
      @getAssetList="getAssetList"
      module="inStorage"
      :warehouseId="warehouseId"
      :status="'0'"
    ></asset-list>
  </div>
</template>

<script>
  import AssetList from '@/views/equiment-full-life-cycle/components/select-spare-parts-dialog';
  import { validateValueThen0 } from '@/util/func';
  export default {
    name: 'DeviceBasicList',
    components: { AssetList },
    props: {
      detail: {
        type: Object,
        default: () => {}
      },
      isShow: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      warehouseId: {
        type: String,
        default: () => {
          return undefined;
        }
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.form.list = this.detail.itemList;
            });
          }
        }
      }
    },
    data() {
      const validateCode = (rule, value, callback) => {
        // 获取当前数组
        let arr = rule.field.split('.');
        let idx = Number(arr[1]);
        let measureUnitPrecision = this.form.list[idx].measureUnitPrecision;
        let val = validateValueThen0(value, 9999, measureUnitPrecision);
        if (!value) {
          callback();
        } else if (val) {
          callback();
        } else {
          callback(
            new Error(`请输入大于0且小于9999的值,精度${measureUnitPrecision}`)
          );
        }
      };
      return {
        validateCode,
        loading: false,
        total: 0,
        form: {
          list: []
        },
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },

    mounted() {},
    methods: {
      del(scope) {
        this.form.list.splice(scope.$index, 1);
      },
      async validForm() {
        if (this.form.list.length === 0) {
          this.$message.warning('请选择备品备件');
          return;
        }
        let valid = await this.$refs['listForm'].validate();
        if (valid) {
          let p = this.form.list.map((it) => {
            return {
              dictId: it.id,
              inboundQuantity: it.inboundQuantity
            };
          });
          return p;
        } else {
          return false;
        }
      },
      resetForm() {
        this.form.list = [];
      },
      //  获取设备
      getAssetList(val) {
        let list = val.map((item) => {
          return {
            ...item,
            dictId: item.id,
            dictNo: item.no,
            dictName: item.name,
            measureUnitPrecision: item.measureUnitPrecision
          };
        });
        this.form.list = list;
      },
      selAsset() {
        if (!this.warehouseId) {
          this.$message.warning('请先选择入库仓库');
          return;
        }
        let list = this.form.list.map((it) => {
          return {
            ...it,
            dictId: it.id,
            dictNo: it.no,
            dictName: it.name,
            num: 1
          };
        });
        this.$refs['assetList'].show(list);
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }
  .operateBtn {
    margin-bottom: 15px;
  }
  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
