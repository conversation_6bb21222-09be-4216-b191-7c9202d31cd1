<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      label-position="right"
      size="small"
      class="search-form"
    >
      <el-form-item label="变更单号" prop="changeNumber">
        <el-input
          style="width: 100%"
          v-model.trim="form.changeNumber"
          placeholder="请输入"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="变更单名称" prop="changeName">
        <el-input
          style="width: 100%"
          v-model.trim="form.changeName"
          placeholder="请输入"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item label="变更单状态" prop="changeStatus">
        <el-select v-model="form.changeStatus" placeholder="请选择" clearable>
          <el-option
            v-for="item in serviceDicts.type['equipment_change_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="applyDate">
        <el-date-picker
          v-model="form.applyDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择部门-->
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    components: {},
    serviceDicts: ['equipment_change_status'],
    data() {
      return {
        form: {
          changeNumber: undefined,
          changeName: undefined,
          changeStatus: undefined,
          applyDate: []
        }
      };
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let [startApplyDate, endApplyDate] = this.form.applyDate || [];
        let params = {
          ...this.form,
          startApplyDate,
          endApplyDate
        };
        delete params.applyDate;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
