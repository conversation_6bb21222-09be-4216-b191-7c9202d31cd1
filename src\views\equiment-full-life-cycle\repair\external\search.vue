<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <!--      <el-form-item label="维修人员" prop="no">-->
      <!--        <el-input-->
      <!--          style="width: 100%"-->
      <!--          v-model.trim="form.no"-->
      <!--          placeholder="请选择维修"-->
      <!--          clearable-->
      <!--          :maxlength="50"-->
      <!--        ></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="工单编号" prop="no">
        <el-input
          style="width: 150px"
          v-model.trim="form.no"
          placeholder="请输入工单编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="报修类型" prop="repairType">
        <el-select
          style="width: 150px"
          v-model="form.repairType"
          placeholder="请选择报修类型"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['repair_type']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="工单状态" prop="status">
        <el-select
          v-model="form.status"
          style="width: 150px"
          placeholder="请选择工单状态"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['order_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="报修日期" prop="time">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          style="width: 250px"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择部门-->
  </div>
</template>

<script>
  import { maintenanceWorkStatus } from './util';
  export default {
    name: 'DeviceListSearch',
    components: {},
    serviceDicts: ['order_status', 'repair_type'],
    data() {
      return {
        maintenanceWorkStatus,
        form: {
          no: undefined,
          orderName: undefined,
          status: undefined,
          time: undefined
        }
      };
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      // 清空并重新设置字段
      setFields() {
        this.$refs['search'].resetFields();
        // const { field, value } = fields;
        // this.form[field] = value;
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          startDate: this.form.time ? this.form.time[0] : undefined,
          endDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
