<template>
  <dialog-drawer
    :title="type === 'verify' ? '验收' : '详情'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="detail" v-loading="loading">
      <changeInfoDetail :detail="detail" :type="type"></changeInfoDetail>
      <div style="padding-bottom: 50px">
        <logs ref="log" title="审核日志"></logs>
      </div>
      <span
        class="el-base-title"
        v-if="type === 'verify'"
        style="margin-top: 20px"
        >变更结果评价及图片</span
      >
      <el-form
        :model="form"
        label-suffix="："
        v-if="type === 'verify'"
        ref="baseForm"
        :rules="rules"
        label-width="100px"
        :label-position="'right'"
        size="small"
      >
        <el-row class="add-info">
          <el-col :span="24">
            <el-form-item prop="renovationEvaluation" label="改造评价">
              <el-input
                type="textarea"
                style="width: 100%"
                v-model="form.renovationEvaluation"
                maxlength="1000"
                placeholder="请输入"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="imageList" label="上传图片">
              <upload-img
                v-model="form.imageList"
                placeholder="上传图片"
                :limit="8"
                formatLimit="jpeg,png,jpg"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="oper_btn">
      <el-button
        size="small"
        v-if="type === 'verify'"
        type="success"
        @click="onVerify(1)"
        :loading="loading"
      >
        确认验收</el-button
      >
      <btn type="close" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>
<script>
  import {
    getChangeViewApi,
    verifyChangeApi
  } from '@/api/equiment-full-life-api/change';
  import Logs from '@/views/equiment-full-life-cycle/components/logs/index.vue';
  import UploadImg from '@/components/uploadImage.vue';
  import changeInfoDetail from './change-info-detail';
  export default {
    name: 'RepairViewIndex',
    components: {
      changeInfoDetail,
      UploadImg,
      Logs
    },

    data() {
      return {
        eqId: '',
        type: '',
        visible: false,
        loading: false,
        detail: {}, // 详情数据
        form: {
          renovationEvaluation: '',
          imageList: []
        },
        rules: {
          renovationEvaluation: [
            { required: true, message: '请输入', trigger: 'blur' }
          ]
        }
      };
    },
    methods: {
      show(id, type) {
        this.type = type || '';
        this.visible = true;
        if (id) {
          this.getDetail(id);
        }
      },
      closed() {
        this.visible = false;
        this.form.renovationEvaluation = '';
        this.form.imageList = [];
        this.detail = {
          equipmentAccount: {}
        };
      },
      // 获取详情接口
      async getDetail(id) {
        try {
          this.loading = true;
          const res = await getChangeViewApi({ id });
          this.detail = res.data.data;
          await this.$refs['log'].getLogs(this.detail.id, 'EQUIPMENT_CHANGE');
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return {
            ...this.form
          };
        } else {
          return false;
        }
      },
      async onVerify() {
        try {
          let changeFileList = this.detail.changeFileList || [];
          let fileList = changeFileList.filter(
            (item) => item.updateStatus !== '2'
          );
          let everyUpdate = fileList.every((item) => item.updateStatus === '1');
          if (!everyUpdate)
            return this.$message.warning('请上传需要更新的文件');
          this.validForm();
          let { renovationEvaluation, imageList } = this.form;
          if (!renovationEvaluation) {
            return this.$message.warning('请输入改造评价');
          }
          // if (!imageList || !imageList.length) {
          //   return this.$message.warning('请上传图片');
          // }
          this.loading = true;
          let imageLists = imageList.map((item) => ({
            attachId: item.attachId || item.id
          }));
          await verifyChangeApi({
            id: this.detail.id,
            renovationEvaluation,
            imageList: imageLists
          });
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  .detail {
    padding-bottom: 80px;
  }

  ::v-deep .el-card__body {
    padding-top: 0;
  }
</style>
