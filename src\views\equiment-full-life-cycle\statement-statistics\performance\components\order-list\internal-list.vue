<template>
  <div class="table-content">
    <span style="margin-bottom: 10px">
      <search @search="search" order-type="repair"></search>
      <section style="display: flex; justify-content: flex-end">
        <el-button
          icon="el-icon-arrow-left"
          type="primary"
          plain
          size="small"
          @click="back"
          >返回</el-button
        >
      </section>
    </span>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 180px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column prop="no" label="工单编号" width="150" align="center">
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.no || '-' }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="sourceName"
        label="工单来源"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.sourceName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="repairTypeName"
        label="报修类型"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.repairTypeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="equipmentName" label="设备名称" align="center">
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="equipmentModel" label="设备型号" align="center">
        <template slot-scope="scope">
          {{ scope.row.equipmentModel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="monitorName"
        label="维修部位"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.monitorName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="reportUserName"
        label="报修人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.reportUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="tel"
        label="报修人电话"
        align="center"
        width="94px"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.tel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="receiveUserName"
        label="维修人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.receiveUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="receiveUserTel"
        label="维修人电话"
        align="center"
        width="94px"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.receiveUserTel || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="是否需审核" align="center" show-overflow-tooltip>
        <template v-slot="{ row }">{{
          row.isNeedApproval ? '是' : '否'
        }}</template>
      </el-table-column>
      <el-table-column label="审核人员" align="center" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.approvalUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="reportTime"
        label="报修时间"
        align="center"
        width="90px"
      >
        <template v-slot="{ row }">
          <template v-if="!row.reportTime">-</template>
          <template v-else>
            <div v-for="item in row.reportTime.split(' ')" :key="item">
              {{ item }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        width="93px"
        prop="completeTime"
        label="预计完成时间"
        align="center"
      >
        <template v-slot="{ row }">
          <template v-if="!row.completeTime">-</template>
          <template v-else>
            <div v-for="item in row.completeTime.split(' ')" :key="item">
              {{ item }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="handleTime"
        label="处理时间"
        align="center"
        width="90px"
      >
        <template v-slot="{ row }">
          <template v-if="!row.handleTime">-</template>
          <template v-else>
            <div v-for="item in row.handleTime.split(' ')" :key="item">
              {{ item }}
            </div>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="90px"
      >
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.statusName || '-' }}</span
          >
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="detail(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <detail-index ref="detailIndex" @success="getList"></detail-index>
  </div>
</template>

<script>
  import DetailIndex from '@/views/equiment-full-life-cycle/repair/internal/detail/index.vue'; // 查看详情页面
  import Pagination from '@/components/pagination';
  import { getInternalPageApi } from '@/api/equiment-full-life-api/repair';
  import RepairInternalOperate from '@/views/equiment-full-life-cycle/repair/internal/operate/index.vue';
  import RepairInternalDispatch from '@/views/equiment-full-life-cycle/repair/internal/components/dispatch.vue';
  import { getKeys } from '@/views/equiment-full-life-cycle/statement-statistics/performance/components';
  import Search from './search.vue';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      RepairInternalDispatch,
      RepairInternalOperate,
      DetailIndex,
      Pagination
    },
    props: {
      orderId: { required: true },
      orderType: { required: true },
      searchType: { required: true },
      searchTime: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    mounted() {
      this.$nextTick(() => {
        if (this.orderType) {
          let key = getKeys(this.orderType, this.searchType);
          let params = {
            [key]: this.orderId
          };
          this.searchParams = { ...this.searchParams, ...params };
          this.getList();
        }
      });
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      //  点击搜索
      search(form) {
        this.searchParams = { ...this.searchParams, ...form };
        this.getList();
      },
      // 返回上一页
      back() {
        this.$emit('compView', {
          comp: 'ImplementView',
          paramsObj: {
            orderType: this.orderType,
            orderId: this.orderId,
            sType: this.searchType
          }
        });
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },
      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getInternalPageApi({
            ...this.searchParams,
            neStatus: '7',
            startDate: this.searchTime[0],
            endDate: this.searchTime[1]
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      detail(row, type) {
        this.$refs['detailIndex'].show(row.no, type);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
