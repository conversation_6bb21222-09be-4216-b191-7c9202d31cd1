import Dict from './DictBiz';
import { mergeOptions } from './DictOptions';

export default function (Vue, options) {
  mergeOptions(options);
  Vue.mixin({
    data() {
      if (
        this.$options === undefined ||
        this.$options.serviceDicts === undefined ||
        this.$options.serviceDicts === null
      ) {
        return {};
      }
      const serviceDicts = new Dict();
      serviceDicts.owner = this;
      return {
        serviceDicts
      };
    },
    created() {
      if (!(this.serviceDicts instanceof Dict)) {
        return;
      }
      options.onCreated && options.onCreated(this.serviceDicts);
      this.serviceDicts.init(this.$options.serviceDicts).then(() => {
        options.onReady && options.onReady(this.serviceDicts);
        this.$nextTick(() => {
          this.$emit('dictReady', this.serviceDicts);
          if (
            this.$options.methods &&
            this.$options.methods.onDictReady instanceof Function
          ) {
            this.$options.methods.onDictReady.call(this, this.serviceDicts);
          }
        });
      });
    }
  });
}
