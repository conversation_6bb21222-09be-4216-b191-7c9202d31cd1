<template>
  <el-dialog
    append-to-body
    width="60%"
    :title="title"
    :visible.sync="open"
    :close-on-click-modal="isView"
    @close="close"
  >
    <add-edit-form
      v-if="!isView"
      ref="addEditForm"
      v-loading="loading"
      v-bind="$attrs"
      :visited="visited"
      :model-type="modelType"
      :form-data="formData"
    />
    <detail v-else v-loading="loading" :form-data="formData" v-bind="$attrs" />
    <span slot="footer" v-if="!isView">
      <el-button type="primary" size="small" @click="ok">保 存</el-button>
      <el-button size="small" @click="open = false">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import AddEditForm from './form';
  import Detail from './detail';
  import { update, add, getDept } from '@/api/system/dept';

  export default {
    components: {
      AddEditForm,
      Detail
    },
    props: {
      title: {
        type: String,
        default: ''
      },
      visited: {
        type: Boolean,
        default: false
      },
      id: {
        type: String,
        default: ''
      },
      modelType: {
        type: String,
        default: ''
      }
    },
    watch: {
      visited(val) {
        if (val && (this.isEdit || this.isView)) {
          this.init();
        }
      }
    },
    data() {
      return {
        loading: false,
        formData: {}
      };
    },
    methods: {
      // 编辑/查看获取初始数据
      async init() {
        this.loading = true;
        try {
          const res = await getDept(this.id);
          this.formData = res.data.data;
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      //关闭
      close() {
        this.open = false;
      },
      // 确定
      ok() {
        this.$refs.addEditForm
          .validateFn()
          .then((form) => {
            const data = Object.assign({}, form);
            this.submit(data);
          })
          .catch((err) => {
            console.error(err);
          });
      },
      // 提交请求
      async submit(data) {
        try {
          if (this.isEdit) {
            await update(data);
          } else {
            await add(data);
          }
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.$emit('refresh');
          this.close();
        } catch (err) {
          console.error(err);
        }
      }
    },
    computed: {
      open: {
        set(bool) {
          !this.isView && this.$refs.addEditForm.clearFn();
          this.$emit('close', bool);
        },
        get() {
          return this.visited;
        }
      },
      isEdit() {
        return this.modelType === 'edit';
      },
      isView() {
        return this.modelType === 'view';
      }
    }
  };
</script>
