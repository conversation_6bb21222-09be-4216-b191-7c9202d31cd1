<template>
  <dialog-drawer
    :title="edit ? '自定义设置' : '自定义设置'"
    :visible="visible"
    @closed="closed"
    size="80%"
    ref="drawer"
    class="device-add"
  >
    <section v-loading="loading">
      <el-card>
        <div slot="header">
          <span class="el-base-title">我的常用功能</span>
          <span class="el-sub-title">点击菜单即可取消常用功能</span>
        </div>
        <div class="commonly-used-functions" v-if="myList.length">
          <el-button
            v-for="(menu, index) in myList"
            @click="removeMenu(menu)"
            type="primary"
            plain
            :key="index"
            size="mini"
            ><i :class="menu.icon" class="el-icon--left"></i>{{ menu.menuName
            }}<i class="el-icon-close el-icon--right"></i
          ></el-button>
        </div>
        <custom-empty :size="70" v-else></custom-empty>
      </el-card>
      <el-card style="margin-top: 10px">
        <div slot="header">
          <span class="el-base-title">所有常用功能</span>
          <span class="el-sub-title">点击菜单即可添加常用功能</span>
        </div>
        <div class="commonly-used-functions">
          <el-button
            v-for="(menu, index) in allList"
            @click="handleSaveMenu(menu)"
            :key="index"
            size="mini"
            ><i :class="menu.icon" class="el-icon--left"></i>{{ menu.menuName
            }}<i class="el-icon-plus el-icon--right"></i
          ></el-button>
        </div>
      </el-card>
    </section>
    <div class="oper_btn">
      <!-- <el-button
        class="el-icon-circle-plus-outline"
        size="small"
        type="primary"
        @click="submit"
        :loading="loading"
      >
        提交</el-button
      > -->
      <btn type="close" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import { allMenuList, saveMenu, removeMenu, myUsualmenu } from '@/api/home';
  import CustomEmpty from '@/components/custom-empty.vue';
  export default {
    name: 'AddMenu',
    props: {},
    components: { CustomEmpty },
    data() {
      return {
        visible: false,
        detail: {},
        allList: [],
        myList: [],
        loading: false,
        eqId: '',
        edit: false
      };
    },
    watch: {},
    methods: {
      async handleSaveMenu(menu) {
        if (this.myList.length >= 6)
          return this.$message.warning('最多只能添加6个常用功能');
        try {
          await saveMenu({ menuCode: menu.menuCode });
          this.getDetail();
        } catch (error) {
          console.log(error);
        }
      },
      async removeMenu(menu) {
        try {
          await removeMenu([menu.id]);
          this.getDetail();
        } catch (error) {
          console.log(error);
        }
      },
      async getDetail() {
        this.loading = true;
        try {
          const res = await allMenuList();
          const myres = await myUsualmenu();
          let arr = res.data.data || [];
          // let myArr = [];
          let allArr = [];
          for (const item of arr) {
            if (!item.existFlag) {
              allArr.push(item);
            }
          }
          this.myList = myres.data.data || [];
          this.allList = allArr;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      show() {
        this.visible = true;
        this.getDetail();
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        // this.form.image = [];
        // this.$refs['info'].resetForm();
        this.detail = {};
        this.$emit('refresh');
      },
      async submit() {
        // if (params) {
        //   await this.save({
        //     ...params,
        //     id: this.edit ? this.eqId : undefined
        //   });
        // }
      },
      // 提交润滑方式
      async save(params) {
        this.loading = true;
        try {
          await saveMenu(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  .el-base-title {
    display: inline-block;
    margin-right: 10px;
  }

  .el-sub-title {
    color: #999;
    font-size: 14px;
  }

  .commonly-used-functions {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 10px;

    .el-button + .el-button {
      margin-left: 0;
    }
  }
</style>
