import Layout from '@/page/index/';

export default [
  {
    path: '/login',
    name: 'login',
    component: () =>
      import(/* webpackChunkName: "page" */ '@/page/login/index'),
    meta: {
      title: '登录页',
      keepAlive: true,
      isHide: 1,
      isTab: false,
      isAuth: false
    }
  },
  {
    path: '/lock',
    name: 'lock',
    component: () => import(/* webpackChunkName: "page" */ '@/page/lock/index'),
    meta: {
      title: '锁屏页',
      keepAlive: true,
      isTab: false,
      isAuth: false
    }
  },
  {
    path: '/404',
    component: () =>
      import(/* webpackChunkName: "page" */ '@/components/error-page/404'),
    name: 'error-404',
    meta: {
      title: '404',
      keepAlive: true,
      isTab: false,
      isAuth: false
    }
  },
  {
    path: '/403',
    component: () =>
      import(/* webpackChunkName: "page" */ '@/components/error-page/403'),
    name: 'error-403',
    meta: {
      title: '403',
      keepAlive: true,
      isTab: false,
      isAuth: false
    }
  },
  {
    path: '/500',
    component: () =>
      import(/* webpackChunkName: "page" */ '@/components/error-page/500'),
    name: 'error-500',
    meta: {
      title: '500',
      keepAlive: true,
      isTab: false,
      isAuth: false
    }
  },
  {
    path: '/',
    name: '主页',
    redirect: '/wel'
  },
  {
    path: '/myiframe',
    component: Layout,
    redirect: '/myiframe',
    children: [
      {
        path: ':routerPath',
        name: 'iframe',
        component: () =>
          import(/* webpackChunkName: "page" */ '@/components/iframe/main'),
        props: true,
        meta: {
          title: 'iframe'
        }
      }
    ]
  }
  // {
  //   path: '*',
  //   redirect: '/404'
  // }
];
