export default {
  tip: '提示',
  title: '设备全生命周期',
  logoutTip: '退出系统, 是否继续?',
  submitText: '确定',
  cancelText: '取消',
  search: '请输入搜索内容',
  menuTip: '没有发现菜单',
  wel: {
    info: '早安，Smallwei，Avuex一款超乎你想象的框架！',
    dept: '我是avue团队下的一个部门-哈皮部门-哈皮职位',
    team: '团队内排名',
    project: '项目访问',
    count: '项目数',
    data: {
      subtitle: '实时',
      column1: '分类统计',
      column2: '附件统计',
      column3: '文章统计',
      key1: '分',
      key2: '附',
      key3: '评',
      text1: '当前分类总记录数',
      text2: '当前上传的附件数',
      text3: '评论次数'
    },
    data2: {
      column1: '今日注册',
      column2: '今日登录',
      column3: '今日订阅',
      column4: '今日评论'
    },
    data3: {
      column1: '转化率（日同比 28%）',
      column2: '签到率（日同比 11%）',
      column3: '签到率（日同比 11%）'
    },
    data4: {
      column1: '错误日志',
      column2: '数据展示',
      column3: '权限管理',
      column4: '用户管理'
    },
    table: {
      rw: '工作任务',
      nr: '工作内容',
      sj: '工作时间'
    }
  },
  route: {
    info: '个人信息',
    website: 'szyk官网',
    avuexwebsite: 'avuex官网',
    dashboard: '首页',
    more: '更多',
    tags: '标签',
    store: '本地存储',
    api: '全局函数',
    logs: '日志监控',
    table: '表格',
    form: '表单',
    top: '返回顶部',
    data: '数据展示',
    permission: '权限',
    error: '异常页面',
    test: '测试页面'
  },
  login: {
    title: '登录 ',
    info: '设备全生命周期',
    tenantId: '请输入租户ID',
    tenantName: '请输入企业名称',
    username: '请输入账号',
    password: '请输入密码',
    wechat: '微信',
    qq: 'QQ',
    github: 'github',
    gitee: '码云',
    phone: '请输入手机号',
    code: '请输入验证码',
    submit: '登录',
    userLogin: '账号密码登录',
    phoneLogin: '手机号登录',
    thirdLogin: '第三方系统登录',
    msgText: '发送验证码',
    msgSuccess: '秒后重发'
  },
  navbar: {
    logOut: '退出登录',
    userinfo: '个人信息',
    switchDept: '部门切换',
    dashboard: '首页',
    lock: '锁屏',
    bug: '没有错误日志',
    bugs: '条错误日志',
    screenfullF: '退出全屏',
    screenfull: '全屏',
    language: '中英文',
    notice: '消息通知',
    theme: '主题',
    color: '换色'
  },
  tagsView: {
    search: '搜索',
    menu: '更多',
    clearCache: '清除缓存',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  }
};
