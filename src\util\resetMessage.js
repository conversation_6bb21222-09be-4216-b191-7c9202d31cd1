// 重写Message 目的是方式重复弹出message 弹窗
import { Message } from 'element-ui';
let messageInstance = null;
const resetMessage = (options) => {
  let op = {
    ...options,
    duration: 3000,
    showClose: true
  };
  if (messageInstance) {
    messageInstance.close();
  }
  messageInstance = Message(op);
};

['error', 'success', 'info', 'warning'].forEach((type) => {
  resetMessage[type] = (options) => {
    console.log('resetMessage[type] = (options):', options);
    if (typeof options === 'string') {
      options = {
        message: options,
        duration: 3000,
        showClose: true
      };
    } else {
      options = {
        message: options.message,
        duration: 3000,
        showClose: true
      };
    }
    options.type = type;
    return resetMessage(options);
  };
});

export const message = resetMessage;
