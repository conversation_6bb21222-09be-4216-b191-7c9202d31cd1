// 检修计划的状态 ，返回各种状态的颜色
export function overhaulPlanStatusColor(val) {
  let status = Number(val);
  switch (status) {
    case 2:
      return '#F2F2F6'; // 未开始
    case 3:
      return '#155CFF'; //执行中
    case 5:
      return '#00BB7D'; // 已完成
    case 4:
      return '#EC9A29'; //已终止
  }
}

// 检修工单 状态颜色 返回
export function overhaulOrderStatusColor(val) {
  let status = Number(val);
  switch (status) {
    case 1:
      return '#155CFF'; //执行中"
    case 2:
      return '#35C24B'; // 已完成
    case 3:
      return '#E23F3F'; //已超期
    case 4:
      return '#EE7C11'; //超期完成"
    case 5:
      return '#EC9A29'; //待验证""
    case 6:
      return '#E23F3F'; //已驳回"
    case 7:
      return '#808080'; //已关闭
  }
}
