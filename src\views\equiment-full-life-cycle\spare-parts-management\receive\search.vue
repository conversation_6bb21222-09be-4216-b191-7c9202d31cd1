<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      label-position="left"
      class="search-form"
    >
      <el-form-item label="领用单号" prop="no">
        <el-input v-model="form.no" placeholder="请输入领用单号" clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="领用单名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入领用单名称" clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="领用单状态" prop="status">
        <el-select
          v-model="form.status"
          placeholder="请选择领用单状态"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['receive_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="time" class="_label">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    components: {},
    serviceDicts: ['receive_status'],
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        form: {
          no: undefined,
          time: undefined,
          status: undefined,
          name: undefined
        }
      };
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          startCreateDate: this.form.time ? this.form.time[0] : undefined,
          endCreateDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
