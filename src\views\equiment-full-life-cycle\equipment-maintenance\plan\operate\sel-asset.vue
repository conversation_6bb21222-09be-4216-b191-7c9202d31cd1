<template>
  <div>
    <el-button
      size="small"
      type="primary"
      style="margin-bottom: 10px"
      @click="selStandard"
      >+ 选择标准</el-button
    >

    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      border
      :header-cell-style="{ background: '#fafafa' }"
      size="small"
    >
      <el-table-column type="index" label="序号"></el-table-column>
      <el-table-column
        prop="equipmentCode"
        label="设备编号"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        property="monitorName"
        label="保养部位"
      ></el-table-column>
      <el-table-column property="standard" label="保养标准"></el-table-column>

      <el-table-column property="method" label="保养方法"> </el-table-column>

      <el-table-column prop="model" label="操作" width="50">
        <template slot-scope="scope">
          <el-button
            style="color: #ff4745"
            slot="reference"
            type="text"
            size="small"
            @click="del(scope)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <sel-standard
      ref="selStandard"
      @getStandardList="getStandardList"
    ></sel-standard>
  </div>
</template>

<script>
  import SelStandard from '@/views/equiment-full-life-cycle/equipment-maintenance/plan/operate/sel-standard-list.vue';
  export default {
    name: 'DeviceBasicList',
    components: { SelStandard },
    props: {
      isDele: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      detail: {
        type: Object,
        default: () => {}
      },
      dept: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.list = this.detail.maintainStandardList;
            });
          }
        }
      },
      'dept.id': {
        immediate: true,
        deep: true,
        handler() {
          //  如果没有部门，清空list 数据,部门改变，清空list数据
          this.list = [];
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },
    methods: {
      //  点击选择标准
      selStandard(row) {
        let list = this.list.map((it) => {
          return {
            ...it,
            num: 1
          };
        });
        this.$refs['selStandard'].show(list);
      },
      del(scope) {
        this.list.splice(scope.$index, 1);
      },
      validForm() {
        if (this.list.length === 0) {
          this.$message.warning('请选择设备');
          return;
        }
        // if (this.list.some((item) => item.maintainStandardList.length === 0)) {
        //   this.$message.warning('请为设备选择标准');
        //   return;
        // }
        // const standardIds = [];
        let standardIds = this.list.map((item) => {
          return item.id;
        });
        return { standardIds: standardIds.join(',') };
      },
      resetForm() {
        this.list = [];
      },
      //  获取设备
      getStandardList(val) {
        console.log('选择的设备', val);
        this.list = val.map((item) => {
          return {
            ...item,
            maintainStandardList: item.maintainStandardList || []
          };
        });
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }

  .operateBtn {
    margin-bottom: 15px;
  }

  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
