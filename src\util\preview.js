import { getFileFullUrl } from './file';
import router from '@/router/router';

//表单序列化
export const serialize = (data) => {
  let list = [];
  Object.keys(data).forEach((ele) => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join('&');
};
// 获取文档类型
export function handleDocType(fileType) {
  let docType = '';
  let fileTypesDoc = [
    'doc',
    'docm',
    'docx',
    'dot',
    'dotm',
    'dotx',
    'epub',
    'fodt',
    'htm',
    'html',
    'mht',
    'odt',
    'ott',
    'pdf',
    'rtf',
    'txt',
    'djvu',
    'xps'
  ];
  let fileTypesCsv = [
    'csv',
    'fods',
    'ods',
    'ots',
    'xls',
    'xlsm',
    'xlsx',
    'xlt',
    'xltm',
    'xltx'
  ];
  let fileTypesPPt = [
    'fodp',
    'odp',
    'otp',
    'pot',
    'potm',
    'potx',
    'pps',
    'ppsm',
    'ppsx',
    'ppt',
    'pptm',
    'pptx'
  ];
  if (fileTypesDoc.includes(fileType)) {
    docType = 'word';
  }
  if (fileTypesCsv.includes(fileType)) {
    docType = 'cell';
  }
  if (fileTypesPPt.includes(fileType)) {
    docType = 'slide';
  }
  return docType;
}
// 按图片id预览
export const previewImage = (file) => {
  console.log('preview', file);
  const { id } = file || {};
  const link = `${location.origin}${getFileFullUrl(id)}`;
  return link;
};

// 按文件id预览
export const previewFile = (file) => {
  console.log('preview', file);
  const { id, originalName: name, extension } = file || {};
  const link = `${location.origin}${getFileFullUrl(id)}`;

  let routeUrl = router.resolve({
    name: 'file-preview',
    query: {
      url: link,
      type: extension,
      name,
      id
    }
  });
  window.open(routeUrl.href, '_blank');
};

// 按文件link预览
export const previewFileByLink = (file) => {
  const { link, originalName: name, extension } = file || {};
  let routeUrl = router.resolve({
    name: 'file-preview',
    query: {
      url: link,
      type: extension,
      name
    }
  });
  window.open(routeUrl.href, '_blank');
};
