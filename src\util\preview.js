import { getFileFullUrl } from './file';
import { filePreviewService } from '@/plugins/file-preview';

//表单序列化
export const serialize = (data) => {
  let list = [];
  Object.keys(data).forEach((ele) => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join('&');
};
// 获取文档类型
export function handleDocType(fileType) {
  let docType = '';
  let fileTypesDoc = [
    'doc',
    'docm',
    'docx',
    'dot',
    'dotm',
    'dotx',
    'epub',
    'fodt',
    'htm',
    'html',
    'mht',
    'odt',
    'ott',
    'pdf',
    'rtf',
    'txt',
    'djvu',
    'xps'
  ];
  let fileTypesCsv = [
    'csv',
    'fods',
    'ods',
    'ots',
    'xls',
    'xlsm',
    'xlsx',
    'xlt',
    'xltm',
    'xltx'
  ];
  let fileTypesPPt = [
    'fodp',
    'odp',
    'otp',
    'pot',
    'potm',
    'potx',
    'pps',
    'ppsm',
    'ppsx',
    'ppt',
    'pptm',
    'pptx'
  ];
  if (fileTypesDoc.includes(fileType)) {
    docType = 'word';
  }
  if (fileTypesCsv.includes(fileType)) {
    docType = 'cell';
  }
  if (fileTypesPPt.includes(fileType)) {
    docType = 'slide';
  }
  return docType;
}
// 按图片id预览
export const previewImage = (file) => {
  console.log('preview', file);
  const { id } = file || {};
  const link = `${location.origin}${getFileFullUrl(id)}`;
  return link;
};

// 按文件id预览
export const previewFile = (file) => {
  console.log('preview', file);
  const { id, originalName: name, extension } = file || {};
  const link = `${location.origin}${getFileFullUrl(id)}`;

  // Use modal preview instead of opening new tab
  filePreviewService.openPreview({
    fileUrl: link,
    fileType: extension,
    fileName: name,
    fileId: id,
    title: name
  });
};

// 按文件link预览
export const previewFileByLink = (file) => {
  const { link, originalName: name, extension } = file || {};

  // Use modal preview instead of opening new tab
  filePreviewService.openPreview({
    fileUrl: link,
    fileType: extension,
    fileName: name,
    fileId: null,
    title: name
  });
};
