import Vue from 'vue';
import { getFileFullUrl } from './file';
import PreviewDialog from '@/page/file-preview/dialog.vue';
import store from '@/store';

//表单序列化
export const serialize = (data) => {
  let list = [];
  Object.keys(data).forEach((ele) => {
    list.push(`${ele}=${data[ele]}`);
  });
  return list.join('&');
};
// 获取文档类型
export function handleDocType(fileType) {
  let docType = '';
  let fileTypesDoc = [
    'doc',
    'docm',
    'docx',
    'dot',
    'dotm',
    'dotx',
    'epub',
    'fodt',
    'htm',
    'html',
    'mht',
    'odt',
    'ott',
    'pdf',
    'rtf',
    'txt',
    'djvu',
    'xps'
  ];
  let fileTypesCsv = [
    'csv',
    'fods',
    'ods',
    'ots',
    'xls',
    'xlsm',
    'xlsx',
    'xlt',
    'xltm',
    'xltx'
  ];
  let fileTypesPPt = [
    'fodp',
    'odp',
    'otp',
    'pot',
    'potm',
    'potx',
    'pps',
    'ppsm',
    'ppsx',
    'ppt',
    'pptm',
    'pptx'
  ];
  if (fileTypesDoc.includes(fileType)) {
    docType = 'word';
  }
  if (fileTypesCsv.includes(fileType)) {
    docType = 'cell';
  }
  if (fileTypesPPt.includes(fileType)) {
    docType = 'slide';
  }
  return docType;
}
// 按图片id预览
export const previewImage = (file) => {
  console.log('preview', file);
  const { id } = file || {};
  const link = `${location.origin}${getFileFullUrl(id)}`;
  return link;
};

// 按文件id预览
export function previewFile({ id, extension, originalName: name }) {
  if (!id) return;

  const DialogCtor = Vue.extend(PreviewDialog);
  const instance = new DialogCtor({ store });
  instance.$mount();
  document.body.appendChild(instance.$el);
  const data = {
    url: `${location.origin}${getFileFullUrl(id)}`,
    type: extension,
    name,
    id
  };
  instance.show(data);

  instance.$on('close', () => {
    document.body.removeChild(instance.$el);
    instance.$destroy();
  });
}

// 按文件link预览
export const previewFileByLink = ({ link, extension, originalName: name }) => {
  if (!link) return;
  const DialogCtor = Vue.extend(PreviewDialog);
  const instance = new DialogCtor({ store });
  instance.$mount();
  document.body.appendChild(instance.$el);
  const data = {
    url: link,
    type: extension,
    name
  };
  instance.show(data);

  instance.$on('close', () => {
    document.body.removeChild(instance.$el);
    instance.$destroy();
  });
};
