<template>
  <dialog-popup
    title="选择标准"
    ref="dialogs"
    :visible="visible"
    @closed="closed"
    width="80%"
    class="selectSensor"
  >
    <section style="margin-bottom: 10px">
      <el-form inline size="small" class="search-form">
        <el-form-item label="标准名称">
          <el-input
            style="width: 360px"
            v-model.trim="standard"
            placeholder="请输入标准名称"
            clearable
            size="small"
            :maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="设备编号">
          <el-input
            style="width: 360px"
            v-model.trim="equipmentCode"
            placeholder="请输入设备编号"
            clearable
            size="small"
            :maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input
            style="width: 360px"
            v-model.trim="equipmentName"
            placeholder="请输入设备名称"
            clearable
            size="small"
            :maxlength="50"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <btn type="search" @click="onSubmit" />
          <btn type="reset" @click="reset" />
        </el-form-item>
      </el-form>
    </section>
    <el-row>
      <el-col :span="24" style="height: 460px; overflow-y: scroll">
        <el-table
          stripe
          ref="multipleTable"
          v-loading="loading"
          :data="dataSource"
          border
          size="small"
          :key="key"
          row-key="id"
          :headerCellStyle="{ background: '#fafafa' }"
          :reserve-selection="true"
          @select="handleCheckBox"
          @select-all="handleSelectAll"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column
            property="equipmentCode"
            label="设备编号"
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            property="equipmentName"
            label="设备名称"
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            property="monitorName"
            label="保养部位"
          ></el-table-column>
          <el-table-column
            property="standard"
            label="保养标准"
          ></el-table-column>

          <el-table-column property="method" label="保养方法">
          </el-table-column>
        </el-table>
        <pagination
          :page-size.sync="searchParams.size"
          :page-no.sync="searchParams.current"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <div
      style="display: flex; align-items: flex-end; justify-content: flex-end"
    >
      <btn type="confirm" @click="confirm" :loading="loading"></btn>
      <btn type="close" @click="closed"></btn>
    </div>
  </dialog-popup>
</template>
<script>
  import { getStandardList } from '@/api/equiment-full-life-api/maintenance';

  export default {
    components: {},
    props: {
      //  有的模块，默认是要传递status的
      status: {
        type: String,
        default: undefined
      }
    },
    data() {
      return {
        standard: undefined,
        equipmentName: undefined,
        equipmentCode: undefined,
        visible: false,
        loading: false,
        dataSource: [],
        total: 0,
        searchParams: {
          size: 10,
          current: 1
        },
        equipmentId: undefined,
        key: 0,
        // 所有页上多选的数据之和
        allPageSelect: [],
        originLength: 0 // 已存在的长度
      };
    },
    watch: {},
    methods: {
      //  点击搜索
      onSubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      reset() {
        this.standard = undefined;
        this.equipmentName = undefined;
        this.equipmentCode = undefined;
        this.searchParams.current = 1;
        this.getList();
      },
      getKey() {
        return Date.now() + '';
      },
      handleCheckBox(rows, row) {
        if (rows.includes(row)) {
          // 新勾选
          this.$set(row, 'num', 1);
          this.$set(row, 'disabled', false);
          this.allPageSelect.push(row);
        } else {
          // 取消勾选
          this.$set(row, 'disabled', true);
          this.$set(row, 'num', undefined);
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item.id !== row.id
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        if (rows.length) {
          rows.forEach((row) => {
            this.$set(row, 'num', 1);
            this.$set(row, 'disabled', false);
            if (!this.allPageSelect.find((item) => item.id === row.id)) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.dataSource.forEach((row) => {
            this.$set(row, 'num', undefined);
            this.$set(row, 'disabled', true);
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item.id !== row.id
            );
          });
        }
      },

      handleClose(row) {
        this.$set(row, 'num', undefined);
        this.$set(row, 'disabled', true);
        this.$refs.multipleTable.toggleRowSelection(row);
        let index = this.allPageSelect.findIndex((it) => it.id === row.id);
        index !== -1 && this.allPageSelect.splice(index, 1);
      },
      search() {
        this.searchParams.current = 1;
        this.getList();
      },
      show(originList) {
        this.resetFrom();
        this.allPageSelect = originList;
        this.visible = true;
        this.searchParams.current = 1;
        this.getList(); // 部位列表
        this.key++;
      },

      resetFrom() {
        this.searchParams = {
          size: 10,
          current: 1
        };
        this.allPageSelect = [];
      },
      closed() {
        this.standard = undefined;
        this.equipmentCode = undefined;
        this.equipmentName = undefined;
        this.visible = false;
        this.allPageSelect = [];
      },
      // 点击clearable时触发
      clear() {
        this.searchParams.current = 1;
        this.getList();
      },
      //
      async getList() {
        this.loading = true;
        try {
          let params;
          params = {
            ...this.searchParams,
            standard: this.standard,
            equipmentId: this.equipmentId,
            equipmentName: this.equipmentName,
            equipmentCode: this.equipmentCode
          };

          let res = await getStandardList(params);
          let data = res.data.data.records || [];
          this.dataSource = data.map((item) => {
            const mathingObj = this.allPageSelect.find((m) => item.id === m.id);
            if (mathingObj) {
              return { ...mathingObj, ...item };
            } else {
              return item;
            }
          });
          this.$nextTick(() => {
            this.dataSource.map((item) => {
              if (item.num) {
                this.$refs.multipleTable.toggleRowSelection(item, true);
              }
            });
            this.total = res.data.data.total;
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      confirm() {
        if (this.allPageSelect.length === 0) {
          this.$message.warning('请选择设备！');
          return;
        }
        this.$emit('getStandardList', this.allPageSelect);
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .el-pagination__sizes {
    display: none !important;
  }

  .search {
    margin-bottom: 10px;
  }

  /deep/ .pagination-container {
    text-align: left !important;
  }

  .clear {
    float: right;
  }

  /deep/ .el-pagination__jump {
    display: none !important;
  }

  .right {
    margin-left: 20px;
  }

  /deep/ {
    .el-form-item--small {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      padding: 10px 0;
    }
  }

  .table {
    :deep(.el-table__cell) {
      padding: 8px 0 !important;
    }
  }

  .selectSensor {
    :deep(.el-dialog__body) {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  ::v-deep {
    .el-input-number--mini {
      width: 100px !important;
    }
  }
</style>
