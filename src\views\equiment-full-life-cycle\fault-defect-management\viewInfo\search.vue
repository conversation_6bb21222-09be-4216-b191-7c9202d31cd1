<template>
  <el-form
    class="_form search-form"
    ref="form"
    :model="form"
    size="small"
    :inline="true"
    label-suffix="："
  >
    <el-form-item label="设备编号" prop="equipmentCode">
      <el-input
        v-model.trim="form.equipmentCode"
        size="small"
        placeholder="请输入设备编号"
        style="margin-right: 10px"
        :maxlength="20"
        clearable
      >
      </el-input>
    </el-form-item>
    <el-form-item label="状态" prop="status">
      <el-select v-model="form.status" placeholder="请选择状态" clearable>
        <el-option
          v-for="dict in faultStatus"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="缺陷等级" prop="level">
      <el-select v-model="form.level" placeholder="请选择缺陷等级" clearable>
        <el-option
          v-for="dict in serviceDicts.type['defect_level']"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="缺陷来源" prop="source">
      <el-select v-model="form.source" placeholder="请选择缺陷来源" clearable>
        <el-option
          v-for="dict in serviceDicts.type['fault_source']"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <btn type="search" @click="onSubmit" />
      <btn type="reset" @click="reset" />
    </el-form-item>
  </el-form>
</template>

<script>
  import 'element-ui/lib/theme-chalk/display.css';
  import { faultStatus } from '../util';

  export default {
    name: 'alarmSearch',
    components: {},
    serviceDicts: ['defect_level', 'fault_source'],
    data() {
      return {
        faultStatus,
        form: {
          level: undefined,
          source: undefined,
          status: undefined,
          equipmentCode: undefined
        }
      };
    },
    created() {},
    methods: {
      onSubmit() {
        let searchParams = {
          ...this.form
        };
        this.$emit('query', searchParams);
      },
      reset() {
        this.$refs['form'].resetFields();
        this.$emit('query', this.form);
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep {
    .el-form {
      margin-bottom: 15px;
    }
  }
</style>
