<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      label-position="left"
    >
      <el-form-item label="油品类型名称" prop="name">
        <el-input
          v-model.trim="form.name"
          placeholder="请输入油品类型名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    data() {
      return {
        form: {
          name: undefined
        }
      };
    },
    methods: {
      reset() {
        this.form.executeDept = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = {
          ...this.form
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
