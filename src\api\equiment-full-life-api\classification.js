import request from '@/router/axios';

/**
 * 台账表接口
 * @param url 接口地址
 */
// 分页列表
export const accountListPage = (params) => {
  return request({
    url: '/api/szyk-common/equipment-account/page',
    method: 'get',
    params
  });
};
// 新增或修改
export const addOrEditAccount = (data) => {
  return request({
    url: '/api/szyk-simas/equipment-account/submit',
    method: 'post',
    data
  });
};

//删除
export const removeAccount = (params) => {
  return request({
    url: '/api/szyk-simas/equipment-account/remove',
    method: 'post',
    params
  });
};

// 详情
export const getAccountDetail = (params) => {
  return request({
    url: '/api/szyk-simas/equipment-account/detail',
    method: 'get',
    params
  });
};

// 工单详情
export const getEquipmentOrderPage = (params) => {
  return request({
    url: '/api/szyk-simas/equipment-account/equipmentOrderPage',
    method: 'get',
    params
  });
};

// 绑定卡片的接口
export const bindCard = (data) => {
  return request({
    url: '/api/szyk-simas/equipment-account/bindNfc',
    method: 'post',
    data
  });
};
// ------------------------------------
// 拓展属性-列表
export const ledgerExpandList = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_category/attr/attrList',
    method: 'get',
    params
  });
};
// 拓展属性-保存编辑
export const ledgerExpandSave = (data) => {
  return request({
    url: '/api/szyk-simas/equipment_category/attr/save',
    method: 'post',
    data
  });
};
// 拓展属性-删除
export const ledgerExpandDel = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_category/attr/remove',
    method: 'post',
    params
  });
};
// 拓展属性-左树搜索
export const searchCategory = (params) => {
  return request({
    url: '/api/szyk-simas/equipment_category/searchCategory',
    method: 'get',
    params
  });
};
