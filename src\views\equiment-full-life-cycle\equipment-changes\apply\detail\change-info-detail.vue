<template>
  <div class="change-info-detail">
    <!-- 变更设备 -->
    <span class="el-base-title">变更设备</span>
    <el-descriptions
      border
      size="small"
      :column="3"
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="设备名称：">{{
        detail.equipmentName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="规格型号：">{{
        detail.model || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备类型：">{{
        detail.equipmentCategoryName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="使用部门：">{{
        detail.useDeptName || '-'
      }}</el-descriptions-item>
    </el-descriptions>
    <!-- 变更明细（状态） -->
    <span class="el-base-title">变更明细（状态）</span>
    <el-descriptions
      border
      size="small"
      :column="3"
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="变更单号：">{{
        detail.changeNumber || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="变更名称：">{{
        detail.changeName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="变更原因：">{{
        detail.changeReason || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="变更目的：">{{
        detail.changePurpose || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="变更类型：">{{
        detail.changeCategoryName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="预计实施时间：">{{
        detail.expectedImplementationDate || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="变更内容：">{{
        detail.changeContent || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="实施方案：">{{
        detail.implementationPlan || '-'
      }}</el-descriptions-item>
      <el-descriptions-item :span="4" label="预期效果：">{{
        detail.expectedEffect || '-'
      }}</el-descriptions-item>
      <el-descriptions-item :span="4" label="备注：">{{
        detail.remarks || '-'
      }}</el-descriptions-item>
    </el-descriptions>
    <p
      class="el-base-title"
      v-if="
        safetyChecklist.length ||
        preHazardAnalysis.length ||
        failureTypeAndImpactAnalysis.length ||
        hazardOperabilityAnalysis.length ||
        other.length
      "
    >
      风险评估资料
    </p>
    <el-form label-suffix="：" label-width="115px">
      <el-row class="form-content-height">
        <el-col :span="8" v-if="safetyChecklist.length">
          <el-form-item label="安全检查表">
            <upload-file
              v-model="safetyChecklist"
              placeholder=""
              :limit="9"
              :isShowBtn="false"
              :isDetailInfo="false"
              formatLimit="pdf,doc,docx,jpeg,png,jpg"
            ></upload-file>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="preHazardAnalysis.length">
          <el-form-item label="预先危险性分析">
            <upload-file
              v-model="preHazardAnalysis"
              placeholder=""
              :limit="9"
              :isShowBtn="false"
              :isDetailInfo="false"
              formatLimit="pdf,doc,docx,jpeg,png,jpg"
            ></upload-file>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="failureTypeAndImpactAnalysis.length">
          <el-form-item label-width="145px" label="故障类型和影响分析">
            <upload-file
              v-model="failureTypeAndImpactAnalysis"
              placeholder=""
              :limit="9"
              :isShowBtn="false"
              :isDetailInfo="false"
              formatLimit="pdf,doc,docx,jpeg,png,jpg"
            ></upload-file>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="form-content-height">
        <el-col :span="8" v-if="hazardOperabilityAnalysis.length">
          <el-form-item label-width="145px" label="危险与可操作性分析">
            <upload-file
              v-model="hazardOperabilityAnalysis"
              placeholder=""
              :limit="9"
              :isShowBtn="false"
              :isDetailInfo="false"
              formatLimit="pdf,doc,docx,jpeg,png,jpg"
            ></upload-file>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="other.length">
          <el-form-item label="其它">
            <upload-file
              v-model="other"
              placeholder=""
              :limit="9"
              :isShowBtn="false"
              :isDetailInfo="false"
              formatLimit="pdf,doc,docx,jpeg,png,jpg"
            ></upload-file>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p class="el-base-title">需要更新的文件</p>
    <UpdateFile
      :isEdit="false"
      :type="type"
      :detail="detail"
      :list="changeFileList"
    ></UpdateFile>
    <!-- <div v-if="detail.rejectionReason && type !== 'verify'">
      <p class="el-base-title">审核日志</p>
      <div>
        <el-descriptions
          border
          size="small"
          :column="4"
          :labelStyle="{ width: '110px' }"
          :contentStyle="{ wordBreak: 'break-all', wordWrap: 'break-word' }"
          contentClassName="contentClassName"
        >
          <el-descriptions-item :span="4" label="驳回原因">
            {{ detail.rejectionReason || '-' }}</el-descriptions-item
          >
        </el-descriptions>
      </div>
    </div> -->
    <div v-if="detail.renovationEvaluation && type !== 'verify'">
      <span class="el-base-title">变更结果评价及图片</span>
      <el-form
        :model="detail"
        label-suffix="："
        ref="baseForm"
        label-width="90px"
        style="margin-top: 20px"
        :label-position="'right'"
        size="small"
      >
        <el-row class="add-info">
          <el-col :span="12">
            <el-form-item prop="renovationEvaluation" label="改造评价">
              {{ detail.renovationEvaluation || '-' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="imageList" label="上传图片">
              <el-image
                v-for="img in detail.imageList"
                :key="img.id"
                style="width: 145px; height: 145px"
                :src="convertFileUrl(img.domain)"
                :preview-src-list="[convertFileUrl(img.domain)]"
              ></el-image>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
  import UploadFile from '@/components/view-upload-file.vue';
  import UpdateFile from '../components/update-file';
  // import { getFileFullUrl } from '@/util/file';
  import { convertFileUrl } from '@/util/util';
  export default {
    name: 'ChangeInfoDetail',
    components: { UploadFile, UpdateFile },
    props: {
      type: {
        type: String,
        default: ''
      },
      detail: {
        type: Object,
        default: () => {}
      }
    },
    computed: {
      // 文件更新状态(0 待更新 1 已更新 2 未选择)
      changeFileList() {
        let changeFileList = this.detail.changeFileList || [];
        let fileList = changeFileList.filter(
          (item) => item.updateStatus !== '2'
        );
        return fileList;
      },
      safetyChecklist() {
        return (
          (this.detail.riskFileList || []).filter(
            (item) => item.riskType === '1'
          ) || []
        );
      },
      preHazardAnalysis() {
        return (
          (this.detail.riskFileList || []).filter(
            (item) => item.riskType === '2'
          ) || []
        );
      },
      failureTypeAndImpactAnalysis() {
        return (
          (this.detail.riskFileList || []).filter(
            (item) => item.riskType === '3'
          ) || []
        );
      },
      hazardOperabilityAnalysis() {
        return (
          (this.detail.riskFileList || []).filter(
            (item) => item.riskType === '4'
          ) || []
        );
      },
      other() {
        return (
          (this.detail.riskFileList || []).filter(
            (item) => item.riskType === '5'
          ) || []
        );
      }
    },
    data() {
      return {
        convertFileUrl
      };
    },
    methods: {
      // getFileFullUrl
    }
  };
</script>
<style lang="scss" scoped></style>
