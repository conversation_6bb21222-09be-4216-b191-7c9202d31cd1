<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="计划名称：">{{
        details.code || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="计划开始时间：">{{
        details.name || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="计划截止时间：">{{
        details.model || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="负责部门：">{{
        details.categoryName || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="负责人：">{{
        details.importantLevelName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="工作时间段设置：">{{
        details.processCategoryName || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
