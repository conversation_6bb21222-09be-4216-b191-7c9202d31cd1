<template>
  <div class="sso-container">
    <onlyoffice-editor
      :src="src"
      :config="editorConfig"
      @ready="onEditorReady"
    />
  </div>
</template>
<script>
  import { mapGetters } from 'vuex';
  import { OnlyofficeEditor } from 'onlyoffice-vue';
  import { handleDocType } from '@/util/preview';
  import { httpPreviewUrl, httpsPreviewUrl } from '@/config/env';

  export default {
    components: {
      OnlyofficeEditor
    },
    name: 'file-preview',
    data() {
      return {
        uid: Math.random().toString(36).slice(-8),
        detail: {}
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      src() {
        let isHttps = window.location.protocol.includes('https');
        return `${
          isHttps ? httpsPreviewUrl : httpPreviewUrl
        }web-apps/apps/api/documents/api.js`;
      },
      editorConfig() {
        let type = this.$route.query.type;
        let documentType = handleDocType(type) || 'word';
        let url = this.$route.query.url;
        let title = this.$route.query.name || '';
        let key = this.$route.query.id || this.uid;
        let userName = this.userInfo['nick_name'];
        return {
          // 编辑器宽度
          width: '100%',
          // 编辑器高度
          height: '100%',
          // 编辑器类型，支持 word（文档）、cell（表格）、slide（PPT）
          documentType,
          // 文档配置
          document: {
            // 文件类型
            fileType: type,
            // 文档标识符
            key,
            // 文档地址，绝对路径
            url,
            // 文档标题
            title,
            permissions: {
              download: false,
              print: false,
              edit: false
            }
          },
          editorConfig: {
            mode: 'view',
            user: {
              name: userName
            }
          }
        };
      }
    },
    created() {
      let isHttps = window.location.protocol.includes('https');
      if (isHttps) {
        // <meta charset="utf-8" http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
        // 插入 meta 标签
        var oMeta = document.createElement('meta');
        oMeta.setAttribute('charset', 'utf-8');
        oMeta.setAttribute('http-equiv', 'Content-Security-Policy');
        oMeta.setAttribute('content', 'upgrade-insecure-requests');
        document.getElementsByTagName('head')[0].appendChild(oMeta);
      }
    },
    methods: {
      // 获取文档配置信息
      queryDocumentInfo() {
        this.loading.page = true;
        // queryDocumentInfo({ key: 'test5.docx' })
        //   .then((res) => {
        //     const data = res.data || {};
        //     const { id, remarks } = data;
        //     this.detail = { id, remarks };
        //     this.editorConfig = data.editorConfig;
        //   })
        //   .finally(() => {
        //     this.loading.page = false;
        //   });
      },
      // 编辑器加载完毕后回调 ready 函数，editor 为当前编辑器实例
      onEditorReady(editor) {
        console.log(editor);
      }
    }
  };
</script>

<style lang="scss">
  .sso-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #303133;
  }
</style>
