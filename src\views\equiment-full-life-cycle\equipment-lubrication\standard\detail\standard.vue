<template>
  <el-table size="small" :data="list" border>
    <el-table-column label="#" type="index" width="50"></el-table-column>
    <el-table-column
      prop="equipmentMonitorName"
      label="润滑部位"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="oilTypeName"
      label="油品类型"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="methodsName"
      label="润滑方式"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="lubricateCycle"
      label="润滑周期（天）"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="floatTime"
      label="执行浮动时间（天）"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
