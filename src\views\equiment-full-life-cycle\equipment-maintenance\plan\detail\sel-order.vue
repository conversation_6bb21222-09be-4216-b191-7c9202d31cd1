<template>
  <div>
    <el-table
      class="table"
      :data="list"
      border
      :header-cell-style="{ background: '#fafafa' }"
      size="small"
      stripe
      height="500px"
    >
      <el-table-column type="index" label="序号"></el-table-column>
      <el-table-column prop="no" label="工单号" show-overflow-tooltip>
        <template v-slot="{ row }"
          ><span @click="rowClick(row)" style="cursor: pointer; color: #1586ef">
            {{ row.no || '-' }}</span
          >
        </template>
      </el-table-column>

      <el-table-column
        prop="equipmentName"
        label="设备名称"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.equipmentName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="executeDeptName"
        label="使用部门"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeDeptName || '-' }}</template>
      </el-table-column>
      <!--       详情的时候出现-->
      <el-table-column prop="startTime" label="开始时间" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.startTime || '-' }}</template>
      </el-table-column>
      <el-table-column prop="endTime" label="结束时间" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.endTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="责任人"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="maintainResult"
        label="检查结果"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.maintainResult || '-' }}</template>
      </el-table-column>
      <el-table-column prop="statusName" label="状态" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.statusName || '-' }}</template>
      </el-table-column>
    </el-table>
    <!--     点检工单详情-->
    <detail-index ref="detailIndex"></detail-index>
  </div>
</template>

<script>
  import DetailIndex from '../../work-order/detail/index.vue'; // 查看详情页面

  export default {
    name: 'DeviceBasicList',
    components: { DetailIndex },
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {};
    },

    mounted() {},
    methods: {
      rowClick(row) {
        this.$refs['detailIndex'].show(row.no);
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }

  .operateBtn {
    margin-bottom: 15px;
  }

  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
