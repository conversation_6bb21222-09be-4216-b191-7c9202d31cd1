<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
      size="small"
    >
      <el-descriptions-item label="入库单号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="入库类型：">{{
        details.inboundTypeName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="供应商：">{{
        details.supplierName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="入库库房：">{{
        details.warehouseName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="入库日期：">{{
        details.inboundDate || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="入库人：">{{
        details.inboundUserName || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="备注：">{{
        details.remark || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }
</style>
