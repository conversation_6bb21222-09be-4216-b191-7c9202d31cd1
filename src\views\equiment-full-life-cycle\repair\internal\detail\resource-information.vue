<template>
  <div>
    <el-descriptions
      border
      size="small"
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="报修人：">{{
        detail.reportUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="报修人电话：">{{
        detail.tel || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="报修时间：">{{
        detail.reportTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="来源：">{{
        detail.sourceName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="来源单号：">
        <template v-if="isNotJump">{{ detail.sourceNo || '-' }}</template>
        <template v-else-if="detail.source === '2'">{{
          detail.sourceNo || '-'
        }}</template>
        <el-button
          v-else
          @click="view(detail.sourceNo)"
          size="mini"
          type="text"
        >
          {{ detail.sourceNo || '-' }}</el-button
        >
      </el-descriptions-item>
    </el-descriptions>
    <OverhaulDetail ref="overhaul"></OverhaulDetail>
    <FaultDefectDetail v-if="!isNotJump" ref="fault"></FaultDefectDetail>
    <!-- <component ref="drawer" :is="drawerComp"></component> -->
  </div>
</template>

<script>
  import OverhaulDetail from '@/views/equiment-full-life-cycle/repair/overhaul/work-order/detail/index.vue';
  export default {
    name: 'ResourceInformation',
    components: {
      OverhaulDetail,
      FaultDefectDetail: () =>
        import(
          '@/views/equiment-full-life-cycle/fault-defect-management/detail/index.vue'
        )
    },
    props: {
      detail: {
        type: Object,
        default: () => {}
      },
      isNotJump: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        drawerComp: 'FaultDefectDetail'
      };
    },
    mounted() {},
    methods: {
      view(no) {
        let type = this.detail.source === '3' ? 'overhaul' : 'fault';
        this.$refs[type].show(no, 'view', true);
      }
    }
  };
</script>
<style scoped lang="scss"></style>
