<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      label-width="110px"
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      label-position="left"
      class="search-form"
    >
      <el-form-item label="备品备件名称" prop="name">
        <el-input
          v-model.trim="form.name"
          placeholder="请输入备品备件名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="备品备件编号" prop="no">
        <el-input
          v-model.trim="form.no"
          placeholder="请输入备品备件编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    props: {},
    data() {
      return {
        form: {
          name: undefined,
          no: undefined
        }
      };
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
