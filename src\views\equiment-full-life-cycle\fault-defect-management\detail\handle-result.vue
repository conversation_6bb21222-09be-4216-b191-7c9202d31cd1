<template>
  <!-- 详情基本信息 -->
  <div>
    <el-form
      ref="form"
      size="small"
      inline
      label-width="120px"
      label-suffix="："
    >
      <!-- 保修详情 -->
      <el-row v-if="details.status === 3" :gutter="15">
        <el-col :span="8">
          <el-form-item label="处理结果" prop="resultName">
            <div class="content-wrapper">
              {{ details.resultName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="维修工单号" prop="repairNo">
            <div v-if="linkDisabled">{{ details.repairNo }}</div>
            <el-button
              v-else
              type="text"
              size="medium"
              @click="$emit('detail', 'repair')"
              >{{ details.repairNo }}</el-button
            >
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处理人" prop="operateUserName">
            <div class="content-wrapper">
              {{ details.operateUserName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="故障缺陷名称" prop="name">
            <div class="content-wrapper">
              {{ details.name || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="故障缺陷类型" prop="typeName">
            <div class="content-wrapper">
              {{ details.typeName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col v-if="details.type === '1'" :span="8">
          <el-form-item label="异常等级" prop="levelName">
            <div class="content-wrapper">
              {{ details.levelName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处理时间" prop="operateTime">
            <div class="content-wrapper">
              {{ details.operateTime || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="异常描述" prop="comment">
            <div class="content-wrapper">
              {{ details.comment || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="补充图片" prop="attachList">
            <!-- <upload-img
              v-if="details.attachList && details.attachList.length"
              v-model="details.attachList"
              :editable="false"
              formatLimit="jpeg,png,jpg"
            /> -->
            <div
              v-if="details.attachList && details.attachList.length"
              class="img-wrapper"
            >
              <el-image
                v-for="img in details.attachList"
                :key="img.id"
                style="width: 80px; height: 80px"
                :src="getFileFullUrl(img.id)"
                fit="cover"
                :preview-src-list="[getFileFullUrl(img.id)]"
              ></el-image>
            </div>
            <div v-else>暂无</div>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 处理详情 -->
      <el-row v-else :gutter="15">
        <el-col :span="8">
          <el-form-item label="处理结果" prop="resultName">
            <div class="content-wrapper">
              {{ details.resultName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处理人" prop="operateUserName">
            <div class="content-wrapper">
              {{ details.operateUserName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处理时间" prop="operateTime">
            <div class="content-wrapper">
              {{ details.operateTime || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="attachList">
            <div class="content-wrapper">
              {{ details.operateRemark || '-' }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
  import UploadImg from '@/components/uploadImage.vue';
  import { getFileFullUrl } from '@/util/file';

  export default {
    components: { UploadImg },
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        default: () => {
          return {};
        }
      },
      linkDisabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {};
    },
    methods: {
      getFileFullUrl
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-form {
      .el-form-item {
        width: 100%;
        margin-bottom: 4px;
      }

      .el-form-item__content {
        width: calc(100% - 120px);
      }
    }
  }

  .img-wrapper {
    .el-image {
      & + .el-image {
        margin-left: 10px;
      }
    }
  }
</style>
