<template>
  <basic-container :autoHeight="true">
    <div class="top-info">
      <search ref="search" @query="search"></search>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="前期资料" name="PRE"> </el-tab-pane>
      <el-tab-pane label="注册资料" name="REGISTER"> </el-tab-pane>
    </el-tabs>
    <el-table
      size="small"
      :data="list"
      border
      v-loading="loading"
      height="calc(100% - 140px)"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="code"
        label="设备编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="sn"
        label="SN编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="model"
        label="规格型号"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.model || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.useDeptName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="userName"
        label="使用人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.userName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="fileStatus"
        label="资料上传结果"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <el-tag
            class="table-custom-tag"
            :type="row.fileStatus ? 'success' : 'warning'"
            >{{ row.fileStatus ? '已上传' : '未上传' }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop=""
        label="操作"
        align="center"
        show-overflow-tooltip
        width="120"
      >
        <template slot-scope="{ row }">
          <el-button size="small" type="text" @click="view(row)"
            >查看</el-button
          >

          <el-button
            size="small"
            type="text"
            @click="configuration(row, 'EDIT')"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />

    <point-dialog ref="pointDialog" @success="getList"></point-dialog>
    <!--     查看详情-->
    <view-info ref="viewInfo"></view-info>
  </basic-container>
</template>

<script>
  import Pagination from '@/components/pagination';
  import Search from './search';
  import PointDialog from '../operate/index.vue';
  import ViewInfo from '../detail/index.vue';

  import { accountListPage } from '@/api/equiment-full-life-api/profile';

  export default {
    name: 'bearingLibraryIndex',
    components: { PointDialog, Search, ViewInfo, Pagination },
    props: {
      categoryId: {
        // 设备id
        type: String,
        default: () => {
          return '';
        }
      }
    },
    data() {
      return {
        activeName: 'PRE',
        nodeId: undefined,
        isShowBtn: false,
        searchParams: {
          size: 10,
          current: 1
        },
        total: 0,
        loading: false,
        list: [],
        num: 0,
        multipleSelection: []
      };
    },
    watch: {
      categoryId: {
        handler(val) {
          this.isShowBtn = !!val;
          if (val) {
            this.nodeId = val;
            this.getList();
          }
        }
      }
    },
    mounted() {},
    methods: {
      //  点击切换前期资料/投用资料的时候
      handleClick() {
        console.log('activeName.......', this.activeName);
        this.getList();
      },

      view(row) {
        this.$refs['viewInfo'].show(row.id, this.activeName);
      },
      // 點擊搜索
      search(params) {
        this.searchParams = { ...this.searchParams, ...params };
        this.getList();
      },
      configuration(row, type) {
        this.$refs['pointDialog'].show(row.id, type, this.activeName);
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await accountListPage({
            categoryId: this.categoryId,
            ...this.searchParams,
            module: this.activeName
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
          this.loading = false;
        } catch (message) {
          this.loading = false;
          console.log('err', message);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-scrollbar__view {
      height: 98% !important;
    }
  }
</style>
