<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">资产盘点</p>
      <base-info
        ref="info"
        :initData="detail"
        :whorehouse="whorehouse"
      ></base-info>

      <p class="el-base-title">盘点部门</p>
      <el-tree
        ref="deptTree"
        :data="deptData"
        show-checkbox
        node-key="id"
        :default-checked-keys="defaultCheckedKeys"
        :props="defaultProps"
        :check-strictly="true"
      >
      </el-tree>
    </section>
    <div class="oper_btn">
      <btn type="submit" :loading="loading" @click="submit"></btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from './base-info.vue';
  import {
    submitDevicePartsInventoryApi,
    getDevicePartsInventoryViewApi
  } from '@/api/equiment-full-life-api/device-inventory';
  import { getList } from '@/api/system/dept';
  export default {
    name: 'AddDevice',
    components: {
      BaseInfo
    },
    props: {},
    data() {
      return {
        defaultCheckedKeys: [],
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {},
        whorehouse: [],
        deptData: [],
        defaultProps: {
          children: 'children',
          label: 'deptName'
        }
      };
    },
    watch: {},
    computed: {},
    methods: {
      //  展示效果详情展示效果
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getDevicePartsInventoryViewApi(id);
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      async getDeptList() {
        this.loading = true;
        try {
          const res = await getList();
          this.deptData = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      show(row) {
        this.visible = true;
        this.edit = !!row.id;
        this.eqId = row.id;
        this.getDeptList(); // 获取部门列表
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.$refs['info'].resetForm();
        this.detail = {};
      },

      async submit() {
        console.log('this.deptId', this.$refs['deptTree'].getCheckedNodes());
        let params = await this.$refs['info'].validForm();
        if (params) {
          const inventoryDept = this.$refs['deptTree'].getCheckedKeys();
          if (inventoryDept.length === 0) {
            this.$message.warning('请选择盘点部门');
            return;
          }
          //  获取部门名称
          let treeNodes = this.$refs['deptTree'].getCheckedNodes();
          let name = treeNodes
            .map((item) => {
              return item.deptName;
            })
            .join(',');

          await this.save({
            ...params,
            inventoryDeptId: inventoryDept.join(','),
            inventoryDeptName: name,
            id: this.edit ? this.eqId : undefined
          });
        }
      },

      // 提交
      async save(params) {
        this.loading = true;
        try {
          await submitDevicePartsInventoryApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
