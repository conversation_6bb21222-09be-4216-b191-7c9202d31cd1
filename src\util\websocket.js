import { wsUrl } from '@/config/env';
import website from '@/config/website';
import ElementUI from 'element-ui';

let countReconnect = website.WS.maxReconnect;
function initWebSocket(e) {
  console.log(wsUrl + e);
  const wsUri = wsUrl + e;
  this.socket = new WebSocket(wsUri); //这里面的this都指向vue
  this.socket.onerror = webSocketOnError;
  this.socket.onmessage = webSocketOnMessage;
  this.socket.onclose = closeWebsocket;
}
function webSocketOnError(e) {
  ElementUI.Notification({
    title: '',
    message: 'WebSocket连接发生错误' + e,
    type: 'error',
    duration: 0
  });
}
function webSocketOnMessage(e) {
  console.log('websocket消息接收信息：' + e.data);
  const data = JSON.parse(e.data);
  console.log(data.type === 'TIP', data.type === 'TIP');
  if (data.type === 'TIP') {
    ElementUI.Notification({
      title: '',
      message: data.title,
      type: 'success',
      duration: 3000
    });
  } else if (data.msgType === 'ERROR') {
    ElementUI.Notification({
      title: '',
      message: data.msg,
      type: 'error',
      duration: 0
    });
  }
}
// 关闭websiocket
function closeWebsocket() {
  console.log('连接已关闭...');
  console.log('尝试重连');
  if (this.lockReconnect || countReconnect <= 0) return;
  setTimeout(() => {
    countReconnect--; // 不做限制 连不上一直重连
    this.initWebSocket();
  }, 5 * 1000);
}
function close() {
  this.socket.close(); // 关闭 websocket
  this.socket.onclose = function (e) {
    console.log(e); //监听关闭事件
    console.log('关闭');
  };
}
// function webSocketSend(agentData) {
//   this.socket.send(agentData);
// }
export default {
  initWebSocket,
  close
};
