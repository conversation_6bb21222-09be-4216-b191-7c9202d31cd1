<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="设备编号：">{{
        details.code || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备名称：">{{
        details.name || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备类型：">{{
        details.categoryName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="规格型号：">{{
        details.model || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备分类：">{{
        details.categoryName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备等级：">{{
        details.importantLevelName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备路径：">{{
        details.locationPath || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备状态：">{{
        details.statusName || '-'
      }}</el-descriptions-item>

      <!--      <el-descriptions-item-->
      <!--        label="设备模型图"-->
      <!--        v-if="details.imageList && details.imageList.length > 0"-->
      <!--      >-->
      <!--        <el-image-->
      <!--          style="width: 100px; height: 100px"-->
      <!--          :src="convertFileUrl(details.imageList[0].domain)"-->
      <!--          fit="cover"-->
      <!--          :preview-src-list="[convertFileUrl(details.imageList[0].domain)]"-->
      <!--        ></el-image-->
      <!--      ></el-descriptions-item>-->
    </el-descriptions>
  </div>
</template>
<script>
  import { convertFileUrl } from '@/util/util';

  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},
    data() {
      return {};
    },
    methods: { convertFileUrl }
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
