<template>
  <div>
    <el-dialog
      width="500px"
      append-to-body
      :close-on-click-modal="viewMode"
      :visible.sync="open"
      :title="dialogTitle"
    >
      <el-form
        v-loading="loading"
        :model="order"
        :rules="rules"
        ref="orderForm"
        label-width="90px"
        label-suffix=": "
        size="mini"
        :disabled="viewMode"
      >
        <el-form-item label="订单编号" prop="num">
          <el-input v-model="order.num" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input
            v-model="order.price"
            type="number"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="支付方式" prop="paymentType">
          <el-radio-group v-model="order.paymentType">
            <el-radio :label="1">银行卡</el-radio>
            <el-radio :label="2">支付宝</el-radio>
            <el-radio :label="3">微信</el-radio>
            <el-radio :label="4">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="订单日期" prop="orderTime">
          <el-date-picker
            v-model="order.orderTime"
            style="width: 100%"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期"
            autocomplete="off"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="open = false" :disabled="btnLoading"
          >取 消</el-button
        >
        <el-button
          v-if="!viewMode"
          :disabled="btnLoading"
          size="small"
          type="primary"
          @click="handleSubmit('orderForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { getDetail, submit } from '@/api/demo/order';

  export default {
    name: 'OrderAddEdit',
    props: {
      dialogTitle: {
        type: String,
        default: ''
      },
      formVisible: {
        type: Boolean,
        default: false
      },
      viewMode: {
        type: Boolean,
        default: false
      },
      orderId: {
        type: String,
        require: true
      }
    },
    watch: {
      orderId: {
        handler(val) {
          val && this.getDetail();
        },
        immediate: true
      }
    },
    data() {
      return {
        // 表单加载
        loading: false,
        // 按钮加载
        btnLoading: false,
        // 校验规则
        rules: {
          num: [{ required: true, message: '请输入订单号', trigger: 'blur' }],
          price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
          paymentType: [
            { required: true, message: '请选择支付方式', trigger: 'blur' }
          ],
          orderTime: [
            { required: true, message: '请选择订单日期', trigger: 'blur' }
          ]
        },
        // 表单映射模型
        order: {},
        // 时间选择器选项
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        }
      };
    },
    methods: {
      // 重置弹窗表单
      resetForm(formName) {
        this.$refs[formName].clearValidate();
      },
      // 获取订单详情
      async getDetail() {
        try {
          this.loading = true;
          const { data = {} } = await getDetail(this.orderId);
          if (data.success) {
            this.order = data.data;
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 提交表单
      handleSubmit(formName) {
        // 表单验证
        this.$refs[formName].validate(async (valid) => {
          if (valid) {
            try {
              this.btnLoading = true;
              const { data } = await submit(this.order);
              if (data.success) {
                this.open = false;
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                });
                this.$emit('refresh');
              }
            } catch (e) {
              console.error(e);
            } finally {
              this.btnLoading = false;
            }
          }
        });
      }
    },
    computed: {
      open: {
        get() {
          return this.formVisible;
        },
        set() {
          this.$refs.orderForm.resetFields();
          this.$emit('close');
        }
      }
    }
  };
</script>
<style lang=""></style>
