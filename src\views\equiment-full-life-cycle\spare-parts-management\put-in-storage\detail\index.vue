<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @closed="close"
    class="detail-drawer"
    size="80%"
  >
    <div class="details" v-loading="loading" v-if="visible">
      <!-- 基本信息 -->
      <section>
        <p class="el-base-title">入库明细</p>
        <base-info :details="details"></base-info>
      </section>

      <p class="el-base-title">信息</p>
      <sel-asset ref="asset" :detail="details" :isShow="false"></sel-asset>

      <p class="el-base-title">入库单备注</p>
      <el-table
        class="table"
        :data="remarkTableData"
        border
        :header-cell-style="{ background: '#fafafa' }"
        size="small"
      >
        <el-table-column prop="userName" label="备注人">
          <template slot-scope="{ row }">
            {{ row.userName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="dateTime" label="备注时间">
          <template slot-scope="{ row }">
            {{ row.dateTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注内容"></el-table-column>
      </el-table>
    </div>
  </dialog-drawer>
</template>

<script>
  import SelAsset from '../operate/sel-asset.vue';
  import BaseInfo from './base-info.vue';
  import { getSparePartsInOutViewApi } from '@/api/equiment-full-life-api/spare-parts';
  export default {
    name: 'RepairViewIndex',
    components: { BaseInfo, SelAsset },
    data() {
      return {
        id: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {}, // 详情数据
        remarkTableData: []
      };
    },

    methods: {
      async getDetail(no) {
        this.loading = true;
        try {
          const res = await getSparePartsInOutViewApi(no);
          this.details = res.data.data;
          if (this.details.completionRemark) {
            this.remarkTableData = [
              {
                dateTime: this.details.completionRemarkDateTime,
                userName: this.details.completionRemarkUserName,
                remark: this.details.completionRemark
              }
            ];
          }

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 点击展示
      async show(no) {
        this.visible = true;
        if (no) {
          await this.getDetail(no);
        }
      },

      // 关闭弹窗
      close() {
        this.remarkTableData = [];
        this.detail = {};
        this.visible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
