// 标题
const title = '设备全生命周期';
const publicPath = process.env.VUE_APP_PUBLIC_PATH;
const app_env = process.env.VUE_APP_ENV;
const packageName = require('./package').name;

// 生产CND
const pro_cdn = {
  script: [
    publicPath + 'cdn/vue/2.6.10/vue.min.js',
    publicPath + 'cdn/vuex/3.1.1/vuex.min.js',
    publicPath + 'cdn/axios/1.0.0/axios.min.js',
    publicPath + 'cdn/element-ui/2.15.6/index.js',
    publicPath + 'cdn/avue/2.9.5/avue.min.js',
    publicPath + 'cdn/vue-router/3.0.1/vue-router.min.js'
  ]
};

module.exports = {
  //路径前缀
  publicPath: publicPath,
  outputDir: 'app-simas',
  lintOnSave: true,
  productionSourceMap: false,
  configureWebpack: {
    name: title,
    output: {
      library: 'simas',
      libraryTarget: 'umd', // 把微应用打包成 umd 库格式
      jsonpFunction: `webpackJsonp_${packageName}`
    }
  },
  chainWebpack: (config) => {
    if (app_env !== 'development') {
      config.plugin('html').tap((args) => {
        args[0].cdn = pro_cdn;
        return args;
      });
      //忽略的打包文件
      config.externals({
        vue: 'Vue',
        'vue-router': 'VueRouter',
        vuex: 'Vuex',
        axios: 'axios',
        'element-ui': 'ELEMENT',
        '@smallwei/avue': 'AVUE'
      });
    }
    config.module
      .rule('fonts')
      .use('url-loader')
      .loader('url-loader')
      .options({})
      .end();
    config.module
      .rule('images')
      .use('url-loader')
      .loader('url-loader')
      .options({})
      .end();
  },
  css: {
    extract: { ignoreOrder: true }
  },
  //开发模式反向代理配置，生产模式请使用Nginx部署并配置反向代理
  devServer: {
    port: 8001,
    open: true,
    disableHostCheck: true,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    proxy: {
      '/api': {
        //本地服务接口地址
        // target: 'http://szyk-single.rdframework.************.nip.io:32393',
        // target: 'http://***************:10000/api',
        // target: 'http://**************:32430/api', // 开发环境
        // target: 'https://wzx.allfinetech.cn/api',
        target: 'http://**************:32496/api', // 测试环境
        // target: 'http://**************:13000',
        // target: 'http://**************:13000',
        // target: 'http://**************:30443/api',

        // target: 'http://**************:85',
        // target: 'http://**************',
        // target: 'http://localhost',
        //远程演示服务地址,可用于直接启动项目
        //target: 'https://saber.szyk.vip/api',
        ws: true,
        pathRewrite: {
          '^/api': '/'
        }
      }
    }
  }
};
