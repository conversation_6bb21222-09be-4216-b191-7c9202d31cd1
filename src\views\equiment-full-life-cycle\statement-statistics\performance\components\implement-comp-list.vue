<template>
  <div class="comp-wrapper">
    <!-- 表格 -->
    <el-table
      v-loading="loading"
      ref="table"
      class="table"
      :data="listData"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="orderTypeName"
        label="部门名称"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="orderNum"
        label="工单总数"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="completeNum"
        label="完成数量"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="unCompleteNum"
        label="未完成数量"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        sortable
        prop="completeRateStr"
        label="工单完成率"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        sortable
        prop=""
        label="操作"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <el-button size="small" type="text" @click="view(row)">
            查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import { getImplementList } from '@/api/equiment-full-life-api/statement-statistics.js';
  import dayjs from 'dayjs';
  import DeptDialog from '@/components/dept-dialog/index.vue';

  export default {
    components: { DeptDialog },
    data() {
      return {
        loading: false,
        form: {
          deptId: undefined,
          deptName: undefined
        },
        searchParams: {
          time: [],
          startDate: undefined,
          endDate: undefined,
          orderType: undefined,
          current: 1,
          size: 10
        },
        // 列表
        listData: []
      };
    },
    created() {
      const start = dayjs().subtract(30, 'days').format('YYYY-MM-DD');
      const end = dayjs().format('YYYY-MM-DD');
      this.searchParams.time = [start, end];
      this.searchParams.startDate = start;
      this.searchParams.endDate = end;
      this.refresh();
    },
    methods: {
      //  点击选择部门筛选条件
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.deptId = dept.id;
        this.form.deptName = dept.deptName;
      },
      // 点击查看
      view(row) {
        console.log(row);
        this.$emit('compView');
      },
      query() {
        const [startDate, endDate] = this.searchParams.time;
        this.searchParams.startDate = startDate;
        this.searchParams.endDate = endDate;
        this.getList();
      },
      reset() {
        this.searchParams.current = 1;
        this.query();
      },
      refresh() {
        this.getList();
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          const params = { ...this.searchParams };
          params.time = [];
          let res = await getImplementList(params);
          this.listData = res.data.data || [];
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .comp-wrapper {
    height: 100%;

    .search-wrapper {
      ::v-deep .el-form-item {
        margin-bottom: 10px;
      }
    }

    .summary-wrapper {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 10px;
    }
  }
</style>
