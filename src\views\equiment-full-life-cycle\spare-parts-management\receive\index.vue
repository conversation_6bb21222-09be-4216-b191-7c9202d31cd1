<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-if="permission['spare-receive-add-edit']"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        align="center"
        prop="no"
        label="领用单号"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.no || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="领用单名称"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="receiveUserName"
        label="领用人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.receiveUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="receiveDeptName"
        align="center"
        label="领用部门"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.receiveDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="receiveUserName"
        align="center"
        label="领用部门"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.receiveDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        align="center"
        label="申请领用时间"
        show-overflow-tooltip
        width="150"
      ></el-table-column>
      <el-table-column
        prop="totalQuantity"
        align="center"
        label="备件数量"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.totalQuantity || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="remark"
        align="center"
        label="领用原因"
        show-overflow-tooltip
        width="110px"
      >
        <template v-slot="{ row }">{{ row.remark || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="auditUserName"
        align="center"
        label="审核人"
        show-overflow-tooltip
        width="110px"
      >
        <template v-slot="{ row }">{{ row.auditUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="auditTime"
        align="center"
        label="审核时间"
        show-overflow-tooltip
        width="150"
      >
        <template v-slot="{ row }">{{ row.auditTime || '-' }} </template>
      </el-table-column>

      <el-table-column
        prop="statusName"
        align="center"
        label="状态"
        width="110px"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${receiveStatusColor(row.status)};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="createUserName"
        align="center"
        label="创建人"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.createUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        align="center"
        label="创建时间"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        align="center"
        label="更新人"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.updateUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        align="center"
        width="150"
        label="更新时间"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.updateTime || '-' }} </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="230" fixed="right">
        <template v-slot="{ row }">
          <section class="cell-operate-class">
            <el-button
              v-if="permission['spare-receive-view']"
              type="text"
              size="small"
              @click="detail(row)"
              >查看</el-button
            >
            <!--    待审核1（查看、撤销、编辑、审核） 、已审核待出库2（查看）、驳回3（撤销、编辑、查看）、已撤销4（查看）、已完成5（查看）-->

            <el-button
              type="text"
              size="small"
              @click="operate(row)"
              v-if="
                (row.status === 4 || row.status === 3) &&
                permission['spare-receive-add-edit']
              "
              >编辑</el-button
            >
            <!--           待审核状态可以撤销、可以审核-->
            <el-button
              class="danger-btn"
              type="text"
              size="small"
              @click="cancelOrder(row)"
              v-if="
                (row.status === 1 || row.status === 3) &&
                permission['spare-receive-add-revocation']
              "
              >撤销</el-button
            >
            <!-- 审核和驳回同一个权限-->
            <el-button
              class="green-btn"
              type="text"
              size="small"
              @click="examine(row)"
              v-if="
                row.status === 1 && permission['spare-receive-review-reject']
              "
              >审核</el-button
            >
            <el-button
              class="danger-btn"
              type="text"
              size="small"
              @click="turnDown(row)"
              v-if="
                row.status === 1 && permission['spare-receive-review-reject']
              "
              >驳回</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex"></detail-index>
    <!--     驳回-->
    <turn-down ref="turnDown" @success="getList"></turn-down>
  </basic-container>
</template>

<script>
  import TurnDown from './operate/turn-down.vue';
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import { convertFileUrl } from '@/util/util';
  import {
    getSparePartsReceiveListApi,
    auditSparePartsReceiveApi,
    cancelSparePartsReceiveApi
  } from '@/api/equiment-full-life-api/spare-parts';
  import { receiveStatusColor } from '@/views/equiment-full-life-cycle/spare-parts-management/util';

  import { mapGetters } from 'vuex';
  export default {
    name: 'MaintenanceList',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination,
      TurnDown
    },
    props: {},
    computed: {
      ...mapGetters(['permission', 'userInfo'])
    },
    data() {
      return {
        receiveStatusColor,
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },

    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      //  驳回
      turnDown(row) {
        this.$refs['turnDown'].show(row);
      },
      //  撤销
      cancelOrder(row) {
        this.$confirm(`是否确定撤销领用？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            await cancelSparePartsReceiveApi(row.id);
            this.$message.success('操作成功');
            await this.getList();
          } catch (e) {
            this.$message.warning(e.data.msg);
          }
        });
      },
      //  审核
      examine(row) {
        this.$confirm(`确定要审核通过此领用单？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await auditSparePartsReceiveApi(
                {
                  auditStatusEnum: 'PASS'
                },
                row.id
              );
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getSparePartsReceiveListApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
