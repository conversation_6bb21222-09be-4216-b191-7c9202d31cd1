const registerReg = {
  landline: /^(((\d{3,4}-)?[0-9]{7,8})|(1(3|4|5|6|7|8|9)\d{9}))$/, // 座机号 手机号验证
  phone: /^1[3|4|5|6|7|8|9][0-9]{9}$/, // 手机号验证
  phoneOrNull: /^((1[3|4|5|6|7|8|9][0-9]{9})|无)$/, // 手机号验证, 可填写无
  number: /^[0-9]*$/, // 纯数字格式验证
  oneToten: /^(10|[1-9])$/, // 1-10
  positiveint: /^(0|[1-9]\d*)$/, // 0 正整数 新添加勿删
  positiveintN: /^[1-9]\d*$/, // 正整数 新添加勿删
  integer: /^([1-9][0-9]{0,1}|100)$/, //大于0小于等于100的正整数
  letter: /^[a-zA-Z]+$/, // 纯字母格式验证
  noSymbol: /^[^@#$%^&*]*$/, // 匹配不包含 @#$%^&* 英文特殊符号
  len10: /^.{0,10}$/, // 最长10个字符
  len20: /^.{0,20}$/, // 最长20个字符
  len50: /^.{0,50}$/, // 最长50个字符
  email: /^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,})$/, //邮箱
  regOneNumber: /^\d+(?:\.\d{1,1})?$/, // 小数并且小数点后最多一位
  regOneNumberNoZero: /(^[1-9](\d+)?(\.\d{1,1})?$)|(^0\.[1-9]{1,1}$)/, // 小数并且小数点后一位不为0，不包括0
  regTwoNumber: /^\d{1,10}(?:\.\d{1,2})?$/, // 小数并且小数点后最多两位
  regThreeNumber: /^\d+(?:\.\d{1,3})?$/, // 小数并且小数点后最多三位
  // URL: /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/|www\.)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+$/, //验证URl
  //验证中国大陆身份证号码
  idcardReg:
    /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/,
  regMobile: /^((13[0-9]|14[5|7]|15[0-9]|16[6]|17[0-9]|18[0-9]|19[9])\d{8})*$/,
  pureEnglish: /^[a-zA-Z]+$/, // 纯英文
  // 密码中允许出现数字、大写字母、小写字母、特殊字符（!#$%^&*），但至少包含其中3种且长度在8-16之间（四种符号任取其三）
  // 包含四种符号中的一种符号，无效。
  // 包含四种符号中的二种符号，无效。
  // 包含四种符号中的三种符号，有效。
  // 包含四种符号中的四种符号，有效。
  regPassword:
    /^(?![\da-z]+$)(?![\dA-Z]+$)(?![\d!#$%^&*]+$)(?![a-zA-Z]+$)(?![a-z!#$%^&*]+$)(?![A-Z!#$%^&*]+$)[\da-zA-z!#$%^&*]{8,16}$/,
  // 只输入大写字母
  onlyMajuscule: /^[A-Z]+$/,
  // 输入大写 字母数字
  alphanumeric: /^[A-Z0-9]+$/
};

export default registerReg;
