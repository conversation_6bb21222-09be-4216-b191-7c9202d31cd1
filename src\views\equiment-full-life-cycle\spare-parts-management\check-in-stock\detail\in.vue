<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'明细'"
    :visible.sync="visible"
    @close="close"
    class="detail-drawer"
    size="80%"
  >
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      border
      ref="table"
      stripe
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="no"
        label="入库单号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.no || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="typeName"
        label="入库日期"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="typeName"
        label="入库人"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="warehouse"
        label="供应商名称"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="createTime"
        label="库房"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="createUserName"
        label="备品备件编号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="备品备件名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="规格型号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="入库数量"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.statusName || '-' }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
  </dialog-drawer>
</template>

<script>
  import { getSparePartsInOutViewApi } from '@/api/equiment-full-life-api/spare-parts';
  export default {
    name: 'RepairViewIndex',
    components: {},
    data() {
      return {
        id: undefined,
        searchParams: {
          current: 1,
          size: 10
        },
        total: 0,
        list: [],
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {}, // 详情数据
        remarkTableData: []
      };
    },

    methods: {
      async getList(no) {
        this.loading = true;
        try {
          const res = await getSparePartsInOutViewApi({ no: no });
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 点击展示
      async show(no) {
        this.visible = true;
        if (no) {
          await this.getDetail(no);
        }
      },

      // 关闭弹窗
      close() {
        this.visible = false;
        this.detail = {};
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
