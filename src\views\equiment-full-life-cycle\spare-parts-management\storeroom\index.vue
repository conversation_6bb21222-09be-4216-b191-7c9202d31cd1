<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-if="permission['spare-storeroom-add-edit']"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      stripe
      height="calc(100% - 200px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        prop="name"
        align="center"
        label="库房名称"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        align="center"
        prop="no"
        label="库房编号"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="manager<PERSON>ame"
        align="center"
        label="管理人员"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="tel"
        label="联系电话"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.tel || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        align="center"
        prop="remark"
        label="备注"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.remark || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        align="center"
        label="创建人"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.createUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        align="center"
        label="创建时间"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        align="center"
        label="更新人"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.updateUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        align="center"
        width="150"
        label="更新时间"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column align="center" label="操作" width="120" fixed="right">
        <template slot-scope="scope">
          <section class="cell-operate-class">
            <el-button
              v-if="permission['spare-storeroom-add-edit']"
              type="text"
              size="small"
              @click="operate(scope.row)"
              >编辑</el-button
            >
            <el-popconfirm
              v-if="permission['spare-storeroom-delete']"
              title="确定删除吗？"
              @confirm="() => handleDelete(scope.row)"
            >
              <el-button
                class="danger-btn"
                slot="reference"
                type="text"
                size="small"
                >删除</el-button
              >
            </el-popconfirm>
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-base-info ref="add" @success="getList"></add-base-info>
  </basic-container>
</template>

<script>
  import Search from './search';
  import AddBaseInfo from './operate/index.vue';
  import Pagination from '@/components/pagination';

  import {
    delCheckRepairDetailApi,
    getStoreHouseListApi
  } from '@/api/equiment-full-life-api/spare-parts';
  import { mapGetters } from 'vuex';
  export default {
    name: 'DeviceBasicList',
    computed: {
      ...mapGetters(['permission'])
    },
    components: {
      Search,
      AddBaseInfo,
      Pagination
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getStoreHouseListApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        this.$refs.add.show(row);
      },

      async handleDelete(row) {
        try {
          await delCheckRepairDetailApi({ id: row.id });
          //  删除的时候，判断当前列表，是不是length 是1 ，如果是1，将current置成1
          if (this.list.length === 1) {
            this.searchParams.current = 1;
          }
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          await this.getList(true);
          // 发信号 通知树更新
        } catch (e) {
          this.$message.warning(e.data.msg);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
