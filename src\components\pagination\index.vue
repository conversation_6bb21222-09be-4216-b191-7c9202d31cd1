<template>
  <div class="pagination-container">
    <el-pagination
      v-bind="$attrs"
      :layout="layout"
      :background="background"
      :current-page.sync="page"
      :page-size.sync="limit"
      :page-sizes="pageSizes"
      :total="total"
      @size-change="handleChange"
      @current-change="handleChange"
    />
  </div>
</template>

<script>
  export default {
    name: 'pagination',
    inheritAttrs: false,
    props: {
      layout: {
        type: String,
        default: 'total, prev, pager, next, jumper, sizes'
      },
      background: {
        type: Boolean,
        default: true
      },
      total: {
        required: true,
        type: Number
      },
      pageNo: {
        type: Number,
        default: 1
      },
      pageSize: {
        type: Number,
        default: 10
      },
      pageSizes: {
        type: Array,
        default() {
          return [10, 20, 30, 40, 50];
        }
      }
    },
    methods: {
      handleChange() {
        this.$emit('pagination');
      }
    },
    computed: {
      page: {
        get() {
          return this.pageNo;
        },
        set(val) {
          this.$emit('update:pageNo', val);
        }
      },
      limit: {
        get() {
          return this.pageSize;
        },
        set(val) {
          this.$emit('update:pageSize', val);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .pagination-container /deep/ {
    padding: 15px 0 0;
    text-align: center;
    background: #fff;
    @media screen and (max-width: 750px) {
      .btn-prev,
      .el-pager,
      .btn-next {
        display: none;
      }
    }

    .el-input__inner {
      vertical-align: middle;
    }

    .el-pagination__total {
      float: left;
      width: 203px;
      text-align: left;
    }

    .el-pagination__jump {
      margin-left: 0;
    }

    .el-pagination__jump,
    .el-pagination__sizes {
      float: right;
    }
  }
</style>
