import request from '@/router/axios';

/**
 * 文件流返回
 * @param url 接口地址
 */
export const exportBlob = (url) => {
  return request({
    url: url,
    method: 'get',
    responseType: 'blob'
  });
};

// 历史指令
export function getCommandHistory() {
  return request({
    url: `/api/szyk-simas/aiAssistant/commandHistoryList`,
    method: 'get'
  });
}

// 智能助手 - 指令模式
export function getDirectiveAnswer(data) {
  return request({
    url: `/api/szyk-simas/aiAssistant/answer`,
    method: 'post',
    data
  });
}

// 智能助手 - 输入模式
export function getCustomAnswer(data) {
  return request({
    url: `/api/szyk-simas/aiAssistant/answerV2`,
    method: 'post',
    data
  });
}

// 获取参数值
export function getParameterValue(paramKey) {
  return request({
    url: `/api/szyk-system/param/detail?paramKey=${paramKey}`,
    method: 'get'
  });
}
