<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <!--      <p class="el-base-title">基本信息</p>-->
      <!--      <info ref="info" :initData="detail"></info>-->
      <p class="el-base-title">文件资料</p>
      <file-upload
        ref="file"
        :fileData="fileData"
        :limit="20"
        :eqId="eqId"
      ></file-upload>
      <p class="el-base-title">适用设备类型</p>
      <asset-info ref="asset" :detail="detail"></asset-info>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"></btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import AssetInfo from './sel-asset.vue';
  import FileUpload from '@/views/equiment-full-life-cycle/components/profile.vue';
  import {
    userDetail,
    equipmentBatchUpload
  } from '@/api/equiment-full-life-api/profile';
  import { convertFileUrl } from '@/util/util';
  export default {
    name: 'AddDevice',
    components: {
      FileUpload,
      AssetInfo
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {
          image: []
        },
        cateGoryList: [],
        fileData: {}
      };
    },
    watch: {},
    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await userDetail({ id: id });
          this.detail = res.data.data;
          let file = this.detail.attachList[0];
          this.fileData = {
            id: this.detail.id,
            fileData: [
              {
                ...file,
                type: this.detail.type,
                name: file.originalName,
                fileCategoryId: this.detail.fileCategoryId
              }
            ] //
          };
          this.form.image = (this.detail.imageList || []).map((it) => {
            return {
              id: it.id,
              fileName: it.fileName,
              filePath: convertFileUrl(it.domain)
            };
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      show(id) {
        this.visible = true;
        this.edit = !!id;
        this.eqId = id;
        if (id) {
          this.getDetail(id);
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.form.image = [];
        // this.$refs['info'].resetForm();
        this.$refs['file'].resetForm();
        this.$refs['asset'].resetForm();
        this.detail = {};
      },
      async submit() {
        let file = await this.$refs['file'].validForm();
        let asset = this.$refs['asset'].validForm();
        if (file.length === 0) {
          return this.$message.warning('请上传文件资料');
        }
        if (file && asset) {
          let files = file.map((item) => ({
            ...item,
            id: this.edit ? this.eqId : undefined
          }));
          let cs = {
            // id: this.edit ? this.eqId : undefined,
            // attachId: file[0].id ? file[0].id : file[0].attachId,
            // fileCategoryId: file[0].fileCategoryId,
            // type: file[0].type,
            // extension: file[0].extension,
            // name: file[0].name,
            files,
            ...asset
          };
          await this.save(cs);
        }
      },

      // 提交设备信息
      async save(params) {
        this.loading = true;
        try {
          await equipmentBatchUpload(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
