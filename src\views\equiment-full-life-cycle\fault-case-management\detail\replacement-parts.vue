<template>
  <el-table size="small" :data="list" border>
    <el-table-column type="index" label="#" align="center"></el-table-column>
    <el-table-column
      prop="name"
      label="名称"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="model"
      label="型号"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="count"
      label="数量"
      align="center"
      show-overflow-tooltip
    >
      <template v-slot="{ row }"> {{ row.count || '-' }} </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'bearingLibraryIndex',
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {};
    },
    watch: {},
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
