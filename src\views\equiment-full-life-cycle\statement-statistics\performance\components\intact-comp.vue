<template>
  <div class="comp-wrapper">
    <!-- 搜索 -->
    <div class="search-wrapper">
      <el-form
        label-suffix="："
        :inline="true"
        ref="search"
        :model="searchParams"
        size="small"
        class="search-form"
      >
        <el-form-item label="统计部门" prop="useDeptId">
          <InputTree
            v-model="searchParams.useDeptId"
            lazy
            clearable
            :form="searchParams"
            :dic="deptData"
            style="width: 100%"
            :props="{
              label: 'deptName',
              value: 'id',
              isLeaf: (row) => !row.hasChildren,
              formLabel: 'useDeptName',
              formValue: 'useDeptId'
            }"
            :load="lazyLoad"
            :lazyLoading="lazyLoading"
            @search="lazySearch"
          ></InputTree>
        </el-form-item>
        <el-form-item label="设备等级" prop="importantLevel">
          <el-select
            v-model="searchParams.importantLevel"
            placeholder="请选择设备等级"
            clearable
          >
            <el-option
              v-for="item in serviceDicts.type['important_level']"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="categoryName">
          <el-input
            placeholder="请选择设备类型"
            type="text"
            v-model="searchParams.categoryName"
            readonly
            @focus.prevent="selectAssetCategory"
          >
            <template slot="append">
              <i class="el-icon-circle-close" @click="clearCategory"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="统计方式" prop="timeType">
          <el-select
            v-model="searchParams.timeType"
            placeholder="请选择统计方式"
          >
            <el-option
              v-for="item in statisticalMethod"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="query">查 询</el-button>
          <el-button @click="reset">重 置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="btn">
      <el-button size="small" type="primary" @click="viewDetail"
        >点击查看设备状态明细
      </el-button>
    </div>
    <intact-comp-chart
      :search-params="searchParams"
      ref="intactCompChart"
    ></intact-comp-chart>
    <!--     选择设备类型-->
    <device-type-dialog
      ref="device-type"
      @success="getSingleRow"
    ></device-type-dialog>
  </div>
</template>

<script>
  import Pagination from '@/components/pagination';
  import { getLazyList } from '@/api/system/dept';
  import { InputTree } from '@/components/yk-select-tree';
  import DeviceTypeDialog from '@/views/equiment-full-life-cycle/components/select-asset-category-dialog/single-index.vue';
  import { statisticalMethod } from '@/views/util/dict';
  import IntactCompChart from '@/views/equiment-full-life-cycle/statement-statistics/performance/components/intact-comp-chart.vue';

  export default {
    serviceDicts: ['important_level'],
    components: {
      DeviceTypeDialog,
      Pagination,
      InputTree,
      IntactCompChart
    },
    data() {
      return {
        statisticalMethod,
        loading: false,
        deptData: [],
        lazyLoading: false,
        searchParams: {
          useDeptName: undefined,
          useDeptId: undefined,
          importantLevel: undefined,
          timeType: 'THIRTY_DAYS',
          categoryName: undefined,
          categoryId: undefined
        }
      };
    },
    created() {},
    methods: {
      async lazySearch(title) {
        this.lazyLoad(null, null, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node || {};
        const { id } = data || {};
        let params = {
          deptName: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyList(title ? '' : id || 0, params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          if (title) {
            this.deptData = arr;
          } else {
            resolve && resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      viewDetail() {
        this.$emit('compView', {
          comp: 'IntactView',
          paramsObj: undefined
        });
      },
      //  清空类型
      clearCategory() {
        this.searchParams.categoryId = undefined;
        this.searchParams.categoryName = undefined;
      },
      // 选择设备类型
      selectAssetCategory() {
        this.$refs['device-type'].show();
      },
      //  选择设备类型回显
      getSingleRow(row) {
        this.searchParams.categoryId = row.id;
        this.searchParams.categoryName = row.categoryName;
      },
      query() {
        this.$refs['intactCompChart'].getCategoryAsset();
      },
      reset() {
        this.clearCategory();
        this.$refs['search'].resetFields();
        this.query();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .comp-wrapper {
    height: 100%;

    .search-wrapper {
      ::v-deep .el-form-item {
        margin-bottom: 10px;
      }
    }

    .summary-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      .left {
        display: flex;
        align-items: center;

        .font-bold {
          font-weight: bold;
        }

        .num {
          margin-left: 12px;
          color: #409eff;
        }
      }
    }
  }

  .btn {
    display: flex;
    justify-content: flex-end;
  }
</style>
