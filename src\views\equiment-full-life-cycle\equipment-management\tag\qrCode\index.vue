<template>
  <el-card class="box-card" shadow="never" v-if="isShow">
    <div slot="header" class="clearfix">
      <span>设备标识卡 </span>
    </div>
    <div class="c_body">
      <el-image
        v-if="currentRow.qrCodeImage"
        style="width: 150px; height: 150px"
        :src="getFileFullUrl(currentRow.qrCodeImage.id)"
        :preview-src-list="[getFileFullUrl(currentRow.qrCodeImage.id)]"
        fit="cover"
      ></el-image>
      <el-descriptions :column="1">
        <el-descriptions-item label="SN编号"
          >{{ currentRow.sn || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="设备名称"
          >{{ currentRow.name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="设备型号">{{
          currentRow.model || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="设备位置">{{
          currentRow.locationPath || '-'
        }}</el-descriptions-item>
        <el-descriptions-item label="使用部门">{{
          currentRow.useDeptName || '-'
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </el-card>
  <custom-empty v-else tip="暂无数据"></custom-empty>
</template>
<script>
  import CustomEmpty from '@/components/custom-empty.vue';
  import { getFileFullUrl } from '@/util/file';

  export default {
    name: 'qrcode-card',
    components: { CustomEmpty },
    props: {
      isShow: {
        type: Boolean,
        default: false
      },
      currentRow: {
        type: Object,
        default: () => {
          return {
            qrCodeImage: {
              domain: ''
            }
          };
        }
      }
    },
    watch: {},
    data() {
      return { getFileFullUrl };
    },
    methods: {}
  };
</script>
<style lang="scss" scoped>
  .clearfix {
    text-align: center;
  }
  .c_body {
    display: grid;
    grid-template-columns: 150px calc(100% - 150px);
    grid-gap: 20px;
  }
  ::v-deep {
    .el-descriptions-item__label {
      width: 70px !important;
    }
    .el-descriptions-item__content {
      width: calc(100% - 80px);
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
</style>
