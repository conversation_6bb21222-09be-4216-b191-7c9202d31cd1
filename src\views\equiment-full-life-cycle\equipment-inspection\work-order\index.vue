<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-if="permission['inspect-order-batchAudit']"
        type="primary"
        size="small"
        @click="handleBatchAudit('agree')"
        >批量审核确认</el-button
      >
      <el-button
        v-if="permission['inspect-order-batchAudit']"
        type="danger"
        size="small"
        @click="handleBatchAudit('refuse')"
        >批量驳回</el-button
      >

      <!-- <el-button
        v-if="permission['inspect-order-export']"
        icon="el-icon-upload2"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      stripe
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        :selectable="(row) => row.status === 5"
      ></el-table-column>
      <el-table-column
        prop="no"
        label="工单编号"
        align="center"
        show-overflow-tooltip
        width="130"
      >
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.no || '-' }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="orderName"
        label="计划名称"
        align="center"
        show-overflow-tooltip
        width="130"
      >
        <template slot-scope="scope">
          {{ scope.row.orderName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentCode"
        label="设备编号"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeDeptName"
        label="点巡检部门"
        align="center"
        show-overflow-tooltip
        width="100"
      >
        <template slot-scope="scope">
          {{ scope.row.executeDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="责任人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="executeDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.executeDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="startTime"
        label="开始时间"
        align="center"
        min-width="90"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="endTime"
        align="center"
        label="结束时间"
        min-width="90"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="cycleTypeName"
        label="计划周期"
        align="center"
        width="70px"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column align="center" label="审核人员" show-overflow-tooltip>
        <template v-slot="{ row }">{{
          (row.isNeedApproval && row.approvalUserName) || '-'
        }}</template>
      </el-table-column>
      <el-table-column
        prop="inspectResult"
        label="检查结果"
        align="center"
        width="70px"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.inspectResult || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="90"
      >
        <template v-slot="{ row }">
          <i
            :style="`color:${inspectOrderStatusColor(
              row.status
            )};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="createTime"
        label="创建时间"
        min-width="136"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <section class="cell-operate-class">
            <el-button type="text" size="small" @click="detail(scope.row)"
              >查看</el-button
            >
            <!--          点巡检操作-->
            <el-button
              v-if="
                (Number(scope.row.status) == 1 ||
                  Number(scope.row.status) == 3 ||
                  Number(scope.row.status) == 6) &&
                permission['inspect-order-operate']
              "
              type="text"
              size="small"
              @click="inspect(scope.row)"
              >点巡检</el-button
            >
            <!--           待确认状态下-->
            <el-button
              v-if="
                Number(scope.row.status) === 5 &&
                permission['inspect-order-confirmAndReview']
              "
              type="text"
              size="small"
              class="green-btn"
              @click="examine(scope.row)"
              >审核确认</el-button
            >
            <el-button
              v-if="
                Number(scope.row.status) === 5 &&
                permission['inspect-order-confirmAndReview']
              "
              type="text"
              size="small"
              class="danger-btn"
              @click="turnDown(scope.row)"
              >驳回</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />

    <detail-index ref="detailIndex"></detail-index>
    <!--     驳回-->
    <turn-down ref="turnDown" @success="getList"></turn-down>
    <!--    打开点巡检操作弹窗-->
    <inspect-operate ref="inspectOperate" @success="getList"></inspect-operate>
  </basic-container>
</template>

<script>
  import TurnDown from './operate/turn-down.vue';
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import Pagination from '@/components/pagination';
  import { mapGetters } from 'vuex';
  import InspectOperate from '@/views/equiment-full-life-cycle/equipment-inspection/work-order/operate/inspect-operate.vue';
  import {
    getOrderList,
    auditOrder,
    batchAuditOrder
  } from '@/api/equiment-full-life-api/inspect';
  import { downloadFileBlob } from '@/util/util';
  import { inspectOrderStatusColor } from '@/views/equiment-full-life-cycle/equipment-inspection/util.js';
  import { getToken } from '@/util/auth';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      DetailIndex,
      Pagination,
      TurnDown,
      InspectOperate
    },
    props: {},
    data() {
      return {
        inspectOrderStatusColor,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {},
        multipleSelection: []
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    activated() {
      const { fromAssist, ...restParams } = this.$route.params;
      if (fromAssist) {
        this.$refs.search.setFields(restParams);
      } else {
        this.getList();
      }
    },
    mounted() {
      // this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      // 批量审核
      handleBatchAudit(type) {
        console.log(this.multipleSelection);
        if (this.multipleSelection.length === 0) {
          return this.$message.warning('请选择至少一条工单');
        }

        if (type === 'agree') {
          this.handleBatchArgee();
        } else {
          this.$refs['turnDown'].show(this.multipleSelection, true);
        }
      },
      // 批量审核确认
      handleBatchArgee() {
        this.$confirm(`确认选中的工单结果符合标准？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await batchAuditOrder({
                orderIds: this.multipleSelection.map((item) => item.id),
                status: 2
              });
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },
      // 表格多选回调
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      //  点击点巡检的操作
      inspect(row) {
        this.$refs['inspectOperate'].show(row.no);
      },
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/inspect-order/export-order?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/inspect-order/export-order?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '点巡检工单.xlsx'
        );
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getOrderList({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },

      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      },
      //  驳回
      turnDown(row) {
        this.$refs['turnDown'].show(row);
      },
      examine(row) {
        this.$confirm(`确认点巡检结果符合标准？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await auditOrder({
                id: row.id,
                status: 2
              });
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
