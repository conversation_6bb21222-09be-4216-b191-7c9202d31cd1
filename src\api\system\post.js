import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/szyk-system/post/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

export const getPostList = (tenantId) => {
  return request({
    url: '/api/szyk-system/post/select',
    method: 'get',
    params: {
      tenantId
    }
  });
};

export const getDetail = (id) => {
  return request({
    url: '/api/szyk-system/post/detail',
    method: 'get',
    params: {
      id
    }
  });
};

export const remove = (ids) => {
  return request({
    url: '/api/szyk-system/post/check-remove',
    method: 'post',
    params: {
      ids
    }
  });
};

export const add = (row) => {
  return request({
    url: '/api/szyk-system/post/submit',
    method: 'post',
    data: row
  });
};

export const update = (row) => {
  return request({
    url: '/api/szyk-system/post/submit',
    method: 'post',
    data: row
  });
};
