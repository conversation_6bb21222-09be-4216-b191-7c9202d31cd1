<template>
  <el-drawer
    :visible.sync="visible"
    class="ai-drawer"
    :with-header="false"
    size="490px"
    direction="rtl"
    append-to-body
    destroy-on-close
    :modal="false"
    :close-on-press-escape="false"
  >
    <chat @close="visible = false"></chat>
  </el-drawer>
</template>

<script>
  import Chat from './index.vue';

  export default {
    name: 'ai-chat-drawer',
    components: { Chat },
    data() {
      return {
        visible: false
      };
    },
    methods: {
      show() {
        this.visible = true;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .ai-drawer ::v-deep {
    .el-drawer {
      top: 5px !important;
      right: 10px !important;
      bottom: 5px !important;
      height: auto !important;
      background: linear-gradient(to bottom, #e5f1fe, #fff 50%);
      border-radius: 10px;

      .el-drawer__body {
        padding: 20px;
      }
    }
  }
</style>
