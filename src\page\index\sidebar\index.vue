<template>
  <div class="avue-sidebar">
    <logo></logo>
    <el-scrollbar style="height: 100%">
      <div v-if="validatenull(menu)" class="avue-sidebar--tip">
        {{ $t('menuTip') }}
      </div>
      <el-menu
        unique-opened
        :default-active="nowTagValue"
        mode="vertical"
        :show-timeout="200"
        :collapse="keyCollapse"
      >
        <el-menu-item
          index="/wel/index"
          @click="$router.push({ path: '/wel/index' })"
        >
          <i class="el-icon el-icon-s-home"></i>

          <template slot="title">
            <span>首页</span>
          </template>
        </el-menu-item>
        <sidebar-item
          :menu="menu"
          :screen="screen"
          first
          :props="website.menu.props"
          :collapse="keyCollapse"
        ></sidebar-item>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import logo from '../logo';
  import sidebarItem from './sidebarItem';

  export default {
    name: 'sidebar',
    components: { sidebarItem, logo },
    inject: ['index'],
    data() {
      return {};
    },
    // created() {
    //   this.index.openMenu();
    // },
    computed: {
      ...mapGetters([
        'website',
        'menu',
        'tag',
        'keyCollapse',
        'screen',
        'menuId'
      ]),
      nowTagValue: function () {
        const route = this.$route;
        const { meta, path } = route;
        // if set path, the sidebar will highlight the path you set
        if (meta.activeMenu) {
          return meta.activeMenu;
        }
        return path;
      }
    },
    methods: {}
  };
</script>
<style lang="scss" scoped></style>
