<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
      size="small"
    >
      <el-descriptions-item label="工单编号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备编号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备名称：">{{
        (details.equipmentAccount && details.equipmentAccount.name) || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="使用部门：">{{
        (details.equipmentAccount && details.equipmentAccount.useDeptName) ||
        '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="使用人：">{{
        (details.equipmentAccount && details.equipmentAccount.userName) || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="存放地点：">{{
        (details.equipmentAccount && details.equipmentAccount.locationPath) ||
        '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="开始时间：">{{
        details.startTime || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="结束时间：">{{
        details.endTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="点巡检部门：">{{
        details.executeDeptName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="责任人：">{{
        details.executeUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="工单状态：">{{
        details.statusName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item v-if="details.rejectReason" label="驳回原因：">{{
        details.rejectReason || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  import { convertFileUrl } from '@/util/util';

  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},
    data() {
      return {};
    },
    methods: { convertFileUrl }
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
