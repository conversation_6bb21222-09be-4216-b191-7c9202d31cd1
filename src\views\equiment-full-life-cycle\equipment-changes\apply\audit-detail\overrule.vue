<template>
  <dialog-popup
    title="驳回原因"
    ref="dialogs"
    :visible="visible"
    @closed="closed"
    width="500px"
    class="selectSensor"
  >
    <el-form
      :model="form"
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      size="small"
    >
      <el-row class="add-info">
        <el-col :span="24">
          <el-form-item prop="rejectReason" label="">
            <el-input
              type="textarea"
              style="width: 100%"
              v-model="form.rejectReason"
              maxlength="1000"
              placeholder="请输入"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="btn-group">
      <btn type="confirm" @click="confirm" :loading="loading"></btn>
      <btn type="close" @click="closed"></btn>
    </div>
  </dialog-popup>
</template>

<script>
  export default {
    name: 'Overrule',
    components: {},
    data() {
      return {
        visible: false,
        loading: false,
        form: {
          status: '2',
          rejectReason: ''
        },
        rules: {
          rejectReason: [
            {
              required: true,
              message: '请输入',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    methods: {
      show() {
        this.visible = true;
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return {
            ...this.form
          };
        }
      },
      async confirm() {
        let form = await this.validForm();
        this.$emit('success', '2', form);
        this.visible = false;
      },
      closed() {
        this.visible = false;
        this.$refs.baseForm.resetFields();
      }
    },
    mounted() {}
  };
</script>
<style lang="scss" scoped>
  .btn-group {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    margin-bottom: -20px;
  }
</style>
