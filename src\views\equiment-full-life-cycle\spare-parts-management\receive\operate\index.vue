<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <plan-cycle ref="info" :initData="detail"></plan-cycle>
      <p class="el-base-title">备件列表</p>
      <sel-asset ref="asset" :detail="detail"></sel-asset>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"></btn>
      <btn type="cancel" @click="closed" :loading="loading"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import PlanCycle from './plan-cycle.vue';
  import SelAsset from './sel-asset.vue';

  import {
    editSparePartsReceiveApi,
    addSparePartsReceiveApi,
    getSparePartsReceiveViewApi
  } from '@/api/equiment-full-life-api/spare-parts';
  export default {
    name: 'AddDevice',
    components: {
      PlanCycle,
      SelAsset
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {},
        whorehouse: [],
        warehouseId: undefined
      };
    },
    watch: {},
    methods: {
      show(row) {
        this.visible = true;
        this.edit = !!row.id;
        this.eqId = row.id;
        if (row.id) {
          this.getDetail(row.id);
        }
      },
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getSparePartsReceiveViewApi(id);
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.$refs['info'].resetForm();
        this.$refs['asset'].resetForm();
        this.detail = {};
      },

      async submit() {
        let params = await this.$refs['info'].validForm();
        let asset = await this.$refs['asset'].validForm();
        if (params && asset) {
          this.edit
            ? await this.editSubmit({
                ...params,
                itemList: asset,
                id: this.edit ? this.eqId : undefined
              })
            : await this.save({
                ...params,
                itemList: asset
              });
        }
      },
      // 提交
      async editSubmit(params) {
        this.loading = true;
        try {
          await editSparePartsReceiveApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 提交
      async save(params) {
        this.loading = true;
        try {
          await addSparePartsReceiveApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  .remark {
    margin-left: 20px;
    color: red;
    font-weight: 100;
    font-size: 14px;
  }
</style>
