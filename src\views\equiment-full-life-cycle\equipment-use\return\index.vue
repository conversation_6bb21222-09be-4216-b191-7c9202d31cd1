<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="设备领用台账" name="2"></el-tab-pane>
      <el-tab-pane label="归还记录" name="3"></el-tab-pane>
    </el-tabs>
    <div class="top-info">
      <search ref="search" :activeName="activeName" @search="onsubmit" />
      <el-button
        icon="el-icon-check"
        v-if="permission['return-device-return'] && activeName === '2'"
        type="primary"
        size="small"
        :disabled="!allPageSelect.length"
        @click="returnDevice()"
        >批量归还</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="customizeId"
      size="small"
      height="calc(100% - 200px)"
      border
      ref="table"
      @select="handleCheckBox"
      @select-all="handleSelectAll"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        align="center"
        v-if="activeName === '2'"
        key="selection"
        type="selection"
        width="55"
      >
      </el-table-column>
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        key="no"
        label="设备编号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.deviceCode || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        key="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <el-button
            class="device-name"
            @click="deviceDetail(scope.row)"
            type="text"
          >
            {{ scope.row.deviceName || '-' }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column
        key="model"
        label="规格型号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.deviceModel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        key="returnTime"
        label="归还时间"
        v-if="activeName === '3'"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.backTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="receiptNumber2"
        label="领用单号"
        v-if="activeName === '3'"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <el-button
            class="tag_ellipsis"
            @click="orderDetail(row)"
            type="text"
            >{{ row.orderNo || '-' }}</el-button
          ></template
        >
      </el-table-column>
      <el-table-column
        key="chargeUserName"
        label="领用日期"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"
          >{{ row.receiveStartDate || '-' }}~{{
            row.receiveEndDate || '-'
          }}</template
        >
      </el-table-column>
      <el-table-column
        key="equipmentCount"
        label="到期"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i
            :style="`color:${expireStatusColor(
              row.expiredName
            )};font-size:18px`"
            >●</i
          >
          {{ row.expiredName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="领用人"
        align="center"
        v-if="activeName === '3'"
        show-overflow-tooltip
        key="recipients"
      >
        <template v-slot="{ row }">{{ row.applyUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        label="领用人所属部门"
        align="center"
        v-if="activeName === '3'"
        show-overflow-tooltip
        key="departmentRecipientBelongs"
      >
        <template v-slot="{ row }">{{ row.belongDeptName || '-' }}</template>
      </el-table-column>
      <el-table-column
        label="申请人"
        align="center"
        v-if="activeName === '2'"
        show-overflow-tooltip
        key="applicant"
      >
        <template v-slot="{ row }">{{ row.applyUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        label="所属部门"
        align="center"
        v-if="activeName === '2'"
        show-overflow-tooltip
        key="affiliation"
      >
        <template v-slot="{ row }">{{ row.belongDeptName || '-' }}</template>
      </el-table-column>
      <el-table-column
        label="领用单号"
        align="center"
        v-if="activeName === '2'"
        show-overflow-tooltip
        key="receiptNumber"
      >
        <template v-slot="{ row }">
          <el-button
            class="tag_ellipsis"
            @click="orderDetail(row)"
            type="text"
            >{{ row.orderNo || '-' }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        key="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="createTime"
        label="创建时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.createTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        key="updateTime"
        label="更新时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.updateTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        label="操作"
        key="operation"
        v-if="activeName === '2'"
        align="center"
        width="80"
        fixed="right"
      >
        <!--         0 =  未开始  1= 执行中 2 = 已完成 3=已终止   -->
        <template v-slot="{ row }">
          <el-button
            type="text"
            size="small"
            @click="returnDevice(row)"
            v-if="permission['return-device-return']"
            >归还</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <detail-index ref="detailIndex"></detail-index>
    <ledger-detail ref="ledgerDetail"></ledger-detail>
    <requisitionDetail ref="requisitionDetail"></requisitionDetail>
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import requisitionDetail from '../requisition/detail/index.vue'; // 查看详情页面
  import Search from './search';
  import Pagination from '@/components/pagination';
  import LedgerDetail from '@/components/ledger-detail';
  import { convertFileUrl } from '@/util/util';
  import { recordPage, back } from '@/api/equiment-full-life-api/acceptance.js';
  import { mapGetters } from 'vuex';
  export default {
    name: 'returnList',
    components: {
      Search,
      DetailIndex,
      Pagination,
      LedgerDetail,
      requisitionDetail
    },
    props: {},
    data() {
      return {
        convertFileUrl,
        loading: false,
        total: 0,
        list: [{}],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {},
        allPageSelect: [],
        activeName: '2'
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      // 设备台账 设备状态状态颜色
      expireStatusColor(val) {
        switch (val) {
          case '未到期':
            return '#35C24B';
          case '已到期':
            return '#EE7C11';
        }
      },
      orderDetail(row) {
        this.$refs['requisitionDetail'].show(row.id);
      },
      deviceDetail(row) {
        this.$refs.ledgerDetail.show(row.deviceId);
      },
      // 归还
      async returnDevice(row) {
        await this.confirm('是否确认归还？');
        let equipmentIds = row
          ? [row.deviceId]
          : this.allPageSelect.map((item) => item.deviceId);
        this.fetchReturn({ equipmentIds });
      },
      async fetchReturn(params) {
        this.loading = true;
        try {
          await back(params);
          this.setPage();
          this.$message.success('操作成功');
          await this.getList();
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      setPage() {
        let rowsLength = (this.allPageSelect || []).length;
        let length = this.list.length;
        if (rowsLength && rowsLength >= length) {
          this.searchParams.current = 1;
        } else {
          if (length === 1) {
            this.searchParams.current = 1;
          }
        }
      },
      handleClick() {
        this.$refs.search.reset();
        // this.searchParams = {
        //   current: 1,
        //   size: 10
        // };
        // this.getList();
      },
      handleCheckBox(rows, row) {
        let key = 'deviceId';
        if (rows.includes(row)) {
          this.allPageSelect.push(row);
        } else {
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item[key] !== row[key]
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        let key = 'deviceId';
        if (rows.length) {
          rows.forEach((row) => {
            if (!this.allPageSelect.find((item) => item[key] === row[key])) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.list.forEach((row) => {
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item[key] !== row[key]
            );
          });
        }
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await recordPage({
            ...this.searchParams,
            status: this.activeName
          });
          // customizeId
          let records = res.data.data.records || [];
          records.forEach((item, index) => {
            item.customizeId = item.deviceId + index;
          });
          this.list = records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      assign(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.assign.show(obj);
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }

  .tag_ellipsis {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
