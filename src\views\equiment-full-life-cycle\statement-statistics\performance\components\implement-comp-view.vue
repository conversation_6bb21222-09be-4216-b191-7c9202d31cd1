<template>
  <div class="comp-wrapper">
    <!-- 搜索 -->
    <implement-comp-search
      :s-type="sType"
      @search="search"
      :searchTime="searchTime"
      num="second"
      v-bind="$attrs"
    ></implement-comp-search>
    <!--    统计方式：-->
    <section style="display: flex; justify-content: space-between">
      <el-radio-group v-model="sType" size="small" @input="input">
        <!-- 按照部门和按照人员是在点检、润滑、保养、检修类型-->
        <span
          v-if="
            orderType === 'INSPECT_ORDER' ||
            orderType === 'MAINTAIN_ORDER' ||
            orderType === 'OVERHAUL_ORDER' ||
            orderType === 'LUBRICATE_ORDER'
          "
        >
          <el-radio-button label="DEPT">按部门统计 </el-radio-button>
          <el-radio-button label="USER">按人员统计 </el-radio-button>
        </span>
        <!--外委维修、内部维修：统计方式为两种“按维修人员统计”，“按设备统计”，页面逻辑与上述内容一致；-->

        <el-radio-button label="USER" v-if="orderType === 'INTERNAL_REPAIR'"
          >按维修人员统计
        </el-radio-button>
        <el-radio-button label="USER" v-if="orderType === 'EXTERNAL_REPAIR'"
          >按跟进人员统计
        </el-radio-button>
        <!--这个是所有的类型-->
        <el-radio-button label="EQUIPMENT">按设备统计 </el-radio-button>
      </el-radio-group>
      <span>
        <el-button
          icon="el-icon-arrow-left"
          type="primary"
          plain
          size="small"
          @click="back"
          >返回</el-button
        ></span
      >
    </section>
    <!-- 表格 -->
    <el-table
      v-loading="loading"
      ref="table"
      class="table"
      :data="listData"
      size="small"
      height="calc(100% - 150px)"
      border
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>

      <!--       按照部门筛选-->
      <el-table-column
        v-if="sType === 'DEPT'"
        prop="name"
        label="部门名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.name || '-' }}
        </template>
      </el-table-column>
      <!--       员工姓名-->
      <el-table-column
        v-if="sType === 'USER'"
        prop="name"
        label="员工姓名"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.name || '-' }}
        </template>
      </el-table-column>
      <!--       按照设备统计-->
      <el-table-column
        v-if="sType === 'EQUIPMENT'"
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        v-if="sType === 'EQUIPMENT'"
        prop="equipmentSn"
        label="SN编码"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="orderNum"
        label="工单总数"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="completeNum"
        label="完成数量"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="unCompleteNum"
        label="未完成数量"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        sortable
        prop="completeRate"
        label="工单完成率"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ (Number(row.completeRate) * 100).toFixed(2) }}%
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="{ row }">
          <el-button type="text" @click="view(row)" size="small"
            >查 看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import { getImplementGroupList } from '@/api/equiment-full-life-api/statement-statistics.js';
  import DeptDialog from '@/components/dept-dialog/index.vue';
  import AssetList from '@/views/equiment-full-life-cycle/components/select-asset-dialog/index.vue';
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import implementCompSearch from '@/views/equiment-full-life-cycle/statement-statistics/performance/components/implement-comp-search.vue';
  export default {
    components: { RecipientDialog, AssetList, DeptDialog, implementCompSearch },
    props: {
      //  二级页面必填参数
      orderType: {
        type: String,
        default: undefined
      },
      //  记录计划执行率首页的时间
      searchTime: {
        type: Array,
        default: () => {
          return [];
        }
      },
      // 查询的类型(人员/部门/设备)
      searchType: {
        type: String,
        default: undefined
      }
    },
    data() {
      return {
        sType: 'DEPT', // 统计方式 DEPT，USER,EQUIPMENT
        loading: false,
        total: 0,
        searchParams: {
          current: 1,
          size: 10
        },
        // 列表
        listData: []
      };
    },
    watch: {},
    created() {
      this.$nextTick(() => {
        this.defaultType(this.orderType);
      });
    },
    methods: {
      back() {
        this.$emit('compView', {
          comp: 'Implement'
        });
      },
      //  点击搜索的时候
      search(form) {
        this.searchParams.current = 1;
        this.searchParams = { ...this.searchParams, ...form };
        this.$nextTick(() => {
          this.getList();
        });
      },
      //  看看上来应该默认是选择什么类型筛选
      defaultType(orderType) {
        if (this.searchType) {
          // 内部维修/外委维修没有部门查询
          if (
            ['INTERNAL_REPAIR', 'EXTERNAL_REPAIR'].includes(orderType) &&
            this.searchType === 'DEPT'
          ) {
            this.sType = 'USER';
          } else {
            this.sType = this.searchType;
          }
        } else {
          if (
            orderType === 'INSPECT_ORDER' ||
            orderType === 'MAINTAIN_ORDER' ||
            orderType === 'OVERHAUL_ORDER' ||
            orderType === 'LUBRICATE_ORDER'
          ) {
            this.sType = 'DEPT';
          } else {
            this.sType = 'USER';
          }
        }
      },
      //  切换统计类型的时候
      input() {
        this.searchParams.current = 1;
        this.getList();
      },
      //  枚举各种类型 - 对应的组件
      enumType() {
        return [
          {
            comp: 'InspectList',
            type: 'INSPECT_ORDER'
          },
          { comp: 'MaintenanceList', type: 'MAINTAIN_ORDER' },
          { comp: 'OveralList', type: 'OVERHAUL_ORDER' },
          { comp: 'LubricationList', type: 'LUBRICATE_ORDER' },
          { comp: 'InternalList', type: 'INTERNAL_REPAIR' },
          { comp: 'EnternalList', type: 'EXTERNAL_REPAIR' }
        ];
      },
      // 枚举搜索统计，如果this.orderType === 'INSPECT_ORDER',并且this.sType === 'DEPT'，就返回receiveDept ，如果this.Type==='USER',key 返回receiveUser等

      // 点击查看
      view(row) {
        //  下面是返回具体展示哪一类型的页面
        const orderType = row.orderType;
        let comp = this.enumType().find((item) => {
          return item.type === orderType;
        }).comp;
        //  更新计划完成率页面
        this.$emit('compView', {
          comp: comp,
          paramsObj: {
            ...row,
            sType: this.sType,
            startDate: this.searchParams.startDate,
            endDate: this.searchParams.endDate
          }
        });
      },
      query() {
        this.searchParams.current = 1;
        this.getList();
      },

      refresh() {
        this.getList();
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          const params = {
            ...this.searchParams,
            statisticsDimension: this.sType,
            orderType: this.orderType
          };

          let res = await getImplementGroupList(params);
          this.listData = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .comp-wrapper {
    height: 100%;

    .summary-wrapper {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-bottom: 10px;
    }
  }

  ::v-deep {
    .el-radio-group {
      // margin-bottom: 10px;
      padding-bottom: 10px;
    }
  }
</style>
