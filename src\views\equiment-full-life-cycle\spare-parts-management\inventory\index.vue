<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <section>
        <el-button
          v-if="permission['spare-inventory-plan-add']"
          icon="el-icon-plus"
          type="primary"
          size="small"
          @click="operate"
          >新增</el-button
        >
      </section>
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        align="center"
        prop="no"
        label="计划单号"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.no || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="name"
        label="盘点名称"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="warehouse"
        label="盘点仓库"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="startDate"
        label="盘点日期"
        align="center"
        show-overflow-tooltip
        width="200px"
      >
        <template v-slot="{ row }"
          >{{ row.startDate || '-' }} ~ {{ row.endDate || '-' }}</template
        >
      </el-table-column>
      <el-table-column
        prop="completeTime"
        align="center"
        label="完成时间"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.completeTime || '-' }}</template>
      </el-table-column>

      <el-table-column
        prop="inventoryUserName"
        align="center"
        label="盘点人员"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.inventoryUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="inventory"
        label="已盘/全部"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.inventory }} / {{ scope.row.totalQuantity || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="statusName"
        label="状态"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${inventoryStatusColor(row.status)};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        align="center"
        label="创建人"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.createUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        align="center"
        label="创建时间"
        width="150"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        align="center"
        label="更新人"
        show-overflow-tooltip
        width="100"
      >
        <template v-slot="{ row }"> {{ row.updateUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        align="center"
        width="150"
        label="更新时间"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" fixed="right">
        <template v-slot="{ row }">
          <section class="cell-operate-class">
            <el-button
              v-if="permission['spare-inventory-plan-view']"
              type="text"
              size="small"
              @click="detail(row)"
              >查看</el-button
            >
            <!-- 执行中可以停止执行 -->
            <el-button
              v-if="
                Number(row.status) !== 2 &&
                permission['spare-inventory-plan-start-stop']
              "
              :class="row.status === 0 ? 'green-btn' : 'danger-btn'"
              type="text"
              size="small"
              @click="sas(row)"
              >{{ Number(row.status) === 0 ? '启动' : '终止' }}
            </el-button>

            <el-popconfirm
              v-if="
                Number(row.status) === 0 &&
                permission['spare-inventory-plan-delete']
              "
              :title="`是否确定删除${row.no}盘点计划？`"
              @confirm="() => handleDelete(row)"
            >
              <el-button
                class="danger-btn"
                slot="reference"
                type="text"
                size="small"
                style="margin-left: 10px"
                >删除</el-button
              >
            </el-popconfirm>
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex"></detail-index>
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import { convertFileUrl } from '@/util/util';
  import {
    getSparePartsCheckListApi,
    delSparePartsCheckApi,
    startSparePartsCheckApi,
    stopSparePartsCheckApi
  } from '@/api/equiment-full-life-api/spare-parts';

  import { mapGetters } from 'vuex';
  import { inventoryStatusColor } from '@/views/equiment-full-life-cycle/spare-parts-management/util';
  export default {
    name: 'MaintenanceList',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination
    },
    props: {},
    computed: {
      ...mapGetters(['permission', 'userInfo'])
    },
    data() {
      return {
        inventoryStatusColor,
        radio2: 'all',
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        totalObj: {}
      };
    },

    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      //  点击去盘点
      inventory() {},
      //  启动终止
      sas(row) {
        this.$confirm(
          `是否确定${Number(row.status) === 0 ? '启动' : '终止'}${
            row.no
          }盘点计划？`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
          .then(async () => {
            try {
              this.loading = true;
              //  启动终止接口 startSparePartsCheckApi /  stopSparePartsCheckApi
              Number(row.status) === 0
                ? await startSparePartsCheckApi({
                    no: row.no
                  })
                : await stopSparePartsCheckApi({ no: row.no });
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            console.log('操作失败');
          });
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      // //  获取统计数据
      // async getTotalList() {
      //   this.loading = true;
      //   try {
      //     let res = await getSparePartsCheckListStaticApi({
      //       module: 'SPARE_PARTS'
      //     });
      //     this.totalObj = res.data.data;
      //   } catch (e) {
      //     this.loading = false;
      //   }
      //   this.loading = false;
      // },
      async getList() {
        this.loading = true;
        try {
          let res = await getSparePartsCheckListApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row);
      },
      async handleDelete(row) {
        this.loading = true;
        try {
          await delSparePartsCheckApi({ id: row.id });
          if (this.list.length === 1) {
            this.searchParams.current = 1;
          }
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          await this.getList();
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
