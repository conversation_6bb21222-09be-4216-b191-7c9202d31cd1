## 快速统一框架
## ( 前端使用的版本是 统一快速开发框架 v1.4.0)

1. 运行步骤：

```
// 安装依赖
cnpm install
// 开发环境
npm run serve
// 生产环境
npm run build
// 测试环境
npm run build:test
// 演示环境
npm run build:show
```

## v1.0.1版本操作说明

### 修改基础容器组件 basic-container

1、添加 autoHeight 是否在前端框架内容显示区显示自动高度
2、添加 fullHeight 是否显示全屏自动高度
3、添加 zoomHeight 在前端框架内容显示区显示自动高度减少高度

### 添加图片预览组件 ImagePreview

1、src String 多个地址用逗号分割
2、with、height 设置宽高

### 添加指令权限 directive/permission

1、v-hasPermi 根据菜单权限判断 实例：v-hasPermi="['notice_add']"
2、v-hasRole 根据角色权限判断 实例: v-hasRole="['administrator']"

### 数据字典使用步骤

1、在字典页面查找字典对应 code 值；
2、在使用页面添加对应字典 code 值 实例：
   系统字典：systemDicts: ["sex", "notice"]
   业务字典：serviceDicts: ["001"]
3、使用对应字典 实例：
   systemDicts.type.sex
   serviceDicts.type['001']

```
<el-select v-model="form.sex" placeholder="请选择性别">
        <el-option
          v-for="dict in systemDicts.type.sex"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
      <el-select v-model="form.warehouse" placeholder="请选择仓库">
        <el-option
          v-for="dict in serviceDicts.type['001']"
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
        ></el-option>
      </el-select>
```
### 优化本地代码格式化
1、项目根目录新增.prettierrc.json
singleQuote：是否格式化单引号； semi：结尾显示分号； trailingComma: 结尾逗号
```
{
  "singleQuote": true,
  "semi": false,
  "trailingComma": "none"
}
```
### 【新增】 版本管理

修改版本号，除了修改package.json 同时还要修改 public/version.js 缺一不可

### 【新增】 动态路由配置

#### 目录
必填：菜单名称、路由地址、菜单图标、菜单类型（菜单）、
菜单编号、菜单别名、菜单状态（显示）、是否缓存（否）、菜单排序
#### 菜单
必填：菜单名称、路由地址、菜单图标、菜单编号、菜单类型（菜单）、菜单别名、
新窗口（是）、组件名称（全局唯一）、菜单状态、激活地址（配置动态路由时填项：高亮作用）、
组件地址（viws文件夹下的地址，且不需要.vue后缀），是否缓存，菜单排序
#### 按钮
必填：菜单名称, 菜单图标，菜单编号、菜单类型（按钮）、菜单别名、菜单显示（隐藏）、
是否缓存（不缓存）、菜单排序

#### 菜单跳转
1、 menuCode：pre-index                          menuName：前期资料管理
2、 menuCode：requisition                        menuName：设备领用
3、 menuCode：return                             menuName：设备归还
4、 menuCode：e-i-standard                       menuName：点巡检标准
5、 menuCode：e-i-plan                           menuName：点巡检计划
6、 menuCode：inspection-work-order              menuName：点巡检工单
7、 menuCode：e-e-standard                       menuName：保养标准
8、 menuCode：maintence-plan                     menuName：保养计划
9、 menuCode：maintenance-work-order             menuName：保养工单
10、menuCode：lubrication-way                    menuName：润滑方式
11、menuCode：lubrication-type                   menuName：油品类型
12、menuCode：lubrication-standard               menuName：润滑标准
13、menuCode：lubrication-plan                   menuName：润滑计划
14、menuCode：lubrication-work-order             menuName：润滑工单
15、menuCode：overhaul-way                       menuName：检修方式
16、menuCode：overhaul-standard                  menuName：检修标准
17、menuCode：overhaul-plan                      menuName：检修计划
18、menuCode：overhaul-work-order                menuName：检修工单
19、menuCode：repair-internal                    menuName：内部维修
20、menuCode：repair-external                    menuName：外委维修
21、menuCode：fault-index                        menuName：故障缺陷列表
22、menuCode：fault-case                         menuName：故障案例库
23、menuCode：device-inventory-plan              menuName：设备盘点计划
24、menuCode：inventory-order                    menuName：设备盘点工单
25、menuCode：changes-apply                      menuName：变更申请
26、menuCode：changes-examine                    menuName：变更审批
27、menuCode：equipment-scrap                    menuName：设备报废
28、menuCode：use-registration                   menuName：检验登记
29、menuCode：registration-lease                 menuName：租赁归还登记
30、menuCode：spare-parts-put-in                 menuName：入库管理
31、menuCode：spare-parts-receive                menuName：领用管理
32、menuCode：spare-parts-out-of                 menuName：出库管理
33、menuCode：spare-parts-inventory              menuName：盘点计划
34、menuCode：spare-parts-inventory-order        menuName：盘点工单
35、menuCode：check-in-stock                     menuName：库存查询
36、menuCode：spare-parts-ledger                 menuName：备品备件字典管理
37、menuCode：storeroom                          menuName：库房管理
38、menuCode：profile-index                      menuName：通用设备资料
39、menuCode：s_inspect                          menuName：设备点巡检统计
40、menuCode：s_maintain                         menuName：设备保养统计
41、menuCode：s_lubricate                        menuName：设备润滑统计
42、menuCode：s_repair                           menuName：设备维修统计
43、menuCode：s_consumable                       menuName：备品备件消耗统计
44、menuCode：s_performance                      menuName：设备绩效管理



