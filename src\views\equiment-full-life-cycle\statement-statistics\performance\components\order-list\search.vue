<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="工单编号" prop="no">
        <el-input
          style="width: 100%"
          v-model.trim="form.no"
          placeholder="请输入工单编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item label="工单状态" prop="status">
        <el-select
          v-model="form.status"
          style="margin-left: 20px"
          placeholder="请选择工单状态"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['order_status'].filter(
              (item) => item.value !== '7'
            )"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择部门-->
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    components: {},
    props: {
      orderType: {
        type: String,
        default: 'order'
      }
    },
    serviceDicts: ['order_status', 'repair_status'],
    data() {
      return {
        form: {
          no: undefined,
          status: undefined
        },
        option: []
      };
    },
    mounted() {},
    methods: {
      returnOption() {
        return this.orderType === 'repair'
          ? this.serviceDicts.type['repair_status'].filter(
              (item) => item.value !== '7'
            )
          : this.serviceDicts.type['order_status'].filter(
              (item) => item.value !== '7'
            );
      },
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      // 清空并重新设置字段
      setFields() {
        this.$refs['search'].resetFields();
        // const { field, value } = fields;
        // this.form[field] = value;
        this.submit();
      },
      submit() {
        let params = {
          ...this.form
        };
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
