<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="110px"
      label-position="right"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="盘点名称" prop="name">
            <el-input
              v-model.trim="form.name"
              placeholder="请输入盘点名称"
              clearable
              maxlength="50"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="盘点日期" prop="time">
            <el-date-picker
              v-model="form.time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              clearable
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label=" 备注" prop="remark">
            <el-input
              placeholder="请输入备注"
              v-model.trim="form.remark"
              :maxlength="200"
              clearable
              show-word-limit
              autosize
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'Inventory',
    props: {
      initData: {
        type: Object,
        default: () => {}
      }
    },

    data() {
      return {
        pickerOptions: {
          disabledDate(time) {
            // 禁用今天的日期及之前的日期
            return time.getTime() <= Date.now(); // 86400000 是一天的毫秒数
          }
        },
        form: {
          name: undefined,
          remark: undefined,
          time: [],
          isInventoryIdle: 2
        },
        edit: false,
        rules: {
          name: [
            {
              required: true,
              message: '请输入盘点名称',
              trigger: 'change'
            }
          ],
          time: [
            { required: true, message: '请选择盘点日期', trigger: 'change' }
          ],
          isInventoryIdle: [
            {
              required: true,
              message: '请选择无部门设备是否盘点',
              trigger: 'change'
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    created() {},
    methods: {
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },

      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          let params = {
            name: this.form.name,
            remark: this.form.remark,
            startDate: this.form.time[0],
            endDate: this.form.time[1],
            isInventoryIdle: this.form.isInventoryIdle
          };
          return params;
        } else {
          return false;
        }
      },
      resetForm() {
        this.form.time = [];
        this.$refs['baseForm'].resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-input-number {
        width: 100%;

        .el-input__inner {
          text-align: left;
        }
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
