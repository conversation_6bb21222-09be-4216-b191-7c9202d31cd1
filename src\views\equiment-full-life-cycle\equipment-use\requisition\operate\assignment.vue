<template>
  <dialog-drawer
    :title="'分配'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <plan-cycle ref="info" :details="detail"></plan-cycle>
      <p class="el-base-title">设备明细</p>
      <sel-asset ref="asset" :timeObj="timeObj" :detail="detail"></sel-asset>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import PlanCycle from '../detail/base-info.vue';
  import SelAsset from './sel-asset.vue';
  import {
    devicereceiveDetail,
    distribution
  } from '@/api/equiment-full-life-api/acceptance';
  export default {
    name: 'AddDevice',
    components: {
      PlanCycle,
      SelAsset
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {
          image: []
        },
        timeObj: {}
      };
    },
    watch: {},
    methods: {
      //  基本信息时间改变
      changeTime(obj) {
        console.log('时间', obj);
        this.timeObj = { ...obj };
      },
      async getDetail() {
        this.loading = true;
        try {
          const res = await devicereceiveDetail({ id: this.eqId });
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      show(row) {
        this.visible = true;
        this.edit = !!row.id;
        this.eqId = row.id;
        this.getDetail();
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.$refs['asset'].resetForm();
        this.detail = {};
        this.timeObj = {};
      },

      async submit() {
        let asset = this.$refs['asset'].validForm();
        let equipmentIds = asset.map((item) => item.id);
        await this.save({
          equipmentIds,
          receiveId: this.eqId
        });
      },

      // 提交计划
      async save(params) {
        this.loading = true;
        try {
          await distribution(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
