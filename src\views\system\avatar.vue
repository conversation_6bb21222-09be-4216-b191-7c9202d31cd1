<template>
  <div class="avatar-wrap">
    <el-image
      class="preview-src-img"
      :src="currentImageList[0]"
      ref="image__preview"
      :preview-src-list="currentImageList"
    >
    </el-image>
    <div v-if="form.avatar" class="el-upload-list el-upload-list--picture-card">
      <div class="el-upload-list__item">
        <img class="avatar" :src="form.avatar" />
        <label class="el-upload-list__item-status-label">
          <i class="el-icon-upload-success el-icon-check" />
        </label>
        <span class="el-upload-list__item-actions">
          <span @click="handlePreview()">
            <i class="el-icon-zoom-in" />
          </span>
          <span class="el-upload-list__item-delete">
            <i class="el-icon-delete" @click.stop="handleRemove()" />
          </span>
        </span>
      </div>
    </div>

    <el-upload
      v-else
      class="avatar-uploader"
      :action="action"
      :headers="hHeaders"
      list-type="picture-card"
      :show-file-list="false"
      :on-success="handleAvatarSuccess"
      :before-upload="beforeAvatarUpload"
    >
      <i class="el-icon-plus avatar-uploader-icon"></i>
      <div class="el-upload__tip" slot="tip">
        只能上传jpg/png用户头像，且不超过500kb
      </div>
    </el-upload>
  </div>
</template>
<script>
  import website from '@/config/website';
  import { Base64 } from 'js-base64';
  export default {
    props: {
      form: {
        type: Object,
        default: () => {}
      },
      action: {
        type: String,
        default: '/api/szyk-resource/oss/endpoint/put-file-attach'
      }
    },
    computed: {
      currentImageList() {
        return [this.form.avatar];
      },
      hHeaders() {
        let headers = {
          Authorization: `Basic ${Base64.encode(
            `${website.clientId}:${website.clientSecret}`
          )}`,
          'Tenant-Id': this.$store.getters.tenantId,
          'User-Type': 'web',
          ...this.headers
        };
        headers[website.tokenHeader] = 'bearer ' + this.$store.getters.token;
        return headers;
      }
    },
    data() {
      return {
        dialogImageUrl: '',
        dialogVisible: false
      };
    },
    methods: {
      handlePreview() {
        this.$nextTick(() => {
          this.$refs.image__preview.clickHandler();
        });
      },
      handleAvatarSuccess(res, file) {
        this.form.avatar = file.response.data.link;
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg' || 'image/jpg' || 'image/png';
        const isLt2M = file.size / 1024 / 1024 < 0.48828125;
        if (!isJPG) {
          this.$message.warning('上传头像图片只能是 jpg,jpeg,png格式!');
        }
        if (!isLt2M) {
          this.$message.warning('上传头像图片大小不能超过 500kb!');
        }
        return isJPG && isLt2M;
      },

      handleRemove() {
        this.form.avatar = '';
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      }
    }
  };
</script>
<style lang="scss">
  .avatar-wrap {
    .preview-src-img {
      display: inherit;

      width: 0;
      height: 0;
    }

    .avatar {
      display: block;

      width: 100%;
      height: 100%;
    }
  }
</style>
