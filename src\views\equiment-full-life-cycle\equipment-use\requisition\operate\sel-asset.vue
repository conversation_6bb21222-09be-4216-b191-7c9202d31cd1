<template>
  <div>
    <el-button
      size="small"
      type="primary"
      style="margin-bottom: 10px"
      @click="selAsset"
      v-if="isDele"
      >+ 选择设备</el-button
    >
    <span class="tip" v-if="isDele"
      >注意！可以不选设备，由设备管理员进行分配。</span
    >
    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
      label-width="10px"
    >
      <el-table
        v-loading="loading"
        class="table"
        :data="form.list"
        border
        :header-cell-style="{ background: '#fafafa' }"
        size="small"
      >
        <el-table-column
          type="index"
          label="序号"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="code"
          label="设备编号"
          align="center"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="sn"
          label="SN编号"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.sn || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="设备名称"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="model"
          label="规格型号"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.model || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="measureUnitName"
          label="计量单位"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.measureUnitName || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="belongDeptName"
          label="归属部门"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.belongDeptName || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="responsiblePersonName"
          label="负责人"
          align="center"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.responsiblePersonName || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="model"
          label="操作"
          width="200"
          align="center"
          v-if="isDele"
        >
          <template v-slot="scope">
            <el-popconfirm title="确定删除吗？" @confirm="() => del(scope)">
              <el-button
                slot="reference"
                type="text"
                size="small"
                style="margin-left: 10px"
                >删除</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <asset-list
      ref="assetList"
      @getAssetList="getAssetList"
      selType="oiling"
      :statusList="[3, 4]"
    ></asset-list>
  </div>
</template>

<script>
  import AssetList from '../select-asset-dialog';
  import { getPartListByType } from '@/api/equiment-full-life-api/oiling';
  export default {
    name: 'DeviceBasicList',
    components: { AssetList },
    props: {
      isDele: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      //  基本信息时间对象
      timeObj: {
        type: Object,
        default: () => {
          return {};
        }
      },
      detail: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.form.list = this.detail.deviceAccountList || [];
            });
          }
        }
      },
      timeObj: {
        immediate: true,
        deep: true,
        handler(val) {
          console.log('时间改变，清空开始执行时间', val);
          //    清空所有的开始时间
          this.form.list.forEach((item) => {
            item.startTime = undefined;
          });
        }
      }
    },
    computed: {
      startOptions() {
        let startDate = new Date(this.timeObj.startTime);
        startDate.setDate(startDate.getDate() - 1);
        let endDate = new Date(this.timeObj.endTime);
        return {
          disabledDate(time) {
            return (
              time.getTime() <= startDate.getTime() ||
              time.getTime() > endDate.getTime()
            );
          }
        };
      }
    },
    data() {
      return {
        form: {
          list: []
        },
        currentSelect: [], // 目前选择的设备
        monitorList: [],
        loading: false,
        total: 0,

        //  计划的开始时间
        // startOptions: {
        //   disabledDate(time) {
        //     const startDate = new Date('2023-10-01');
        //     const endDate = new Date('2023-10-31');
        //     return (
        //       time.getTime() < startDate.getTime() ||
        //       time.getTime() > endDate.getTime()
        //     );
        //   }
        // },
        endOptions: {
          disabledDate(time) {
            return time.getTime() <= Date.now();
          }
        },
        //  请选择首次加油时间
        repeatRuleTime: [
          {
            required: true,
            message: '请选择首次加油时间',
            trigger: 'change'
          }
        ],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },

    mounted() {},
    methods: {
      del(scope) {
        this.form.list.splice(scope.$index, 1);
      },
      validForm() {
        // if (this.form.list.length === 0) {
        //   this.$message.warning('请选择设备');
        //   return;
        // }
        let params = this.form.list;
        return params;
      },
      resetForm() {
        this.form.list = [];
      },
      //  获取设备
      async getAssetList(val) {
        // let idArr = val.map((it) => it.id).join(',');
        this.currentSelect = val; // 把当前选择的设备存起来
        // await this.getMonitorList(idArr);
        this.form.list = val;
      },
      //  获取设备标准部位信息
      async getMonitorList(idArr) {
        let res = await getPartListByType({
          equipmentIds: idArr
        });
        //  设置首次加油的初始时间
        // 1.如果已经有了列表、再次请求列表的时候，将已经获取的列表和已经存在的列表做对比，将相同的取出来。
        let newList = res.data.data;

        // 步骤1: 找出两个数组中 id 重复的对象
        const duplicateIds = this.form.list.filter((listId) =>
          newList.some((item) => item.equipmentId === listId.equipmentId)
        );

        // 步骤2: 从第二个数组中移除这些重复的对象
        const filteredArray2 = newList.filter(
          (item) =>
            !duplicateIds.some(
              (nList) => item.equipmentId === nList.equipmentId
            )
        );

        // 步骤3: 将第一个数组和处理后的第二个数组合并
        const mergedArray = [...duplicateIds, ...filteredArray2];
        console.log('合并后的数组:', mergedArray);
        this.form.list = mergedArray;
        if (this.timeObj.startTime) {
          filteredArray2.forEach((item) => {
            item.startTime = this.timeObj.startTime;
          });
        } else {
          console.log('sdfsdsfd');
          this.form.list.forEach((item) => {
            item.startTime = undefined;
          });
        }
      },
      selAsset() {
        // 将设备找出来
        const list = this.form.list;
        this.$refs['assetList'].show(list);
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  .tip {
    padding-left: 10px;
    color: #f59a23;
    font-size: 14px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }

  .operateBtn {
    margin-bottom: 15px;
  }

  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
