import request from '@/router/axios';

// 设备领用分页
export const devicereceiveListPage = (params) => {
  return request({
    url: '/api/szyk-simas/receive/devicereceive/page',
    method: 'get',
    params
  });
};
// 新增或修改
export const addOrEditAccount = (data) => {
  return request({
    url: '/api/szyk-simas/receive/devicereceive/save',
    method: 'post',
    data
  });
};
// 详情
export const devicereceiveDetail = (params) => {
  return request({
    url: '/api/szyk-simas/receive/devicereceive/detail',
    method: 'get',
    params
  });
};

// 设备的分配
export const distribution = (data) => {
  return request({
    url: '/api/szyk-simas/receive/devicereceive/distribution',
    method: 'post',
    data
  });
};
// 设备的归还
export const back = (data) => {
  return request({
    url: '/api/szyk-simas/receive/devicereceive/back',
    method: 'post',
    data
  });
};
// 设备领用台账分页
export const recordPage = (params) => {
  return request({
    url: '/api/szyk-simas/receive/devicereceive/recordPage',
    method: 'get',
    params
  });
};
