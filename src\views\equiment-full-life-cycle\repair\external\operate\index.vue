<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    size="80%"
    class="device-add"
  >
    <section v-loading="loading">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-position="right"
        label-suffix="："
        label-width="130px"
      >
        <p class="el-base-title">工单信息</p>
        <el-row>
          <el-col :span="8">
            <el-form-item label="维修单号">
              <span>{{ form.no || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备编号">
              <span>{{ form.code || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SN编号">
              <span>{{ form.sn || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备名称" prop="equipmentId" ref="equipment">
              <el-input
                v-model="form.equipmentName"
                readonly
                placeholder="请选择设备名称"
                @focus.prevent="onChooseDeviceClick"
              >
                <template slot="append">
                  <i
                    class="el-icon-circle-close"
                    @click="
                      () => {
                        form.equipmentId = undefined;
                        form.equipmentName = undefined;
                      }
                    "
                  ></i>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备位置">
              <span>{{ form.locationPath || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备部位" prop="monitorId">
              <el-select
                v-loading="monitorListLoading"
                v-model="form.monitorId"
                style="width: 100%"
                filterable
                allow-create
                @change="handleSiteChange"
              >
                <el-option
                  v-for="item in monitorList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="故障缺陷名称" prop="faultName">
              <el-input
                v-model="form.faultName"
                maxlength="50"
                placeholder="请输入故障缺陷名称"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障缺陷类型" prop="repairType">
              <el-select v-model="form.repairType" style="width: 100%">
                <el-option
                  v-for="dict in serviceDicts.type['repair_type']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.repairType == 1">
            <el-form-item label="异常等级" prop="faultLevel">
              <el-select v-model="form.faultLevel" style="width: 100%">
                <el-option
                  v-for="item in serviceDicts.type['defect_level']"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- </el-row>
        <el-row> -->
          <el-col :span="8">
            <el-form-item label="跟进人" prop="followUser">
              <el-select
                v-loading="userListLoading"
                v-model="form.followUser"
                filterable
                style="width: 100%"
                @change="handlefollowUserChange"
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.realName"
                  :value="user.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="跟进人电话" prop="followUserTel">
              <el-input
                v-model="form.followUserTel"
                maxlength="11"
                placeholder="请输入跟进人电话"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计完成时间" prop="completeTime">
              <el-date-picker
                style="width: 100%"
                type="datetime"
                v-model="form.completeTime"
                value-format="yyyy-MM-dd HH:mm:00"
                placeholder="请选择预计完成时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="承修单位" prop="supplierId">
              <el-select
                v-model="form.supplierId"
                filterable
                style="width: 100%"
                placeholder="请选择承修单位"
                clearable
              >
                <el-option
                  v-for="item in supplierList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="问题描述" prop="problemComment">
              <el-input
                type="textarea"
                v-model="form.problemComment"
                maxlength="200"
                placeholder="请输入问题描述"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="attachList" label="上传照片">
              <upload-img
                v-model="form.attachList"
                placeholder="上传照片"
                :limit="9"
                formatLimit="jpeg,png,jpg"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <p class="el-base-title" style="margin-top: 0">来源信息</p>
        <el-row>
          <el-col :span="8">
            <el-form-item label="报修人" prop="reportUser">
              <el-select
                v-loading="userListLoading"
                v-model="form.reportUser"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.realName"
                  :value="user.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报修人电话" prop="tel">
              <el-input
                v-model="form.tel"
                maxlength="11"
                placeholder="请输入报修人电话"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>

    <asset-list
      ref="assetList"
      @on-choose="onChooseDeviceSuccess"
      selType="maintain"
      :statusList="[4]"
    ></asset-list>
    <!--    :statusList="[4]"-->
    <!--     选择人员-->
    <recipient-dialog
      ref="recipient"
      @selected="onUserSelect"
    ></recipient-dialog>
    <!-- 选择备品备件 -->
    <spare-parts-dialog
      ref="spareParts"
      module="repair"
      status="1"
      showWarehouseSearch
      @getAssetList="handlePartsSelect"
    ></spare-parts-dialog>
  </dialog-drawer>
</template>

<script>
  import {
    getPartList,
    getPlanDetail
  } from '@/api/equiment-full-life-api/maintenance';
  import AssetList from '@/views/equiment-full-life-cycle/components/select-asset-dialog/index.vue';
  import SparePartsDialog from '@/views/equiment-full-life-cycle/components/select-spare-parts-dialog';
  import { mapGetters } from 'vuex';
  import { addOrEditRepairApi } from '@/api/equiment-full-life-api/repair';
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import SparePartsTable from '@/views/equiment-full-life-cycle/components/spare-parts-table.vue';
  import dayjs from 'dayjs';
  import { getUserList } from '@/api/system/dept';
  // import UploadFile from '@/components/upload-file.vue';
  import UploadImg from '@/components/uploadImage.vue';
  import reg from '@/util/regexp';
  import { getUserInfo } from '@/api/system/user';
  import { getSupplierList } from '@/api/equiment-full-life-api/common';
  const form = {
    equipmentId: '',
    equipmentName: '',
    equipmentModel: '',
    locationPath: '',
    useDeptName: '',
    userName: '',
    // 上报时间
    reportTime: '',
    reportUser: '',
    // 上报时间
    tel: '',
    // 报修类型
    repairType: '',
    // 维修部位
    monitorId: '',
    monitorName: '',
    // 故障名称
    faultName: '',
    // 故障等级
    faultLevel: '',
    // 跟进人
    followUser: '',
    followUserName: '',
    followUserTel: '',
    // 预计完成时间
    completeTime: '',
    // 问题描述
    problemComment: '',
    supplierId: '',
    supplierName: '',
    // 备品备件
    materialList: [],
    // 附件
    attachList: []
  };
  export default {
    name: 'repair-internal-operate',
    components: {
      // UploadFile,
      UploadImg,
      RecipientDialog,
      AssetList,
      SparePartsDialog,
      SparePartsTable
    },
    serviceDicts: ['repair_type', 'defect_level'],
    data() {
      return {
        useDept: undefined, // 当前设备的部门
        visible: false,
        detail: {},
        selectedDevice: {}, // 选中的设备信息
        list: [],
        loading: false,
        userListLoading: false,
        monitorListLoading: false,
        eqId: '',
        edit: false,
        form: { ...form },
        monitorList: [],
        userList: [],
        rules: {
          equipmentId: [
            {
              required: true,
              message: '请选择设备名称',
              trigger: ['blur', 'change']
            }
          ],
          monitorId: [
            {
              required: true,
              message: '请选择设备部位',
              trigger: ['blur', 'change']
            }
          ],
          faultName: [
            { required: true, message: '请输入故障缺陷名称', trigger: 'blur' }
          ],
          faultLevel: [
            {
              required: true,
              message: '请选择异常等级',
              trigger: ['blur', 'change']
            }
          ],
          repairType: [
            {
              required: true,
              message: '请选择故障缺陷类型',
              trigger: ['blur', 'change']
            }
          ],
          followUser: [
            {
              required: true,
              message: '请选择跟进人',
              trigger: ['blur', 'change']
            }
          ],
          followUserTel: [
            {
              pattern: reg.phone,
              message: '请输入正确的手机号码',
              trigger: 'change'
            }
          ],
          completeTime: [
            {
              required: true,
              message: '请选择预计完成时间',
              trigger: ['change']
            }
          ],
          supplierId: [
            {
              required: true,
              message: '请选择承修单位',
              trigger: ['change']
            }
          ],
          problemComment: [
            { required: true, message: '请输入问题描述', trigger: 'blur' }
          ],
          reportUser: [
            { required: true, message: '请输入报修人', trigger: 'blur' }
          ],
          tel: [
            {
              pattern: reg.phone,
              message: '请输入正确的手机号码',
              trigger: 'blur'
            }
          ]
        },
        supplierList: []
      };
    },
    watch: {},
    computed: {
      ...mapGetters(['userInfo', 'permission'])
    },
    methods: {
      async getSupplierList() {
        this.loading = true;
        try {
          let res = await getSupplierList({ current: 1, size: -1 });
          this.supplierList = res.data.data.records || [];
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      async getDetail(no) {
        this.loading = true;
        try {
          const res = await getPlanDetail({ no: no });
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      async show(row = {}) {
        this.visible = true;
        this.monitorList = [];
        this.getUserList();
        this.edit = !!row.id;
        this.eqId = row.id;
        if (row.id) {
          this.getDetail(row.no);
        } else {
          this.getSupplierList();
          // 获取用户手机号
          const { data } = await getUserInfo();
          this.form.tel = data.data.phone || '';
          this.form.reportUser = data.data.id || '';
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.form.image = [];
        this.form = {
          equipmentId: '',
          equipmentName: '',
          equipmentModel: '',
          locationPath: '',
          useDeptName: '',
          userName: '',
          // 上报时间
          reportTime: '',
          reportUser: '',
          // 上报时间
          tel: '',
          // 报修类型
          repairType: '',
          // 维修部位
          monitorId: '',
          monitorName: '',
          // 故障名称
          faultName: '',
          // 故障等级
          faultLevel: '',
          // 跟进人
          followUser: '',
          followUserName: '',
          followUserTel: '',
          // 预计完成时间
          completeTime: '',
          // 问题描述
          problemComment: '',
          // 备品备件
          materialList: [],
          // 附件
          attachList: []
        };
        this.$refs['form'].resetFields();
        this.detail = {};
        this.visible = false;
      },
      async submit() {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            // const tableValid = await this.$refs.sparePartsTable.validate();
            // if (!tableValid) return;
            try {
              this.loading = true;
              const attachId =
                this.form.attachList
                  .map((item) => {
                    return item.attachId ? item.attachId : item.id;
                  })
                  .join(',') || '';
              let { faultLevel, repairType } = this.form;
              const params = {
                ...this.form,
                attachId,
                faultLevel: repairType == 1 ? faultLevel : undefined,
                bizType: 'EXTERNAL',
                reportTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
              };
              // 处理维修部位 - 判断是否是自定义部位
              const flag = this.monitorList
                .map((item) => item.id)
                .includes(params.monitorId);
              if (!flag) params.monitorId = undefined;
              // 提交数据
              await addOrEditRepairApi(params);
              this.$message.success('操作成功');
              this.$emit('success');
              this.visible = false;
              this.loading = false;
            } catch (e) {
              this.loading = false;
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      async getUserList() {
        try {
          this.userListLoading = true;
          const res = await getUserList({
            size: -1
          });
          this.userList = res.data.data.records;
          this.userListLoading = false;
        } catch (e) {
          this.userListLoading = false;
          this.monitorList = [];
        }
      },
      // 跟进人员选择
      handlefollowUserChange(val) {
        const user = this.userList.find((item) => item.id === val) || {};
        this.form.followUserTel = user.phone;
      },
      onChooseDeviceClick() {
        this.$refs.assetList.show([], true);
      },
      onChooseDeviceSuccess(device) {
        this.selectedDevice = device;
        console.log('device', device);
        this.useDept = device.useDept; //记录下设备的部门
        this.form.equipmentId = device.id;
        this.form.equipmentName = device.name;
        this.form.locationPath = device.locationPath;
        this.form.useDeptName = device.useDeptName;
        this.form.userName = device.userName;
        this.form.no = device.no;
        this.form.code = device.code;
        this.form.sn = device.sn;
        this.form.monitorId = undefined;
        this.form.monitorName = undefined;

        this.getMonitorList();
        // this.getUserList();
        this.$refs['equipment'].clearValidate();
      },
      async getMonitorList() {
        try {
          this.monitorList = [];
          this.monitorListLoading = true;
          const res = await getPartList({ equipmentId: this.form.equipmentId });
          this.monitorList = res.data.data;
          this.monitorListLoading = false;
        } catch (e) {
          this.monitorListLoading = false;
          this.monitorList = [];
        }
      },
      // 选择设备部位
      handleSiteChange(val) {
        const item = this.monitorList.find((item) => item.id === val);
        if (item) {
          this.form.monitorName = item.name;
        } else {
          this.form.monitorName = val;
        }
      },
      // 选择备品备件弹窗
      handleShowSpareParts() {
        this.$refs['spareParts'].show(this.form.materialList);
      },
      // 选择备品备件回调
      handlePartsSelect(val) {
        this.form.materialList = val;
      },
      //  选择人员
      onChooseUser() {
        this.$refs['recipient'].show();
      },
      onUserSelect(user) {
        console.log('user', user);
        this.form.followUser = user.id;
        this.form.followUserName = user.realName;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      margin-bottom: 60px;
      padding: 10px 10px 0;
    }
  }

  // /deep/.el-form-item__content {
  //   height: 32px;
  // }

  .btn-wrapper {
    margin-bottom: 12px;
  }
</style>
