<template>
  <basic-container :auto-height="true" no-scrollbar>
    <search ref="search" @search="onsubmit" />
    <el-button
      type="primary"
      size="small"
      class="el-icon-plus"
      @click="handleOperate('add', { path: '' })"
    >
      新 增</el-button
    >
    <el-button
      type="success"
      size="small"
      class="el-icon-thumb"
      @click="standardReference"
      v-if="permission.chemical_device_types_btn"
    >
      设备分类标准引用</el-button
    >
    <el-button
      :class="flag ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"
      type="warning"
      size="small"
      @click="oneClickFolding"
      >{{ flag ? ' 一键折叠' : ' 一键展开' }}</el-button
    >
    <section v-loading="loading" style="height: calc(100% - 120px)">
      <el-table
        v-if="!flag && tableData.length > 0"
        :data="tableData"
        ref="treeTable"
        style="width: 100%; margin-top: 15px; margin-bottom: 20px"
        row-key="id"
        border
        lazy
        height="calc(100%)"
        :load="tableLoad"
        :tree-props="props"
        size="small"
        highlight-current-row
      >
        <el-table-column prop="categoryName" label="类型名称" align="left">
        </el-table-column>
        <el-table-column prop="remark" label="类型编码" align="left">
          <template v-slot="{ row }">{{ row.remark || '-' }}</template>
        </el-table-column>
        <el-table-column prop="address" label="操作" align="center" width="250">
          <template v-slot="scope">
            <el-button
              type="text"
              size="small"
              link
              @click="handleOperate('add', scope.row)"
              >新增子项</el-button
            >
            <el-button
              type="text"
              size="small"
              link
              @click="handleOperate('edit', scope.row)"
              >编辑</el-button
            >
            <el-popconfirm
              title="确定删除吗？"
              @confirm="() => handleDelete(scope.row)"
            >
              <el-button
                link
                size="small"
                type="text"
                slot="reference"
                style="margin-left: 10px"
                >删除
              </el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-table
        v-if="flag && tableData.length > 0"
        :data="tableData"
        ref="treeTable"
        style="width: 100%; margin-top: 15px; margin-bottom: 20px"
        row-key="id"
        border
        height="calc(100%)"
        :tree-props="props"
        size="small"
        highlight-current-row
        default-expand-all
      >
        <el-table-column prop="categoryName" label="类型名称" align="left">
        </el-table-column>
        <el-table-column prop="remark" label="类型编码" align="left">
          <template v-slot="{ row }">{{ row.remark || '-' }}</template>
        </el-table-column>
        <el-table-column prop="address" label="操作" align="center" width="250">
          <template v-slot="scope">
            <el-button
              type="text"
              size="small"
              link
              @click="handleOperate('add', scope.row)"
              >新增子项</el-button
            >
            <el-button
              type="text"
              size="small"
              link
              @click="handleOperate('edit', scope.row)"
              >编辑</el-button
            >
            <el-popconfirm
              title="确定删除吗？"
              @confirm="() => handleDelete(scope.row)"
            >
              <el-button
                link
                size="small"
                type="text"
                slot="reference"
                style="margin-left: 10px"
                >删除
              </el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-empty
        style="height: 100%"
        :image-size="70"
        v-if="tableData.length === 0"
      ></el-empty>
    </section>
    <handle-operate ref="view" @success="onOperateSuccess"></handle-operate>
  </basic-container>
</template>

<script>
  import Search from './search';
  import Pagination from '@/components/pagination';
  import HandleOperate from './operate/index.vue';
  import {
    getDeviceTypeLazyList,
    removeDeviceType,
    getDeviceTypeTreeList
  } from '@/api/equiment-full-life-api/device-type';
  import { mapGetters } from 'vuex';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      Pagination,
      HandleOperate
    },
    props: {},
    data() {
      return {
        props: {
          children: 'children',
          hasChildren: 'hasChildren'
        },
        search: '',
        maps: new Map(),
        loading: false,
        tableData: [],
        expandAll: false,
        flag: false,
        node: undefined,
        nodeId: undefined // 点击左边的树结构，传递的id
      };
    },

    mounted() {
      this.getList();
    },
    computed: {
      ...mapGetters(['permission'])
    },

    methods: {
      // 分类标准引用
      standardReference() {
        this.$router.push({
          path: '/equiment-full-life-cycle/basic-manager/chemical-device-types'
        });
      },

      //  点击一键折叠
      async oneClickFolding() {
        //  现在一键展开，和一键折叠，会调用全树和懒加载列表接口，不需要一级一级展开了。
        this.flag = !this.flag;
        if (this.flag) {
          await this.getTreeData();
        } else {
          await this.getList();
        }
      },
      //  获取全树
      async getTreeData(params) {
        try {
          this.loading = true;
          this.tableData = [];
          const res = await getDeviceTypeTreeList(params);
          this.tableData = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },

      onsubmit(form) {
        this.categoryName = form.categoryName;
        this.$nextTick(() => {
          if (this.categoryName) {
            this.flag = true;
            // 搜索，非懒加载
            this.getTreeData({ categoryName: this.categoryName });
          } else {
            this.flag = false;
            this.getList();
          }
        });
      },

      async tableLoad(tree, treeNode, resolve) {
        this.loading = true;
        const res = await getDeviceTypeLazyList({
          parentId: tree.id
        });
        let data = res.data.data;
        if (data.length === 0) {
          this.$set(
            this.$refs.treeTable.store.states.lazyTreeNodeMap,
            tree.id,
            []
          );
        } else {
          resolve(data);
        }
        // 记录treeNode 节点
        this.maps.set(tree.id, { tree, treeNode, resolve });
        this.loading = false;
      },
      async handleDelete(row) {
        let res = await removeDeviceType({ ids: row.id });
        if (Number(res.data.data.successNumber) === 0) {
          this.$message.error(res.data.data.detailVOList[0].message);
          return;
        }
        if (Number(row.parentId) !== 0) {
          if (this.flag) {
            await this.getTreeData({ categoryName: this.categoryName });
          } else {
            await this.refreshLoad({ id: row.parentId });
          }
        } else {
          this.getList();
        }
        this.$message.success('操作成功');
      },
      handleOperate(type, row) {
        this.$refs['view'].show(type, row);
      },
      //  新增或者编辑的时候，
      onOperateSuccess(row) {
        if (this.flag) {
          this.getTreeData({ categoryName: this.categoryName });
        } else {
          this.refreshLoad(row);
        }
      },
      getList() {
        this.tableData = [];
        this.loading = true;
        getDeviceTypeLazyList({ parentId: '0' })
          .then((res) => {
            this.tableData = res.data.data;
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
      },
      // 获取map中的数据，重新加载
      refreshLoad(node) {
        if (this.maps.get(node.id)) {
          const { tree, treeNode, resolve } = this.maps.get(node.id);
          if (tree) {
            this.tableLoad(tree, treeNode, resolve);
          }
        } else {
          // 如果没有点击过懒加载，直接刷新列表
          this.getList();
        }
      }
    }
  };
</script>

<style scoped lang="scss"></style>
