<template>
  <div class="table-content">
    <div class="top-info">
      <el-button icon="el-icon-plus" type="primary" size="small" @click="add"
        >添加标准</el-button
      >
      <el-button
        v-if="form.monitorList.length > 0"
        icon="el-icon-delete"
        type="danger"
        size="small"
        @click="delAll"
        >删除所选</el-button
      >
    </div>
    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
      label-width="10px"
    >
      <el-table
        ref="point-table"
        class="table table-vertical-top"
        :data="form.monitorList"
        style="width: 100%"
        size="mini"
        border
        :header-cell-style="{ background: '#fafafa' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="monitorId" label="保养部位" align="center">
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.monitorId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="repeatRuleMonitor"
              label=" "
            >
              <el-select
                v-model="scope.row.monitorId"
                placeholder="请选择保养部位"
                clearable
              >
                <el-option
                  v-for="dict in monitorList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="standard" label="保养标准" align="center">
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.standard'"
              style="width: 100%"
              :rules="repeatRule"
              label=" "
            >
              <el-input
                placeholder="请输入保养标准"
                v-model.trim="scope.row.standard"
                :maxlength="50"
                type="textarea"
                :rows="3"
                clearable
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="method" label="保养方法" align="center">
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.method'"
              style="width: 100%"
              label=" "
              :rules="repeatRuleMethod"
            >
              <el-input
                placeholder="请输入保养方法"
                v-model.trim="scope.row.method"
                :maxlength="50"
                type="textarea"
                :rows="3"
                clearable
              />
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column prop="code" label="操作" align="center" width="70px">
          <template v-slot="scope">
            <el-button size="small" type="text" @click="del(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>
<script>
  import { getPartList } from '@/api/equiment-full-life-api/inspect';

  export default {
    name: 'PointList',
    systemDicts: ['yes_no'],
    components: {},
    props: {
      details: {
        type: Object,
        default: () => {
          return {
            maintainStandardList: []
          };
        }
      }
    },
    watch: {
      'details.equipmentId': {
        immediate: true,
        handler(val) {
          if (val) {
            this.$nextTick(async () => {
              await this.getMonitorList(val);
              let list = this.details.maintainStandardList || [];
              if (list.length > 0) {
                this.edit = true;
                // 如果已经添加了标准
                await this.setList(list);
              } else {
                this.edit = false;
                // 没有添加标准
                await this.setList(this.monitorList);
              }
            });
          }
        }
      }
    },
    data() {
      function codeRepeat(all, val) {
        //  1、本地判重
        let codeArr = all.map((it) => it.monitorId + it.standard + it.method);
        let first = codeArr.indexOf(val);
        let last = codeArr.lastIndexOf(val);

        if (first !== last) {
          return true;
        } else {
          return false;
        }
      }
      return {
        edit: false,
        num: 0,
        //  标准和方法唯一
        repeatRuleMonitor: [
          {
            required: true,
            message: '请选择保养部位',
            trigger: 'change'
          },
          {
            validator: (rule, value, callback) => {
              let fieldArr = rule.field.split('.');
              const method = this.form.monitorList[fieldArr[1]]['method'];
              const standard = this.form.monitorList[fieldArr[1]]['standard'];

              const val = value + standard + method;
              // 如果是null 空字符串
              if (!value) {
                callback();
              } else if (codeRepeat(this.form.monitorList, val)) {
                callback(new Error('存在同一部位的相同标准'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        repeatRule: [
          {
            required: true,
            message: '请输入保养标准',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              let fieldArr = rule.field.split('.');
              const method = this.form.monitorList[fieldArr[1]]['method'];
              const monitorId = this.form.monitorList[fieldArr[1]]['monitorId'];
              const val = monitorId + value + method;
              // 如果是null 空字符串
              if (!value) {
                callback();
              } else if (codeRepeat(this.form.monitorList, val)) {
                callback(new Error('存在同一部位的相同标准'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        repeatRuleMethod: [
          {
            required: true,
            message: '请输入保养方法',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              let fieldArr = rule.field.split('.');
              const stardard = this.form.monitorList[fieldArr[1]]['standard'];
              const monitorId = this.form.monitorList[fieldArr[1]]['monitorId'];
              const val = monitorId + stardard + value;
              if (!value) {
                callback();
              } else if (codeRepeat(this.form.monitorList, val)) {
                callback(new Error('存在同一部位的相同标准'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        form: {
          monitorList: []
        },
        monitorList: [],
        currentIdx: undefined, // 当前的要匹配的索引
        // 某个部位已选择的传感器列表
        initSelectList: [],
        multipleSelection: [],
        repeatCode: [] // 接口返回的 重复的编码
      };
    },
    methods: {
      //  获取所有的测点
      async getMonitorList(id) {
        try {
          this.loading = true;
          const res = await getPartList({ equipmentId: id });
          let data = res.data.data || [];
          this.monitorList = data.map((item) => {
            return {
              name: item.name,
              id: item.id,
              monitorName: item.name,
              monitorId: item.id
            };
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      delAll() {
        if (this.multipleSelection.length === 0) {
          this.$message.warning('请先选择要删除的标准');
          return;
        }
        const filteredArray = this.form.monitorList.filter(
          (item) =>
            !this.multipleSelection.some(
              (deleteItem) => deleteItem.num === item.num
            )
        );

        this.form.monitorList = [...filteredArray];
      },

      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      del(scope) {
        console.log(scope.$index);
        this.form.monitorList.splice(scope.$index, 1);
      },
      // 初始化设置列表（编辑时）
      setList(list) {
        const listNum = list.map((it) => {
          return {
            ...it,
            num: this.num++
          };
        });
        this.form.monitorList = listNum;
      },

      // 点击添加部位 在列表新增一行列表
      add() {
        this.num++;
        //1.0选择设备 1.1输入设备；
        let listData = [
          {
            num: this.num,
            monitorId: undefined,
            monitorName: undefined,
            standard: undefined,
            method: undefined
            // needConfirm: '1'
          }
        ];
        // 点击新增的时候，如果部位超过20个（最多20个），那么就新增，如果超过20个
        if (this.form.monitorList.length >= 50) {
          this.$message.warning('最多能增加50个标准');
          return;
        }
        this.form.monitorList = [...this.form.monitorList, ...listData];
      },

      async validForm() {
        if (this.form.monitorList.length === 0) {
          return [];
        }
        let valid = await this.$refs['listForm'].validate();
        if (valid) {
          let params;
          if (this.edit) {
            params = this.form.monitorList;
          } else {
            params = this.form.monitorList.map((i) => {
              return {
                method: i.method,
                monitorId: i.monitorId,
                standard: i.standard
              };
            });
          }
          return params;
        } else {
          return false;
        }
      },

      resetForm() {
        this.num = 0;
        this.form.monitorList = [];
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    margin-bottom: 10px;
  }

  .el-form {
    padding-bottom: 50px;
  }

  /deep/ {
    .el-table__cell {
      padding: 0;
    }

    .el-input__inner {
      padding-right: 10px !important;
    }

    .el-form-item--small {
      margin-top: 16px;
    }

    .el-table .warning-row {
      background: #d3dcecff !important;
    }

    .el-table__header {
      line-height: 50px !important;
    }

    .el-form-item__content {
      width: calc(100% - 25px) !important;
    }
  }
</style>
