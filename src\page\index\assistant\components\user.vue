<template>
  <el-card class="message-card user-message">
    <div class="flex items-center">
      <span class="font-medium text-base text-color">{{ msg.content }}</span>
    </div>
  </el-card>
</template>

<script>
  export default {
    name: 'user',
    props: {
      msg: {
        type: Object,
        default() {
          return {};
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .message-card {
    width: 100%;
    margin-bottom: 12px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 5%);
    transition: all 0.3s ease;
  }

  .user-message {
    width: 100%;
    background-color: #eff6ff;
    border-right: 4px solid #409eff;
  }
</style>
