import Vue from 'vue';
import DataDict from '@/util/dict/systemDict';
import { getDictionary as getDicts } from '@/api/system/dict';

function install() {
  Vue.use(DataDict, {
    metas: {
      '*': {
        labelField: 'dictValue',
        valueField: 'dictKey',
        systemRequest(dictMeta) {
          return getDicts(dictMeta.type).then((res) => res.data);
        }
      }
    }
  });
}

export default {
  install
};
