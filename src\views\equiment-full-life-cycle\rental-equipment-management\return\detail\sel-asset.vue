<template>
  <el-table
    class="table"
    :data="list"
    border
    :header-cell-style="{ background: '#fafafa' }"
    size="small"
  >
    <el-table-column type="index" label="序号" align="center"></el-table-column>
    <el-table-column
      prop="code"
      label="设备编号"
      align="center"
      show-overflow-tooltip
    >
    </el-table-column>
    <el-table-column
      prop="sn"
      label="SN编号"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.sn || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="name"
      label="设备名称"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.name || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="model"
      label="规格型号"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.model || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="measureUnitName"
      label="计量单位"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.measureUnitName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="belongDeptName"
      label="归属部门"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.belongDeptName || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="responsiblePersonName"
      label="负责人"
      align="center"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.responsiblePersonName || '-' }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'DeviceBasicList',
    components: {},
    props: {
      detail: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.list = this.detail.equipmentMonitorList;
            });
          }
        }
      }
    },

    data() {
      return {
        list: []
      };
    },

    mounted() {},
    methods: {}
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }

  .operateBtn {
    margin-bottom: 15px;
  }

  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
