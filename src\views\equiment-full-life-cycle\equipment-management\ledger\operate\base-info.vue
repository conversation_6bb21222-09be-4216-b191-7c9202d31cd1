<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="130px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="SN编号" prop="sn">
            <el-input
              placeholder="请输入SN编号"
              v-model.trim="form.sn"
              maxlength="20"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备名称" prop="name">
            <el-input
              placeholder="请输入设备名称"
              v-model.trim="form.name"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="规格型号" prop="model">
            <el-input
              placeholder="请输入规格型号"
              v-model.trim="form.model"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备类型" prop="categoryName">
            <el-input
              placeholder="请选择设备类型"
              type="text"
              v-model="form.categoryName"
              readonly
              @focus.prevent="selectAssetCategory"
            >
              <template slot="append">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      initData.categoryId = undefined;
                      form.categoryId = undefined;
                      form.categoryName = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备等级" prop="importantLevel">
            <el-select
              v-model="form.importantLevel"
              style="margin-left: 20px"
              placeholder="请选择设备等级"
              clearable
            >
              <el-option
                v-for="item in serviceDicts.type['important_level']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="工艺类别" prop="processCategory">
            <el-select
              v-model="form.processCategory"
              placeholder="请选择工艺类别"
              clearable
            >
              <el-option
                v-for="item in serviceDicts.type['process_category']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="特种设备类型" prop="specialType">
            <el-select
              v-model="form.specialType"
              placeholder="非特种设备请忽略"
              @change="changeSpecialType"
              clearable
            >
              <el-option
                v-for="item in serviceDicts.type['equipment_special_type']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="form.specialType">
          <el-form-item label="特种设备检查周期" prop="specialInspectPeriod">
            <el-input
              placeholder="请输入特种设备检查周期"
              type="text"
              maxlength="10"
              clearable
              v-model="form.specialInspectPeriod"
            >
              <template slot="append"> 天 </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="计量单位" prop="measureUnit">
            <el-select
              v-model="form.measureUnit"
              style="margin-left: 20px"
              placeholder="请选择计量单位"
              clearable
            >
              <el-option
                v-for="item in serviceDicts.type['measure_unit']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备状态" prop="status">
            <el-select
              v-model="form.status"
              style="margin-left: 20px"
              placeholder="请选择设备状态"
              clearable
              @change="changeStatus"
              :disabled="form.status === '4' || form.status === '3'"
            >
              <el-option
                v-for="item in serviceDicts.type['equipment_status']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.value === '4' || item.value === '3'"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="使用部门" prop="useDeptName">
            <el-input
              style="width: 100%"
              placeholder="请选择使用部门"
              v-model.trim="form.useDeptName"
              @focus.prevent="onSelectDeptClick"
              readonly
              clearable
              :disabled="form.status === '3' || form.status === '4'"
            >
              <template slot="append">
                <i
                  v-if="form.status === '1' || form.status === '2'"
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      form.useDeptName = undefined;
                      form.useDept = undefined;
                      form.userId = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用人员" prop="userId">
            <!--            &lt;!&ndash;            <el-input&ndash;&gt;-->
            <!--            &lt;!&ndash;              style="width: 100%"&ndash;&gt;-->
            <!--            &lt;!&ndash;              placeholder="请选择使用人员"&ndash;&gt;-->
            <!--            &lt;!&ndash;              v-model.trim="form.userName"&ndash;&gt;-->
            <!--            &lt;!&ndash;              readonly&ndash;&gt;-->
            <!--            &lt;!&ndash;              @focus.prevent="receiveUsers"&ndash;&gt;-->
            <!--            &lt;!&ndash;              :disabled="form.status === '3' || form.status === '4'"&ndash;&gt;-->
            <!--            &lt;!&ndash;            >&ndash;&gt;-->
            <!--            &lt;!&ndash;              <template slot="append">&ndash;&gt;-->
            <!--            &lt;!&ndash;                <i&ndash;&gt;-->
            <!--            &lt;!&ndash;                  v-if="form.status === '1' || form.status === '2'"&ndash;&gt;-->
            <!--            &lt;!&ndash;                  class="el-icon-circle-close"&ndash;&gt;-->
            <!--            &lt;!&ndash;                  @click="&ndash;&gt;-->
            <!--            &lt;!&ndash;                    () => {&ndash;&gt;-->
            <!--            &lt;!&ndash;                      form.userId = undefined;&ndash;&gt;-->
            <!--            &lt;!&ndash;                      form.userName = undefined;&ndash;&gt;-->
            <!--            &lt;!&ndash;                    }&ndash;&gt;-->
            <!--            &lt;!&ndash;                  "&ndash;&gt;-->
            <!--            &lt;!&ndash;                ></i>&ndash;&gt;-->
            <!--            &lt;!&ndash;              </template>&ndash;&gt;-->
            <!--            &lt;!&ndash;            </el-input>&ndash;&gt;-->
            <el-select
              v-loading="userLoading"
              v-model="form.userId"
              filterable
              placeholder="请选择使用人员"
              clearable
              :disabled="form.status === '3' || form.status === '4'"
            >
              <el-option
                v-for="item in executeUserList"
                :key="item.id"
                :label="item.realName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="购买日期" prop="purchaseDate">
            <el-date-picker
              v-model="form.purchaseDate"
              type="date"
              placeholder="请选择购买日期"
              clearable
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投产日期" prop="productDate">
            <el-date-picker
              v-model="form.productDate"
              type="date"
              placeholder="请选择投产日期"
              clearable
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备位置" prop="locationId">
            <location-panel-list
              ref="location"
              v-model="form.locationId"
              @getValue="getLocation"
              size="small"
              :clearable="true"
            ></location-panel-list>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生产厂家" prop="supplier">
            <el-input
              placeholder="请输入生产厂家"
              v-model.trim="form.supplier"
              clearable
              maxlength="50"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="tel">
            <el-input
              placeholder="请输入联系电话"
              v-model.trim="form.tel"
              clearable
              maxlength="50"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系人员" prop="contact">
            <el-input
              placeholder="请输入联系人员"
              v-model.trim="form.contact"
              clearable
              maxlength="50"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
    <!--     选择设备类型-->
    <device-type-dialog
      ref="device-type"
      @success="getSingleRow"
    ></device-type-dialog>
  </div>
</template>

<script>
  import LocationPanelList from '@/components/location-pannel-list/index.vue';
  import DeptDialog from '@/components/dept-dialog/index.vue';
  import DeviceTypeDialog from '@/views/equiment-full-life-cycle/components/select-asset-category-dialog/single-index.vue';
  import { getUserListByDeptId } from '@/api/equiment-full-life-api/common';
  import reg from '@/util/regexp';
  export default {
    serviceDicts: [
      'important_level',
      // 'process_category',
      'equipment_special_type',
      'measure_unit',
      'equipment_status'
    ],
    name: 'AddDeviceInfo',
    components: {
      DeptDialog,
      LocationPanelList,
      DeviceTypeDialog
    },
    props: {
      initData: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {
        form: {
          name: undefined,
          model: undefined,
          sn: undefined,
          status: undefined,
          categoryId: undefined,
          categoryName: undefined,
          importantLevel: undefined,
          processCategory: undefined,
          measureUnit: undefined,
          useDeptName: undefined,
          useDept: undefined,
          userId: undefined,
          purchaseDate: undefined,
          productDate: undefined,
          locationId: undefined,
          supplier: undefined,
          contact: undefined,
          tel: undefined,
          specialInspectPeriod: undefined,
          specialType: undefined
        },
        userLoading: false,
        executeUserList: [], // 负责人
        fileList: [], // 根据设备类型筛选的文件列表
        rules: {
          code: [
            {
              required: true,
              message: '请输入设别编码',
              trigger: 'blur'
            }
          ],
          sn: [
            {
              required: true,
              message: '请输入SN编号',
              trigger: 'blur'
            }
          ],
          name: [
            {
              required: true,
              message: '请输入设备名称',
              trigger: 'blur'
            }
          ],
          measureUnit: [
            {
              required: true,
              message: '请选择计量单位',
              trigger: 'change'
            }
          ],
          status: [
            {
              required: true,
              message: '请选择设备状态',
              trigger: 'change'
            }
          ],
          importantLevel: [
            {
              required: true,
              message: '请选择设备等级',
              trigger: ['change']
            }
          ],
          specialInspectPeriod: [
            {
              pattern: reg.positiveintN,
              message: '请输入正整数',
              trigger: 'blur'
            }
          ],
          categoryName: [
            {
              required: true,
              message: '请选择设备类型',
              trigger: ['change']
            }
          ],
          locationId: [
            {
              required: true,
              message: '请选择设备位置',
              trigger: ['change']
            }
          ],
          useDeptName: [
            {
              required: false,
              message: '请选择使用部门',
              trigger: ['change']
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    methods: {
      changeSpecialType(val) {
        !val && (this.form.specialInspectPeriod = '');
      },
      //  更改状态的时候
      changeStatus(val) {
        console.log('val', val);
        if (val === '1') {
          this.rules.useDeptName[0].required = false;
        } else if (val === '2') {
          this.rules.useDeptName[0].required = true;
        }
      },
      //  选择地点
      getSingleRow(row) {
        this.form.categoryId = row.id;
        this.initData.categoryId = row.id;
        this.form.categoryName = row.categoryName;
        // this.$emit('getAttribute', row.id);
        //    选择设备类型成功之后
        // this.getEquipmentFileList(row.id);
        this.$emit('getCategoryId', row.id);
      },
      // //  更换设备类型下的通用资料 equipmentFileList
      // async getEquipmentFileList(categoryId) {
      //   try {
      //     let params = {
      //       categoryId: categoryId
      //     };
      //
      //     let res = await equipmentFileList(params);
      //     console.log('将更新好的通用资料更新', res.data.data);
      //     let data = res.data.data || [];
      //     let fileList = data.map((i) => {
      //       return {
      //         data: {
      //           ...i.attach
      //         },
      //         module: i.module,
      //         name: i.name,
      //         fileCategoryId: i.fileCategoryId, // 资料类型id,
      //         extension: this.getString(i.name), // 文件后缀类型
      //         type: i.type,
      //         no: 1
      //       };
      //     });
      //     this.fileList = fileList;
      //     this.$emit('getFileList', this.fileList);
      //   } catch ({ message }) {
      //     this.userLoading = false;
      //     console.log(message);
      //   }
      // },
      getString(str) {
        const lastDotIndex = str.lastIndexOf('.');
        let result;
        if (lastDotIndex !== -1) {
          // 截取点后面的部分
          result = str.slice(lastDotIndex + 1);
        } else {
          // 如果没有找到点，则返回原字符串
          result = str;
        }
        return result;
      },
      selectAssetCategory() {
        this.$refs['device-type'].show();
      },
      //  存放地点
      getLocation(val) {
        this.form.locationId = val;
      },

      // 选择部门
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.useDept = dept.id;
        this.form.useDeptName = dept.deptName;
        this.form.userId = undefined;
        this.getUser(dept.id);
      },
      // 选择人员
      async getUser(deptId) {
        this.userLoading = true;
        try {
          let params = {
            deptId: deptId,
            includeSubDepartments: true
          };

          let res = await getUserListByDeptId(params);
          this.executeUserList = res.data.data;

          this.userLoading = false;
        } catch ({ message }) {
          this.userLoading = false;
          console.log(message);
        }
      },
      setData(initData) {
        if (initData.useDept) {
          this.getUser(initData.useDept);
        }
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
        this.form.status = initData.status + '';
        if (this.form.status === '2') {
          this.rules.useDeptName[0].required = true;
        } else {
          this.rules.useDeptName[0].required = false;
        }
        // this.form.image = (initData.imageList || []).map((it) => {
        //   return {
        //     id: it.id,
        //     fileName: it.fileName,
        //     filePath: convertFileUrl(it.domain)
        //   };
        // });
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();

        if (valid) {
          return {
            ...this.form
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.edit = false;
        this.form.categoryId = undefined;
        this.form.types = undefined;
        this.$refs['baseForm'].resetFields();
        this.rules.useDeptName[0].required = false;
      },

      select(data) {
        this.form.name = data.name;
        this.form.id = data.id;
      }
    },
    computed: {},
    created() {}
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-select {
        margin-left: 0 !important;
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
