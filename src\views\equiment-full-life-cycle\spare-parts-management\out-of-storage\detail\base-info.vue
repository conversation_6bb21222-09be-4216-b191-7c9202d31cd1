<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="出库单号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="出库类型：">{{
        details.outboundTypeName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="用途：">{{
        details.outboundUseName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="领用人：">{{
        details.receiveUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="领用部门：">{{
        details.receiveDeptName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item
        v-if="details.outboundType === '1'"
        label="请领单："
        >{{ details.issuanceOrderName || '-' }}</el-descriptions-item
      >

      <el-descriptions-item label="操作人：">{{
        details.createUserName
      }}</el-descriptions-item>
      <el-descriptions-item label="出库日期：">{{
        details.createTime
      }}</el-descriptions-item>
      <el-descriptions-item label="备件数量：">{{
        details.totalQuantity
      }}</el-descriptions-item>
      <el-descriptions-item
        label="出库仓库："
        v-if="details.outboundType === '2'"
        >{{ details.warehouseName || '-' }}</el-descriptions-item
      >
      <el-descriptions-item label="状态：">{{
        details.statusName
      }}</el-descriptions-item>
      <el-descriptions-item label="备注：">{{
        details.remark || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }
</style>
