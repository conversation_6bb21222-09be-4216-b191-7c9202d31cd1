<template>
  <el-row id="root-tree" ref="root-tree">
    <el-popover
      placement="bottom"
      :width="popoverWidth"
      trigger="manual"
      v-model="popoverVisible"
      popper-class="filter-popover"
    >
      <el-tree
        ref="filterTree"
        class="filter-tree"
        v-loading="treeFilterLoading"
        :data="filterData"
        default-expand-all
        node-key="id"
        :props="filterTreeDefaultProps"
        :expand-on-click-node="false"
        @node-click="filterNodeClick"
        @mouseenter.native="popOver"
        @mouseleave.native="popOut"
      >
        <span class="custom-tree-node" slot-scope="{ node }">
          <el-row type="flex" align="middle">
            <span>
              <span style="margin-left: 5px">{{ node.label }}</span></span
            >
          </el-row>
        </span>
      </el-tree>

      <el-input
        ref="search"
        placeholder="输入名称进行过滤"
        slot="reference"
        v-model="filterText"
        size="mini"
        :maxlength="50"
        clearable
        @focus="
          (e) => {
            this.searchInputFocus(e);
          }
        "
        @blur="
          () => {
            this.inputFocus = false;
          }
        "
      >
      </el-input>
    </el-popover>
    <el-scrollbar style="flex: 1">
      <el-tree
        id="tree"
        ref="tree"
        :current-node-key="currentNodeKey"
        v-loading="treeLoading"
        calss="equipment-category-tree"
        :data="treeData"
        :show-checkbox="showCheckbox"
        node-key="id"
        :expand-on-click-node="false"
        :props="treeDefaultProps"
        @node-click="nodeClick"
        :load="loadNode"
        :lazy="true"
        :default-expanded-keys="expandKeys"
      >
        <span class="custom-tree-node" slot-scope="{ node }">
          <el-row type="flex" align="middle">
            <span>
              <span style="margin-left: 5px">{{ node.label }}</span></span
            >
          </el-row>
        </span>
      </el-tree>
    </el-scrollbar>
  </el-row>
</template>

<script>
  import { searchCategory } from '@/api/equiment-full-life-api/classification';
  import { getDeviceTypeLazyList } from '@/api/equiment-full-life-api/device-type';
  import { mapGetters } from 'vuex';
  export default {
    name: 'simas-position-tree',
    props: {
      showCheckbox: {
        type: Boolean,
        default: false
      },
      hiddenButtons: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      filterText(val) {
        if (val !== '') {
          // 查询
          this.queryAllTree(val);
        } else {
          console.log('清空检索结果');
          this.filterData = [];
        }
      },
      popVisible(val) {
        console.log('popVisible 发生变化 ', val);
        this.popoverVisible = val;
      }
    },
    data() {
      return {
        // returnStrng,
        filterText: '',
        filterTree: [],
        treeFilterLoading: false,
        treeLoading: false,
        treeData: [],
        treeDefaultProps: {
          children: 'children',
          label: 'name',
          isLeaf: 'isLeaf'
        },
        filterTreeDefaultProps: {
          children: 'children',
          label: 'categoryName'
        },
        expandKeys: [],
        currentNodeKey: '',
        filterData: [],
        popoverVisible: false,
        willSelectId: '', // 将要选中的节点的id
        oldCurrentKey: undefined,
        popoverWidth: 0,
        mouseOnPop: false, // 标识当前鼠标是否在pop搜索框上
        inputFocus: false // 标识当前搜索框是否在focus状态
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'topOrgId']),
      // 用于决定 搜索结果弹窗是否显示
      popVisible() {
        return (
          (this.mouseOnPop || this.inputFocus) && this.filterText.trim() !== ''
        );
      }
    },
    created() {
      window.addEventListener('resize', this.getTreeWidth);
    },
    destroyed() {
      console.log('threshold-tree destroyed');
      window.removeEventListener('resize', this.getTreeWidth);
    },
    mounted() {
      this.popoverWidth = document.getElementById('root-tree').clientWidth;
      this.init();
    },
    methods: {
      getTreeWidth() {
        // console.log('门限设备树 resize');
        let tree = document.getElementById('root-tree');
        this.popoverWidth = tree && tree.clientWidth;
      },
      searchInputFocus() {
        console.log('focus ', this.filterText);

        this.inputFocus = true;
        // this.popoverVisible = this.filterText.trim() !== '';
      },

      popOver() {
        this.mouseOnPop = true;
      },
      popOut() {
        this.mouseOnPop = false;
      },
      async queryAllTree(val) {
        try {
          this.treeFilterLoading = true;
          const res = await searchCategory({
            categoryName: val
          });
          this.filterData = res.data.data;
          this.treeFilterLoading = false;
        } catch (e) {
          console.log(e);
          this.$message.warning(e.data.msg);
          this.treeFilterLoading = false;
        }
      },
      async loadNode(node, resolve) {
        // console.log('loadMore ', node);
        if (node.level === 0) {
          // console.log('顶层');
          return resolve(this.data || []);
        } else {
          const res = await getDeviceTypeLazyList({
            parentId: node.data.id
          });
          const data = res.data.data.map((it) => {
            return {
              ...it,
              // category: it.nodeCategory,
              // categoryName: it.nodeCategoryName,
              // code: it.nodeCode,
              // level: it.nodeLevel,
              name: it.categoryName,
              isLeaf: !it.hasChildren
            };
          });
          // if (data.length > 0) {
          //   if (data[0].category === 0) {
          //     node.data.hasChildren = true;
          //   } else if (data[0].category === 1) {
          //     node.data.hasDevice = true;
          //   }
          // } else {
          //   node.data.hasChildren = false;
          //   node.data.hasDevice = false;
          // }
          const findI = data.findIndex((it) => {
            return it.id === this.willSelectId;
          });
          // console.log('node.data.id ', node.data.id);
          // console.log('this.willSelectId ', this.willSelectId);
          // console.log('findI ', findI);
          if (node.data.id === this.willSelectId || findI !== -1) {
            // console.log('设置选中');
            this.$nextTick(() => {
              this.$refs.tree &&
                this.$refs.tree.setCurrentKey(this.willSelectId);
              const node = this.$refs.tree.getNode(this.willSelectId);
              console.log('触发信号 category-change ');
              this.$emit('category-change', node);
              this.willSelectId = '';
            });
          }
          return resolve(data);
        }
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.name.indexOf(value) !== -1;
      },
      init() {
        this.getTree();
      },
      async getTree() {
        try {
          this.treeLoading = true;
          const res = await getDeviceTypeLazyList({
            parentId: '0',
            category: 0
          });
          this.treeData = res.data.data.map((it) => {
            return {
              ...it,
              // category: it.nodeCategory,
              // categoryName: it.nodeCategoryName,
              // code: it.nodeCode,
              // level: it.nodeLevel,
              name: it.categoryName,
              isLeaf: !it.hasChildren
            };
          });
          this.setDefaultSelect();
          this.treeLoading = false;
        } catch (e) {
          console.log('e', e);
          this.treeLoading = false;
          this.treeData = [];
        }
      },
      async reload() {
        console.log('reload');
        // 记录重新加载前的选中值
        this.oldCurrentKey = this.$refs.tree.getCurrentKey();
        try {
          this.treeLoading = true;
          const res = await getDeviceTypeLazyList({ parentId: '0' });
          this.treeData = res.data.data.map((it) => {
            return {
              ...it,
              // category: it.categoryName,
              // categoryName: it.nodeCategoryName,
              // code: it.nodeCode,
              // level: it.nodeLevel,
              name: it.categoryName,
              isLeaf: !it.hasChildren
            };
          });

          const findI = this.treeData.findIndex((it) => {
            return it.id === (this.willSelectId || this.oldCurrentKey);
          });

          // 重新加载后 设置选中值
          console.log('this.willSelectId ', this.willSelectId);
          console.log('findI ', findI);
          if (findI !== -1) {
            console.log('设置选中');
            this.$nextTick(() => {
              this.$refs.tree &&
                this.$refs.tree.setCurrentKey(
                  this.willSelectId || this.oldCurrentKey
                );
              const node = this.$refs.tree.getNode(
                this.willSelectId || this.oldCurrentKey
              );

              this.$emit('category-change', node);
              this.willSelectId = '';
              this.oldCurrentKey = undefined;
            });
          } else {
            this.$emit('category-change', null);
          }

          this.treeLoading = false;
        } catch (e) {
          console.log('e', e);
          this.treeLoading = false;
          this.treeData = [];
        }
      },
      // 添加树节点
      append(node, level) {
        // console.log('append ', node);
        let parentNode = null;
        if (level === 'current') {
          // 同级  寻找父节点
          if (node && node.parent.level !== 0) {
            parentNode = node.parent;
            this.$emit('append', parentNode, node, level);
            return;
          }
          this.$emit('append', parentNode, node, level);
        } else {
          //子级
          parentNode = node;
          this.$emit('append', parentNode, null, level);
        }
      },
      // 编辑树节点
      edit(node, data) {
        // console.log('edit node ', node);
        this.$emit('edit', node, data);
      },
      // 删除树节点
      async remove(node) {
        console.log('remove ', node);
        let parentNode = null;
        if (node.parent.level !== 0) {
          parentNode = node.parent;
        }
        this.$emit('remove', parentNode, node);
      },
      // 点击全部展示全部设备
      resetAll() {
        this.$emit('category-reset', null);
      },
      nodeClick(data, node) {
        // console.log('nodeClick', data);
        this.$emit('category-change', node);
      },
      filterNodeClick(data) {
        let paths = data.path.split(',');
        paths.forEach((it, index) => {
          const findIn = this.expandKeys.findIndex((m) => m === it);
          if (findIn === -1) {
            //console.log('搜索结果还没展开');
            this.expandKeys.push(it);
          }
          if (index === paths.length - 1 && findIn !== -1) {
            //console.log('搜索结果已经展开');
            this.$refs.tree && this.$refs.tree.setCurrentKey(it);

            this.$nextTick(() => {
              const node = this.$refs.tree.getNode(this.willSelectId);
              this.$emit('category-change', node);
            });
          }
        });

        this.popoverVisible = false;
        this.willSelectId = paths[paths.length - 1];
      },
      clear() {
        this.$refs.tree && this.$refs.tree.setCurrentKey(null);
        this.$emit('category-change', null);
      },
      // 设置第一个数据默认选中
      setDefaultSelect() {
        // console.log('setDefaultSelect');
        if (this.treeData.length === 0) {
          return;
        }

        // 请求第一个节点的子节点
        this.expandKeys = [this.treeData[0].id];
        this.$nextTick(() => {
          const first = this.$refs.tree.getNode(this.treeData[0].id);
          // console.log('first ', first)
          this.willSelectId = this.treeData[0].id;
          // this.$refs.tree && this.$refs.tree.setCurrentKey(this.treeData[0].id);

          // // 设置未进行懒加载状态
          // first.loaded = false;
          // // 重新展开节点就会间接重新触发load达到刷新效果
          // first.expand();
          this.$emit('category-change', first);
        });
      },
      // 重新懒加载某个节点
      refreshTree(node) {
        if (node === null) {
          this.reload();
          return;
        }

        //  设置未进行懒加载状态
        node.loaded = false;
        // 重新展开节点就会间接重新触发load达到刷新效果
        node.expand();
        this.willSelectId = node.data.id;
      },
      // 刷新某个节点 并选中其某个子节点
      refreshParent(parent, childId) {
        this.willSelectId = childId;
        if (parent === null) {
          this.reload();
        } else {
          console.log('123 ', parent);
          // parent.data.hasChildren = true;

          // 设置未进行懒加载状态
          parent.loaded = false;
          // 重新展开节点就会间接重新触发load达到刷新效果
          parent.expand();
        }

        // this.$nextTick(() => {
        //   setTimeout(() => {
        //     console.log('设置默认选中的点 ', chilId);
        //     this.$refs.tree && this.$refs.tree.setCurrentKey(chilId);
        //   }, 1000);
        // });
      }
    }
  };
</script>

<style lang="scss" scoped>
  #root-tree {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    ::v-deep {
      .el-tree-node > .el-tree-node__children {
        overflow: unset;
      }
    }
  }

  :deep(.el-scrollbar__bar) {
    display: none;
  }

  .filter-tree {
    height: 300px;
    overflow: auto;

    ::v-deep {
      .el-tree-node > .el-tree-node__children {
        overflow: unset;
      }
    }
  }
</style>
<style lang="scss">
  .filter-popover {
    padding-right: 5px;
  }
</style>
