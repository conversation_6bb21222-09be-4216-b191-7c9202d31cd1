<template>
  <el-table size="small" :data="list" border>
    <el-table-column label="#" type="index" width="50"></el-table-column>
    <el-table-column
      prop="monitorName"
      label="保养部位"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="standard"
      label="保养标准"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="method"
      label="保养方法"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <!-- <el-table-column
      prop="needConfirmName"
      label="是否需要确认"
      align="center"
      show-overflow-tooltip
    ></el-table-column> -->
  </el-table>
</template>

<script>
  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
