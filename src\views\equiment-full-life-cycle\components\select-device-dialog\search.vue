<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      label-width="90px"
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="设备编号" prop="code">
        <el-input
          style="width: 100%"
          v-model.trim="form.code"
          placeholder="请输入设备编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="设备名称" prop="name">
        <el-input
          style="width: 100%"
          v-model.trim="form.name"
          placeholder="请输入设备名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="SN编号" prop="sn">
        <el-input
          style="width: 100%"
          v-model.trim="form.sn"
          placeholder="请输入SN编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="设备类型" prop="categoryName">
        <el-input
          placeholder="请选择设备类型"
          type="text"
          v-model="form.categoryName"
          readonly
          @focus.prevent="selectAssetCategory"
        >
          <template slot="append">
            <i
              class="el-icon-circle-close"
              @click="
                () => {
                  form.categoryId = undefined;
                  form.categoryName = undefined;
                }
              "
            ></i>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="设备等级" prop="importantLevel">
        <el-select
          v-model="form.importantLevel"
          placeholder="请选择设备等级"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['important_level']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择设备状态" clearable>
          <el-option
            v-for="item in serviceDicts.type['equipment_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="使用部门" prop="useDept">
        <InputTree
          v-model="form.useDept"
          lazy
          clearable
          :form="form"
          :dic="deptData"
          style="width: 190px"
          :props="{
            label: 'deptName',
            value: 'id',
            isLeaf: (row) => !row.hasChildren,
            formLabel: 'useDeptName',
            formValue: 'useDept'
          }"
          :load="lazyLoad"
          :lazyLoading="lazyLoading"
          @search="lazySearch"
        ></InputTree>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择设备类型-->
    <device-type-dialog
      ref="device-type"
      @success="getSingleRow"
    ></device-type-dialog>
  </div>
</template>

<script>
  import { getLazyList } from '@/api/system/dept';
  import { InputTree } from '@/components/yk-select-tree';
  import DeviceTypeDialog from '@/views/equiment-full-life-cycle/components/select-asset-category-dialog/single-index.vue';
  export default {
    name: 'DeviceListSearch',
    components: { DeviceTypeDialog, InputTree },
    props: {
      selType: {
        type: String,
        default: undefined
      }
    },
    serviceDicts: ['equipment_status', 'important_level'],
    data() {
      return {
        orgListLoading: false,
        deptData: [],
        lazyLoading: false,
        form: {
          code: undefined,
          sn: undefined,
          name: undefined,
          useDeptName: undefined,
          useDept: undefined,
          categoryId: undefined,
          categoryName: undefined,
          importantLevel: undefined,
          status: undefined
        }
      };
    },
    methods: {
      async lazySearch(title) {
        this.lazyLoad(null, null, title);
      },
      async lazyLoad(node, resolve, title) {
        const { data } = node || {};
        const { id } = data || {};
        let params = {
          deptName: title || undefined
        };
        this.lazyLoading = true;
        try {
          const {
            data: { data: list }
          } = await getLazyList(title ? '' : id || 0, params);
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let arr = list || [];
          if (title) {
            this.deptData = arr;
          } else {
            resolve && resolve(arr);
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.lazyLoading = false;
        }
      },
      selectAssetCategory() {
        this.$refs['device-type'].show();
      },
      //  选择类型
      getSingleRow(row) {
        this.form.categoryId = row.id;
        this.form.categoryName = row.categoryName;
      },
      reset() {
        this.form.useDept = undefined;
        this.form.useDeptName = undefined;
        this.form.categoryId = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
