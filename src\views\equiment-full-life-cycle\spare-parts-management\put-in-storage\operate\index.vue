<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">
        基本信息
        <span class="remark">【注】：改变入库仓库，会清空备件列表</span>
      </p>
      <base-info
        ref="info"
        :initData="detail"
        :whorehouse="whorehouse"
        @warehouseId="getWarehouse"
        :supList="supList"
      ></base-info>
      <p class="el-base-title">入库明细</p>
      <sel-asset
        ref="asset"
        :detail="detail"
        :warehouseId="warehouseId"
      ></sel-asset>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"></btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from './base-info.vue';
  import SelAsset from './sel-asset.vue';
  import {
    addOrEditSparePartsInApi,
    getCheckRepairTypeListApi,
    getSparePartsInOutViewApi
  } from '@/api/equiment-full-life-api/spare-parts';
  import { mapGetters } from 'vuex';
  import { getSupplierList } from '@/api/equiment-full-life-api/common';
  export default {
    name: 'AddDevice',
    components: {
      BaseInfo,
      SelAsset
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {},
        whorehouse: [],
        supList: [],
        warehouseId: undefined
      };
    },
    watch: {},
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods: {
      //  选择供应商
      async getSupList() {
        let res = await getSupplierList({ current: 1, size: -1 });
        this.supList = res.data.data.records || [];
      },
      //  拿到入库仓库的id
      getWarehouse(e) {
        this.warehouseId = e;
        this.$nextTick(() => {
          this.$refs['asset'].form.list = [];
        });
      },
      //  获取库放列表
      async getStoreHouseList() {
        let res = await getCheckRepairTypeListApi({
          manager: this.userInfo.user_id
        });
        this.whorehouse = res.data.data;
      },
      async getDetail(no) {
        this.loading = true;
        try {
          const res = await getSparePartsInOutViewApi({ no: no });
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      show(row) {
        this.visible = true;
        this.edit = !!row.id;
        this.eqId = row.id;
        this.getStoreHouseList();
        this.getSupList();
        if (row.id) {
          this.getDetail(row.no);
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.warehouseId = undefined;
        this.$refs['info'].resetForm();
        this.$refs['asset'].resetForm();
        this.detail = {};
      },

      async submit() {
        let params = await this.$refs['info'].validForm();
        let asset = await this.$refs['asset'].validForm();
        if (params && asset) {
          await this.save({
            ...params,
            itemList: asset,
            id: this.edit ? this.eqId : undefined
          });
        }
      },

      // 提交
      async save(params) {
        this.loading = true;
        try {
          await addOrEditSparePartsInApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  .remark {
    margin-left: 20px;
    color: red;
    font-weight: 100;
    font-size: 14px;
  }
</style>
