<template>
  <div class="avue-contail" :class="{ 'avue--collapse': isCollapse }">
    <div class="avue-header">
      <!-- 顶部导航栏 -->
      <top ref="top" />
    </div>
    <div class="avue-layout">
      <div class="avue-left">
        <!-- 左侧导航栏 -->
        <sidebar />
      </div>
      <div class="avue-main">
        <!-- 顶部标签卡 -->
        <tags />
        <transition name="fade-scale">
          <search class="avue-view" v-show="isSearch"></search>
        </transition>
        <!-- 主体视图层 -->
        <div
          style="height: 100%; overflow-x: hidden; overflow-y: auto"
          id="avue-view"
          v-show="!isSearch"
        >
          <keep-alive>
            <router-view
              class="avue-view"
              v-if="$route.meta.keepAlive"
              :key="$route.fullPath"
            />
          </keep-alive>
          <router-view
            class="avue-view"
            v-if="!$route.meta.keepAlive"
            :key="$route.fullPath"
          />
        </div>
      </div>
    </div>
    <div class="avue-shade" @click="showCollapse"></div>
    <!-- 特种设备工单提醒弹窗 -->
    <special-notice ref="specialNotice" />
    <!-- 煤炭设备知识服务平台入口 -->
    <cmks-entry v-if="hasBindCmksTenant" />
  </div>
</template>

<script>
  import CmksEntry from '@/components/cmks-entry';
  import { mapActions, mapGetters } from 'vuex';
  import tags from './tags';
  import search from './search';
  import top from './top/';
  import sidebar from './sidebar/';
  import admin from '@/util/admin';
  import { validatenull } from '@/util/validate';
  import { calcDate } from '@/util/date.js';
  import { getToken } from '@/util/auth.js';
  import website from '@/config/website';
  import SpecialNotice from './special-notice.vue';
  import { getCheckRepairOrderPageApi } from '@/api/equiment-full-life-api/repair';

  export default {
    components: {
      top,
      tags,
      search,
      sidebar,
      SpecialNotice,
      CmksEntry
    },
    name: 'index',
    provide() {
      return {
        index: this
      };
    },
    data() {
      return {
        //搜索控制
        isSearch: false,
        //刷新token锁
        refreshLock: false,
        //刷新token的时间
        refreshTime: ''
      };
    },
    created() {
      //实时检测刷新token
      this.refreshToken();
      this.checkShowSpecialNotice();
    },
    mounted() {
      this.init();
    },
    computed: {
      ...mapGetters([
        'userInfo',
        'isMenu',
        'isLock',
        'isCollapse',
        'website',
        'menu',
        'hasOverhaulOrderPermission'
      ]),
      hasBindCmksTenant() {
        return this.userInfo.detail && this.userInfo.detail.cmksTenantId;
      }
    },
    props: [],
    methods: {
      ...mapActions(['GetButtons']),
      // 判断是否显示特种设备工单提醒弹窗
      async checkShowSpecialNotice() {
        try {
          // 判断是否初次登录
          const loginFlag = window.sessionStorage.getItem('SIMAS_LOGIN_FLAG');
          if (loginFlag !== 'true') return;
          // 判断是否有特种设备检修工单菜单权限
          await this.GetButtons();
          this.hasOverhaulOrderPermission && this.checkSpecialRepairOrder();
        } catch (e) {
          console.error(e);
        }
      },
      // 判断是否存在未完成的特种设备检修工单
      async checkSpecialRepairOrder() {
        try {
          const { data } = await getCheckRepairOrderPageApi({
            current: 1,
            size: 10,
            onlyQuerySpecialType: true,
            statuses: '1,3,5,6'
          });
          // 清空登录标识符
          window.sessionStorage.setItem('SIMAS_LOGIN_FLAG', false);
          const listData = data.data.records || [];
          const total = data.data.total;
          if (data.data.total > 0) {
            this.$refs.specialNotice.show(listData, total);
          }
        } catch (e) {
          console.error(e);
        }
      },
      showCollapse() {
        this.$store.commit('SET_COLLAPSE');
      },
      // 初始化
      init() {
        this.$store.commit('SET_SCREEN', admin.getScreen());
        window.onresize = () => {
          setTimeout(() => {
            this.$store.commit('SET_SCREEN', admin.getScreen());
          }, 0);
        };
        this.$store.dispatch('FlowRoutes').then(() => {});
      },
      //打开菜单
      // openMenu(item = {}) {
      //   this.$store.dispatch('GetMenu', item.id).then((data) => {
      //     if (data.length !== 0) {
      //       this.$router.$avueRouter.formatRoutes(data, true);
      //     }
      //     //当点击顶部菜单后默认打开第一个菜单
      //     /*if (!this.validatenull(item)) {
      //       let itemActive = {},
      //         childItemActive = 0;
      //       if (item.path) {
      //         itemActive = item;
      //       } else {
      //         if (this.menu[childItemActive].length === 0) {
      //           itemActive = this.menu[childItemActive];
      //         } else {
      //           itemActive = this.menu[childItemActive].children[childItemActive];
      //         }
      //       }
      //       this.$store.commit('SET_MENU_ID', item);
      //       this.$router.push({
      //         path: this.$router.$avueRouter.getPath({
      //           name: (itemActive.label || itemActive.name),
      //           src: itemActive.path
      //         }, itemActive.meta)
      //       });
      //     }*/
      //   });
      // },
      // 定时检测token
      refreshToken() {
        this.refreshTime = setInterval(() => {
          const token = getToken() || {};
          const date = calcDate(token.datetime, new Date().getTime());
          if (validatenull(date)) return;
          if (date.seconds >= this.website.tokenTime && !this.refreshLock) {
            this.refreshLock = true;
            this.$store
              .dispatch('refreshToken')
              .then(() => {
                this.refreshLock = false;
              })
              .catch(() => {
                this.refreshLock = false;
              });
          }
        }, 10000);
        if (website.WS.enable) {
          this.$websocket.initWebSocket(this.userInfo.user_id);
        }
      }
    }
  };
</script>

<style lang="scss" scoped></style>
