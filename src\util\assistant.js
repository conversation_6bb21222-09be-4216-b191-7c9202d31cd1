import dayjs from 'dayjs';

// 格式化日期的函数, 可以把'年'和'月'替换为'-', 把'日'替换为''
export const formatDate = (date, format = 'YYYY-MM-DD HH:mm:00') => {
  if (!date || date === '无') return '';
  const res = date
    .replace(/年/g, '-')
    .replace(/月/g, '-')
    .replace(/日/g, '')
    .replace(/[\u4e00-\u9fa5]/g, '');
  return dayjs(res).format(format);
};

// 智能助手 - 格式化日期范围
export function formatDateRange(dateRange) {
  if (!dateRange) return '';
  const [start, end] = dateRange.split('到');
  const startDate = formatDate(start, 'YYYY-MM-DD 00:00:00');
  const endDate = formatDate(end, 'YYYY-MM-DD 23:59:59');
  return [startDate, endDate];
}

// 格式化人员字符串
export function formatUserStr(str) {
  // 正则表达式匹配名字和ID
  const match = str.match(/(.*)\((\d+)/);
  if (!match) return '';
  // 解构匹配结果
  // eslint-disable-next-line no-unused-vars
  const [_, userName, userId] = match;
  return {
    userName,
    userId
  };
}

// 智能助手 - 格式化人员
export function formatUser(str) {
  if (!str) return '';
  const arr = str.split('/');
  return arr.map((item) => formatUserStr(item));
}

// 格式化部门字符串
export function formatDeptStr(str, deptOpts) {
  // 正则表达式匹配名字和ID
  const match = str.match(/(.*)\((.+)/);
  if (!match) return '';
  // 解构匹配结果
  // eslint-disable-next-line no-unused-vars
  const [_, deptName] = match;
  const dept = deptOpts.find((item) => item.label === deptName) || {};
  return dept.value;
}

// 智能助手 - 根据部门名称获取部门id
export function formatDept(str, deptOpts) {
  if (!str) return '';
  const arr = str.split('/');
  return arr.map((item) => formatDeptStr(item, deptOpts));
}
