<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'盘点'"
    :visible.sync="visible"
    @closed="close"
    class="detail-drawer"
    size="80%"
  >
    <div class="details" v-loading="loading" v-if="visible">
      <!-- 基本信息 -->
      <section>
        <p class="el-base-title">基本信息</p>
        <base-info :details="details"></base-info>
      </section>

      <p class="el-base-title">物品列表</p>
      <section style="display: flex; justify-content: space-between">
        <el-radio-group
          style="margin-bottom: 10px"
          v-model="result"
          size="small"
          @change="change"
        >
          <el-radio-button label="all"
            >全部({{ totalNumber.total }})</el-radio-button
          >
          <el-radio-button label="NORMAL"
            >正常({{ totalNumber.normal }})</el-radio-button
          >
          <el-radio-button label="NOT_START"
            >未盘点({{ totalNumber.notStart }})</el-radio-button
          >
          <el-radio-button label="PROFIT"
            >盘盈({{ totalNumber.profit }})</el-radio-button
          >
          <el-radio-button label="LOSS"
            >盘亏({{ totalNumber.loss }})</el-radio-button
          >
        </el-radio-group>
      </section>
      <spare-part
        ref="sparePart"
        @selectDict="selectDict"
        @newSpareList="newSpareList"
        @deleteSpare="deleteSpare"
      ></spare-part>
    </div>
    <div class="oper_btn">
      <btn
        type="submit"
        v-if="result === 'all'"
        @click="submit"
        :loading="loading"
      >
      </btn>
      <btn type="save" v-if="result === 'all'" @click="save" :loading="loading">
      </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from '@/views/equiment-full-life-cycle/spare-parts-management/inventory-order/detail/base-info.vue';
  import SparePart from '@/views/equiment-full-life-cycle/spare-parts-management/inventory-order/operate/spare-part.vue';
  import {
    getSparePartsCheckItemStatisticsApi,
    getSparePartsInventoryViewApi,
    saveSparePartsInventoryApi,
    submitSparePartsInventoryApi
  } from '@/api/equiment-full-life-api/spare-parts';
  import { deepClone } from '@/util/util';
  export default {
    name: 'RepairViewIndex',
    components: { BaseInfo, SparePart },
    data() {
      return {
        id: undefined,
        totalNumber: {},
        totalNumberCopy: {},
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: -1
        },
        warehouseId: undefined,
        result: 'all',
        planWarehouseList: [],
        row: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {} // 详情数据
      };
    },

    methods: {
      //  删除备品备件
      deleteSpare() {
        this.totalNumber.total = this.totalNumberCopy.total - 1;
        this.totalNumber.notStart = this.totalNumberCopy.notStart - 1;
      },
      //  选择完成备品备件之后，更新统计数据
      newSpareList(num) {
        this.totalNumber.total = this.totalNumberCopy.total + num;
        this.totalNumber.notStart = this.totalNumberCopy.notStart + num;
      },
      //  选择备品备件的时候
      async selectDict() {
        this.result = 'all';
        await this.$refs['sparePart'].getList({
          ...this.searchParams,
          inventoryOrderId: this.row.id,
          resultEnum: this.result === 'all' ? undefined : this.result
        });
        await this.$refs['sparePart'].openSpareDialog();
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.warehouseId = undefined;
        this.detail = {};
      },
      async save() {
        this.loading = true;
        try {
          let params = await this.$refs['sparePart'].validForm();
          if (params) {
            await saveSparePartsInventoryApi(params);
            //  统计列表
            await this.getTotal();
            // 下面的分页列表
            await this.$refs['sparePart'].getList({
              ...this.searchParams,
              inventoryOrderId: this.row.id,
              resultEnum: this.result === 'all' ? undefined : this.result
            });
            this.$emit('success');
            this.$message.success('操作成功');
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      async submit() {
        this.loading = true;
        try {
          let params = await this.$refs['sparePart'].validForm();
          if (params) {
            await submitSparePartsInventoryApi(this.row.planId, params);
            this.$message.success('操作成功');
            this.$emit('success');
            this.visible = false;
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      //  切换物品分类
      change(e) {
        this.$refs['sparePart'].getList({
          ...this.searchParams,
          inventoryOrderId: this.row.id,
          resultEnum: this.result === 'all' ? undefined : this.result
        });
      },
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getSparePartsInventoryViewApi(id);
          this.details = res.data.data;
        } catch (e) {
          this.loading = false;
        }
      },
      //  统计
      async getTotal() {
        try {
          const res = await getSparePartsCheckItemStatisticsApi({
            inventoryOrderId: this.row.id
          });
          this.totalNumber = res.data.data;
          this.totalNumberCopy = deepClone(res.data.data);
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      // 点击展示
      async show(row) {
        this.row = row;
        this.visible = true;
        if (row.no) {
          await this.getDetail(row.id);
          await this.getTotal();
          await this.$refs['sparePart'].getList({
            ...this.searchParams,
            inventoryOrderId: this.row.id,
            resultEnum: this.result === 'all' ? undefined : this.result
          });
        }
      },

      // 关闭弹窗
      close() {
        this.detail = {};
        this.result = 'all';
        this.visible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
