<template>
  <dialog-popup
    title="选择领用单"
    ref="dialogs"
    :visible="visible"
    @closed="closed"
    width="80%"
    class="selectSensor"
  >
    <search ref="search" @search="onSubmit"></search>

    <el-table
      v-loading="loading"
      class="table"
      :data="dataSource"
      row-key="id"
      size="small"
      stripe
      border
      ref="table"
      height="500px"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#"></el-table-column>
      <el-table-column prop="no" label="领用单号" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.no || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="领用单名称" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="receiveUserName"
        label="领用人"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.receiveUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="申请领用时间"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="total" label="备件数量" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.totalQuantity || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="remark"
        label="领用原因"
        show-overflow-tooltip
        width="110px"
      >
        <template v-slot="{ row }">{{ row.remark || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="auditUserName"
        label="审核人"
        show-overflow-tooltip
        width="110px"
      >
        <template v-slot="{ row }">{{ row.auditUserName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="auditTime"
        label="审核时间"
        show-overflow-tooltip
        width="110px"
      >
        <template v-slot="{ row }">{{ row.auditTime || '-' }} </template>
      </el-table-column>

      <el-table-column prop="statusName" label="状态" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="statusName" label="操作" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <el-button size="small" type="text" @click="select(row)"
            >选择</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
  </dialog-popup>
</template>
<script>
  import { getSparePartsReceiveListApi } from '@/api/equiment-full-life-api/spare-parts';
  import Search from './search.vue';

  export default {
    components: { Search },
    props: {},
    data() {
      return {
        visible: false,
        loading: false,
        dataSource: [],
        total: 0,
        searchParams: {
          size: 10,
          current: 1
        },
        warehouseId: undefined
      };
    },
    watch: {},
    methods: {
      //该方法是当页全选的方法
      search() {
        this.searchParams.current = 1;
        this.getList();
      },
      show(warehouseId) {
        this.warehouseId = warehouseId;
        this.visible = true;
        this.searchParams.current = 1;
        this.getList();
      },
      select(row) {
        this.$emit('selectedRow', row);
        this.visible = false;
      },
      onSubmit(params) {
        this.searchParams = { ...this.searchParams, current: 1, ...params };
        this.getList();
      },

      closed() {
        this.visible = false;
        this.dataSource = [];
      },

      async getList() {
        this.loading = true;

        try {
          let params = {
            ...this.searchParams,
            warehouseId: this.warehouseId,
            status: 2 // 已审核待出库
          };
          let res = await getSparePartsReceiveListApi(params);
          this.dataSource = res.data.data.records || [];
          this.total = res.data.data.total;
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .el-pagination__sizes {
    display: none !important;
  }

  .search {
    margin-bottom: 10px;
  }

  /deep/ .pagination-container {
    text-align: left !important;
  }

  /deep/ .el-pagination__jump {
    display: none !important;
  }

  .right {
    margin-left: 20px;
  }

  /deep/ {
    .el-form-item--small {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      padding: 10px 0;
    }
  }

  .table {
    :deep(.el-table__cell) {
      padding: 8px 0 !important;
    }
  }

  .selectSensor {
    :deep(.el-dialog__body) {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  ::v-deep {
    .el-input-number--mini {
      width: 100px !important;
    }
  }
</style>
