<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      label-position="left"
      class="search-form"
    >
      <el-form-item label="出库单号" prop="no">
        <el-input v-model="form.no" placeholder="请输入出库单号" clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="出库类型" prop="outboundType">
        <el-select
          v-model="form.outboundType"
          placeholder="请选择出库类型"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['outbound_type']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出库状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择出库状态" clearable>
          <el-option
            v-for="item in in_storage_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出库时间" prop="time" class="_label">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { in_storage_status } from '../util';
  export default {
    name: 'DeviceListSearch',
    components: {},
    serviceDicts: ['outbound_type'], //out_storage_type
    data() {
      return {
        in_storage_status,
        orgListLoading: false,
        useDeptOptions: [],
        form: {
          no: undefined,
          outboundType: undefined,
          time: undefined,
          status: undefined
        }
      };
    },
    methods: {
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          startOutboundDate: this.form.time ? this.form.time[0] : undefined,
          endOutboundDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
