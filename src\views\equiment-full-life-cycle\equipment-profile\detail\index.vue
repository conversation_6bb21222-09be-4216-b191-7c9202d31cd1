<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @close="close"
    class="detail-drawer"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <!--      <section>-->
      <!--        <span class="el-base-title">基础信息</span>-->
      <!--        <base-info :details="details"></base-info>-->
      <!--      </section>-->
      <section>
        <span class="el-base-title">文件资料</span>
        <file-name :detail="details"></file-name>
      </section>
      <section>
        <span class="el-base-title">适用设备类型 </span>
        <asset-list :detail="details"></asset-list>
      </section>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from './base-info.vue';
  import FileName from './file.vue';
  import AssetList from './sel-asset.vue';
  import { userDetail } from '@/api/equiment-full-life-api/profile';
  export default {
    name: 'RepairViewIndex',
    components: { BaseInfo, FileName, AssetList },
    data() {
      return {
        id: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {} // 详情数据
      };
    },

    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await userDetail({ id: id });
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 点击展示
      async show(id) {
        this.visible = true;
        if (id) {
          this.id = id;
          await this.getDetail(id);
        }
      },

      // 关闭弹窗
      close() {
        this.visible = false;
        this.detail = {};
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
