<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="handleQuery" />
      <el-button
        icon="el-icon-plus"
        v-if="permission['move-log-add']"
        type="primary"
        size="small"
        @click="handleAdd"
        >新增</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="sourceName"
        label="来源"
        align="center"
        width="80"
      />
      <el-table-column prop="no" label="移动单号" align="center" width="120" />
      <el-table-column
        prop="deviceCode"
        label="设备编号"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="deviceName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="deviceModel"
        label="规格型号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.deviceModel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.useDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="userName"
        label="使用人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.userName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="originalLocationName"
        label="移动前位置"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.originalLocationName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="newLocationName"
        label="移动后位置"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.newLocationName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="moveTime"
        label="移动日期"
        align="center"
        width="90"
      >
        <template v-slot="{ row }">{{ row.moveTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="operateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.operateUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="operateTime"
        label="更新时间"
        align="center"
        show-overflow-tooltip
        width="136"
      >
        <template v-slot="{ row }">{{ row.operateTime || '-' }}</template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!-- 新增弹窗 -->
    <add-dialog v-model="addVisible" @refresh="getList" />
  </basic-container>
</template>

<script>
  import Search from './search';
  import AddDialog from './add-dialog.vue';
  import { getEquipmentMoveLogList } from '@/api/equiment-full-life-api/location';
  import Pagination from '@/components/pagination';
  import { mapGetters } from 'vuex';

  export default {
    name: 'EquipmentMoveLogList',
    components: {
      Search,
      Pagination,
      AddDialog
    },
    props: {},
    data() {
      return {
        resizeTimer: null,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        addVisible: false
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    activated() {
      this.getList();
    },
    mounted() {
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },
    methods: {
      handleResize() {
        if (this.resizeTimer) return;
        console.log('resize');
        this.resizeTimer = setTimeout(() => {
          this.$refs.table.doLayout();
          this.resizeTimer = null;
        }, 500);
        // 触发表格更新
      },
      handleQuery(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getEquipmentMoveLogList({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 新增
      handleAdd() {
        this.addVisible = true;
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
