import Vue from 'vue';
import Avue from '@smallwei/avue';
import '@smallwei/avue/lib/index.css';
import axios from './router/axios';
import VueAxios from 'vue-axios';
import App from './App';
import routerInstance from './router/router';
import './filter';
import './permission'; // 权限
import './error'; // 日志
import './cache'; //页面缓存
import './plugins';
import './base';
import './public-path'; // qiankun子应用路径

import store from './store';
// import { loadStyle } from './util/util';
import * as urls from '@/config/env';
import Element from 'element-ui';
import VueCropper from 'vue-cropper';
import i18n from './lang'; // Internationalization
import './styles/common.scss';
import basicBlock from './components/basic-block/main';
import basicContainer from './components/basic-container/main';
import thirdRegister from './components/third-register/main';

import website from '@/config/website';
import crudCommon from '@/mixins/crud';
// 业务组件
import tenantPackage from './views/system/tenantpackage';
import directive from './directive'; // directive
import websocket from './util/websocket.js';

import { message } from '@/util/resetMessage';
import ElTableInfiniteScroll from 'el-table-infinite-scroll';

Vue.directive('el-table-infinite-scroll', ElTableInfiniteScroll);

// 注册全局crud驱动
window.$crudCommon = crudCommon;
// 加载Vue拓展
Vue.use(VueAxios, axios);
Vue.use(Element, {
  i18n: (key, value) => i18n.t(key, value)
});
Vue.prototype.$message = message;
// if (
//   process.env.VUE_APP_ENV === 'production' ||
//   process.env.VUE_APP_ENV === 'test'
// ) {
//   Vue.use(window.AVUE, {
//     size: 'small',
//     tableSize: 'small',
//     calcHeight: 65,
//     i18n: (key, value) => i18n.t(key, value)
//   });
// } else {
Vue.use(Avue, {
  axios,
  size: 'small',
  tableSize: 'small',
  calcHeight: 65
  // i18n: (key, value) => i18n.t(key, value)
});
// }

Vue.use(VueCropper);

// online offline
let networkNotification = null;

window.addEventListener('online', () => {
  networkNotification && networkNotification.close();
  // alert('网络重连成功!');
  networkNotification = Element.Notification({
    message: `<div style='margin-top: -6px'>网络重连成功 ${dayjs().format(
      'YYYY-MM-DD HH:mm:ss'
    )}</div>`,
    type: 'success',
    dangerouslyUseHTMLString: true,
    duration: 0
  });
});

window.addEventListener('offline', () => {
  networkNotification && networkNotification.close();
  networkNotification = Element.Notification({
    message: `<div style='margin-top: -6px'>网络已断开 ${dayjs().format(
      'YYYY-MM-DD HH:mm:ss'
    )}</div>`,
    type: 'warning',
    dangerouslyUseHTMLString: true,
    duration: 0
  });
});

// 注册全局容器
Vue.component('basicContainer', basicContainer);
Vue.component('basicBlock', basicBlock);
Vue.component('thirdRegister', thirdRegister);
Vue.component('tenantPackage', tenantPackage);

// 通用组件
import Pagination from '@/components/pagination';
Vue.component('Pagination', Pagination);

// 字典数据组件
import systemDict from '@/components/DictData/systemDict';
import serviceDict from '@/components/DictData/serviceDict';
import dayjs from 'dayjs';
systemDict.install();
serviceDict.install();

Vue.use(directive);

// 加载相关url地址
Object.keys(urls).forEach((key) => {
  Vue.prototype[key] = urls[key];
});
// 加载website
Vue.prototype.website = website;
Vue.prototype.$websocket = websocket;
// 动态加载阿里云字体库
// iconfontVersion.forEach((ele) => {
//   loadStyle(iconfontUrl.replace('$key', ele));
// });

Vue.config.productionTip = false;

// 引入markdown预览组件
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';

// highlightjs
import hljs from 'highlight.js';

VMdPreview.use(githubTheme, {
  Hljs: hljs
});

Vue.use(VMdPreview);

// qiankun
let router = null;
let instance = null;

function render(props = {}) {
  const { container } = props;
  if (window.__POWERED_BY_QIANKUN__) {
    const userInfo = JSON.parse(
      localStorage.getItem('mine-main-userInfo-object')
    );
    store.commit('SET_USER_INFO', userInfo);
  }

  router = routerInstance;
  instance = new Vue({
    router,
    store,
    i18n,
    render: (h) => h(App)
  }).$mount(container ? container.querySelector('#app') : '#app');
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}
export default instance;
// 暴露生命周期钩子函数
export async function bootstrap() {
  console.log('[vue] vue app bootstraped');
}

export async function mount(props) {
  // props 包含主应用传递的参数  也包括为子应用 创建的节点信息
  console.log('[vue] props from main framework', props);
  render(props);
}

export async function unmount() {
  instance.$destroy();
  instance = null;
  router = null;
}
