<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
    >
      <el-form-item label="润滑方式" prop="name">
        <el-input
          v-model.trim="form.name"
          placeholder="请输入润滑方式"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    components: {},
    data() {
      return {
        form: {
          name: undefined
        }
      };
    },
    methods: {
      reset() {
        this.form.executeDept = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = {
          ...this.form
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
