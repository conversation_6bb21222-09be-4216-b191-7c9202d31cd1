<template>
  <basic-dialog
    :title="dialogTitle"
    top="5vh"
    class="file-preview-dialog"
    width="1200px"
    :visible="visible"
    @closed="visible = false"
  >
    <div class="sso-container">
      <onlyoffice-editor
        :src="src"
        :config="editorConfig"
        @ready="onEditorReady"
      />
    </div>
  </basic-dialog>
</template>
<script>
  import BasicDialog from '@/components/basic-dialog';
  import { mapGetters } from 'vuex';
  import { OnlyofficeEditor } from 'onlyoffice-vue';
  import { handleDocType } from '@/util/preview';
  import { httpPreviewUrl, httpsPreviewUrl } from '@/config/env';

  export default {
    name: 'file-preview',
    components: {
      BasicDialog,
      OnlyofficeEditor
    },
    data() {
      return {
        visible: false,
        uid: Math.random().toString(36).slice(-8),
        previewParams: {}
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
      src() {
        let isHttps = window.location.protocol.includes('https');
        return `${
          isHttps ? httpsPreviewUrl : httpPreviewUrl
        }web-apps/apps/api/documents/api.js`;
      },
      editorConfig() {
        let type = this.previewParams.type;
        let documentType = handleDocType(type) || 'word';
        let url = this.previewParams.url;
        let title = this.previewParams.name || '';
        let key = this.previewParams.id || this.uid;
        let userName = this.userInfo['nick_name'];
        return {
          // 编辑器宽度
          width: '100%',
          // 编辑器高度
          height: '100%',
          // 编辑器类型，支持 word（文档）、cell（表格）、slide（PPT）
          documentType,
          // 文档配置
          document: {
            // 文件类型
            fileType: type,
            // 文档标识符
            key,
            // 文档地址，绝对路径
            url,
            // 文档标题
            title,
            permissions: {
              download: false,
              print: false,
              edit: false
            }
          },
          editorConfig: {
            mode: 'view',
            user: {
              name: userName
            }
          }
        };
      },
      dialogTitle() {
        return `文件预览 - ${this.previewParams.name}`;
      }
    },
    created() {
      let isHttps = window.location.protocol.includes('https');
      if (isHttps) {
        var oMeta = document.createElement('meta');
        oMeta.setAttribute('charset', 'utf-8');
        oMeta.setAttribute('http-equiv', 'Content-Security-Policy');
        oMeta.setAttribute('content', 'upgrade-insecure-requests');
        document.getElementsByTagName('head')[0].appendChild(oMeta);
      }
    },
    methods: {
      show(params) {
        this.visible = true;
        this.previewParams = params || {};
      },
      // 编辑器加载完毕后回调 ready 函数，editor 为当前编辑器实例
      onEditorReady(editor) {
        console.log(editor);
      }
    }
  };
</script>

<style lang="scss">
  .sso-container {
    position: absolute;
    top: 45px;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #303133;
  }
</style>
