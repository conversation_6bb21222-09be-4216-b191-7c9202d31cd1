<template>
  <basic-container :auto-height="true" noScrollbar>
    <el-table
      v-loading="loading"
      :data="tableData"
      ref="treeTable"
      border
      size="small"
      :header-cell-style="{ background: '#fafafa' }"
      height="calc(100% - 50px)"
    >
      <el-table-column prop="name" label="设备大类" align="center">
      </el-table-column>
      <el-table-column prop="name" label="设备中类" align="center">
        <template slot-scope="scope">
          <div
            class="middle"
            v-if="scope.row.children && scope.row.children.length > 0"
            :style="
              'display: grid;height:' +
              getHeight(scope) +
              'px;grid-template-rows:' +
              getData(scope.row.children, 3)
            "
          >
            <div
              v-for="(i, index) in scope.row.children"
              :class="
                index === scope.row.children.length - 1
                  ? 'noBorder line'
                  : 'border line'
              "
              :key="i.id"
              style=""
            >
              {{ i.name }}
            </div>
          </div>
          <p class="middle" v-else>-</p>
        </template>
      </el-table-column>
      <el-table-column
        class="category"
        prop="name"
        label="设备小类"
        align="center"
      >
        <template slot-scope="scope">
          <!--           分的这个 是小类的length；有5个小类，每个小类的名称为-->

          <!--             先判断有没有二级-->
          <div
            v-if="scope.row.children && scope.row.children.length > 0"
            :style="
              'display: grid; grid-template-rows:' +
              getData(scope.row.children, 3)
            "
          >
            <!-- 循环二级的children，找出来有多少三级-->
            <div
              v-for="(i, index) in scope.row.children"
              :key="i.id"
              :style="
                i.children
                  ? `?display: grid;border-bottom:1px solid #ebeef5; grid-template-rows:repeat(${i.children.length},1fr)`
                  : ``
              "
              :class="
                index === scope.row.children.length - 1 ? 'noBorder' : 'border'
              "
            >
              <div
                style="padding: 10px"
                v-if="i.children && i.children.length > 0"
                v-for="(j, index) in i.children"
                :key="j.id"
                :class="index === i.children.length - 1 ? 'noBorder' : 'border'"
              >
                {{ j.name || '-' }}
              </div>
              <div v-if="!i.children || i.children.length === 0">-</div>
            </div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
    </el-table>
    <section class="oper_btn">
      <span style="font-size: 14px; line-height: 32px">
        提示：系统支持一、二、三级设备分类，引用时，可根据管理需求设置。
      </span>
      <span>
        <el-button size="small" type="primary" @click="quote">引 用</el-button>
        <el-button size="small" type=" " @click="cancel">取消</el-button>
      </span>
    </section>
    <!--     引用弹窗-->
    <quote-dialog ref="quoteDialog"></quote-dialog>
  </basic-container>
</template>

<script>
  import QuoteDialog from '../component/quote.vue';
  import { getDeviceTypeTemplateDetail } from '@/api/equiment-full-life-api/device-type';
  export default {
    name: 'DeviceBasicList',
    components: {
      QuoteDialog
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        tableData: []
      };
    },

    mounted() {
      this.$nextTick(async () => {
        await this.getList();
      });
    },
    methods: {
      //  设置种类行高
      getHeight(scope) {
        let rowNum = scope.$index + 1;
        this.$nextTick(() => {
          const table = this.$refs.treeTable.$el;
          //  获取指定单元格
          const secondRowMiddleCell = table.querySelector(
            `.el-table__body-wrapper .el-table__row:nth-child(${rowNum}) .el-table__cell:nth-child(2)`
          );
          const centerEle = table.querySelector(
            `.el-table__body-wrapper .el-table__row:nth-child(${rowNum}) .el-table__cell:nth-child(2) .middle`
          );
          const borderLineHeight = table.querySelectorAll(
            `.el-table__body-wrapper .el-table__row:nth-child(${rowNum}) .el-table__cell:nth-child(2) .middle .line`
          );
          console.log(secondRowMiddleCell, centerEle, borderLineHeight);
          if (secondRowMiddleCell && centerEle && borderLineHeight) {
            //  待页面准备好之后，将获取的该单元格中的center 类的元素的高度赋值为 210px
            centerEle.style.height = secondRowMiddleCell.offsetHeight + 'px';
            //  这一句必须要放在下面
            borderLineHeight.forEach((it) => {
              it.style.lineHeight = it.clientHeight + 'px';
            });
          }
        });
      },
      // 分割小类
      getData(secChild, type) {
        if (type === 2) {
          // 输出的就是名称，直接分成length = 2 ； 平均分成2分
          return `repeat(${secChild.length}, 1fr)`;
          // 中类
        } else if (type === 3) {
          let arr = [];
          //  secChild 肯定有值，因为表格中做了判断
          if (secChild && secChild.length > 0) {
            arr = secChild.map((item) => {
              // 循环找出 每个下面存在几个小类，加起来，然后按照比例分配高度
              if (item.children && item.children.length > 0) {
                return item.children.length;
              } else {
                return 1;
              }
            });
          }

          //  将数组的值累加起来
          let sum = arr.reduce((a, b) => {
            return a + b;
          });
          //  根据数字值计算出比例值，进行分配
          let rows = arr
            .map((item) => {
              return `${(item / sum) * 100}%`;
            })
            .join(' ');

          return rows;
          // 小类
        }
      },
      //  获取数据
      async getList() {
        console.log(this.$route.query.templateId);
        try {
          let res = await getDeviceTypeTemplateDetail({
            templateId: this.$route.query.templateId
          });
          this.tableData = res.data.data || [];
          this.total = res.data.data.total;
        } catch (e) {
          console.log(e);
        }
      },
      //  点击引用
      quote() {
        this.$refs.quoteDialog.show(this.$route.query.templateId);
      },
      //  返回上一页
      cancel() {
        this.$router.push({
          path: '/equiment-full-life-cycle/basic-manager/chemical-device-types'
        });
      }
    }
  };
</script>

<style scoped lang="scss">
  .border {
    display: block;
    border-bottom: 1px solid #ebeef5;
  }

  .noBorder {
    display: block;
    border-bottom: none !important;
  }

  ::v-deep {
    .el-table__cell {
      padding: 0 !important;
    }

    .cell {
      padding: 0 !important;
    }

    .el-table__header-wrapper .cell {
      padding: 10px 0 !important;
    }
  }

  .oper_btn {
    display: flex;
    justify-content: space-between;
  }
</style>
