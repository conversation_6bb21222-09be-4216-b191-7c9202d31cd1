// 表格配合搜索样式------------
//.swing-search {
//  margin-bottom: 10px;
//  .el-form-item {
//    width: calc(33% - 20px);
//  }
//  .el-form-item__content {
//    width: calc(100% - 95px);
//  }
//  .el-select,.el-input__inner {
//    width: 100%;
//  }
//  .el-form-item {
//    margin-bottom: 8px;
//  }
//  .el-form-item__label {
//    text-align: left;
//  }
//
//}
//.search-btn{
//  .el-form-item__content{
//    width: 250px !important;
//  }
//}
//.swing-content {
//  display: flex;
//  flex-direction: column;
//  width: 100%;
//  height: 100%;
//  overflow-y: hidden;
//  .el-card__body {
//    padding: 20px;
//    display: grid;
//    grid-template-rows: 160px calc(100% - 210px) 50px;
//  }
//}
//.no-add-swing-content{
//  display: flex;
//  flex-direction: column;
//  width: 100%;
//  height: 100%;
//  overflow-y: hidden;
//  .el-card__body {
//    padding: 20px;
//    display: grid;
//    grid-template-rows: 120px calc(100% - 170px) 50px;
//  }
//}
//.swing-content-small{
//  display: flex;
//  flex-direction: column;
//  width: 100%;
//  height: 100%;
//  overflow-y: hidden;
//  .el-card__body {
//    padding: 20px;
//    display: grid;
//    grid-template-rows: 100px calc(100% - 150px) 50px;
//  }
//}
//.no-add-swing-content-small{
//  display: flex;
//  flex-direction: column;
//  width: 100%;
//  height: 100%;
//  overflow-y: hidden;
//  .el-card__body {
//    padding: 20px;
//    display: grid;
//    grid-template-rows: 80px calc(100% - 130px) 50px;
//  }
//}
//.search-top {
//  width: 100%;
//  margin-bottom: 15px;
//  overflow-y: auto;
//}
//.el-range-separator{
//  width: 20px !important;
//}
//// 搜索配合表格样式
//
//// basicContainter 样式--------------
//.basic-container {
//  box-sizing: border-box;
//  .el-card.is-always-shadow {
//    box-shadow: none !important;
//  }
//  &--block {
//    height: 100%;
//    .basic-container__card {
//      height: 100%;
//    }
//  }
//
//  &__card {
//    width: 100%;
//  }
//
//  &:first-child {
//    padding-top: 0;
//  }
//}
//// 结束

// 操作按钮整体样式开始---------------------------
.cell-operate-class{ // 每个单元格 下面的按钮
  .el-button--text{
    span{
      display: inline-block;
      color: #3066FE;
      padding: 0 10px;
      border-right:1px solid #dcdfe6
    }
  }
  .danger-btn{
    span{
      color: #FF4745;
    }
   }
  .green-btn{
    span{
      color: #00BB7D;
    }
  }
  .el-button+.el-button{
    margin-left: 0 !important;
  }
  // 去掉最后一个分割线
  &>:last-child{
     .el-button--text{
      span{
        border-right: none !important;
      }
    }
  }
  // 这个是普通的按钮
  &>:last-child{
       span{
        border-right: none !important;
      }
  }
}

// 操作按钮整体样式结束---------------------------

// 新增编辑的时候  form 表单展示样式  label-position:top
.el-form--label-top{
.el-form-item__label,.el-form-item__content,.el-date-editor,.el-select{
  width: 100%;
}
  .el-form-item{
    display: block !important;
    margin-bottom: 10px !important;
   }
  .el-form-item__label{
    padding-bottom: 0;
  }
  .el-col{
    margin-bottom: 0 !important;
   }
}

// tag自定义样式
.el-tag {
  &.table-custom-tag {
    text-align: center;

    &.el-tag--success {
      color: #009b2a;
      background: #e5fced;
      border: 1px solid #009b2a;
    }

    &.el-tag--primary {
      color: #197df5;
      background: #e3f0ff;
      border: 1px solid #197df5;
    }

    &.el-tag--warning {
      color: #ff7e00;
      background: #fdf8e7;
      border: 1px solid #ff7e00;
    }
  }
}

// 搜索表单
.search-form {
  .el-form-item {
    margin-bottom: 10px;
  }
}

// 表格的描述样式
.el-descriptions--small {
  font-size: 14px;
}