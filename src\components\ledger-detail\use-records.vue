<template>
  <div style="height: 100%">
    <div class="top-info">
      <el-form
        label-suffix="："
        label-width="90px"
        :inline="true"
        ref="search"
        :model="form"
        size="small"
      >
        <el-form-item prop="timeType">
          <el-select
            v-model="form.timeType"
            placeholder="请选择"
            @change="query"
          >
            <el-option label="全部" :value="undefined"></el-option>
            <el-option
              v-for="dict in serviceDicts.type['time_period']"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            icon="el-icon-upload2"
            type="success"
            @click="exportExcel"
            size="mini"
            >导出</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="500px"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="operatorName"
        label="操作人员"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="startDate"
        label="开始时间"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.startDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="endDate"
        label="截止时间"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.endDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="preUseStatus"
        label="使用前状态"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.preUseStatus || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="postUseStatus"
        label="使用后状态"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.postUseStatus || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="remark"
        label="备注"
        align="center"
        show-overflow-tooltip
        width="90px"
      >
        <template slot-scope="scope">
          {{ scope.row.remark || '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-view"
            type="text"
            size="small"
            @click="detail(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/pagination';
  import { getUseLogList } from '@/api/equiment-full-life-api/special-device';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';

  export default {
    name: 'DeviceBasicList',
    serviceDicts: ['time_period'],
    components: {
      Pagination
    },
    props: {
      equipmentId: {
        type: String,
        default: () => {
          return undefined;
        }
      }
    },
    watch: {
      equipmentId: {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.eqId = val;
            this.query();
          } else {
            this.eqId = undefined;
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        eqId: undefined,
        form: {
          timeType: 'THIRTY_DAYS'
        }
      };
    },

    methods: {
      // 导出
      exportExcel() {
        let path = `/api/szyk-simas/special-equipment-usage-log/export?equipmentId=${this.eqId}&`;
        if (Object.keys(this.form).length > 0) {
          let params = '';
          for (const key in this.form) {
            if (this.form[key]) {
              params += `${key}=${this.form[key]}&`;
            }
          }
          path += params;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          `特种设备使用记录${Date.now()}.xlsx`
        );
      },
      query() {
        this.searchParams = { ...this.searchParams, ...this.form };
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getUseLogList({
            ...this.searchParams,
            equipmentId: this.eqId
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    text-align: right;
  }
</style>
