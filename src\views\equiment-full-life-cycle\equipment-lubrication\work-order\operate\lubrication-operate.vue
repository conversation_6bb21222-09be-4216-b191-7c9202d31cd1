<template>
  <dialog-drawer
    title="润滑工单"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <span class="el-base-title">设备信息</span>
      <base-info :details="details"></base-info>

      <span class="el-base-title">润滑标准</span>
      <standard ref="standard" :detail="details"></standard>
      <span class="el-base-title">润滑备品备件消耗记录 </span>
      <spare-part ref="sparePart"></spare-part>
      <span class="el-base-title">备注信息</span>
      <el-input
        v-model="remark"
        type="textarea"
        placeholder="请输入备注信息"
        clearable
        show-word-limit
        :maxlength="200"
        autosize
      >
      </el-input>
    </div>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"></btn>
      <btn type="cancel" @click="closed" :loading="loading"></btn>
    </div>
  </dialog-drawer>
</template>
<script>
  import BaseInfo from '@/views/equiment-full-life-cycle/equipment-lubrication/work-order/detail/base-info.vue';
  import Standard from '@/views/equiment-full-life-cycle/equipment-lubrication/work-order/operate/standard.vue';
  import {
    getOrderDetail,
    oilingOrderButton
  } from '@/api/equiment-full-life-api/oiling';
  import SparePart from '@/views/equiment-full-life-cycle/equipment-maintenance/work-order/operate/spare-part.vue';

  export default {
    name: 'RepairViewIndex',
    components: {
      SparePart,
      BaseInfo,
      Standard
    },
    data() {
      return {
        remark: '',
        eqId: '',
        visible: false,
        loading: false,
        details: {} // 详情数据
      };
    },
    methods: {
      async show(id) {
        this.visible = true;
        await this.getDetail(id);
      },
      // 保养操作接口
      async oilingOperate(standard, materialList) {
        try {
          this.loading = true;
          let params = {
            id: this.details.id,
            equipmentId: this.details.equipmentId,
            remark: this.remark,
            ...standard,
            materialList: materialList
          };
          await oilingOrderButton(params);
          this.visible = false;
          this.$message.success('操作成功');
          this.$emit('success');
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      async submit() {
        this.loading = true;
        try {
          const standard = await this.$refs.standard.validForm();
          console.log(standard);
          const materialList = await this.$refs.sparePart.validForm();
          console.log(materialList);
          // 判断返回的数据， 检查结果都是正常，询问是否提交
          if (standard.isAbnormal === 0) {
            this.$confirm(`检查结果为正常，是否提交？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(async () => {
                try {
                  await this.oilingOperate(standard, materialList);
                } catch (e) {
                  this.$message.warning(e.data.msg);
                }
              })
              .catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                });
              });
          } else {
            console.log('else');
            await this.oilingOperate(standard, materialList);
          }

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      closed() {
        this.visible = false;
        this.details = {};
        this.$refs.standard.resetForm();
        this.$refs.sparePart.resetForm();
        this.visible = false;
        this.remark = undefined;
      },

      // 获取详情接口
      async getDetail(no) {
        try {
          this.loading = true;
          const res = await getOrderDetail({ no: no });
          this.details = res.data.data;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 10px;
  }

  .details {
    padding-bottom: 60px;
  }
</style>
