<template>
  <el-dialog
    title="提示"
    :visible.sync="dialogVisible"
    width="35%"
    :before-close="handleClose"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="90px"
      :label-position="'right'"
      size="small"
    >
      <el-form-item label="驳回原因" prop="rejectReason">
        <el-input
          maxlength="200"
          type="textarea"
          v-model.trim="form.rejectReason"
        />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose" :loading="loading">取 消</el-button>
      <el-button type="primary" @click="submit" :loading="loading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
  import { checkCheckRepairPlanApi } from '@/api/equiment-full-life-api/repair';

  export default {
    data() {
      return {
        dialogVisible: false,
        loading: false,
        form: {
          rejectReason: undefined
        },
        rules: {
          rejectReason: [
            {
              required: true,
              message: '请输入驳回原因',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    methods: {
      show(row) {
        this.dialogVisible = true;
        this.row = row;
      },

      async submit() {
        try {
          let valid = await this.$refs['baseForm'].validate();
          if (valid) {
            this.loading = true;
            await checkCheckRepairPlanApi({
              id: this.row.id,
              rejectReason: this.form.rejectReason,
              status: 2
            });
            this.$message.success('操作成功');
            this.$emit('success');
            this.dialogVisible = false;
            this.loading = false;
          } else {
            this.loading = false;
          }
        } catch (e) {
          console.log(e);
          this.loading = false;
        }
      },
      handleClose() {
        this.form.rejectReason = undefined;
        this.dialogVisible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-form-item {
      width: 100%;
    }
    .el-form-item__content {
      width: calc(100% - 90px);
    }
  }
</style>
