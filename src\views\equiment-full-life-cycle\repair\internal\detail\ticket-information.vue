<template>
  <el-descriptions
    border
    size="small"
    v-if="detail"
    :labelStyle="{ width: '110px', textAlign: 'right' }"
    :contentStyle="{
      width: '300px',
      wordBreak: 'break-all',
      wordWrap: 'break-word'
    }"
    contentClassName="contentClassName"
  >
    <el-descriptions-item label="维修单号：">{{
      detail.no || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="设备编号：">{{
      detail.equipmentAccount.code || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="SN编号：">{{
      detail.equipmentAccount.sn || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="设备名称：">{{
      detail.equipmentAccount.name || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="设备位置：">{{
      detail.equipmentAccount.locationPath || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="维修部位：">{{
      detail.monitorName || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="故障缺陷名称：">{{
      detail.faultName || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="异常等级：">{{
      detail.faultLevelName || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="故障缺陷类型：">{{
      detail.repairTypeName || '-'
    }}</el-descriptions-item>
    <el-descriptions-item v-if="!external" label="维修人：">{{
      detail.receiveUserName || '-'
    }}</el-descriptions-item>
    <el-descriptions-item v-if="!external" label="维修人电话：">{{
      detail.receiveUserTel || '-'
    }}</el-descriptions-item>
    <el-descriptions-item v-if="external" label="跟进人：">{{
      detail.followUserName || '-'
    }}</el-descriptions-item>
    <el-descriptions-item v-if="external" label="跟进人电话：">{{
      detail.followUserTel || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="维修状态：">{{
      detail.statusName || '-'
    }}</el-descriptions-item>
    <el-descriptions-item label="预计完成时间：">{{
      detail.completeTime || '-'
    }}</el-descriptions-item>
    <el-descriptions-item v-if="external" :span="3" label="承修单位：">{{
      detail.supplierName || detail.externalOrg || '-'
    }}</el-descriptions-item>
    <el-descriptions-item :span="3" label="问题描述：">{{
      detail?.faultDefect?.comment || '-'
    }}</el-descriptions-item>
    <el-descriptions-item :span="3" label="上传照片：">
      <template v-if="detail?.faultDefect?.attachList">
        <upload-img
          v-model="detail.faultDefect.attachList"
          placeholder="上传照片"
          :editable="false"
          formatLimit="jpeg,png,jpg"
        />
      </template>
      <template v-else>-</template>
    </el-descriptions-item>
  </el-descriptions>
</template>

<script>
  import UploadImg from '@/components/uploadImage.vue';
  export default {
    components: { UploadImg },
    props: {
      detail: {
        type: Object,
        default: () => {}
      },
      external: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>
<style scoped lang="scss"></style>
