<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <info ref="info" :initData="detail" :managerList="managerList"></info>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"></btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import Info from './base-info.vue';
  import {
    addOrEditCheckRepairDetailApi,
    getCheckRepairDetailViewApi
  } from '@/api/equiment-full-life-api/spare-parts';
  import { getUserList } from '@/api/equiment-full-life-api/common';
  export default {
    name: 'AddDevice',
    components: {
      Info
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        fileData: {},
        managerList: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {
          image: []
        }
      };
    },
    watch: {},
    methods: {
      async getDetail(no) {
        this.loading = true;
        try {
          const res = await getCheckRepairDetailViewApi({ no: no });
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  获取库房管理员列表
      async getUser(row) {
        const res = await getUserList({
          alias: 'warehouse_admin'
        });
        this.managerList = res.data.data;
        if (row.id) {
          this.getDetail(row.no);
        }
      },
      show(row) {
        this.visible = true;
        this.edit = !!row.id;
        this.eqId = row.id;
        this.getUser(row); // 获取仓库管理员
      },

      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.$refs['info'].resetForm();
        this.detail = {};
      },
      async submit() {
        let params = await this.$refs['info'].validForm();
        await this.save({
          id: this.edit ? this.eqId : undefined,
          ...params
        });
      },

      // 提交设备
      async save(params) {
        this.loading = true;
        try {
          await addOrEditCheckRepairDetailApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
