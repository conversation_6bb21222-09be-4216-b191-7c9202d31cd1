<template>
  <basic-container class="table-content" :auto-height="true">
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
      <!--      <el-button-->
      <!--        icon="el-icon-download"-->
      <!--        type="warning"-->
      <!--        size="small"-->
      <!--        @click="importExcel"-->
      <!--        >导入</el-button-->
      <!--      >-->
      <el-button
        icon="el-icon-upload2"
        type="success"
        size="small"
        @click="exportExcel"
        >导出</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="name"
        label="人员姓名"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="tel"
        label="联系电话"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.tel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="deptName"
        label="部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.deptName || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="sexName"
        label="人员性别"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.sexName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="postName"
        label="岗位"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.postName || '-' }}
        </template></el-table-column
      >
      <el-table-column
        prop="qualificationName"
        label="学历"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="age"
        label="年龄"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.age || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="title"
        label="职称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.title || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="workYears"
        label="工作年限"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.workYears || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="attachList"
        label="资质证书"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{
            (scope.row.attachList && scope.row.attachList[0].originalName) ||
            '-'
          }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-edit"
            type="text"
            size="small"
            @click="operate(scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            title="确定删除吗？"
            @confirm="() => handleDelete(scope.row)"
          >
            <el-button
              icon="el-icon-delete"
              slot="reference"
              type="text"
              size="small"
              style="margin-left: 10px"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex"></detail-index>
    <!--       文件导入-->
    <import-files
      ref="file"
      :templateUrl="'/szyk-asset/asset/exportTemplate'"
      action="/szyk-asset/asset/import"
      downLoadFileName="设备台账下载"
      :isCovered="false"
      :isInstanceExport="true"
      @refresh="getList"
    ></import-files>
  </basic-container>
</template>

<script>
  import ImportFiles from '@/components/import-files.vue';
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import {
    userListPage,
    userDelete
  } from '@/api/equiment-full-life-api/employee';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination,
      ImportFiles
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    watch: {},
    mounted() {
      window.addEventListener('resize', this.handleResize);
      this.getList();
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },
    methods: {
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/staff/export-staff?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/staff/export-staff?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '员工列表.xlsx'
        );
      },
      importExcel() {
        this.$refs.file.show();
      },
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.searchParams.current = 1;
        this.exportParams = param;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await userListPage({
            ...this.searchParams,
            deviceId: this.id
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        let id = row ? row.id : undefined;
        this.$refs.add.show(id);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      },

      async handleDelete(row) {
        try {
          await userDelete({ ids: row.id });
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          await this.getList(true);
          // 发信号 通知树更新
        } catch (e) {
          this.$message.warning(e.data.msg);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
