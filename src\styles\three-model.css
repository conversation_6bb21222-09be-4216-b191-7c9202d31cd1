
/* 2D、3D的label的样式 */
.element {
  /* position: absolute; */

  /* transform: translate(-50%, -50%) translate(460px, 195.638px); */
  z-index: 1;
   width: 300px;
  padding: 20px;
  font-family: Arial, sans-serif;
  line-height: normal;
  text-align: center;
  background-color: rgba(0, 127, 127, 49%);
  border: 1px solid rgba(127,255,255,25%);

  /* height: 160px; */
  box-shadow: 0 0 12px rgba(0,255,255,50%);
  cursor: default;
}

.element:hover {
  border: 1px solid rgba(127,255,255,75%);
  box-shadow: 0 0 12px rgba(0,255,255,75%);
}

.element .number {
  /* position: absolute; */

  /* top: 20px; */

  /* right: 20px; */
  color: rgba(127,255,255,75%);
  font-size: 12px;
}

.element .symbol {
  /* position: absolute; */

  /* top: 40px; */

  /* right: 0; */

  /* left: 0; */
  color: rgba(255,255,255,75%);
  font-weight: bold;
  font-size: 18px;
  text-shadow: 0 0 10px rgba(0,255,255,95%);
}

.element .details {
  /* position: absolute; */

  /* right: 0; */

  /* bottom: 15px; */

  /* left: 0; */
  color: rgba(127,255,255,75%);
  font-size: 12px;
}
