<!-- 设备数量统计 = 设备总数、监测设备台数、监测设备部位 commonly-used-function-entrances-->
<template>
  <div class="alarmTrend" v-loading="loading">
    <span class="title">
      <section>
        <span style="color: #333; font-size: 20px"> 快捷入口</span>
      </section>
      <el-button
        @click="customizeSettings"
        size="mini"
        style="margin-top: -8px; color: #1f61ff; font-size: 15px"
        type="text"
        >管理 <i class="el-icon-d-arrow-right"></i
      ></el-button>
    </span>

    <div class="total" v-if="data.length">
      <div
        class="total_item"
        v-for="item in data.slice(0, 6)"
        @click="handleGo(item)"
        :key="item.id"
      >
        <div class="total_item_icon"><i :class="item.icon"></i></div>
        <div class="total_item_title">{{ item.menuName }}</div>
      </div>
    </div>
    <custom-empty :size="70" v-else></custom-empty>
    <commonlyEntrances
      ref="commonlyEntrances"
      @refresh="getAsset"
    ></commonlyEntrances>
  </div>
</template>
<script>
  import { myUsualmenu } from '@/api/home';
  import commonlyEntrances from '../operate/index';
  import { mapGetters } from 'vuex';
  import CustomEmpty from '@/components/custom-empty.vue';
  export default {
    components: { commonlyEntrances, CustomEmpty },
    props: {},
    data() {
      return { loading: false, data: [] };
    },
    computed: {
      ...mapGetters(['menu'])
    },
    mounted() {
      this.$nextTick(async () => {
        await this.getAsset(0);
      });
    },
    methods: {
      handleGo(item) {
        this.$router.push(item.path);
      },
      customizeSettings() {
        this.$refs.commonlyEntrances.show();
      },
      async getAsset() {
        this.loading = true;
        try {
          const res = await myUsualmenu();
          let arr = res.data.data || [];
          this.data = arr;
          if (arr.length) {
            this.flag = true;
          } else {
            this.flag = false;
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      jump(status) {
        this.$router.push({
          path: '/asset-file/index',
          query: {
            status: status
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .alarmTrend {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: var(--el-bg-color-overlay);
    background: #fff;
    border-radius: 5px;
  }

  .title {
    display: flex;
    justify-content: space-between;

    section {
      display: flex;
      width: calc(100% - 220px);
      overflow: hidden;
      text-overflow: ellipsis;
    }

    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    em {
      display: inline-block;
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-style: normal;
      line-height: 10%;
    }
  }

  .total {
    display: grid;
    grid-template-rows: repeat(2, auto); /* 定义两行 */
    grid-template-columns: repeat(3, 1fr); /* 定义两列 */
    gap: 20px;
    height: calc(100% - 45px);
    margin-top: 15px;

    .total_item {
      cursor: pointer;

      .total_item_icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        margin: 0 auto;
        margin-bottom: 8px;
        background-color: #e8efff;
        border-radius: 10px;
        box-shadow: 1px 1px 1px #efe7e7;

        i {
          color: #155cff;
          font-weight: 600;
          font-size: xx-large;
          line-height: 56px;
        }
      }

      .total_item_title {
        margin-top: 10px;
        overflow: hidden;
        color: #333;
        font-size: 14px;
        white-space: nowrap;
        text-align: center;
        text-overflow: ellipsis;
      }
    }

    .total_item:hover {
      // background-color: #00a385;
      .total_item_icon {
        box-shadow: 1px 1px 4px #c3c3c3;
      }
    }
  }
</style>
