<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search
        ref="search"
        @search="onsubmit"
        :isShowDept="false"
        :isShowQueryType="true"
      >
      </search>
      <!-- <el-button
        slot="button"
        icon="el-icon-upload2"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
      :default-sort="{ prop: 'count', order: 'ascending' }"
      @sort-change="handleSortChange"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>

      <el-table-column
        prop="name"
        label="备品备件名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="model"
        label="备品备件类型"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.model || '-' }}
        </template>
      </el-table-column> -->
      <el-table-column
        prop="bizModuleName"
        label="消耗方式"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.bizModuleName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="count"
        label="消耗数量"
        align="center"
        show-overflow-tooltip
        sortable="custom"
        :sort-orders="['ascending', 'descending']"
      >
        <template slot-scope="scope">
          {{ scope.row.count || '-' }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
  </basic-container>
</template>

<script>
  import Search from '../component/search.vue';
  import Pagination from '@/components/pagination/index.vue';
  import { convertFileUrl, downloadFileBlob } from '@/util/util';
  import { getSpareStatistics } from '@/api/equiment-full-life-api/statement-statistics';
  import { getToken } from '@/util/auth';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      Pagination
    },
    props: {},
    data() {
      return {
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10,
          asc: 1
        },
        exportParams: {}
      };
    },

    methods: {
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/statistical-report/export-component-material?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/statistical-report/export-component-material?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '备品备件耗损统计.xlsx'
        );
      },
      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getSpareStatistics({
            ...this.searchParams
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 监听表格排序
      handleSortChange({ order }) {
        this.searchParams.asc = order === 'ascending' ? 1 : 0;
        this.getList();
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
