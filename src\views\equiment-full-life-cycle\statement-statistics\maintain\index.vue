<template>
  <basic-container class="table-content" :auto-height="true">
    <div class="top-info">
      <search
        ref="search"
        :statusArr="serviceDicts.type['order_status']"
        :isShowOrderStatus="activeName === 'order'"
        @search="onsubmit"
      >
      </search>
      <!-- <el-button
        slot="button"
        icon="el-icon-upload2"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="保养工单统计" name="order">
        <order-statics ref="order" :list="list"></order-statics>
      </el-tab-pane>
      <el-tab-pane label="保养设备统计" name="device">
        <device-statics ref="device" :list="list"></device-statics>
      </el-tab-pane>
    </el-tabs>
  </basic-container>
</template>

<script>
  import Search from '../component/search.vue';
  import DeviceStatics from './component/device.vue';
  import OrderStatics from './component/order.vue';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      DeviceStatics,
      OrderStatics
    },
    serviceDicts: ['order_status'],
    props: {},
    data() {
      return {
        activeName: 'order',
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    mounted() {},
    // 页面销毁取消监听
    methods: {
      async exportExcel() {
        let params = '';
        let path =
          this.activeName === 'device'
            ? `/api/szyk-simas/statistical-report/export-equipment-maintain?bizModule=MAINTAIN_ORDER&`
            : `/api/szyk-simas/statistical-report/export-maintain-order?`;

        if (Object.keys(this.exportParams).length >= 0) {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
        }
        downloadFileBlob(
          ` ${path}${params}${this.website.tokenHeader}=${getToken()}`,
          this.activeName === 'device'
            ? '保养设备统计.xlsx'
            : '保养工单统计.xlsx'
        );
      },
      handleClick() {
        this.searchParams.current = 1;
        // this.getLists();
        this.$refs.search.resetStatus();
      },
      getLists() {
        this.searchParams.current = 1;
        this.activeName === 'device'
          ? this.$refs['device'].getData(this.searchParams)
          : this.$refs['order'].getData(this.searchParams);
      },
      onsubmit(param) {
        this.exportParams = { ...param };
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getLists(this.searchParams);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }

  ::v-deep {
    .el-tabs {
      height: calc(100% - 150px);
    }

    .el-tabs__content {
      height: calc(100% - 10px);
    }

    .el-tab-pane {
      height: calc(100% - 50px);
    }
  }
</style>
