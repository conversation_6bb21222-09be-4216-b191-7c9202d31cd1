<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @close="close"
    class="detail-drawer"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <section>
        <span class="el-base-title">基础信息</span>
        <base-info :details="details"></base-info>
      </section>
      <section>
        <span class="el-base-title">文件资料</span>
        <file-name :details="details"></file-name>
      </section>

      <!-- 部位信息 -->
      <section>
        <span class="el-base-title">部位信息</span>
      </section>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from './base-info.vue'; // 设备基本信息
  import { convertFileUrl } from '@/util/util';
  import { userDetail } from '@/api/equiment-full-life-api/employee';

  export default {
    name: 'RepairViewIndex',
    components: { BaseInfo },
    data() {
      return {
        convertFileUrl,
        id: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {}, // 详情数据
        monitorList: []
      };
    },

    methods: {
      // 点击展示
      async show(id) {
        this.visible = true;
        if (id) {
          this.id = id;
          await this.getDetail();
        }
      },

      // 获取详情接口
      async getDetail() {
        try {
          this.loading = true;
          const res = await userDetail(this.id);
          this.details = res.data.data;

          this.loading = false;
        } catch (e) {
          this.loading = false;
          this.details = {};
        }
      },
      // 关闭弹窗
      close() {
        this.visible = false;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
