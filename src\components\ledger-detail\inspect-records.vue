<template>
  <div style="height: 100%">
    <div class="top-info">
      <el-form
        label-suffix="："
        label-width="90px"
        :inline="true"
        ref="search"
        :model="searchParams"
        size="small"
        class="search-form"
      >
        <el-form-item label="检验类型" prop="inspectType">
          <el-select
            v-model="searchParams.inspectType"
            placeholder="请选择检验类型"
            clearable
          >
            <el-option
              v-for="item in serviceDicts.type['se_inspect_type']"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检验结论" prop="inspectResult">
          <el-select
            v-model="searchParams.inspectResult"
            placeholder="请选择检验结论"
            clearable
          >
            <el-option
              v-for="item in serviceDicts.type['se_inspect_result']"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <btn type="search" @click="getList" />
          <btn type="reset" @click="reset" />
        </el-form-item>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="500px"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="inspectTypeName"
        label="检验类型"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="inspectDate"
        label="检验日期"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="nextInspectDate"
        label="下次检验日期"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="inspectResultName"
        label="检验结论"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="updateTime"
        label="更新日期"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="updateUserName"
        label="更新人"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button type="text" size="small" @click="handleEdit(scope.row)"
            >编辑</el-button
          >
          <el-button type="text" size="small" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!-- 查看弹窗 -->
    <detail-dialog ref="detailDialog"></detail-dialog>
    <!-- 编辑弹窗 -->
    <edit-dialog ref="editDialog" @refresh="getList"></edit-dialog>
  </div>
</template>

<script>
  import Pagination from '@/components/pagination';
  import DetailDialog from './register-detail-dialog.vue';
  import EditDialog from './register-dialog.vue';
  import {
    getInspectLogList,
    removeInspectLog
  } from '@/api/equiment-full-life-api/special-device';

  export default {
    name: 'DeviceBasicList',
    serviceDicts: ['se_inspect_type', 'se_inspect_result'],
    components: {
      Pagination,
      DetailDialog,
      EditDialog
    },
    props: {
      equipmentId: {
        type: String,
        default: () => {
          return undefined;
        }
      }
    },
    watch: {
      equipmentId: {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.eqId = val;
            this.getList();
          } else {
            this.eqId = undefined;
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10,
          inspectType: undefined,
          inspectResult: undefined
        },
        eqId: undefined,
        form: {
          timeType: 'THIRTY_DAYS'
        }
      };
    },

    methods: {
      // 查看
      handleView(row) {
        this.$refs.detailDialog.show(row);
      },
      // 编辑
      handleEdit(row) {
        this.$refs.editDialog.show(row, true);
      },
      // 删除
      handleDelete(row) {
        this.$confirm('确定删除该记录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            await removeInspectLog({ ids: row.id });
            this.$message({
              type: 'success',
              message: '删除成功'
            });
            this.onsubmit();
          } catch (e) {
            this.$message.warning(e.data.msg);
          }
        });
      },

      async getList() {
        this.loading = true;
        try {
          let res = await getInspectLogList({
            ...this.searchParams,
            equipmentId: this.eqId
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      reset() {
        this.$refs.search.resetFields();
        this.onsubmit();
      },

      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
  }
</style>
