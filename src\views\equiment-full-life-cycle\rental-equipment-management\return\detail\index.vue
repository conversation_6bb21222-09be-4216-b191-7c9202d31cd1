<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :tip="tip"
    :title="'归还登记'"
    :visible.sync="visible"
    @closed="closed"
    class="detail-drawer"
    size="80%"
  >
    <span class="return-tips"></span>
    <div style="padding-bottom: 50px" v-loading="loading" v-if="visible">
      <!-- 基本信息 -->
      <section>
        <p class="el-base-title">归还设备</p>
        <base-info :details="details"></base-info>
      </section>
      <p class="el-base-title">归还明细</p>
      <plan-cycle
        ref="info"
        :initData="details"
        @changeTime="changeTime"
      ></plan-cycle>
      <p class="el-base-title">归还资料</p>
      <div class="file-flow">
        <span class="red">*</span>
        <upload-file
          v-model="attachList"
          placeholder=""
          class="upload-file-plugin"
          accept=".jpg, .png, .jpeg, .xlsx, .pdf, .docx, .md"
          :url="returnUrl"
          :limit="9"
        ></upload-file>
      </div>

      <div class="oper_btn">
        <el-button
          class="el-icon-circle-plus-outline"
          size="small"
          type="primary"
          @click="submit"
          :loading="loading"
        >
          提交</el-button
        >
        <btn type="cancel" @click="closed"></btn>
      </div>
    </div>
  </dialog-drawer>
</template>

<script>
  import SelAsset from './sel-asset.vue';
  import BaseInfo from './base-info.vue';
  import PlanCycle from './plan-cycle.vue';
  import {
    getAccountDetail,
    deviceback
  } from '@/api/equiment-full-life-api/ledger';
  import UploadFile from '@/components/upload-file.vue';
  export default {
    name: 'RepairViewIndex',
    components: { BaseInfo, SelAsset, PlanCycle, UploadFile },
    data() {
      return {
        id: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {}, // 详情数据
        attachList: [],
        returnUrl: '/api/szyk-system/attach/put-file-attach-for-simas',
        tip: '注意！租赁设备归还后，设备台账中将不再显示租赁设备记录。'
      };
    },

    methods: {
      async submit() {
        let params = await this.$refs['info'].validForm();
        if (params) {
          let [actualStartLeaseDate, actualEndLeaseDate] = params.time || [];
          let fileIds = (this.attachList || []).map(
            (item) => item.id || item.attachId
          );
          if (!fileIds.length) return this.$message.warning('请上传归还资料');
          await this.confirm(
            '归还后设备台账中将不再显示租赁设备记录。确认归还？'
          );
          await this.save({
            ...params,
            deviceId: this.details.id,
            actualStartLeaseDate,
            actualEndLeaseDate,
            time: null,
            fileIds
          });
        }
      },

      // 提交计划
      async save(params) {
        this.loading = true;
        try {
          await deviceback(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.closed();

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  基本信息时间改变
      changeTime(obj) {
        console.log('时间', obj);
        this.timeObj = { ...obj };
      },
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getAccountDetail({ id: id });
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 点击展示
      async show(id) {
        this.visible = true;
        if (id) {
          await this.getDetail(id);
        }
      },

      // 关闭弹窗
      closed() {
        this.visible = false;
        this.attachList = [];
        this.detail = {};
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    .file-flow {
      display: flex;

      .red {
        margin-right: 8px;
        color: #f56c6c;
      }

      .upload-file-plugin {
        flex: 1;
      }
    }

    .return-tips {
      margin: 20px 0;
      color: #f59a23;
      font-size: 14px;
      text-align: left;
    }

    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
