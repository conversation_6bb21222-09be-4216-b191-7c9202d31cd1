<template>
  <div>
    <el-dialog
      append-to-body
      :title="isEdit ? '编辑' : '新增'"
      :visible.sync="appendFormDialogVisible"
      width="50%"
      class="add-tree-item"
      @closed="closed"
      :close-on-click-modal="false"
      @opened="open"
    >
      <el-row>
        <el-form
          :model="appendForm"
          :rules="appendFormRules"
          style="margin-top: 10px"
          label-position="right"
          label-width="90px"
          label-suffix="："
          ref="appendForm"
          size="small"
        >
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="上级类型" prop="codePath">
                {{
                  (currentRow.pathName &&
                    currentRow.pathName.replaceAll(',', '/')) ||
                  '-'
                }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="categoryName" label="类型名称">
                <el-input
                  v-model.trim="appendForm.categoryName"
                  placeholder="请输入类型名称"
                  maxlength="50"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="remark" label="类型编码">
                <el-input
                  v-model.trim="appendForm.remark"
                  placeholder="请输入类型编码"
                  maxlength="50"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="appendFormDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="appendFormSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import {
    addOrEditDeviceType,
    getDeviceTypeDetail
  } from '@/api/equiment-full-life-api/device-type';

  export default {
    name: 'AddTreeItem',
    components: {},
    data() {
      return {
        isEdit: false,
        loading: false,
        cascaderLoading: false,
        currentRow: { path: '' },
        appendForm: {
          categoryName: '',
          remark: ''
        },
        details: {},
        appendFormDialogVisible: false,
        appendFormRules: {
          categoryName: {
            required: true,
            message: '请输入类型名称',
            trigger: 'blur'
          }
        }
      };
    },

    methods: {
      open() {
        this.$nextTick(() => {
          if (this.isEdit && this.currentRow) {
            this.appendForm.name = this.currentRow.name;
            this.appendForm.remark = this.currentRow.remark;
          } else if (this.currentRow) {
          }
        });
      },
      show(type, row) {
        this.isEdit = type === 'edit';
        this.appendFormDialogVisible = true;
        this.currentRow = row;
        if (type === 'edit') {
          this.getDetail();
        }
      },

      closed() {
        this.appendFormDialogVisible = false;
        this.root = false;
        this.locationShow = false;
        this.details = {};
        this.isEdit = false;
        this.$refs['appendForm'].resetFields();
        this.currentRow = { path: '' };
      },
      // 消息结果展示 获取详情信息
      async getDetail() {
        this.loading = true;
        try {
          let res = await getDeviceTypeDetail({ id: this.currentRow.id });
          this.appendForm.categoryName = res.data.data.categoryName;
          this.appendForm.remark = res.data.data.remark;
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      appendFormSubmit() {
        this.$refs.appendForm.validate(async (validate) => {
          if (validate) {
            this.loading = true;
            console.log(this.currentRow);
            const form = {
              categoryName: this.appendForm.categoryName,
              remark: this.appendForm.remark,
              parentId: this.currentRow.id,
              id: this.type === 'edit' ? this.currentRow.id : ''
            };
            if (this.isEdit) {
              const form = { ...this.details, ...this.appendForm };
              await addOrEditDeviceType(form);
            } else {
              await addOrEditDeviceType(form);
            }

            this.$message.success('操作成功');
            this.loading = false;
            this.appendFormDialogVisible = false;
            //  当前这个节点，下面是不是存在节点
            let params;
            if (this.currentRow.hasChildren) {
              params = this.isEdit
                ? { id: this.currentRow.parentId }
                : this.currentRow;
            } else {
              params = {
                id: this.currentRow.parentId
              };
            }
            this.$emit('success', params);
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-tree-item {
    ::v-deep .el-dialog__body {
      padding: 5px 20px;
    }

    ::v-deep .el-input-number {
      width: 100%;
    }

    .tool {
      display: flex;
      align-items: center;
      width: 100%;
    }
  }
</style>
