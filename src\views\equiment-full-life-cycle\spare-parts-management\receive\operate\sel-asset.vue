<template>
  <div>
    <el-button
      size="small"
      type="primary"
      style="margin-bottom: 15px"
      @click="selAsset"
      v-if="isShow"
      >+ 选择备品备件库存</el-button
    >
    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
    >
      <el-table
        v-loading="loading"
        class="table"
        :data="form.list"
        border
        :header-cell-style="{ background: '#fafafa' }"
        size="small"
        stripe
      >
        <el-table-column type="index" label="#"></el-table-column>
        <el-table-column prop="name" label="备品备件名称" show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="no" label="备品备件编号" show-overflow-tooltip>
          <template v-slot="{ row }">{{ row.no || '-' }}</template>
        </el-table-column>
        <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
          <template v-slot="{ row }">{{ row.model || '-' }}</template>
        </el-table-column>
        <el-table-column
          prop="warehouseName"
          label="库房名称"
          show-overflow-tooltip
        >
          <template v-slot="{ row }" style="color: red">{{
            row.warehouseName || '-'
          }}</template>
        </el-table-column>
        <el-table-column
          prop="measureUnitName"
          label="计量单位"
          show-overflow-tooltip
        >
          <template v-slot="{ row }">{{ row.measureUnitName || '-' }}</template>
        </el-table-column>

        <el-table-column
          v-if="isShow"
          prop="currentQuantity"
          label="当前库存"
          show-overflow-tooltip
        >
          <template v-slot="{ row }" style="color: red">{{
            row.currentQuantity || '-'
          }}</template>
        </el-table-column>
        <el-table-column prop="issuanceQuantity" label="领用数量" width="300px">
          <template v-slot="scope">
            <el-form-item
              v-if="isShow"
              :prop="'list.' + scope.$index + '.issuanceQuantity'"
              :rules="[
                {
                  required: true,
                  message: '请输入领用数量',
                  trigger: 'blur'
                },
                {
                  validator: (rule, value, callback) =>
                    validateCode(rule, value, callback)
                }
              ]"
              label=" "
            >
              <!--              <el-input-number-->
              <!--                size="small"-->
              <!--                :controls="false"-->
              <!--                v-model="scope.row.issuanceQuantity"-->
              <!--                :min="1"-->
              <!--                :max="Number(scope.row.currentQuantity)"-->
              <!--                :precision="scope.row.measureUnitPrecision"-->
              <!--                placeholder="请输入领用数量"-->
              <!--              ></el-input-number>-->
              <el-input
                style="width: 240px"
                size="small"
                placeholder="请输入领用数量"
                v-model.trim="scope.row.issuanceQuantity"
                clearable
                maxlength="50"
              />
            </el-form-item>
            <span v-else>{{ scope.row.issuanceQuantity }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="model" label="操作" v-if="isShow">
          <template v-slot="scope">
            <el-button
              size="mini"
              type="text"
              style="color: red"
              @click="del(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <asset-list
      ref="assetList"
      @getAssetList="getAssetList"
      module="receive"
      showWarehouseSearch
    ></asset-list>
  </div>
</template>

<script>
  import AssetList from '@/views/equiment-full-life-cycle/components/select-receive-spare-parts-dialog';
  import { validateValueThen0 } from '@/util/func';
  export default {
    name: 'DeviceBasicList',
    components: { AssetList },
    props: {
      detail: {
        type: Object,
        default: () => {}
      },
      // 是操作还是详情
      isShow: {
        type: Boolean,
        default: () => {
          return true;
        }
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              let list = this.detail.itemList.map((item) => {
                return {
                  ...item,
                  name: item.dictName,
                  no: item.dictNo
                };
              });
              this.form.list = list;
            });
          }
        }
      }
    },
    data() {
      const validateCode = (rule, value, callback) => {
        // 获取当前数组
        let arr = rule.field.split('.');
        let idx = Number(arr[1]);
        let max = this.form.list[idx].currentQuantity;
        let measureUnitPrecision = this.form.list[idx].measureUnitPrecision;
        let val = validateValueThen0(value, max, measureUnitPrecision);
        if (!value) {
          callback();
        } else if (val) {
          callback();
        } else {
          callback(
            new Error(
              `请输入大于0且小于${max}的值，精度${measureUnitPrecision}`
            )
          );
        }
      };
      return {
        validateCode,
        loading: false,
        total: 0,
        form: {
          list: []
        },
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },

    mounted() {},
    methods: {
      del(scope) {
        this.form.list.splice(scope.$index, 1);
      },
      async validForm() {
        if (this.form.list.length === 0) {
          this.$message.warning('请选择备品备件');
          return;
        }
        let valid = await this.$refs['listForm'].validate();
        if (valid) {
          let p = this.form.list.map((it) => {
            return {
              dictId: it.dictId,
              issuanceQuantity: it.issuanceQuantity,
              stockId: it.stockId,
              warehouseId: it.warehouseId
            };
          });
          return p;
        } else {
          return false;
        }
      },
      resetForm() {
        this.form.list = [];
      },
      //  备品备件回显
      getAssetList(list) {
        console.log(list);
        let arr = [...list];
        let arrList = arr.map((item) => {
          return {
            no: item.dictNo,
            dictId: item.dictId, // 这两个id必传
            dictName: item.dictName,
            dictNo: item.dictNo,
            dictModel: item.model,
            stockId: item.id, // 这两个id必传
            name: item.dictName,
            model: item.model,
            issuanceQuantity: item.issuanceQuantity,
            warehouseId: item.warehouseId,
            warehouseName: item.warehouseName,
            measureUnitName: item.measureUnitName,
            measureUnitId: item.measureUnitId,
            currentQuantity: item.currentQuantity,
            measureUnitPrecision: item.measureUnitPrecision
          };
        });
        this.form.list = [...arrList];
      },

      //  点击选择的时候
      selAsset() {
        let list = this.form.list.map((it) => {
          return {
            ...it,
            id: it.stockId, // 这个是唯一匹配库存列表的数据
            num: 1
          };
        });

        this.$refs['assetList'].show(list);
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }
  .operateBtn {
    margin-bottom: 15px;
  }
  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
