<template>
  <el-form
    class="_form"
    ref="form"
    :model="form"
    size="small"
    :inline="true"
    label-suffix="："
  >
    <!--    <el-form-item label="设备编号" prop="code">-->
    <!--      <el-input-->
    <!--        v-model="form.code"-->
    <!--        size="small"-->
    <!--        placeholder="请输入设备编号"-->
    <!--        style="margin-right: 10px"-->
    <!--        :maxlength="50"-->
    <!--        clearable-->
    <!--      >-->
    <!--      </el-input>-->
    <!--    </el-form-item>-->
    <el-form-item label="设备SN" prop="sn">
      <el-input
        v-model="form.sn"
        size="small"
        placeholder="请输入设备SN"
        style="margin-right: 10px"
        :maxlength="50"
        clearable
      >
      </el-input>
    </el-form-item>
    <el-form-item label="设备名称" prop="name">
      <el-input
        v-model="form.name"
        size="small"
        placeholder="请输入设备名称"
        style="margin-right: 10px"
        :maxlength="50"
        clearable
      >
      </el-input>
    </el-form-item>

    <el-form-item>
      <el-button
        type="primary"
        icon="el-icon-search"
        @click="onSubmit"
        size="small"
        >搜索</el-button
      >
    </el-form-item>
    <el-form-item>
      <el-button @click="reset" size="small" type="reset" icon="el-icon-delete"
        >清空</el-button
      >
    </el-form-item>
  </el-form>
</template>

<script>
  export default {
    name: 'alarmSearch',
    components: {},
    data() {
      return {
        form: {
          sn: undefined,
          name: undefined
        }
      };
    },
    created() {},
    methods: {
      onSubmit() {
        let searchParams = {
          ...this.form
        };
        this.$emit('query', searchParams);
      },
      reset() {
        this.$refs['form'].resetFields();
        this.$emit('query', this.form);
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep {
    .el-form {
      margin-bottom: 15px;
    }
  }
</style>
