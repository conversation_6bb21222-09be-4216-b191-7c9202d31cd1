<template>
  <div class="maintenance-standards-wrap">
    <!-- <el-form
      :model="standardForm"
      label-suffix="："
      ref="baseForm"
      label-width="110px"
      :label-position="'right'"
      size="small"
    > -->
    <standard-item
      v-for="(item, index) in standardForm.standardList"
      :key="item.id"
      :ref="item.id"
      :standard-Index="index"
      :standard-item="item"
    ></standard-item>
    <!-- </el-form> -->
  </div>
</template>

<script>
  import StandardItem from './standard-item.vue';
  export default {
    components: { StandardItem },
    props: {
      standardForm: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {
      async validForm() {
        try {
          // let bool = false;
          // this.standardForm.standardList.forEach((item,index)=>{
          //    bool  this.$refs[item.id].validForm();
          // })
          let standardList = this.standardForm.standardList;
          let valid = true;
          for (const item of standardList) {
            let bool = await this.$refs[item.id][0].validForm();
            if (!bool) {
              valid = false;
              return false;
            }
          }
          return valid;
          // let valid = await this.$refs['baseForm'].validate();
          // if (valid) {
          //   return true;
          // } else {
          //   return false;
          // }
        } catch (error) {
          return false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .maintenance-standards-wrap {
    padding: 0 20px;

    .el-base-title {
      color: #409eff;
    }
  }
</style>
