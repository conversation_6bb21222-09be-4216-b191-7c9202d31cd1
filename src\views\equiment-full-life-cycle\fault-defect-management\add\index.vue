<template>
  <dialog-drawer
    title="新增"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="add-wrapper" v-loading="loading">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        inline
        label-width="100px"
        label-suffix="："
      >
        <el-collapse v-model="collapseActive">
          <!-- 基本信息 -->
          <el-collapse-item name="1">
            <span slot="title" class="el-base-title">基本信息</span>
            <el-row :gutter="15">
              <el-col :span="8">
                <el-form-item label="设备编号" prop="equipmentCode">
                  <div class="content-wrapper">
                    {{ form.equipmentCode || '-' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="SN编号" prop="equipmentSn">
                  <div class="content-wrapper">
                    {{ form.equipmentSn || '-' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备名称" prop="name">
                  <el-input
                    v-model="form.name"
                    readonly
                    placeholder="请选择设备名称"
                    @focus.prevent="onChooseDeviceClick"
                  >
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="规格型号" prop="model">
                  <div class="content-wrapper">
                    {{ form.model || '-' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备位置" prop="locationName">
                  <div class="content-wrapper">
                    {{ form.locationName || '-' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="使用部门" prop="useDeptName">
                  <div class="content-wrapper">
                    {{ form.useDeptName || '-' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="使用人" prop="userName">
                  <div class="content-wrapper">
                    {{ form.userName || '-' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="报修人" prop="reportUser">
                  <el-select
                    v-loading="userListLoading"
                    v-model="form.reportUser"
                    filterable
                    @change="handleUserChange"
                  >
                    <el-option
                      v-for="user in userList"
                      :key="user.id"
                      :label="user.realName"
                      :value="user.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
          <!-- 异常信息 -->
          <el-collapse-item name="2">
            <span slot="title" class="el-base-title">异常信息</span>
            <el-row :gutter="15">
              <el-col :span="8">
                <el-form-item label="异常部位" prop="monitorId">
                  <el-select
                    v-loading="monitorListLoading"
                    v-model="form.monitorId"
                    filterable
                    allow-create
                    @change="handleSiteChange"
                  >
                    <el-option
                      v-for="item in monitorList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="异常等级" prop="abnormalLevel">
                  <el-select v-model="form.abnormalLevel" style="width: 100%">
                    <el-option
                      v-for="item in serviceDicts.type['defect_level']"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="异常描述" prop="abnormalComment">
                  <el-input
                    type="textarea"
                    v-model="form.abnormalComment"
                    placeholder="请输入异常描述"
                  >
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="缺陷材料" prop="attachId">
                  <upload-file
                    ref="file"
                    v-model="form.attachList"
                    url="/api/szyk-system/attach/put-file-attach-for-simas"
                    btnText="上传材料"
                    accept=".jpg, .png, .jpeg, .xlsx, .pdf, .docx, .md"
                    :limit="9"
                    @input="handleSuccess"
                  ></upload-file>
                </el-form-item>
              </el-col>
            </el-row>
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </div>
    <!--     状态是待处理的时候才展示提交-->
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
    <!-- 设备弹窗 -->
    <asset-list
      ref="assetList"
      @on-choose="onChooseDeviceSuccess"
      selType="maintain"
      :statusList="[4]"
    ></asset-list>
  </dialog-drawer>
</template>
<script>
  import UploadFile from '@/components/upload-file.vue';
  import AssetList from '@/views/equiment-full-life-cycle/components/select-device-dialog/index.vue';
  import { getUserInfo } from '@/api/system/user';
  import { getUserList } from '@/api/system/dept';
  import { getPartList } from '@/api/equiment-full-life-api/maintenance';
  import { addDefect } from '@/api/equiment-full-life-api/defect';
  import dayjs from 'dayjs';

  export default {
    name: 'RepairAddIndex',
    components: { UploadFile, AssetList },
    serviceDicts: ['defect_level'],
    data() {
      return {
        collapseActive: ['1', '2'],
        visible: false,
        loading: false,
        // 用户列表
        userListLoading: false,
        userList: [],
        // 部位列表
        monitorListLoading: false,
        monitorList: [],
        // 表单
        form: {
          name: undefined,
          monitorId: undefined,
          abnormalComment: undefined,
          abnormalLevel: undefined,
          attachList: []
        },
        rules: {
          name: [
            {
              required: true,
              message: '请选择设备',
              trigger: 'change'
            }
          ],
          reportUser: [
            { required: true, message: '请选择报修人', trigger: 'change' }
          ],
          monitorId: [
            { required: true, message: '请选择异常部位', trigger: 'change' }
          ],
          abnormalLevel: [
            { required: true, message: '请选择异常等级', trigger: 'change' }
          ],
          abnormalComment: [
            { required: true, message: '请输入异常描述', trigger: 'change' }
          ]
        }
      };
    },
    methods: {
      // 文件上传成功回调
      handleSuccess(files) {
        console.log('ff', files, this.form.attachList);
      },
      // 获取用户列表
      async getUserList() {
        try {
          this.userListLoading = true;
          const res = await getUserList({
            size: -1
          });
          this.userList = res.data.data.records;
          this.userListLoading = false;

          // 默认选中当前用户
          const { data } = await getUserInfo();
          this.$set(this.form, 'reportUser', data.data.id);
          this.$set(this.form, 'reportDept', data.data.deptId);
        } catch (e) {
          this.userListLoading = false;
        }
      },
      // 获取部位列表
      async getMonitorList() {
        try {
          this.monitorList = [];
          this.monitorListLoading = true;
          const res = await getPartList({ equipmentId: this.form.equipmentId });
          this.monitorList = res.data.data;
          this.monitorListLoading = false;
        } catch (e) {
          this.monitorListLoading = false;
          this.monitorList = [];
        }
      },
      // 显示选择的设备弹窗
      onChooseDeviceClick() {
        this.$refs.assetList.show([], true);
      },
      // 选择设备回调
      onChooseDeviceSuccess(device) {
        console.log('device', device);
        const form = {};
        form.equipmentCode = device.code;
        form.equipmentSn = device.sn;
        form.equipmentId = device.id;
        form.model = device.model;
        form.locationName = device.locationName;
        form.useDeptName = device.useDeptName;
        form.userName = device.userName;
        this.form.monitorId = undefined;
        this.form.monitorName = undefined;
        Object.assign(this.form, form);
        this.$set(this.form, 'name', device.name);

        this.getMonitorList();
      },
      // 选择用户回调
      handleUserChange(val) {
        const item = this.userList.find((item) => item.id === val);
        if (item) {
          this.form.reportDept = item.deptId;
        }
      },
      // 选择设备部位回调
      handleSiteChange(val) {
        const item = this.monitorList.find((item) => item.id === val);
        if (item) {
          this.form.monitorName = item.name;
        } else {
          this.form.monitorName = val;
        }
      },
      // 重置表单
      resetForm() {
        this.$refs.form && this.$refs.form.resetFields();
        this.monitorList = [];
        this.form.attachList = [];
      },
      // 新增故障缺陷 - 提交
      async handleSubmit() {
        try {
          const {
            monitorId,
            monitorName,
            abnormalLevel,
            abnormalComment,
            equipmentId,
            reportUser
          } = this.form;
          // 组装异常信息
          const faultDefectAbnormal = {
            monitorId,
            monitorName,
            abnormalLevel,
            abnormalComment
          };
          if (this.form.attachList && this.form.attachList.length > 0) {
            faultDefectAbnormal.abnormalImage = this.form.attachList
              .map(({ attachId, id }) => attachId || id)
              .join(',');
          }
          // 处理维修部位 - 判断是否是自定义部位
          const flag = this.monitorList
            .map((item) => item.id)
            .includes(faultDefectAbnormal.monitorId);
          if (!flag) faultDefectAbnormal.monitorId = undefined;
          // 组装参数
          const params = {
            equipmentId,
            reportUser,
            faultDefectAbnormal,
            source: 'MANUAL',
            reportTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
          };
          await addDefect(params);
          this.$message.success('操作成功');
          this.visible = false;
          this.$emit('success');
        } catch (e) {
          console.error(e);
        }
      },
      // 新增故障缺陷 - 校验
      submit() {
        this.$refs.form.validate(async (valid) => {
          if (!valid) return;
          this.handleSubmit();
        });
      },
      // 关闭处理
      closed() {
        this.resetForm();
        this.visible = false;
      },
      // 点击展示
      async show() {
        this.visible = true;
        this.getUserList();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-wrapper {
    padding-bottom: 60px;
  }

  .el-collapse {
    border: none;

    ::v-deep .el-collapse-item__wrap {
      border-bottom: none;
    }

    ::v-deep .desc-content {
      width: 22%;
    }

    .content-wrapper {
      max-width: 200px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .el-form-item,
    .el-select {
      width: 100%;
    }

    ::v-deep .el-form-item__content {
      width: calc(100% - 120px);
    }
  }
</style>
