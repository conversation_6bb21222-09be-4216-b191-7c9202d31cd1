import JsBarcode from 'jsbarcode';
import { Message } from 'element-ui';
import Qrcode from 'qrcode';
import { convertFileUrl } from '@/util/util';

//打印条码
export const printBarCode = (list) => {
  let html = '';
  html += `<div
    style="width: 188px; background: #ccc"
    class="barcode-container"
  >`;
  list.forEach((item) => {
    html += `<div style="width: 188px; background: #ccc">
          <svg id="${item.codeId}"></svg>
          <section style="text-align: center; font-size: 12px; margin-left: 10px">
            <div>使用人:${item.userName}</div>
            <div>使用部门：:${item.userDept}</div>
          </section>
        </div>`;
  });
  html += `</div>`;
  Message.success('请稍后，正在生成打印队列，成功后会自动跳转打印');

  let newWin = window.open('');
  newWin.document.write(html);
  list.forEach(async (it) => {
    console.log(newWin.document.getElementById(`${it.codeId}`));
    let ele = newWin.document.getElementById(`${it.codeId}`);
    if (ele) {
      await JsBarcode(ele, it.code, {
        format: 'CODE128',
        width: 1.5,
        height: 70,
        displayValue: true,
        fontSize: 15,
        margin: 10,
        background: '#FFFFFF', // 设置背景色为白色
        lineColor: '#000000' // 设置线条颜色为黑色
      });
    }
  });
  newWin.document.close();

  newWin.document.head.innerHTML = `<meta name="viewport" content="width=device-width, initial-scale=1">

  <style>
   * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      word-break: break-all;
      font-family: SimSun;
    }
    section{
      padding:10px;
    }

  </style>`;
  newWin.onload = function () {
    newWin.focus(); // 处理ie兼容
    newWin.print();
  };

  newWin.print();
};
//打印二维码
export const printQrCode = (list) => {
  let html = '';
  list.forEach((item) => {
    html += `<div class="block">
          <canvas id="${item.code}"></canvas>
          <section>
            <div class="label">资产编号:${item.code}</div>
            <div class="label">使用人:${item.useUserName}</div>
            <div class="label">使用部门:${item.useDeptName}</div>
          </section>
        </div>`;
  });
  Message.success('请稍后，正在生成打印队列，成功后会自动跳转打印');
  let newWin = window.open('');
  newWin.document.write(html);
  list.forEach(async (it) => {
    console.log(newWin.document.getElementById(`${it.code}`));
    let ele = newWin.document.getElementById(`${it.code}`);
    if (ele) {
      await Qrcode.toCanvas(ele, it.code, {
        width: 70,
        height: 70,
        margin: 0,
        color: {
          dark: '#000000', // dark color for dots
          light: '#ffffff' // light color for the background
        }
      });
    }
  });
  newWin.document.close();

  newWin.document.head.innerHTML = `<meta name="viewport" content="width=device-width, initial-scale=1">

  <style>
   * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      word-break: break-all;
      font-family: SimSun;
    }
      @media print {
         .page-break {
            page-break-after: always;
         }
     }

     .block{
        width: 227px;
        height: 85px;
        background: #ccc;
        display: flex;
        justify-content: flex-start;
        margin-bottom: 10px;
        padding: 7.5px;
        section{
           font-size: 12px;
        }
        .label{
           white-space: nowrap;
           overflow: hidden;
           text-overflow: ellipsis;
           line-height: 18px;
        }
     }
    section{
      padding:10px;
    }
  </style>`;
  newWin.onload = function () {
    newWin.focus(); // 处理ie兼容
    newWin.print();
  };

  newWin.print();
};
export const printQrCodeImg = (list) => {
  let html = '';
  // <div className="label">设备位置:<span>${
  //   item.locationPath || '-'
  // }</span></div>
  list.forEach((item) => {
    html += `<div class="block">
          <div class="imgCont">
            <img alt="二维码" class="img" src="${convertFileUrl(
              item.qrCodeImage.domain
            )}"/>
          </div>

           <div class="cont">
            <div class="label"><span class="tit">SN编号:</span><span class="val">${
              item.sn
            }</span></div>
            <div class="label"><span class="tit">名称:</span><span class="val">${
              item.name || '-'
            }</span></div>
            <div class="label" class="tit"><span class="tit">型号:</span><span class="val">${
              item.model || '-'
            }</span></div>
            <div class="label"><span class="tit">使用部门:</span><span class="val">${
              item.useDeptName || '-'
            }</span></div>

          </div>
        </div>`;
  });
  Message.success('请稍后，正在生成打印队列，成功后会自动跳转打印');
  let newWin = window.open('');
  newWin.document.write(html);
  newWin.document.close();
  newWin.document.head.innerHTML = `<meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
   * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      word-break: break-all;
      font-family: SimSun;

    }

      @media print {
         .page-break {
            page-break-after: always;
         }
     }

     .block{
          height: 57.3mm;
          width: 90mm;
          padding: 15px;
          display: flex;
          /*flex-direction: column;*/
          align-content: center;
          justify-content: center;
          page-break-after: always;
          background: transparent;
          page-break-inside: avoid;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: right top;
          overflow: hidden;
     }
        .label{
           display: grid;
           grid-template-columns: 17mm 30mm;
           line-height: 25px;
           font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", Arial, sans-serif;

        }
         .val,.tit{
             white-space: normal;    /* 正常换行 */
            word-wrap: break-word;  /* 旧属性名 */
            overflow-wrap: break-word; /* 新推荐的属性名 */
            word-break: normal;
            font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", Arial, sans-serif;
           }
        .cont{
            display: flex;
            justify-content: center;
            flex-direction: column;
             overflow: hidden;
             font-size: 14px;
             width: 54mm;
             height: 50mm;
             padding:3mm;
        }
     .cont {
           /*overflow: hidden;*/
           /*font-size: 14px;*/
           /*width: 54mm;*/
           /*height: 50mm;*/
        }
        .imgCont{
         width: 30mm;
         height: 50mm;
         display: flex;
         justify-content: center;
         flex-direction: column;

        }
         .img {
            width: 30mm;
            height: 30mm;
          }
    section{
      padding:3mm;
    }
  </style>`;
  newWin.onload = function () {
    newWin.focus(); // 处理ie兼容
    // newWin.print();
    newWin.print();
  };
};
//打印耗材二维码
export const printMaterialQrCode = (list) => {
  let html = '';
  list.forEach((item) => {
    html += `<div class="block">
          <canvas id="${item.codeId}"></canvas>
          <section>
            <div>耗材编号:${item.code}</div>
            <div>耗材名称:${item.name}</div>
            <div>使用人:${item.name}</div>
            <div>使用部门:${item.name}</div>
          </section>
        </div>`;
  });
  Message.success('请稍后，正在生成打印队列，成功后会自动跳转打印');
  let newWin = window.open('');
  newWin.document.write(html);
  list.forEach(async (it) => {
    console.log(newWin.document.getElementById(`${it.codeId}`));
    let ele = newWin.document.getElementById(`${it.codeId}`);
    if (ele) {
      await Qrcode.toCanvas(ele, it.code, {
        width: 70,
        height: 70,
        margin: 0,
        color: {
          dark: '#000000', // dark color for dots
          light: '#ffffff' // light color for the background
        }
      });
    }
  });
  newWin.document.close();

  newWin.document.head.innerHTML = `<meta name="viewport" content="width=device-width, initial-scale=1">

  <style>
   * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      word-break: break-all;
      font-family: SimSun;
    }
      @media print {
         .page-break {
            page-break-after: always;
         }
     }

     .block{
        width: 227px;
        height: 85px;
        background: #ccc;
        display: flex;
        justify-content: flex-start;
        margin-bottom: 10px;
        padding: 7.5px;
        section{
           font-size: 12px;
        }
     }
    section{
      padding:10px;
    }
  </style>`;
  newWin.onload = function () {
    newWin.focus(); // 处理ie兼容
    newWin.print();
  };

  newWin.print();
};
export const printTable = (tableData) => {
  let html = '';
  tableData.forEach((it) => {
    console.log(it);

    html += `<div class="page-break">
        <h2 class="title">
         山东能源集团有限公司固定资产领用单
        </h2>
        <span class="time">填表日期：${it.operateDate}</span>

       <section class='table'>
                  <table class="tableContent">
                   <thead>
                    <tr>
                      <td><span>资产编码</span></th>
                      <td><span>发票资产名称</span></th>
                      <td><span>资产品牌</span></th>
                      <td><span>资产型号</span></th>
                      <td><span>出厂编号</span></th>
                      <td><span>单位</span></th>
                      <td><span>使用人员</span></th>
                      <td><span>存放地点</span></th>
                      <td><span>备注</span></th>
                    </tr>
                    </thead>
                    <tbody>`;
    it.details.map(async (item) => {
      html += `
                    <tr class="td_content">
                      <td><span>${
                        item.code !== null ? item.code : '--'
                      }</span></td>
                      <td><span>${
                        item.invoiceAssetName ? item.invoiceAssetName : '--'
                      }</span></td>
                      <td><span>${
                        item.brandName ? item.brandName : '--'
                      }</span></td>
                      <td><span>${item.model}</span></td>
                      <td><span>${
                        item.serialNumber ? item.serialNumber : '--'
                      }</span></td>
                        <td><span>${
                          item.measureUnit ? item.measureUnit : '--'
                        }</span></td>
                        <td><span>${
                          item.userName ? item.userName : '--'
                        }</span></td>
                          <td><span>${
                            item.locationName ? item.locationName : '--'
                          }</span></td>
                           <td><span>${
                             item.remark ? item.remark : '--'
                           }</span></td>
                    </tr>
                 `;
    });

    html += `</tbody></table></section>
           <div class="footer"> <span></span><span>资产使用部门</span>
           <span>经办人</span></div>
    </div>`;
  });
  Message.success('请稍后，正在生成打印队列，成功后会自动跳转打印');
  let newWin = window.open('');
  newWin.document.write(html);
  newWin.document.close();
  newWin.document.head.innerHTML = `<meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
   * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      word-break: break-all;
      font-family: SimSun;
    }
     @media print {

            .page-break {
                page-break-after: always;
            }
     }
    section{
      padding:20px;
    }
    .footer{
    padding:0  100px;
    display: flex;
    justify-content: space-around;
    }
    .time{
      display: flex;
      justify-content: flex-end;
      margin-right: 20px;
    }
    .title{
    text-align: center;
    }
    .table {
          width: 100%;
         }
       .tableContent {
          width: 100%;
          border-collapse: collapse;
          position: relative;
        }
       .tableContent:after{
          content: "";
          position: absolute;
          border: 1px solid #000;
          width: 200%;
          height: 200%;
          transform-origin: 0 0;
          transform: scale(0.5,0.5);
          box-sizing: border-box;
          top: 0;
       }
        td {
          padding: 5px;
          text-align:center;
          word-break: break-all;
          font-weight:100;
           line-height:15px;
          position: relative;

          word-wrap: break-word; /* 允许内容换行 */
          overflow: hidden; /* 隐藏溢出的内容 */
        }
        td span{
          transform: scale(1);
          display: inline-block;
        }
        td:after{
         content: "";
         position: absolute;
         border-right: 1px solid #000 !important;
         width: 200%;
         height: 200%;
         transform-origin: 0 0;
         transform: scale(0.5,0.5);
         box-sizing: border-box;
         top: 0;
         left: 0;
         border-bottom: 1px solid #000 !important;
       }
       td:last-child {
            border-right: none; /* 移除最后一个单元格的右边框 */
       }
       td:last-child:after{
         content: "";
         position: absolute;
         border-right: 0px solid #000 !important;
         width: 200%;
         height: 200%;
         transform-origin: 0 0;
         transform: scale(0.5,0.5);
         box-sizing: border-box;
         top: 0;
         left: 0;
         border-bottom: 1px solid #000 !important;
       }
       .td_content td:after{
         content: "";
         position: absolute;
         width: 200%;
         height: 200%;
         transform-origin: 0 0;
         box-sizing: border-box;
         top: 0;
         left: 0;
         border-top: 0px solid #000;
        }
  </style>`;
  newWin.onload = function () {
    newWin.focus(); // 处理ie兼容
    newWin.print();
  };
  newWin.print();
};
