<template>
  <div>
    <el-table
      class="table"
      :data="list"
      border
      stripe
      :header-cell-style="{ background: '#fafafa' }"
      size="small"
      height="500px"
    >
      <el-table-column type="index" label="序号"></el-table-column>
      <el-table-column
        prop="equipmentCode"
        label="设备编号"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="monitorName"
        label="保养部位"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="standard"
        label="保养标准"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="method"
        label="保养方法"
        show-overflow-tooltip
      ></el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: 'DeviceBasicList',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {};
    },

    mounted() {},
    methods: {}
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }
  .operateBtn {
    margin-bottom: 15px;
  }
  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
