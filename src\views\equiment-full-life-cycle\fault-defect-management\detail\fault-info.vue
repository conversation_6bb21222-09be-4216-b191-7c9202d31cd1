<template>
  <!-- 详情基本信息 -->
  <div>
    <el-form
      ref="form"
      size="small"
      inline
      label-width="120px"
      label-suffix="："
    >
      <el-row :gutter="15">
        <el-col :span="8">
          <el-form-item label="异常部位" prop="monitorName">
            <div class="content-wrapper">
              {{ details.monitorName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="异常等级" prop="abnormalLevelName">
            <div class="content-wrapper">
              {{ details.abnormalLevelName || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="异常描述" prop="abnormalComment">
            <div class="content-wrapper">
              {{ details.abnormalComment || '-' }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="缺陷资料" prop="attachList">
            <show-file
              v-if="
                details.abnormalAttachList && details.abnormalAttachList.length
              "
              :list="details.abnormalAttachList"
            />
            <div v-else>暂无</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
  import { ShowFile } from '@/components/yk-upload-file';

  export default {
    components: { ShowFile },
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return {};
    },
    methods: {}
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-form {
      .el-form-item {
        width: 100%;
        margin-bottom: 4px;
      }

      .el-form-item__content {
        width: calc(100% - 120px);
      }
    }
  }
</style>
