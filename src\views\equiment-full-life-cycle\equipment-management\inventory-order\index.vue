<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        align="center"
        prop="no"
        label="盘点单号"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="planName"
        label="盘点名称"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="planCreateUserName"
        label="制定人员"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="deviceUserName"
        label="盘点人员"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.deviceUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="startDate"
        label="盘点日期"
        align="center"
        show-overflow-tooltip
        width="200px"
      >
        <template v-slot="{ row }"
          >{{ row.planStartDate || '-' }} ~
          {{ row.planEndDate || '-' }}</template
        >
      </el-table-column>
      <el-table-column
        prop="completeTime"
        label="完成时间"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.completeTime || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="deviceDeptName"
        label="盘点部门"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.deviceDeptName || '-' }}</template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="inventory"
        label="已盘/全部"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.hasInventoryQuantity }} / {{ scope.row.totalQuantity }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="statusName"
        label="状态"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${inventoryStatusColor(row.status)};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="createTime"
        width="120"
        align="center"
        label="创建时间"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column align="center" label="操作" width="200" fixed="right">
        <template v-slot="{ row }">
          <section class="cell-operate-class">
            <el-button
              v-if="permission['device-inventory-order-list']"
              type="text"
              size="small"
              @click="detail(row)"
              >查看</el-button
            >
            <el-button
              v-if="
                row.status === 1 && permission['device-inventory-order-operate']
              "
              class="green-btn"
              type="text"
              size="small"
              @click="inventory(row)"
              >去盘点</el-button
            >
            <el-button
              v-if="
                row.status === 1 &&
                permission['device-inventory-order-complete']
              "
              type="text"
              size="small"
              @click="complete(row)"
              >完成</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <detail-index ref="detailIndex"></detail-index>
    <to-inventory ref="toInventory" @success="getList"></to-inventory>
  </basic-container>
</template>

<script>
  import DetailIndex from '@/views/equiment-full-life-cycle/equipment-management/inventory-order/detail/index.vue'; // 查看详情页面
  import ToInventory from '@/views/equiment-full-life-cycle/equipment-management/inventory-order/operate/inventory.vue';
  import Search from './search';
  import Pagination from '@/components/pagination';
  import { convertFileUrl } from '@/util/util';
  import {
    getDevicePartsCheckListStaticApi,
    completeDevicePartsInventoryApi
  } from '@/api/equiment-full-life-api/device-inventory';

  import { mapGetters } from 'vuex';
  import { inventoryStatusColor } from '@/views/equiment-full-life-cycle/spare-parts-management/util';
  export default {
    name: 'MaintenanceList',
    components: {
      Search,
      DetailIndex,
      Pagination,
      ToInventory
    },
    props: {},
    computed: {
      ...mapGetters(['permission'])
    },
    data() {
      return {
        inventoryStatusColor,
        radio2: 'all',
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        totalObj: {}
      };
    },

    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      //  点击完成
      complete(row) {
        this.$confirm('确定要结束此次盘点吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              this.loading = true;
              await completeDevicePartsInventoryApi(row.id);
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.loading = false;
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
            this.loading = false;
          });
      },

      //  点击去盘点
      inventory(row) {
        this.$refs.toInventory.show(row);
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },

      //  工单列表
      async getList() {
        this.loading = true;
        try {
          let res = await getDevicePartsCheckListStaticApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
  }
</style>
