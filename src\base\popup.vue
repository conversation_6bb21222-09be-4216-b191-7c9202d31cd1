<template>
  <el-dialog
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="currentVisible"
    @closed="closed"
    :fullscreen="fullscreen"
    :width="width"
    v-bind="$attrs"
    :destroy-on-close="destroyOnClose"
  >
    <div slot="title">
      {{ title }}
      <i @click="zoScreen" v-if="classNameBool" class="el-icon-full-screen"></i>
      <i @click="zoScreen" v-else class="el-dialog__close el-icon-news"></i>
    </div>
    <slot></slot>
    <slot name="footer" slot="footer"> </slot>
  </el-dialog>
</template>

<script>
  export default {
    name: 'dialogPopup',
    components: {},
    props: {
      // 弹窗标题
      title: {
        type: String,
        required: true,
        default: () => {
          return '标题';
        }
      },
      destroyOnClose: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      width: {
        type: String,
        default: ''
      },
      visible: {
        type: Boolean,
        required: true,
        default: () => {
          return false;
        }
      }
    },
    data() {
      return {
        currentVisible: false,
        fullscreen: false,
        classNameBool: true // 展示哪个类名bool
      };
    },
    watch: {
      visible: {
        handler(val) {
          this.currentVisible = val;
        },
        immediate: true
      }
    },
    methods: {
      closed() {
        this.$emit('update:visible', false);
        this.$emit('closed');
      },
      // 点击放大缩小屏幕按钮
      zoScreen() {
        this.classNameBool = !this.classNameBool;
        this.fullscreen = !this.fullscreen;
        if (this.fullscreen) {
          this.$emit('update:dialogWidth', 5); // 弹窗实际宽度
        } else {
          this.$emit('update:dialogWidth', 2); // 弹窗实际宽度
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  /deep/.el-dialog {
    padding-bottom: 15px;
  }

  /deep/.el-dialog__header {
    color: #909399;
    border-bottom: 1px solid #ebeef5;

    div > i {
      float: right;
      margin-right: 30px;
      cursor: pointer;
    }
  }
</style>
