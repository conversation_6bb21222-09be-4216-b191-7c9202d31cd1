<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      ref="table"
      @select="handleCheckBox"
      @select-all="handleSelectAll"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="code"
        label="设备编号"
        width="116"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.code || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
        width="100px"
      >
        <template slot-scope="scope">
          {{ scope.row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="model"
        label="规格型号"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.model || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="categoryName"
        align="center"
        label="设备类型"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        align="center"
        prop="model"
        label="是否归还"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.isLeaseBackName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="model"
        label="业务域"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.businessDomainName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="importantLevelName"
        label="设备等级"
        align="center"
        show-overflow-tooltip
        width="96px"
      >
        <template slot-scope="{ row }">
          <el-tag
            :type="getTagType(row.importantLevel)"
            size="small"
            class="table-custom-tag"
          >
            {{ row.importantLevelName || '-' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="特种设备"
        show-overflow-tooltip
        width="90px"
      >
        <template slot-scope="scope">
          {{ scope.row.isSpecialName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="特种设备检查周期(天)"
        align="center"
        show-overflow-tooltip
        width="85px"
      >
        <template slot-scope="scope">
          {{ scope.row.specialInspectPeriod || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="租赁设备"
        show-overflow-tooltip
        width="90px"
      >
        <template slot-scope="scope">
          {{ scope.row.isLeaseName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="measureUnitName"
        label="计量单位"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        align="center"
        prop="statusName"
        label="设备状态"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i :style="`color:${equipmentStatusColor(row.status)};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="归属部门"
        show-overflow-tooltip
        width="90px"
      >
        <template slot-scope="scope">
          {{ scope.row.belongDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="负责人"
        show-overflow-tooltip
        width="90px"
      >
        <template slot-scope="scope">
          {{ scope.row.responsiblePersonName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="useDeptName"
        label="使用部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.useDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="userName"
        label="使用人员"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.userName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="sn"
        label="SN编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="productDate"
        label="投产日期"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.productDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="locationPath"
        label="设备位置"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.locationPath || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="supplier"
        label="生产厂家"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.supplierName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="contact"
        label="联系人"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.contact || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="tel"
        label="联系电话"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.tel || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        label="创建人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="createTime"
        label="创建时间"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.createTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="contact"
        label="更新人"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.updateUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="contact"
        label="更新人所属部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.updateDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="contact"
        label="更新时间"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.updateTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        key="operation"
        v-if="activeName === '2'"
        align="center"
        width="100"
        fixed="right"
      >
        <!--         0 =  未开始  1= 执行中 2 = 已完成 3=已终止   -->
        <template v-slot="{ row }">
          <el-button type="text" size="small" @click="deviceDetail(row)"
            >查看</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="returnDevice(row)"
            v-if="
              permission['registration-lease-return'] && row.isLeaseBack === 0
            "
            >归还</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <detail-index ref="detailIndex" @success="getList"></detail-index>
    <ledger-detail ref="ledgerDetail"></ledger-detail>
    <!-- <requisitionDetail ref="requisitionDetail"></requisitionDetail> -->
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  // import requisitionDetail from '../requisition/detail/index.vue'; // 查看详情页面
  import Search from './search';
  import Pagination from '@/components/pagination';
  import LedgerDetail from '@/components/ledger-detail';
  import { convertFileUrl } from '@/util/util';
  import { leaseBackPage } from '@/api/equiment-full-life-api/ledger';
  import { mapGetters } from 'vuex';
  export default {
    name: 'returnList',
    components: {
      Search,
      DetailIndex,
      Pagination,
      LedgerDetail
      // requisitionDetail
    },
    props: {},
    data() {
      return {
        convertFileUrl,
        loading: false,
        total: 0,
        list: [{}],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {},
        allPageSelect: [],
        activeName: '2'
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      // 设备台账 设备状态状态颜色
      equipmentStatusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#35C24B'; // 在用
          case 1:
            return '#155CFF'; // 备用
          case 3:
            return '#EE7C11'; // 维修
          case 4:
            return '#E23F3F'; // 报废
        }
      },
      // 设备登记标签颜色
      getTagType(val) {
        const typeObj = {
          1: 'success',
          2: 'primary',
          3: 'warning'
        };
        return typeObj[val];
      },
      deviceDetail(row) {
        this.$refs.ledgerDetail.show(row.id);
      },
      // 归还
      async returnDevice(row) {
        this.$refs['detailIndex'].show(row.id);
      },
      handleClick() {
        this.$refs.search.reset();
        // this.searchParams = {
        //   current: 1,
        //   size: 10
        // };
        // this.getList();
      },
      handleCheckBox(rows, row) {
        let key = 'no';
        if (rows.includes(row)) {
          this.allPageSelect.push(row);
        } else {
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item[key] !== row[key]
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        let key = 'no';
        if (rows.length) {
          rows.forEach((row) => {
            if (!this.allPageSelect.find((item) => item[key] === row[key])) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.list.forEach((row) => {
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item[key] !== row[key]
            );
          });
        }
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await leaseBackPage({
            ...this.searchParams,
            containLeaseBack: 1
          });
          // customizeId
          let records = res.data.data.records || [];
          records.forEach((item, index) => {
            item.customizeId = item.deviceId + index;
          });
          this.list = records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
  }

  .tag_ellipsis {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
</style>
