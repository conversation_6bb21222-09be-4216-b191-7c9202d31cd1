<template>
  <div>
    <el-select
      :clearable="clearable"
      :title="selectData.label"
      :value="selectData.label"
      :disabled="disabled"
      v-loading="selectLoading"
      ref="elSelect"
      style="width: 100%"
      @clear="clearHandle"
      @visible-change="visibleChange"
      :placeholder="placeholder"
      popper-class="popper-box"
    >
      <el-option style="display: none" value="0" />
      <div v-show="showSearch" class="filter-input" @click.stop>
        <el-input
          clearable
          placeholder="请输入关键字"
          v-model.trim="filterText"
          @keyup.enter.native.stop.prevent="searchHandle"
        >
        </el-input>
      </div>
      <el-tree
        ref="selectTree"
        v-if="loadingTree"
        :class="{ 'input-tree-wrap': showSearch }"
        v-loading="lazyLoading"
        :show-checkbox="multiple"
        :highlight-current="!multiple"
        :current-node-key="!multiple ? value : null"
        :accordion="accordion"
        :check-strictly="checkStrictly"
        :expand-on-click-node="false"
        :data="dic"
        :lazy="lazy"
        :load="load || treeLoad"
        :props="defaultProps"
        :node-key="defaultProps.value"
        :default-expanded-keys="expandKeys"
        :default-checked-keys="defaultCheckNodes"
        :default-expand-all="expandAll"
        :filter-node-method="filterNode"
        @check="checkHandle"
        @node-click="nodeClickHandle"
        @node-expand="nodeExpand"
      >
        <span class="custom-tree-wrap" slot-scope="{ node, data }">
          <span
            >{{ node.label }}
            <template v-if="showCoding">（{{ data.value }}）</template>
          </span>
        </span>
      </el-tree>
    </el-select>
    <template v-if="multiple && multipleList">
      <!-- 如果选中组织在fixedNodes中,  则不可取消选择 -->
      <el-tag
        v-for="(tag, index) in selectedList"
        :key="tag[defaultProps.value]"
        :closable="!fixedNodes.includes(tag[defaultProps.value])"
        style="margin-top: 10px; margin-right: 10px"
        @close="closeDept(index)"
      >
        {{ tag[defaultProps.label] }}
      </el-tag>
    </template>
  </div>
</template>

<script>
  export default {
    name: 'el-tree-select',
    props: {
      form: {
        type: Object,
        default: () => {
          return {};
        }
      },
      defaultExpandKeys: {
        type: Array, // 必须是数组
        default: () => {
          return [];
        }
      },
      dic: {
        type: Array, // 必须是树形结构的对象数组
        default: () => {
          return [];
        }
      },
      expandAll: {
        type: Boolean,
        default: false
      },
      showSearch: {
        type: Boolean,
        default: true
      },
      // 选项分隔符
      separator: {
        type: String,
        default: ','
      },
      placeholder: {
        type: String,
        default: '请选择'
      },
      // 是否显示多选
      multiple: {
        type: Boolean,
        default: false
      },
      multipleList: {
        type: Boolean,
        default: true
      },
      // 是否可清除
      clearable: {
        type: Boolean,
        default: false
      },
      // 父子不关联
      checkStrictly: {
        type: Boolean,
        default: true
      },
      lazy: {
        type: Boolean,
        default: false
      },
      localSearch: {
        type: Boolean,
        default: false
      },
      load: {
        type: Function
      },
      accordion: {
        type: Boolean,
        default: false
      },
      props: {
        type: Object,
        default: () => {
          return {
            value: 'value', // ID字段名
            label: 'label', // 显示名称
            children: 'children', // 子级字段名
            formLabel: 'deptName',
            formValue: 'deptId'
          };
        }
      },

      value: {
        type: [Number, String, Array],
        default: ''
      },
      show: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      },
      // 是否是同一个接口懒加载 搜索
      samePort: {
        type: Boolean,
        default: false
      },
      showCoding: { type: Boolean, default: false },
      lazyLoading: { type: Boolean, default: false },
      defaultCheckNodes: {
        type: Array, // 已经分配的资源
        default: () => {
          return [];
        }
      },
      // 固定选中且不可取消的节点
      fixedNodes: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return {
        filterText: '',
        selectData: {
          label: '', // 显示文本
          value: this.value, // 初始值
          node: '' // 选中的节点数据
        },
        selectLoading: false,
        strictlyExpand: [],
        loadingTree: true
      };
    },
    computed: {
      expandKeys() {
        // ? [...this.value, ...this.defaultExpandKeys]
        // [...this.defaultExpandKeys, this.value];//  是否展开
        return !this.checkStrictly
          ? [...this.strictlyExpand]
          : Array.isArray(this.value)
          ? [...this.defaultExpandKeys]
          : [...this.defaultExpandKeys];
      },
      defaultProps() {
        // 如果fixedNodes不为空, 判断节点是否禁用
        let disabled = null;
        if (this.fixedNodes.length) {
          disabled = (data) => {
            return this.fixedNodes.includes(data.value);
          };
        }

        if (this.props) {
          let { children, label, value, isLeaf, showFormLabel } = this.props;
          return {
            children: children || 'children',
            label: label || 'label',
            value: value || 'value',
            isLeaf: isLeaf || null,
            showFormLabel: showFormLabel || false,
            disabled
          };
        }
        return {
          children: 'children',
          label: 'label',
          value: 'value',
          disabled
        };
      },
      // 获取选中数据
      selectedList() {
        let deptName =
          this.form[this.props.formLabel] || this.form[this.props.label] || '';
        // this.form[
        //   this.props.showFormLabel ? this.props.formLabel : this.props.label
        // ] || '';
        deptName = Array.isArray(deptName)
          ? deptName.join(this.separator)
          : deptName;

        let deptList = deptName ? deptName.split(this.separator) : [];
        let list = deptList.map((item, index) => ({
          [this.defaultProps.label]: item,
          [this.defaultProps.value]: this.multiple
            ? this.value[index]
            : this.value
        }));
        return list;
      }
    },
    watch: {
      // 关闭弹框清除搜索
      show: {
        handler(n) {
          if (!n) {
            this.filterText = '';
          }
        }
      },
      dic(n) {
        this._setTreeStatus(this.value);
        let selectData = [];
        // 获取选中的节点数据
        this._getSelectData(n, selectData);
        // 获取选中值
        let list = this.selectedList;
        let complexList = [...list, ...selectData];
        let arr = Array.isArray(this.value)
          ? complexList.slice(0, this.value.length)
          : complexList.slice(0, 1);
        this._setLabel(arr);
      },
      value: {
        handler(n) {
          if (n != this.selectData.value) {
            this.selectData.value = n;
            this._initData(n);
          }
        }
      }
      // filterText(val) {
      //   this.$refs.selectTree.filter(val);
      // }
    },
    mounted() {
      this._initData(this.value);
    },
    methods: {
      setCurrentKey(val) {
        this.$refs.selectTree && this.$refs.selectTree.setCurrentKey(val);
      },
      visibleChange(visible) {
        // 初始值
        this.refreshTree();
        if (visible) {
          this.$emit('initialValue', this.form);
          this.$refs.selectTree && this.$refs.selectTree.setCurrentKey(null);
          this.$refs.selectTree && this.$refs.selectTree.setCheckedKeys([]);
          this._setTreeStatus(this.value, 600);
        } else {
          this.filterText = '';
          this.searchHandle();
          this.$refs.selectTree.$data.store.lazy = true; // 懒加载
          this.$emit('search');
        }
      },
      closeDept(index) {
        let data = this.selectedList[index];
        let filterData = this.selectedList.filter(
          (item) =>
            item[this.defaultProps.value] !== data[this.defaultProps.value]
        );
        let checkedKeys = filterData.map(
          (item) => item[this.defaultProps.value]
        );
        let checked = {
          checkedKeys,
          checkedNodes: filterData
        };
        this.checkHandle(data, checked);
        this._setTreeStatus(checkedKeys);
      },
      nodeExpand() {
        this._setTreeStatus(this.selectData.value);
        // if (!this.multiple) {
        //   setTimeout(() => {
        //     this.$refs.selectTree.setCurrentKey(this.selectData.value);
        //   }, 1600);
        // }
        !this.checkStrictly && this.setStrictly();
      },
      // 搜索
      searchHandle() {
        if (this.localSearch) {
          return this.$refs.selectTree.filter(this.filterText);
        }
        // !this.samePort && this.refreshTree();
        if (this.filterText) {
          this.$refs.selectTree &&
            (this.$refs.selectTree.$data.store.lazy = this.lazy); // 当输入框有值时关闭懒加载
          this.$emit('search', this.filterText);
        } else {
          !this.filterText && this.refreshTree();
          this.$refs.selectTree &&
            (this.$refs.selectTree.$data.store.lazy = this.lazy); // 懒加载
          this.$emit('search');
          this._initData(this.value);
        }
      },
      refreshTree() {
        //<el-tree>组件使用v-if重新加载
        this.loadingTree = false;
        this.$nextTick(() => {
          this.loadingTree = true;
        });
      },
      treeLoad() {},
      // 初始化 回显状态和数据
      _initData(n) {
        this.$refs.selectTree && this.$refs.selectTree.setCurrentKey(null);
        this.$refs.selectTree && this.$refs.selectTree.setCheckedKeys([]);
        this._setTreeStatus(n);
        let selectData = [];
        // 获取选中的节点数据
        this._getSelectData(this.dic, selectData);
        // 默认显示
        let list = this.selectedList;
        // 默认显示end
        this._setLabel(list);
        // list.length && this._setLabel(list);
      },
      // 根据父id和子id 生成唯一标识 用于id不唯一的数据
      _createUniqueKeyData(arr, parentKeyList = []) {
        let { value, children } = this.defaultProps;
        arr.map((item) => {
          item._uniqueKey =
            parentKeyList.join(',') +
            (parentKeyList.length ? `,${item[value]}` : item[value]);
          if (item[children] && item[children].length) {
            this._createUniqueKeyData(
              item[children],
              parentKeyList.concat(item[value])
            );
          }
        });
      },
      // 查找已选数据 用于回显label字段
      _getSelectData(data, selectData) {
        let key = this.defaultProps.value;
        data.forEach((item) => {
          let isMultipleValue =
            Array.isArray(this.value) && this.value.includes(item[key]);
          if (this.value == item[key] || isMultipleValue) {
            selectData.push(item);
          }
          if (item.children && item.children.length) {
            this._getSelectData(item.children, selectData);
          }
        });
      },
      // 设置回显文字
      _setLabel(data) {
        if (data && data.length && this.multiple) {
          let labelList = data.map((item) => item[this.props.label]);
          this.selectData.label = labelList.join(this.separator);
          this.form[this.props.formLabel] = this.selectData.label;
        } else if (data.length && !this.multiple) {
          this.selectData.label = data[0][this.props.label];
          this.form[this.props.formLabel] = this.selectData.label;
        } else {
          this.selectData.label = '';
          this.form[this.props.formLabel] = '';
        }
      },
      _setTreeStatus(val, timestamp = 1600) {
        this.$nextTick(() => {
          if (!val || (Array.isArray(val) && !val.length)) {
            this.$refs.selectTree && this.$refs.selectTree.setCurrentKey(null);
            this.$refs.selectTree && this.$refs.selectTree.setCheckedKeys([]);
          } else if (val && !Array.isArray(val)) {
            setTimeout(() => {
              this.$refs.selectTree && this.$refs.selectTree.setCurrentKey(val);
            }, timestamp);
          } else if (Array.isArray(val) && val.length) {
            setTimeout(() => {
              this.$refs.selectTree &&
                this.$refs.selectTree.setCheckedKeys([...val]);
            }, timestamp);
          }
        });
      },
      // 筛选搜索
      filterNode(value, data) {
        if (!value) return true;
        return data[this.defaultProps.label].indexOf(value) !== -1;
      },
      // 抛出input事件改变父组件绑定值
      modelChange() {
        this.$emit('input', this.selectData.value);
        this.$emit('change', this.selectData.value, this.selectData.node);
      },
      // 对象数组去重
      unique(arr, key = 'id') {
        var result = [];
        var obj = {};
        for (var i = 0; i < arr.length; i++) {
          if (!obj[arr[i][key]]) {
            result.push(arr[i]);
            obj[arr[i][key]] = true;
          }
        }
        return result;
      },
      // 选中项关闭
      handleClose() {},
      // 复选框选中
      checkHandle(data, checked) {
        if (this.checkStrictly) {
          this.setNotStrictly(data, checked);
          this.$emit('check', data, checked);
        } else {
          // this.setNotStrictly(data, checked);
          this.strictlyExpand = [data[this.defaultProps.value]];
          this.setStrictly(data, checked);
        }
        // this.form[this.props.formLabel] = this.selectData.label;
      },
      setStrictly(data, checked) {
        setTimeout(() => {
          let checkedNodes = this.$refs.selectTree.getCheckedNodes();
          let checkedKeys = this.$refs.selectTree.getCheckedKeys();
          this._setLabel(this.unique(checkedNodes, this.defaultProps.value));
          this.selectData.value = [...new Set(checkedKeys)];
          this.selectData.node = checkedNodes;
          this.modelChange();
          this.$emit('check', data, checked);
        }, 1500);
      },
      // 父子不关联
      setNotStrictly(data, checked) {
        setTimeout(() => {
          // 获取选中值
          let list = this.selectedList;
          let { value, node } = this.selectData;
          let id = data[this.defaultProps.value];
          // includes选中   false取消
          if (checked.checkedKeys.includes(id)) {
            let labelList = [...list, ...checked.checkedNodes];
            this._setLabel(this.unique(labelList, this.defaultProps.value));
            // 设置value
            let valueList = [...value, ...checked.checkedKeys];
            this.selectData.value = [...new Set(valueList)];
            // 设置node
            let nodeList = node
              ? [...node, ...checked.checkedNodes]
              : [...checked.checkedNodes];
            this.selectData.node = this.unique(
              nodeList,
              this.defaultProps.value
            );
          } else {
            // 设置label
            let labelList = list.filter(
              (item) => item[this.defaultProps.value] !== id
            );
            this._setLabel([...labelList]);
            // 设置value
            let valueList = (value || []).filter(
              (item) => !(item + '').includes(id)
            );
            this.selectData.value = valueList;
            // 设置node
            let nodeList = (node || []).filter(
              (item) => item[this.defaultProps.value] !== id
            );
            this.selectData.node = nodeList;
          }
          this.modelChange();
        }, 800);
      },
      // 点击选中
      nodeClickHandle(data) {
        if (!this.multiple) {
          // this.props.formLabel
          this.selectData.label =
            data[this.props.showFormLabel ? 'ancestorName' : this.props.label];
          this.form[this.props.formLabel] = this.selectData.label;
          this.selectData.value = data[this.props.value];
          this.selectData.node = data;
          this.$refs.elSelect.blur();
          this.modelChange();
          this.$emit('node-click', data);
        }
      },
      // 清除选中
      clearHandle() {
        this.selectData.label = '';
        this.form[this.props.formLabel] = '';
        this.selectData.node = null;
        this.selectData.value = this.multiple ? [] : '';
        this.$refs.selectTree.setCurrentKey(null);
        if (this.multiple) {
          this.$refs.selectTree.setCheckedKeys([]);
        }
        this.modelChange();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .custom-tree-wrap {
    font-size: 14px;
  }

  .active-placeholder {
    .el-input__inner::placeholder {
      color: #606266 !important;
    }
  }

  >>> .el-tree-node__content {
    padding: 0 20px;
  }

  .filter-input {
    position: absolute;
    top: 0;
    z-index: 2;
    box-sizing: border-box;
    width: 100%;
    padding: 5px 10px;
    background-color: white;

    >>> .el-input__inner {
      height: 28px;
      font-size: 12px;
      line-height: 28px;
    }

    /deep/ .el-input__suffix {
      .el-input__icon.el-input__validateIcon.el-icon-circle-close {
        display: none;
      }
    }
  }

  .input-tree-wrap {
    // margin-top: 52px;
    margin-top: 38px;
  }
</style>
