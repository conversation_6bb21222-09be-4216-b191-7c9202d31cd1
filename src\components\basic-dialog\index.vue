<template>
  <el-dialog
    v-if="visible"
    :visible="visible"
    :modal-append-to-body="true"
    :show-close="showClose"
    :append-to-body="true"
    :modal="modal"
    :fullscreen="fullscreen"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :width="width"
    @close="reset"
    class="distribute-dialog"
    v-bind="$attrs"
  >
    <div slot="title" class="distribute-header">
      <span>{{ title }}</span>
      <div @click="handleFullscreen" class="avue-crud__dialog__menu">
        <i class="el-dialog__close el-icon-full-screen"></i>
      </div>
    </div>
    <slot></slot>
    <div slot="footer" class="border-top">
      <slot name="footer"></slot>
    </div>
  </el-dialog>
</template>
<script>
  export default {
    name: 'basic-dialog',
    props: {
      showClose: {
        type: <PERSON>olean,
        default: true
      },
      modal: {
        type: Boolean,
        default: true
      },
      visible: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: '查看'
      },
      width: {
        type: String,
        default: '800px'
      }
    },
    data() {
      return {
        fullscreen: false
      };
    },
    methods: {
      handleFullscreen() {
        this.fullscreen = !this.fullscreen;
      },
      reset() {
        this.$emit('closed');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .border-top {
    ::v-deep .dialog-footer {
      .el-button {
        position: relative;
        z-index: 999;
      }
    }
  }
</style>
