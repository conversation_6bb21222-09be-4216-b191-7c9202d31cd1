/**
 * 通用工具类
 */
export default class func {
  /**
   * 不为空
   * @param val
   * @returns {boolean}
   */
  static notEmpty(val) {
    return !this.isEmpty(val);
  }

  /**
   * 是否为定义
   * @param val
   * @returns {boolean}
   */
  static isUndefined(val) {
    return val === null || typeof val === 'undefined';
  }

  /**
   * 为空
   * @param val
   * @returns {boolean}
   */
  static isEmpty(val) {
    if (
      val === null ||
      typeof val === 'undefined' ||
      (typeof val === 'string' && val === '' && val !== 'undefined')
    ) {
      return true;
    }
    return false;
  }

  /**
   * 强转int型
   * @param val
   * @param defaultValue
   * @returns {number}
   */
  static toInt(val, defaultValue) {
    if (this.isEmpty(val)) {
      return defaultValue === undefined ? -1 : defaultValue;
    }
    const num = parseInt(val, 0);
    return Number.isNaN(num)
      ? defaultValue === undefined
        ? -1
        : defaultValue
      : num;
  }

  /**
   * Json强转为Form类型
   * @param obj
   * @returns {FormData}
   */
  static toFormData(obj) {
    const data = new FormData();
    Object.keys(obj).forEach((key) => {
      data.append(key, Array.isArray(obj[key]) ? obj[key].join(',') : obj[key]);
    });
    return data;
  }

  /**
   * date类转为字符串格式
   * @param date
   * @param format
   * @returns {null}
   */
  static format(date, format = 'YYYY-MM-DD HH:mm:ss') {
    return date ? date.format(format) : null;
  }

  /**
   * 根据逗号联合
   * @param arr
   * @returns {string}
   */
  static join(arr) {
    return arr ? arr.join(',') : '';
  }

  /**
   * 根据逗号分隔
   * @param str
   * @returns {string}
   */
  static split(str) {
    return str ? String(str).split(',') : '';
  }
}
/**
 * 是否是数字
 */
export function myIsNum(value) {
  value = `${value}`;
  //验证字符串是否是数字
  const reg = /^\d+(\.\d{1,2})?$/; // /^[0-9]+\.?[0-9]*$/; //999999999999999999.99
  if (reg.test(value)) {
    return value <= 9999999999999999.99 ? true : false;
  } else {
    return false;
  }
}

export function isLiftNum(value, max) {
  let maxVal = max ? max : 365000;
  value = `${value}`;
  //验证字符串是否是数字
  const reg = /^[1-9]\d*$/; ///^\d+(\.\d{1,1})?$/; // /^[0-9]+\.?[0-9]*$/;
  if (reg.test(value)) {
    return value <= maxVal ? true : false;
  } else {
    return false;
  }
}
export function isLiftNum0(value, max, num) {
  let maxVal = max !== null && max !== undefined ? max : 365000;
  value = `${value}`;
  //验证字符串是否是数字
  const reg = /^[0-9]\d*$/; ///^\d+(\.\d{1,1})?$/; // /^[0-9]+\.?[0-9]*$/;
  if (reg.test(value)) {
    return value <= maxVal ? true : false;
  } else {
    return false;
  }
}
//  验证数据，并且保留小数
export function validatePositiveValue(value, max, num) {
  value = `${value}`;
  // 构造正则表达式，限制小数位数为 num，且不包含负数
  const reg = new RegExp(`^\\d*(\\.\\d{0,${num}})?$`);

  // 验证是否为有效数字且小数位数符合要求
  if (!reg.test(value)) {
    return false;
  }

  // 将字符串转换为数字，验证是否大于 0 且小于等于最大值
  const numericValue = parseFloat(value);
  return numericValue >= 0 && numericValue <= max;
}
//  大于0 小于等于最大值 并且保留精度
export function validateValueThen0(value, max, num) {
  value = `${value}`;
  // 构造正则表达式，限制小数位数为 num，且不包含负数
  // const reg = new RegExp(`^\\d*(\\.\\d{0,${num}})?$`);
  const reg = new RegExp(`^(0|[1-9]\\d*)(\\.\\d{0,${num}})?$`);
  // 验证是否为有效数字且小数位数符合要求
  if (!reg.test(value)) {
    return false;
  }
  // 将字符串转换为数字，验证是否大于 0 且小于等于最大值
  const numericValue = parseFloat(value);
  return numericValue > 0 && numericValue <= max;
}
/**
 * 是否是数字 包括负数
 */
export function isNum(value) {
  value = `${value}`;
  //验证字符串是否是数字
  const reg = /^[0-9]+\.?[0-9]*$/;
  const reg2 = /^-[0-9]+\.?[0-9]*$/;
  return reg.test(value) || reg2.test(value);
}
export function presionAny(value, num) {
  value = `${value}`;
  //验证字符串是否是数字
  const reg = new RegExp(`^\\d*(\\.?\\d{0,${num}})`, 'g');
  return reg.exec(value)[0];
}
/**
 * 按照要求匹配 最多5位小数
 */
export function presion5(value, num) {
  value = `${value}`;
  //验证字符串是否是数字
  const reg = num === 5 ? /^\d*(\.?\d{0,5})/g : /^\d*(\.?\d{0,4})/g;
  return reg.exec(value)[0];
}
// 小数点后只保留3为小数，如果是0 自动删除
export function presion3(value, num) {
  value = `${value}`;
  //验证字符串是否是数字
  const reg = num === 3 ? /^\d*(\.?\d{0,3})/g : /^\d*(\.?\d{0,3})/g;
  return reg.exec(value)[0];
}
export function presion4(value, num) {
  value = `${value}`;
  //验证字符串是否是数字
  const reg = num === 4 ? /^\d*(\.?\d{0,4})/g : /^\d*(\.?\d{0,4})/g;
  return reg.exec(value)[0];
}
