<template>
  <div class="table-content">
    <div class="top-info">
      <el-button icon="el-icon-plus" type="primary" size="small" @click="add"
        >添加标准</el-button
      >
      <el-button
        icon="el-icon-delete"
        type="danger"
        size="small"
        @click="delAll"
        >删除所选</el-button
      >
    </div>
    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
      label-width="10px"
    >
      <el-table
        ref="point-table"
        class="table table-vertical-top"
        :data="form.monitorList"
        style="width: 100%"
        size="mini"
        border
        :header-cell-style="{ background: '#fafafa' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="index"
          label="#"
          align="center"
        ></el-table-column>
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="monitorId" label="检修部位" align="center">
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.monitorId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="{
                required: true,
                message: '请选择检修部位',
                trigger: 'change'
              }"
              label=" "
            >
              <el-select
                v-model="scope.row.monitorId"
                placeholder="请选择检修部位"
                clearable
              >
                <el-option
                  v-for="dict in monitorList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                  :disabled="isItemSelected(dict.id)"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="standard" label="检修标准" align="center">
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.standard'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="{
                required: true,
                message: '请输入检修标准',
                trigger: 'change'
              }"
              label=" "
            >
              <el-input
                v-model.trim="scope.row.standard"
                placeholder="请输入检修标准"
                clearable
                maxlength="50"
                type="textarea"
                :rows="3"
              >
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="methodsId" label="检修方式" align="center">
          <template slot-scope="scope">
            <el-form-item
              :prop="'monitorList.' + scope.$index + '.methodsId'"
              style="width: 100%"
              :key="`${scope.$index}`"
              :rules="{
                required: true,
                message: '请选择检修方式',
                trigger: 'change'
              }"
              label=" "
            >
              <el-select
                v-model="scope.row.methodsId"
                placeholder="请选择检修方式"
                clearable
              >
                <el-option
                  v-for="dict in wayList"
                  :key="dict.id"
                  :label="dict.name"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column prop="code" label="操作" align="center" width="70px">
          <template v-slot="scope">
            <el-popconfirm title="是否确认删除？" @confirm="del(scope)">
              <el-button
                style="margin-right: 10px"
                type="text"
                slot="reference"
                size="small"
                >删除</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>
<script>
  import { getPartList } from '@/api/equiment-full-life-api/maintenance';

  export default {
    name: 'PointList',
    components: {},
    props: {
      details: {
        type: Object,
        default: () => {
          return {
            overhaulStandardList: []
          };
        }
      },
      //  检修方式列表
      wayList: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    watch: {
      'details.equipmentId': {
        immediate: true,
        handler(val) {
          if (val) {
            this.$nextTick(async () => {
              await this.getMonitorList(val);
              let list = this.details.overhaulStandardList || [];
              if (list.length > 0) {
                // 如果已经添加了标准
                await this.setList(list);
              } else {
                // 没有添加标准
                await this.setList(this.monitorList);
              }
            });
          }
        }
      }
    },
    data() {
      return {
        num: 0,
        form: {
          monitorList: []
        },
        monitorList: [],
        currentIdx: undefined, // 当前的要匹配的索引
        // 某个部位已选择的传感器列表
        initSelectList: [],
        multipleSelection: [],
        repeatCode: [] // 接口返回的 重复的编码
      };
    },
    methods: {
      //  获取所有的部位
      async getMonitorList(id) {
        try {
          this.loading = true;
          const res = await getPartList({ equipmentId: id });
          let data = res.data.data || [];
          this.monitorList = data.map((item) => {
            return {
              name: item.name,
              id: item.id,
              type: item.type,
              monitorName: item.name,
              monitorId: item.id,
              monitorType: item.type
            };
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  部位选择的直接禁用 不能继续选择
      isItemSelected(id) {
        return this.form.monitorList.some((item) => item.monitorId === id);
      },
      async delAll() {
        if (this.multipleSelection.length === 0) {
          this.$message.warning('请先选择要删除的标准');
          return;
        }
        await this.confirm('是否确认删除？');
        const filteredArray = this.form.monitorList.filter(
          (item) =>
            !this.multipleSelection.some(
              (deleteItem) => deleteItem.num === item.num
            )
        );

        this.form.monitorList = [...filteredArray];
      },

      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      del(scope) {
        console.log(scope.$index);
        this.form.monitorList.splice(scope.$index, 1);
      },
      // 初始化设置列表（编辑时）
      setList(list) {
        const listNum = list.map((it) => {
          return {
            ...it,
            num: this.num++
          };
        });
        this.form.monitorList = listNum;
      },

      // 点击添加部位 在列表新增一行列表
      add() {
        this.num++;
        //1.0选择设备 1.1输入设备；
        let listData = [
          {
            num: this.num,
            monitorId: undefined,
            methodsId: undefined,
            standard: undefined
          }
        ];
        // 点击新增的时候，如果部位超过20个（最多20个），那么就新增，如果超过20个
        if (this.form.monitorList.length >= 50) {
          this.$message.warning('最多能增加50个标准');
          return;
        }
        this.form.monitorList = [...this.form.monitorList, ...listData];
      },

      async validForm() {
        if (this.form.monitorList.length === 0) {
          // this.$message.warning('请添加标准');
          return [];
        }

        let valid = await this.$refs['listForm'].validate();
        if (valid) {
          // 修复新增检修标准bug - 2025.06.11
          let params;
          if (this.edit) {
            params = this.form.monitorList;
          } else {
            params = this.form.monitorList.map((i) => {
              return {
                methodsId: i.methodsId,
                monitorId: i.monitorId,
                standard: i.standard
              };
            });
          }
          return params;
        } else {
          return false;
        }
      },

      resetForm() {
        this.num = 0;
        this.form.monitorList = [];
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    margin-bottom: 10px;
  }

  .el-form {
    padding-bottom: 50px;
  }

  /deep/ {
    .el-table__cell {
      padding: 0;
    }

    .el-input__inner {
      padding-right: 10px !important;
    }

    .el-form-item--small {
      margin-top: 16px;
    }

    .el-table .warning-row {
      background: #d3dcecff !important;
    }

    .el-table__header {
      line-height: 50px !important;
    }

    .el-form-item__content {
      width: calc(100% - 25px) !important;
    }
  }
</style>
