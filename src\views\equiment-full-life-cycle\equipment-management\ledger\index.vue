<template>
  <el-row>
    <el-col :span="5" style="margin-bottom: 0">
      <basic-container :autoHeight="true">
        <el-tabs v-model="leftTab" tab-position="top">
          <el-tab-pane label="按设备位置" name="position"> </el-tab-pane>
          <el-tab-pane label="按设备类型" name="type"> </el-tab-pane>
          <el-tab-pane label="按使用部门" name="dept"> </el-tab-pane>
        </el-tabs>
        <simas-position-tree
          v-if="leftTab === 'position'"
          noInit
          @category-change="positionChange"
        />
        <simas-type-tree
          v-else-if="leftTab === 'type'"
          @change="categoryChange"
        />
        <simas-dept-tree v-else ignoreAuth @change="deptChange" />
      </basic-container>
    </el-col>
    <el-col :span="19" style="margin-bottom: 0">
      <view-info
        :locationId="locationId"
        :categoryId="categoryId"
        :useDept="useDept"
      ></view-info>
    </el-col>
  </el-row>
</template>

<script>
  import ViewInfo from './view-info.vue';
  import SimasPositionTree from '@/views/equiment-full-life-cycle/components/simas-position-tree.vue';
  import SimasTypeTree from '@/views/equiment-full-life-cycle/components/simas-type-tree.vue';
  import SimasDeptTree from '@/views/equiment-full-life-cycle/components/simas-dept-tree.vue';

  export default {
    name: 'EquipmentManagement',
    components: {
      SimasPositionTree,
      SimasTypeTree,
      SimasDeptTree,
      ViewInfo
    },
    watch: {
      leftTab(val) {
        if (val === 'position') {
          this.categoryId = undefined;
          this.useDept = undefined;
        } else if (val === 'type') {
          this.locationId = undefined;
          this.useDept = undefined;
        } else if (val === 'dept') {
          this.locationId = undefined;
          this.categoryId = undefined;
        }
      }
    },
    data() {
      return {
        // 选中的tab
        leftTab: 'position',
        locationId: undefined, // 设备位置id
        categoryId: undefined, // 设备类型id
        useDept: undefined // 使用部门id
      };
    },

    mounted() {},
    methods: {
      // 设备位置选择
      positionChange(node) {
        this.locationId = node.data.id;
      },
      // 设备类型选择
      categoryChange(node) {
        this.categoryId = node.data.id;
      },
      // 使用部门选择
      deptChange(dept) {
        console.log('dept', dept);
        this.useDept = dept.data.id;
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    margin-bottom: 10px;
  }

  // .table {
  //   height: 695px;
  //   max-height: 695px;
  // }

  ::v-deep {
    .el-tabs__content {
      flex: 1;

      // overflow: auto;
    }

    .el-tabs__header {
      margin: 0 0 10px;
    }
  }
</style>
