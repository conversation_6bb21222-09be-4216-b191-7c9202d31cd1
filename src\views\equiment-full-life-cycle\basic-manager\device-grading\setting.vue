<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    @closed="resetForm"
    width="600px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="font-bold">消息推送</div>
    <avue-form
      ref="form"
      v-loading="loading"
      :option="formOption"
      v-model="form"
      @submit="handleFormSubmit"
    />
    <span slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" @click="() => $refs.form.submit()"
        >保 存</el-button
      >
      <el-button size="small" @click="visible = false">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { getRoleTree } from '@/api/system/role';
  import {
    getGradingDetail,
    postGradingData
  } from '@/api/equiment-full-life-api/grading';

  export default {
    name: 'deviceGradingSetting',
    serviceDicts: ['important_level', 'push_type'],
    data() {
      return {
        visible: false,
        loading: false,
        title: '新增',
        formOption: {
          labelWidth: '100',
          menuBtn: false,
          column: [
            {
              label: '设备等级',
              prop: 'importantLevelList',
              span: 16,
              type: 'checkbox',
              dicData: [],
              rules: [
                {
                  required: true,
                  message: '请选择设备等级',
                  trigger: 'change'
                }
              ]
            },
            {
              label: '推送内容',
              prop: 'pushTypeList',
              span: 16,
              type: 'checkbox',
              dicData: [],
              rules: [
                {
                  required: true,
                  message: '请选择推送内容',
                  trigger: 'change'
                }
              ]
            },
            {
              label: '推送对象',
              placeholder: '请选择角色（多选）',
              prop: 'pushRoleIdList',
              span: 16,
              type: 'tree',
              multiple: true,
              dicData: [],
              props: {
                label: 'title'
              },
              checkStrictly: true,
              rules: [
                {
                  required: true,
                  message: '请选择角色',
                  trigger: 'change'
                }
              ]
            }
          ]
        },
        form: {
          importantLevelList: [],
          pushTypeList: [],
          pushRoleIdList: []
        }
      };
    },
    mounted() {},
    methods: {
      // 获取角色信息
      async initData() {
        getRoleTree().then((res) => {
          // 角色信息
          const column1 = this.findObject(
            this.formOption.column,
            'pushRoleIdList'
          );
          column1.dicData = res.data.data;
          // 设备等级
          const column2 = this.findObject(
            this.formOption.column,
            'importantLevelList'
          );
          column2.dicData = this.serviceDicts.type['important_level'];
          // 推送内容
          const column3 = this.findObject(
            this.formOption.column,
            'pushTypeList'
          );
          column3.dicData = this.serviceDicts.type['push_type'];
        });
      },
      // 获取详情
      async getDetail() {
        try {
          this.loading = true;
          const { data } = await getGradingDetail();
          if (data.data.id) this.title = '编辑';
          const { id, levelList, pushTypeList, pushRoleIdList } = data.data;
          this.form = {
            id,
            importantLevelList: levelList.map((item) => `${item}`),
            pushTypeList: pushTypeList.map((item) => `${item}`),
            pushRoleIdList
          };
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      show() {
        this.visible = true;
        this.initData();
        this.getDetail();
      },
      // 点击提交
      async handleFormSubmit(form, done) {
        try {
          await postGradingData(form);
          done();
          this.$message.success('保存成功');
          this.visible = false;
        } catch (e) {
          console.error(e);
        }
      },
      resetForm() {
        this.visible = false;
        this.loading = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep {
    .e l-form-item__content {
      width: 100%;
    }

    .e l-form-item {
      width: 100%;
    }

    .e l-input-number {
      width: calc(100% - 30px);
    }

    .e l-dialog__body {
      padding: 10px 20px;
    }
  }

  .font-bold {
    margin-bottom: 15px;
    color: #333;
    font-weight: bold;
    font-size: 16px;
  }

  .unit_layout span {
    margin-left: 5px;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
  }
</style>
