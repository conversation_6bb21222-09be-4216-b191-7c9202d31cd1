<template>
  <el-row id="root-tree" ref="root-tree">
    <el-popover
      placement="bottom"
      :width="popoverWidth"
      trigger="manual"
      v-model="popoverVisible"
      popper-class="filter-popover"
    >
      <el-tree
        ref="filterTree"
        class="filter-tree"
        v-loading="treeFilterLoading"
        :data="filterData"
        default-expand-all
        node-key="id"
        :props="filterTreeDefaultProps"
        :expand-on-click-node="false"
        @node-click="filterNodeClick"
        @mouseenter.native="popOver"
        @mouseleave.native="popOut"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <el-row type="flex" align="middle">
            <span>
              <span style="margin-left: 5px">{{ node.label }}</span></span
            >
          </el-row>
        </span>
      </el-tree>

      <el-input
        ref="search"
        placeholder="输入名称进行过滤"
        slot="reference"
        v-model="filterText"
        size="mini"
        :maxlength="50"
        clearable
        style="margin-bottom: 10px"
        @focus="
          (e) => {
            this.searchInputFocus(e);
          }
        "
        @blur="
          () => {
            this.inputFocus = false;
          }
        "
      >
      </el-input>
    </el-popover>
    <el-scrollbar style="flex: 1">
      <el-tree
        id="tree"
        ref="tree"
        :current-node-key="currentNodeKey"
        v-loading="treeLoading"
        calss="equipment-category-tree"
        :data="treeData"
        :show-checkbox="showCheckbox"
        node-key="id"
        :expand-on-click-node="false"
        :props="treeDefaultProps"
        @node-click="nodeClick"
        :load="loadNode"
        :lazy="true"
        :default-expanded-keys="expandKeys"
      >
        <span class="custom-tree-node" slot-scope="{ data }">
          <el-row type="flex" align="middle">
            <span :title="handleTreeLabel(data)" class="label">
              {{ handleTreeLabel(data) }}
            </span>
          </el-row>
        </span>
      </el-tree>
    </el-scrollbar>
  </el-row>
</template>

<script>
  import {
    getDeviceTypeLazyList,
    getDeviceTypeLazyListWithCount,
    getDeviceTypePageList
  } from '@/api/equiment-full-life-api/device-type';
  import { mapGetters } from 'vuex';
  export default {
    name: 'simas-position-tree',
    props: {
      showCheckbox: {
        type: Boolean,
        default: false
      },
      countTree: {
        type: Boolean,
        default: false
      },
      dataScope: {
        type: String,
        default: ''
      }
    },
    watch: {
      filterText(val) {
        if (val !== '') {
          // 查询
          this.queryAllTree(val);
        } else {
          console.log('清空检索结果');
          this.filterData = [];
        }
      },
      popVisible(val) {
        console.log('popVisible 发生变化 ', val);
        this.popoverVisible = val;
      }
    },
    data() {
      return {
        // returnStrng,
        filterText: '',
        filterTree: [],
        treeFilterLoading: false,
        treeLoading: false,
        treeData: [],
        treeDefaultProps: {
          children: 'children',
          label: 'name',
          isLeaf: 'isLeaf'
        },
        filterTreeDefaultProps: {
          children: 'children',
          label: 'categoryName'
        },
        expandKeys: [],
        currentNodeKey: '',
        filterData: [],
        popoverVisible: false,
        willSelectId: '', // 将要选中的节点的id
        oldCurrentKey: undefined,
        popoverWidth: 0,
        mouseOnPop: false, // 标识当前鼠标是否在pop搜索框上
        inputFocus: false // 标识当前搜索框是否在focus状态
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'topOrgId']),
      // 用于决定 搜索结果弹窗是否显示
      popVisible() {
        return (
          (this.mouseOnPop || this.inputFocus) && this.filterText.trim() !== ''
        );
      }
    },
    created() {
      window.addEventListener('resize', this.getTreeWidth);
    },
    destroyed() {
      console.log('threshold-tree destroyed');
      window.removeEventListener('resize', this.getTreeWidth);
    },
    mounted() {
      this.popoverWidth = document.getElementById('root-tree').clientWidth;
      this.init();
    },
    methods: {
      // 处理树标签
      handleTreeLabel(data) {
        const remark = `${data.remark ? `${data.remark} ` : ''}`;
        const deviceCount = this.countTree
          ? `（${data.deviceCount || 0}）`
          : '';
        return `${remark}${data.name}${deviceCount}`;
      },
      getTreeWidth() {
        // console.log('门限设备树 resize');
        let tree = document.getElementById('root-tree');
        this.popoverWidth = tree && tree.clientWidth;
      },
      searchInputFocus() {
        console.log('focus ', this.filterText);

        this.inputFocus = true;
        // this.popoverVisible = this.filterText.trim() !== '';
      },

      popOver() {
        this.mouseOnPop = true;
      },
      popOut() {
        this.mouseOnPop = false;
      },
      async queryAllTree(val) {
        try {
          this.treeFilterLoading = true;
          const res = await getDeviceTypePageList({
            keywords: val,
            size: -1
          });
          this.filterData = res.data.data.records;
          this.treeFilterLoading = false;
        } catch (e) {
          console.log(e);
          this.$message.warning(e.data.msg);
          this.treeFilterLoading = false;
        }
      },
      async loadNode(node, resolve) {
        // console.log('loadMore ', node);
        if (node.level === 0) {
          // console.log('顶层');
          return resolve(this.data || []);
        } else if (!node.isLeaf) {
          const api = this.countTree
            ? getDeviceTypeLazyListWithCount
            : getDeviceTypeLazyList;
          const res = await api({
            parentId: node.data.id,
            dataScope: this.dataScope
          });
          const data = res.data.data.map((it) => {
            return {
              ...it,
              name: it.categoryName,
              isLeaf: !it.hasChildren
            };
          });
          // 求和第一层级设备数量, 并放入到根节点
          if (this.countTree && node.data.id === '0') {
            const totalCount = data.reduce((a, b) => {
              return a + b.deviceCount;
            }, 0);
            this.treeData[0].deviceCount = totalCount;
          }
          const findI = data.findIndex((it) => {
            return it.id === this.willSelectId;
          });
          if (node.data.id === this.willSelectId || findI !== -1) {
            // console.log('设置选中');
            this.$nextTick(() => {
              this.$refs.tree &&
                this.$refs.tree.setCurrentKey(this.willSelectId);
              const node = this.$refs.tree.getNode(this.willSelectId);
              console.log('触发信号 category-change ');
              this.$emit('change', node);
              this.willSelectId = '';
            });
          }
          return resolve(data);
        }
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.name.indexOf(value) !== -1;
      },
      init() {
        this.getTree();
      },
      async getTree() {
        try {
          this.treeLoading = true;
          this.treeData = [
            {
              name: '全部',
              categoryName: '全部',
              remark: 'ALL',
              id: '0',
              isLeaf: false,
              deviceCount: 0,
              children: []
            }
          ];
          this.setDefaultSelect();
          this.treeLoading = false;
        } catch (e) {
          console.log('e', e);
          this.treeLoading = false;
          this.treeData = [];
        }
      },
      // async getTree() {
      //   try {
      //     this.treeLoading = true;
      //     const api = this.countTree
      //       ? getDeviceTypeLazyListWithCount
      //       : getDeviceTypeLazyList;
      //     const res = await api({
      //       parentId: '0',
      //       category: 0
      //     });
      //     this.treeData = res.data.data.map((it) => {
      //       return {
      //         ...it,
      //         name: it.categoryName,
      //         isLeaf: !it.hasChildren
      //       };
      //     });
      //     console.log('ttt', this.treeData);
      //     // this.setDefaultSelect();
      //     this.treeLoading = false;
      //   } catch (e) {
      //     console.log('e', e);
      //     this.treeLoading = false;
      //     this.treeData = [];
      //   }
      // },
      async reload() {
        console.log('reload');
        // 记录重新加载前的选中值
        this.oldCurrentKey = this.$refs.tree.getCurrentKey();
        try {
          this.treeLoading = true;
          const api = this.countTree
            ? getDeviceTypeLazyListWithCount
            : getDeviceTypeLazyList;
          const res = await api({
            parentId: '0',
            category: 0,
            dataScope: this.dataScope
          });
          this.treeData = res.data.data.map((it) => {
            return {
              ...it,
              name: it.categoryName,
              isLeaf: !it.hasChildren
            };
          });

          const findI = this.treeData.findIndex((it) => {
            return it.id === (this.willSelectId || this.oldCurrentKey);
          });

          // 重新加载后 设置选中值
          console.log('this.willSelectId ', this.willSelectId);
          console.log('findI ', findI);
          if (findI !== -1) {
            console.log('设置选中');
            this.$nextTick(() => {
              this.$refs.tree &&
                this.$refs.tree.setCurrentKey(
                  this.willSelectId || this.oldCurrentKey
                );
              const node = this.$refs.tree.getNode(
                this.willSelectId || this.oldCurrentKey
              );

              this.$emit('change', node);
              this.willSelectId = '';
              this.oldCurrentKey = undefined;
            });
          } else {
            this.$emit('change', null);
          }

          this.treeLoading = false;
        } catch (e) {
          console.log('e', e);
          this.treeLoading = false;
          this.treeData = [];
        }
      },
      nodeClick(data, node) {
        this.$emit('change', node);
      },
      filterNodeClick(data) {
        this.$refs.tree && this.$refs.tree.setCurrentKey(data.id);
        this.$nextTick(() => {
          const node = this.$refs.tree.getNode(data.id);
          this.$emit('change', node);
        });
        this.popoverVisible = false;
      },
      // 设置第一个数据默认选中
      setDefaultSelect() {
        if (this.treeData.length === 0) {
          return;
        }

        // 请求第一个节点的子节点
        console.log(this.treeData[0]);
        this.expandKeys = [this.treeData[0].id];
        this.$nextTick(() => {
          const first = this.$refs.tree.getNode(this.treeData[0].id);
          this.willSelectId = this.treeData[0].id;
          this.$emit('change', first);
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  #root-tree {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100% - 50px);

    ::v-deep {
      .el-tree-node > .el-tree-node__children {
        overflow: unset;
      }
    }
  }

  :deep(.el-scrollbar__bar) {
    display: none;
  }

  .filter-tree {
    height: 300px;
    overflow: auto;

    ::v-deep {
      .el-tree-node > .el-tree-node__children {
        overflow: unset;
      }
    }
  }
</style>
<style lang="scss">
  .filter-popover {
    padding-right: 5px;
  }
</style>
