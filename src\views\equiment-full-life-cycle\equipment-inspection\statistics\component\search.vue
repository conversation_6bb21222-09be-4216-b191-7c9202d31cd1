<template>
  <el-radio-group v-model="active" size="mini" @change="change">
    <el-radio-button label="2" type="link">近7天</el-radio-button>
    <el-radio-button label="1">近30天</el-radio-button>
    <el-radio-button label="0">近一年</el-radio-button>
  </el-radio-group>
</template>
<script>
  export default {
    name: 'alarmTrend',
    props: {},
    components: {},

    data() {
      return {
        active: '1'
      };
    },
    mounted() {},
    methods: {
      change(e) {
        console.log('e。。。。。。。。。。', e);
        this.$emit('changeTime', e);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
