<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="130px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="备品备件编号" prop="no">
            <el-input
              placeholder="请输入备品备件编号"
              v-model.trim="form.no"
              maxlength="20"
              clearable
              :disabled="edit"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="备品备件名称" prop="name">
            <el-input
              placeholder="请输入备品备件名称"
              v-model.trim="form.name"
              maxlength="50"
              clearable
              :disabled="edit"
            />
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="规格型号" prop="model">
            <el-input
              placeholder="请输入规格型号"
              v-model.trim="form.model"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计量单位" prop="measureUnitId">
            <el-select
              v-model="form.measureUnitId"
              placeholder="请选择计量单位"
              clearable
              filterable
              :disabled="edit"
              @change="changeUnit"
            >
              <el-option
                v-for="item in unitArr"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="默认库房" prop="defaultWarehouseId">
            <el-select
              v-model="form.defaultWarehouseId"
              placeholder="请选择默认库房"
              clearable
              filterable
            >
              <el-option
                v-for="item in whorehouse"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="安全库存数量" prop="safeStockAmount">
            <el-input
              placeholder="请输入安全库存数量"
              v-model.trim="form.safeStockAmount"
              clearable
              maxlength="50"
            />
          </el-form-item>
        </el-col>

        <!--        <el-col :span="8">-->
        <!--          <el-form-item label="备注" prop="remark">-->
        <!--            <el-input-->
        <!--              placeholder="请输入备注"-->
        <!--              v-model.trim="form.remark"-->
        <!--              clearable-->
        <!--              maxlength="200"-->
        <!--              type="textarea"-->
        <!--              :rows="1"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
      </el-row>
    </el-form>
  </div>
</template>

<script>
  import { validatePositiveValue } from '@/util/func';

  export default {
    serviceDicts: ['measure_unit'],
    name: 'AddDeviceInfo',
    components: {},
    props: {
      whorehouse: {
        type: Array,
        default: () => {
          return [];
        }
      },
      //  计量单位
      unitArr: {
        type: Array,
        default: () => {
          return [];
        }
      },
      initData: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      const validateCode = (rule, value, callback, max) => {
        let val = validatePositiveValue(value, 9999, this.measureUnitPrecision);
        if (!value) {
          callback();
        } else if (val) {
          callback();
        } else {
          callback(
            new Error(
              `请输入大于等于0且小于等于9999的值，小数点保留${this.measureUnitPrecision}位`
            )
          );
        }
      };
      return {
        form: {
          no: undefined,
          name: undefined,
          model: undefined,
          defaultWarehouseId: undefined,
          safeStockAmount: undefined,
          measureUnitId: undefined,
          remark: undefined
        },
        measureUnitPrecision: 0,
        userLoading: false,
        edit: false,
        rules: {
          name: [
            {
              required: true,
              message: '请输入备品备件名称',
              trigger: 'blur'
            }
          ],
          no: [
            {
              required: true,
              message: '请输入备品备件编号',
              trigger: 'blur'
            }
          ],

          measureUnitId: [
            {
              required: true,
              message: '请选择计量单位',
              trigger: 'change'
            }
          ],
          safeStockAmount: [
            {
              required: false,
              message: '请输入安全库存数量',
              trigger: 'blur'
            },
            {
              validator: (rule, value, callback) =>
                validateCode(rule, value, callback)
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.edit = true;
            this.setData(this.initData);
          }
        }
      }
    },
    methods: {
      changeUnit(e) {
        console.log(e);
        let a = this.unitArr.find((item) => {
          return item.id == e;
        });
        this.measureUnitPrecision = a.accuracy;
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
        this.measureUnitPrecision = initData.measureUnitPrecision;
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return {
            ...this.form
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.edit = false;
        this.$refs['baseForm'].resetFields();
      }
    },
    computed: {},
    created() {}
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-select {
        margin-left: 0 !important;
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
