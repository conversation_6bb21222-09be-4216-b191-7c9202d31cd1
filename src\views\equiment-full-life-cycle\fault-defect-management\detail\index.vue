<template>
  <dialog-drawer
    :title="drawerTitle"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <el-collapse v-model="collapseActive">
        <!-- 基本信息 -->
        <el-collapse-item name="1">
          <span slot="title" class="el-base-title">基本信息</span>
          <base-info
            ref="baseInfo"
            :details="details"
            @detail="handleShowDetail"
          ></base-info>
        </el-collapse-item>
        <!-- 异常信息 -->
        <el-collapse-item name="2">
          <span slot="title" class="el-base-title">异常信息</span>
          <fault-info ref="faultInfo" :details="details"></fault-info>
        </el-collapse-item>
        <!--       查看的结果  只要不是已经处理的，可能都能查看结果  已处理的才展示这个处理结果-->
        <el-collapse-item name="3" v-if="[2, 3, 4].includes(details.status)">
          <span slot="title" class="el-base-title">处理结果</span>
          <handle-result
            res="handleResult"
            :details="details"
            :linkDisabled="linkDisabled"
            @detail="handleShowDetail"
          ></handle-result>
        </el-collapse-item>
        <!-- 处理信息 -->
        <el-collapse-item name="5" v-if="type === 'handle'">
          <span slot="title" class="el-base-title">处理信息</span>
          <handle-info
            ref="handleInfo"
            :locationPath="details.monitorName"
            :detail="details"
          ></handle-info>
        </el-collapse-item>
      </el-collapse>
    </div>
    <!--     状态是待处理的时候才展示提交-->
    <div
      class="oper_btn"
      v-if="Number(details.status) === 1 && type === 'handle'"
    >
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
    <!-- 来源工单详情 -->
    <component
      v-if="type !== 'repair' || !linkDisabled"
      :is="detailComp"
      ref="sourceDetail"
    ></component>
  </dialog-drawer>
</template>
<script>
  // 点检工单详情
  import InspectDetail from '@/views/equiment-full-life-cycle/equipment-inspection/work-order/detail/index.vue';
  // 保养工单详情
  import MaintainDetail from '@/views/equiment-full-life-cycle/equipment-maintenance/work-order/detail/index.vue';
  // 润滑工单详情
  import LubricateDetail from '@/views/equiment-full-life-cycle/equipment-lubrication/work-order/detail/index.vue';
  // 内部维修单详情
  import InternalDetail from '@/views/equiment-full-life-cycle/repair/internal/detail/index.vue';

  import ContentDesc from './content-desc.vue';
  import BaseInfo from './base-info.vue';
  import FaultInfo from './fault-info.vue';
  import HandleInfo from './handle-info.vue';
  import { getDetail, handle } from '@/api/equiment-full-life-api/defect';
  import HandleResult from './handle-result.vue';
  import { convertFileUrl } from '@/util/util';

  export default {
    name: 'FaultDefectDetail',
    components: {
      HandleResult,
      BaseInfo,
      ContentDesc,
      FaultInfo,
      HandleInfo,
      InspectDetail,
      MaintainDetail,
      LubricateDetail,
      InternalDetail
    },
    data() {
      return {
        convertFileUrl,
        collapseActive: ['1', '2', '3', '4', '5'],
        visible: false,
        loading: false,
        type: undefined,
        // 详情数据
        details: {
          equipmentAccount: { locationPath: '' },
          monitorStandardList: []
        },
        // 详情抽屉
        detailComp: undefined,
        // 是否可以通过点击维修单号跳转到内部维修单详情
        linkDisabled: false
      };
    },
    computed: {
      drawerTitle() {
        const titleMap = new Map([
          ['view', '详情'],
          ['handle', '处理']
        ]);
        return titleMap.get(this.type);
      }
    },
    methods: {
      // 跳转到工单详情
      handleShowDetail(type) {
        // 分为内部维修单和来源单(点巡检、保养、润滑)
        let no = this.details.sourceNo;
        if (type === 'repair') {
          this.detailComp = 'InternalDetail';
          no = this.details.repairNo;
        } else {
          const compMap = new Map([
            ['INSPECT', 'InspectDetail'],
            ['MAINTAIN', 'MaintainDetail'],
            ['LUBRICATE', 'LubricateDetail']
          ]);
          this.detailComp = compMap.get(this.details.source);
        }
        this.$nextTick(() => {
          this.$refs.sourceDetail.show(no, null, true);
        });
      },
      //  维修上报
      async submit() {
        let handleForm = await this.$refs['handleInfo'].validForm();
        if (!handleForm) return;
        let params = {
          ...handleForm,
          id: this.details.id
        };
        await this.handleApi(params);
      },
      //  处理接口
      async handleApi(params) {
        try {
          await handle(params);
          this.visible = false;
          this.$message.success('操作成功');
          this.$emit('success');
        } catch (e) {
          console.log(e);
        }
      },
      //   关闭处理
      closed() {
        this.details = {
          equipmentAccount: { locationPath: '' },
          monitorStandardList: []
        }; // 详情数据
        this.$refs['handleInfo'] && this.$refs['handleInfo'].resetForm();
        this.visible = false;
      },

      // 点击展示
      async show(no, type, flag) {
        this.type = type;
        this.visible = true;
        this.linkDisabled = flag;
        no && (await this.getDetail(no));
      },
      // 获取详情接口
      async getDetail(no) {
        try {
          this.loading = true;
          const res = await getDetail({ no });
          this.details = res.data.data || {};
          // 处理附件名
          this.details.abnormalAttachList = this.details.abnormalAttachList.map(
            (item) => {
              return {
                ...item,
                name: item.originalName
              };
            }
          );
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .details {
    padding-bottom: 60px;
  }

  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }

  .el-collapse ::v-deep {
    border: none;

    .el-collapse-item__wrap {
      border-bottom: none;
    }

    .desc-content {
      width: 22%;
    }

    .content-wrapper {
      max-width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .el-form-item,
    .el-select {
      width: 100%;
    }

    .el-form-item__content {
      width: calc(100% - 140px);
    }
  }

  ::v-deep .el-button--text.el-button--medium {
    padding: 8px 0 !important;
  }
</style>
