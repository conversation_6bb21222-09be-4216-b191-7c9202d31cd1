<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    size="80%"
    class="device-add"
  >
    <section v-loading="loading">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-suffix="："
        label-position="right"
        label-width="120px"
      >
        <ChangeInfo :form="form"></ChangeInfo>
      </el-form>
      <p class="el-base-title">风险评估资料</p>
      <el-form label-suffix="：" label-width="125px">
        <el-row class="form-content-height">
          <el-col :span="8">
            <el-form-item label="安全检查表" label-width="155px">
              <upload-file
                v-model="safetyChecklist"
                placeholder=""
                :limit="9"
                btnSize="medium"
                formatLimit="pdf,doc,docx,jpeg,png,jpg"
                accept=".pdf,.doc,.docx,.jpeg,.png,.jpg"
              ></upload-file>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预先危险性分析">
              <upload-file
                v-model="preHazardAnalysis"
                placeholder=""
                :limit="9"
                btnSize="medium"
                formatLimit="pdf,doc,docx,jpeg,png,jpg"
                accept=".pdf,.doc,.docx,.jpeg,.png,.jpg"
              ></upload-file>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label-width="155px" label="故障类型和影响分析">
              <upload-file
                v-model="failureTypeAndImpactAnalysis"
                placeholder=""
                :limit="9"
                btnSize="medium"
                formatLimit="pdf,doc,docx,jpeg,png,jpg"
                accept=".pdf,.doc,.docx,.jpeg,.png,.jpg"
              ></upload-file>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="form-content-height">
          <el-col :span="8">
            <el-form-item label-width="155px" label="危险与可操作性分析">
              <upload-file
                v-model="hazardOperabilityAnalysis"
                placeholder=""
                :limit="9"
                btnSize="medium"
                formatLimit="pdf,doc,docx,jpeg,png,jpg"
                accept=".pdf,.doc,.docx,.jpeg,.png,.jpg"
              ></upload-file>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其它">
              <upload-file
                v-model="other"
                placeholder=""
                :limit="9"
                btnSize="medium"
                formatLimit="pdf,doc,docx,jpeg,png,jpg"
                accept=".pdf,.doc,.docx,.jpeg,.png,.jpg"
              ></upload-file>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <p class="el-base-title">需要更新的文件</p>
      <UpdateFile
        ref="updateFile"
        v-loading="monitorListLoading"
        :isEdit="true"
        :list="monitorList"
      ></UpdateFile>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>

    <!--     选择人员-->
    <recipient-dialog
      ref="recipient"
      @selected="onUserSelect"
    ></recipient-dialog>
  </dialog-drawer>
</template>

<script>
  import { mapGetters } from 'vuex';
  import {
    addOrEditRepairApi,
    listFileByEquipmentId,
    getChangeViewApi
  } from '@/api/equiment-full-life-api/change';
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import { roleUserListApi } from '@/api/user';
  import UploadFile from '@/components/view-upload-file.vue';
  import ChangeInfo from './change-info';
  import UpdateFile from '../components/update-file';
  const form = {
    id: null,
    equipmentId: '',
    equipmentName: '',
    model: '',
    equipmentCategoryName: '',
    useDeptName: '',
    // 变更明细
    changeNumber: '',
    changeName: '',
    changeReason: '',
    changePurpose: '',
    changeCategory: '',
    expectedImplementationDate: '',
    changeContent: '',
    implementationPlan: '',
    expectedEffect: '',
    remarks: ''
  };
  export default {
    name: 'repair-internal-operate',
    components: { UploadFile, RecipientDialog, ChangeInfo, UpdateFile },
    serviceDicts: ['equipment_change_catgegory'],
    data() {
      return {
        useDept: undefined, // 当前设备的部门
        visible: false,
        detail: {},
        list: [],
        loading: false,
        userListLoading: false,
        monitorListLoading: false,
        eqId: '',
        edit: false,
        form: { ...form },
        monitorList: [],
        userList: [],
        rules: {
          changeName: [{ required: true, message: '请输入', trigger: 'blur' }],
          changeReason: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          changePurpose: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          changeCategory: [
            { required: true, message: '请选择', trigger: 'change' }
          ],
          expectedImplementationDate: [
            { required: true, message: '请选择', trigger: 'change' }
          ]
        },
        safetyChecklist: [], // 安全检查表
        preHazardAnalysis: [], // 预先危险性分析
        failureTypeAndImpactAnalysis: [], // 故障类型和影响分析
        hazardOperabilityAnalysis: [], // 危险与可操作性分析
        other: [] // 其它
      };
    },
    watch: {
      'form.equipmentId': {
        immediate: true,
        handler(val) {
          if (val && !this.eqId) {
            this.listFileByEquipmentId();
            this.$refs.updateFile.allPageSelect = [];
          }
        }
      }
    },
    computed: {
      ...mapGetters(['userInfo', 'permission'])
    },
    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getChangeViewApi({ id });
          this.detail = res.data.data;
          this.setForm();
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      setForm() {
        let detail = this.detail;
        for (const key in this.form) {
          this.form[key] = detail[key];
        }
        let riskFileList = detail.riskFileList || [];
        this.safetyChecklist =
          (riskFileList || []).filter((item) => item.riskType === '1') || [];
        this.preHazardAnalysis =
          (riskFileList || []).filter((item) => item.riskType === '2') || [];
        this.failureTypeAndImpactAnalysis =
          (riskFileList || []).filter((item) => item.riskType === '3') || [];
        this.hazardOperabilityAnalysis =
          (riskFileList || []).filter((item) => item.riskType === '4') || [];
        this.other =
          (riskFileList || []).filter((item) => item.riskType === '5') || [];

        this.monitorList = detail.changeFileList || [];
        this.initializeSelected();
      },
      initializeSelected() {
        // 文件更新状态(0 待更新 1 已更新 2 未选择)
        let tableData = this.monitorList;
        let selectedList = tableData.filter(
          (item) => item.updateStatus === '0'
        );
        this.$refs.updateFile.allPageSelect = selectedList;
        let selectedIds = selectedList.map((item) => item.id);
        this.$nextTick(() => {
          let multipleTable = this.$refs.updateFile.$refs.multipleTable;
          tableData.forEach((row) => {
            if (selectedIds.includes(row.id)) {
              multipleTable.toggleRowSelection(row, true);
            } else {
              multipleTable.toggleRowSelection(row, false);
            }
          });
        });
      },
      async show(row = {}) {
        this.visible = true;
        this.monitorList = [];
        // this.getUserList();
        this.edit = !!row.id;
        this.eqId = row.id;
        if (row.id) {
          this.$refs.form && this.$refs.form.clearValidate();
          await this.getDetail(row.id);
        } else {
          this.$refs.form && this.$refs.form.clearValidate();
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.$refs.form && this.$refs.form.clearValidate();
        this.edit = false;
        this.eqId = '';
        this.form.image = [];
        this.form = { ...form };
        this.detail = {};
        this.safetyChecklist = [];
        this.preHazardAnalysis = [];
        this.failureTypeAndImpactAnalysis = [];
        this.hazardOperabilityAnalysis = [];
        this.other = [];
        this.visible = false;
      },
      getRiskAssessmentInformation() {
        let safetyChecklist = this.safetyChecklist;
        let preHazardAnalysis = this.preHazardAnalysis;
        let failureTypeAndImpactAnalysis = this.failureTypeAndImpactAnalysis;
        let hazardOperabilityAnalysis = this.hazardOperabilityAnalysis;
        let other = this.other;
        safetyChecklist = safetyChecklist.map((item) => ({
          riskType: '1',
          id: item.id === item.attachId ? null : item.id,
          attachId: item.attachId
        }));
        preHazardAnalysis = preHazardAnalysis.map((item) => ({
          riskType: '2',
          id: item.id === item.attachId ? null : item.id,
          attachId: item.attachId
        }));
        failureTypeAndImpactAnalysis = failureTypeAndImpactAnalysis.map(
          (item) => ({
            riskType: '3',
            id: item.id === item.attachId ? null : item.id,
            attachId: item.attachId
          })
        );
        hazardOperabilityAnalysis = hazardOperabilityAnalysis.map((item) => ({
          riskType: '4',
          id: item.id === item.attachId ? null : item.id,
          attachId: item.attachId
        }));
        other = other.map((item) => ({
          riskType: '5',
          id: item.id === item.attachId ? null : item.id,
          attachId: item.attachId
        }));
        return [
          ...safetyChecklist,
          ...preHazardAnalysis,
          ...failureTypeAndImpactAnalysis,
          ...hazardOperabilityAnalysis,
          ...other
        ];
      },
      async submit() {
        let { equipmentId } = this.form;
        if (!equipmentId) return this.$message.warning('请选择变更设备');
        let changeFileList = this.$refs.updateFile.allPageSelect;
        // if (!changeFileList.length)
        //   return this.$message.warning('请选择需要更新的文件');
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            try {
              this.loading = true;
              let riskFileList = this.getRiskAssessmentInformation();
              let files = changeFileList.map((item) => ({
                equipmentFileId: item.equipmentFileId || item.id,
                attachId: item.attachId,
                id: item.equipmentFileId ? item.id : null,
                // 添加传参
                name: item.name,
                type: item.type,
                fileCategoryId: item.fileCategoryId,
                extension: item.extension
              }));
              await addOrEditRepairApi({
                ...this.form,
                riskFileList,
                changeFileList: files
              });
              this.$message.success('操作成功');
              this.$emit('success');
              this.visible = false;
              this.loading = false;
            } catch (e) {
              this.loading = false;
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      async getUserList() {
        try {
          this.userListLoading = true;
          const res = await roleUserListApi({
            alias: 'repair_user',
            deptId: this.useDept
          });
          this.userList = res.data.data;
          this.userListLoading = false;
        } catch (e) {
          this.userListLoading = false;
          this.monitorList = [];
        }
      },
      onChooseDeviceClick() {
        this.$refs.assetList.show([], true);
      },
      async listFileByEquipmentId() {
        try {
          this.monitorList = [];
          this.monitorListLoading = true;
          const res = await listFileByEquipmentId({
            equipmentId: this.form.equipmentId
          });
          this.monitorList = res.data.data;
          this.monitorListLoading = false;
        } catch (e) {
          this.monitorListLoading = false;
          this.monitorList = [];
        }
      },
      //  选择人员
      onChooseUser() {
        this.$refs['recipient'].show();
      },
      onUserSelect(user) {
        console.log('user', user);
        this.form.receiveUser = user.id;
        this.form.receiveUserName = user.realName;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  ::v-deep .form-content-height {
    .el-form-item__content {
      height: auto;
    }
  }

  /deep/.el-form-item__content {
    height: 32px;
  }
</style>
