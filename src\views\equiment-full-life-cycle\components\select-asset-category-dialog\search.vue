<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      label-width="110px"
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="设备类型名称" prop="keywords">
        <el-input
          style="width: 100%"
          v-model.trim="form.keywords"
          placeholder="请输入设备类型名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceListSearch',
    data() {
      return {
        form: {
          keywords: undefined
        }
      };
    },
    methods: {
      reset() {
        this.form.keywords = undefined;
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
