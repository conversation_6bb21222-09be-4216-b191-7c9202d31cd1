<template>
  <el-dialog
    title="导入"
    append-to-body
    :visible.sync="visible"
    width="555px"
    @close="knowBtn"
    v-if="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    custom-class="dialog"
  >
    <avue-form
      v-if="!isMsg"
      :option="excelOption"
      v-model="excelForm"
      ref="excelForm"
    >
      <template slot="excelTemplate">
        <el-button type="primary" @click="handleTemplate">
          点击下载<i class="el-icon-download el-icon--right"></i>
        </el-button>
      </template>
    </avue-form>
    <!--     存在未导入的数据，展示提示信息-->
    <section class="msg">
      <span v-html="msg"></span>
      <p>{{ failMsg }}</p>
    </section>
    <div class="footer">
      <span slot="footer" class="dialog-footer">
        <el-button
          size="small"
          type="primary"
          plain
          @click="knowBtn"
          v-if="isMsg"
        >
          知道了
        </el-button>
        <el-button
          v-if="!!failureNumber && !isInstanceExport"
          size="small"
          type="primary"
          @click="downFailData"
        >
          下载失败数据<i class="el-icon-download el-icon--right"></i>
        </el-button>
      </span>
    </div>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </el-dialog>
</template>
<script>
  import { exportBlob } from '@/api/common';
  import { getToken } from '@/util/auth';
  import { downloadXls } from '@/util/util';
  import DeptDialog from '@/components/dept-dialog/index.vue';

  export default {
    components: { DeptDialog },
    props: {
      action: {
        type: String,
        require: true
      },
      // 模版下载路径
      templateUrl: {
        type: String,
        require: true
      },
      // 导入失败原因下载路径
      failListUrl: {
        type: String,
        require: true
      },
      downLoadFileName: {
        type: String,
        default: () => {
          return '轴承库模板';
        }
      },
      //  是不是展示标准部门选择
      isShow: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      deptId: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      //  导入导出目前是 轴承库 和 传感器实例在使用
      isInstanceExport: {
        type: Boolean,
        default: () => {
          return false;
        }
      }
    },
    data() {
      return {
        visible: false,
        msg: '',
        failMsg: '',
        excelForm: {},
        loading: false,
        excelOption: {
          submitBtn: false,
          emptyBtn: false,
          column: []
        },

        columnConfig: [
          {
            label: '模板上传',
            prop: 'excelFile',
            type: 'upload',
            showFileList: false,
            drag: true,
            fileSize: 2000, // 2000kB = 2M
            maxFileSize: 2000,
            accept: '.xlsx,.xls',
            loadText: '导入中，请稍等',
            span: 24,
            propsHttp: {
              res: 'data'
            },
            fileType: 'xls,xlsx',
            tip: '请上传 .xls,.xlsx 标准格式文件,且不超过2M',
            action: `/api${this.action}`,
            uploadError: this.uploadError,
            uploadBefore: this.uploadBefore
          },

          {
            // disabled: this.isInstanceExport ? true : false,
            display: this.isShow, // 展示隐藏
            label: '选择部门',
            prop: 'deptName',
            type: 'input',
            align: 'center',
            width: 120,
            slot: true,
            focus: this.focusDept,
            readonly: true,
            placeholder: '请选择部门',
            value: undefined,
            rules: [
              {
                required: true,
                message: '请选择部门',
                trigger: ['blur', 'change']
              }
            ]
          },
          {
            label: '模板下载',
            prop: 'excelTemplate',
            formslot: true,
            span: 24
          }
        ],
        isMsg: false,
        failureNumber: undefined
      };
    },
    watch: {
      'excelForm.deptId'() {
        console.log('watch.............', this.excelForm.deptId);
        if (this.excelForm.deptId !== '') {
          const column = this.findObject(this.excelOption.column, 'excelFile');
          column.action = `/api${this.action}?deptId=${this.excelForm.deptId}`;
        }
      },
      visible() {
        // 导入清除文件
        this.excelForm.excelFile = [];
      }
    },
    methods: {
      focusDept() {
        this.$refs['dept-dialog'].show();
      }, // 选择部门回调
      onSelectDept(dept) {
        console.log(dept);
        this.excelForm.deptId = dept.id;
        this.excelForm.deptName = dept.deptName;
        console.log('th', this.columnConfig);
        // this.excelOption.column[1] = dept.deptName;
        // this.$set(this.excelOption.column[1], 'deptName', dept.deptName);
      },
      show() {
        this.visible = true;
        this.excelOption.column = [...this.columnConfig];
      },
      handleTemplate() {
        if (this.isShow && !this.excelForm.deptId) {
          this.$message.warning('请选择部门');
          return;
        }
        this.$message.warning('模板下载中，请稍后...');
        exportBlob(
          `/api${this.templateUrl}?deptId=${this.excelForm.deptId}&${
            this.website.tokenHeader
          }=${getToken()}`
        ).then((res) => {
          downloadXls(res.data, `${this.downLoadFileName}.xlsx`);
        });
      },
      // 下载失败数据
      downFailData() {
        this.$message.warning('数据下载中，请稍后...');
        exportBlob(
          `/api${this.failListUrl}?${this.website.tokenHeader}=${getToken()}`
        ).then((res) => {
          downloadXls(res.data, '导入失败数据.xlsx');
        });
        this.knowBtn();
      },
      // 知道了
      knowBtn() {
        this.visible = false;
        this.excelForm.excelFile = [];
        this.msg = '';
        this.isMsg = false;
        this.failMsg = '';
        this.excelForm.deptId = undefined;
        this.excelForm.deptName = undefined;
        this.failureNumber = undefined;
      },

      uploadError() {
        console.log('error...');
        this.$message.error(
          '导入失败，请上传 .xls,.xlsx 标准格式文件,且不超过2M'
        );
        this.knowBtn();
      },
      async uploadBefore(file) {
        const formData = new FormData();
        formData.append('file', file);
        //  如果是传感器实例导入，将参数去掉
        let url = this.isInstanceExport
          ? `/api${this.action}?deptId=undefined`
          : `/api${this.action}?deptId=${this.excelForm.deptId}`;
        await this.$http
          .post(url, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          })
          .then(async (res) => {
            let data = res.data;
            await this.uploadAfter(data.data);
            return true;
          })
          .catch(() => {
            this.knowBtn();
            return false;
          });
      },
      uploadAfter(res) {
        let isSuccess = res === null || res.length === 0;
        // 如果失败条数为0 直接关闭，刷新列表，提示导入成功 2. 如果失败条数不为0 展示 成功几条，失败几条， 下载失败模板，和知道了按钮 3. 如果格式不匹配：展示文案，和知道了按钮
        if (isSuccess) {
          this.$message.success('导入成功');
          this.knowBtn();
        } else {
          this.isMsg = true;
          this.msg = res.join('<br>');
          this.excelOption.column = [];
        }
        this.$emit('refresh');
      }
    }
  };
</script>
<style lang="scss" scoped>
  .footer {
    display: flex;
    justify-content: flex-end;
  }

  .msg {
    padding-bottom: 30px;
    //line-height: 160px;
    text-align: center;
    height: 90%;
    overflow-y: scroll;
    p {
      text-align: center;
    }
  }
  .dialog {
    ::v-deep {
      .el-dialog__body {
        height: 280px;
      }
    }
  }
</style>
