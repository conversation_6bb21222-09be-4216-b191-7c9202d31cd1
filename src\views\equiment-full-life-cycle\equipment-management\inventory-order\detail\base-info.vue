<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
      size="small"
    >
      <el-descriptions-item label="盘点单号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="盘点名称：">{{
        details.planName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="制定人员：">{{
        details.planCreateUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="盘点人员：">{{
        details.deviceUserName || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="盘点日期："
        >{{ details.planStartDate || '-' }} ~
        {{ details.planEndDate || '-' }}</el-descriptions-item
      >
      <el-descriptions-item label="完成时间：">{{
        details.completeTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="盘点部门：">{{
        details.deviceDeptName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="已盘/全部：">
        {{ details.hasInventoryQuantity }}
        / {{ details.totalQuantity }}
      </el-descriptions-item>
      <el-descriptions-item label="盘点状态：">{{
        details.statusName || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }
</style>
