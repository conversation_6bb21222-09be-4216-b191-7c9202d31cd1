<template>
  <basic-container>
    <search
      :show-search="showSearch"
      :tenant-data="tenantData"
      @search="search"
    />
    <el-row :gutter="15">
      <el-col :span="1.5">
        <el-button
          v-if="permission.system_dept_add"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-if="permission.system_dept_delete"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleBatchDelete"
          >删除</el-button
        >
      </el-col>
      <yk-right-tool
        :show-search.sync="showSearch"
        :columns="columns"
        @queryTable="request"
      ></yk-right-tool>
    </el-row>
    <table-list
      v-loading="loading"
      :columns="columns"
      :tenant-data="tenantData"
      :list="list"
      @dispatch="dispatch"
    />
    <!-- 新增/编辑/查看 -->
    <model
      :title="title"
      :visited="visited"
      :id="rowId"
      :child-id="childId"
      :tenant-data="tenantData"
      :model-type="modelType"
      @close="handleClose"
      @refresh="request"
    />
    <!-- 批量删除结果弹窗 -->
    <del-res
      :resVisible="resVisible"
      :resData="resData"
      @close="handleModalClose"
    />
  </basic-container>
</template>

<script>
  import Search from './components/search.vue';
  import TableList from './components/table-list.vue';
  import Model from './components/model';
  import DelRes from '@/components/batch-delete-result';
  import { getLazyList, remove } from '@/api/system/dept';
  import { mapGetters } from 'vuex';
  import website from '@/config/website';
  import { getTenantData } from '@/api/system/tenant';
  import { formatTreeData } from '@/util/util';

  const columns = [
    { key: 0, label: `机构名称`, visible: true },
    { key: 1, label: `所属租户`, visible: true },
    { key: 2, label: `机构全称`, visible: true },
    { key: 3, label: `机构类型`, visible: true },
    { key: 4, label: `排序`, visible: true }
  ];

  export default {
    name: 'deptList',
    components: {
      Search,
      TableList,
      Model,
      DelRes
    },
    data() {
      return {
        columns,
        queryParams: {},
        showSearch: true,
        loading: false,
        list: [],
        title: '添加菜单',
        visited: false,
        modelType: '',
        rowIds: [],
        rowId: '',
        childId: '',
        // 租户信息
        tenantData: [],
        // 结果弹窗
        resVisible: false,
        resData: {}
      };
    },
    computed: {
      ...mapGetters(['permission'])
    },
    mounted() {
      if (website.tenantMode) {
        this.getTenantData();
      }
    },
    methods: {
      // 获取租户数据
      async getTenantData() {
        try {
          const res = await getTenantData();
          this.tenantData = formatTreeData(res.data.data, {
            label: 'tenantName',
            value: 'tenantId'
          });
        } catch (e) {
          console.error(e);
        }
      },
      // 查询操作
      search(params = {}) {
        this.queryParams = Object.assign({}, params);
        this.request();
      },
      // 列表api查询
      async request() {
        try {
          this.loading = true;
          const res = await getLazyList('', this.queryParams);
          this.list = res.data.data;
        } catch (err) {
          console.error(err);
        } finally {
          this.loading = false;
        }
      },
      // 新增
      handleAdd() {
        this.title = '新 增';
        this.visited = true;
        this.modelType = 'add';
        console.log(1 + this.childId);
      },
      // 关闭弹框
      handleClose() {
        this.visited = false;
        this.modelType = '';
        this.rowId = '';
        this.childId = '';
      },
      // 列表事件
      dispatch(type, row) {
        switch (type) {
          case 'view': // 查看
            return this.handleView(row);
          case 'add_child': // 新增子项
            return this.handleAddChild(row);
          case 'edit': // 编辑
            return this.handleEdit(row);
          case 'delete': // 列表单个删除
            return this.handleDelete(row);
          case 'selections': // 批量删除
            return (this.rowIds = row);
        }
      },
      // 查看
      handleView(row) {
        this.title = '查 看';
        this.visited = true;
        this.modelType = 'view';
        this.rowId = row.id;
      },
      // 新增子项
      handleAddChild(row) {
        this.title = '新 增';
        this.visited = true;
        this.modelType = 'addChild';
        this.childId = row.id;
      },
      // 编辑
      handleEdit(row) {
        this.title = '编 辑';
        this.visited = true;
        this.modelType = 'edit';
        this.rowId = row.id;
      },
      // 删除
      handleDelete(row) {
        this.$confirm('确定删除选择的数据吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            const res = await remove(row.id);
            const data = res.data.data;
            if (data.failureNumber === 0) {
              this.$message({
                type: 'success',
                message: '操作成功!'
              });
              this.request();
            } else {
              this.$message({
                type: 'error',
                message: data.detailVOList[0].message
              });
            }
          } catch (e) {
            console.error(e);
          }
        });
      },
      // 批量删除
      handleBatchDelete() {
        if (this.rowIds.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.$confirm('确定删除选择的数据吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            const res = await remove(this.rowIds.join(','));
            this.resData = res.data.data;
            this.resVisible = true;
          } catch (e) {
            console.error(e);
          }
        });
      },
      // 删除结果弹窗关闭
      handleModalClose() {
        this.resVisible = false;
        // 表格数据重载
        this.request();
      }
    }
  };
</script>
