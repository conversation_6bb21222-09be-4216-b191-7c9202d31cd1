<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="领用单号：">{{
        details.orderNo || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="领用日期：">
        {{ details.receiveStartDate || '-' }}~{{
          details.receiveEndDate || '-'
        }}
      </el-descriptions-item>
      <el-descriptions-item label="申请人：">{{
        details.applyUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="使用位置">{{
        details.locationPath || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="所属部门">{{
        details.belongDeptName || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="领用状态：">{{
        details.statusName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item :span="3" label="申请时间">{{
        details.applyTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item :span="3" label="设备需求：">{{
        details.demand || '-'
      }}</el-descriptions-item>
      <el-descriptions-item :span="3" label="补充说明：">{{
        details.additionalRemarks || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
