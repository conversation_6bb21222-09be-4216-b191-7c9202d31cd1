<template>
  <el-table
    class="table"
    :data="list"
    row-key="id"
    size="small"
    border
    ref="table"
    :header-cell-style="{ background: '#fafafa' }"
  >
    <el-table-column type="index" label="#"></el-table-column>
    <el-table-column prop="no" label="备品备件编号" show-overflow-tooltip>
    </el-table-column>
    <el-table-column prop="name" label="备品备件名称" show-overflow-tooltip>
    </el-table-column>
    <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
      <template slot-scope="scope">
        {{ scope.row.model || '-' }}
      </template>
    </el-table-column>
    <el-table-column
      prop="measureUnitName"
      label="计量单位"
      show-overflow-tooltip
    >
      <template slot-scope="scope">
        {{ scope.row.measureUnitName || '-' }}
      </template>
    </el-table-column>
    <el-table-column prop="count" label="消耗数量" show-overflow-tooltip>
    </el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
