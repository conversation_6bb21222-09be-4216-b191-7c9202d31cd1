<!-- 设备数量统计 = 设备总数、监测设备台数、监测设备部位-->
<template>
  <div class="alarmTrend" v-loading="loading">
    <span class="title">
      <section>
        <i class="wel_icon iconfont el-icon-ud-biaotitubiao2"></i>
        <span style="color: #3d446e"> 保养任务情况统计</span>
      </section>
      <search @changeTime="changeTime"></search>
    </span>

    <div class="total">
      <section class="_dev">
        <span class="img">T</span>
        <section class="cont">
          <span class="_dev_num">{{ data.maintainCount || 0 }} </span>
          <span class="_dev_desc">保养设备总数</span>
        </section>
      </section>
      <section class="_dev">
        <span class="img">N</span>
        <section class="cont">
          <span class="_dev_num"> {{ data.normalCount || 0 }}</span>
          <span class="_dev_desc">正常设备 </span>
        </section>
      </section>
      <section class="_dev">
        <span class="img">A</span>
        <section class="cont">
          <span class="_dev_num"> {{ data.abnormalCount || 0 }}</span>
          <span class="_dev_desc">异常设备</span>
        </section>
      </section>
      <section class="_dev">
        <span class="img">O</span>
        <section class="cont">
          <span class="_dev_num"> {{ data.omissionCount || 0 }}</span>
          <span class="_dev_desc">漏检设备</span>
        </section>
      </section>
    </div>
  </div>
</template>
<script>
  import { getStatisticsTask } from '@/api/equiment-full-life-api/maintenance';
  import Search from './search.vue';
  export default {
    name: 'factoryCab',
    components: { Search },
    props: {},
    data() {
      return { loading: false, data: {} };
    },
    watch: {},
    mounted() {
      this.$nextTick(() => {
        this.getAsset(0);
      });
    },
    methods: {
      changeTime(e) {
        this.getAsset(e);
      },
      async getAsset(day) {
        this.loading = true;
        try {
          const res = await getStatisticsTask({
            queryDate: day
          });
          if (res.data.data) {
            this.flag = true;
            this.data = res.data.data;
          } else {
            this.flag = false;
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      jump(status) {
        this.$router.push({
          path: '/asset-file/index',
          query: {
            status: status
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .alarmTrend {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: var(--el-bg-color-overlay);
    border-radius: 5px;
  }
  .title {
    display: flex;
    justify-content: space-between;

    section {
      width: calc(100% - 220px);
      overflow: hidden;
      text-overflow: ellipsis;
      display: flex;
    }
    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    em {
      display: inline-block;
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-style: normal;
      line-height: 10%;
    }
  }
  .total {
    margin-top: 15px;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 定义两列 */
    grid-template-rows: repeat(2, auto); /* 定义两行 */
    gap: 15px; /* 间距 */
    //width: 100%;
    height: calc(100% - 45px);
    ._dev:nth-child(1) {
      .img {
        background: url('../../../asset/images/1.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    ._dev:nth-child(2) {
      .img {
        background: url('../../../asset/images/2.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    ._dev:nth-child(3) {
      .img {
        background: url('../../../asset/images/3.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    ._dev:nth-child(4) {
      .img {
        background: url('../../../asset/images/4.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  ._dev {
    position: relative;
    height: 100%;
    width: 100%;
    text-align: center;
    background-size: 100% 100%;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    cursor: pointer;
    .img {
      position: absolute;
      width: 40%;
      height: 30px;
      background-size: 100% 100%;
      left: 35px;
      top: 12px;
      display: flex;
      justify-content: center;
      flex-direction: column;
      color: #fff;
      text-align: left;
      text-indent: 41%;
      font-weight: 600;
      font-family: electronicFont;
      z-index: 1;
    }

    .cont {
      position: absolute;
      width: 80%;
      height: 115px;
      background: #fcfcfd;
      box-shadow: 5px 5px 2px rgba(61, 68, 110, 0.1);
      margin-top: 10%;
      margin-left: 10%;
      left: 0;
      top: 0px;
      z-index: 0;
    }

    &_desc {
      display: block;
      font-size: 15px;
      line-height: 40px;
      color: #3d446e;
    }

    &_num {
      float: left;
      width: 100%;
      color: #3d446e;
      font-weight: 600;
      font-size: 28px;
      font-family: electronicFont;
      margin-top: 30px;
    }
  }

  .unit {
    color: #999db4;
    font-size: 14px;
    font-style: normal;
  }
</style>
