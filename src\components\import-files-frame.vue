<template>
  <el-dialog
    title="导入"
    append-to-body
    :visible.sync="visible"
    width="555px"
    @close="knowBtn"
    v-if="visible"
  >
    <avue-form
      v-if="!isMsg"
      :option="excelOption"
      v-model="excelForm"
      ref="excelForm"
    >
      <template slot="excelTemplate">
        <el-button type="primary" @click="handleTemplate">
          点击下载<i class="el-icon-download el-icon--right"></i>
        </el-button>
      </template>
    </avue-form>
    <!--     存在未导入的数据，展示提示信息-->
    <section class="msg">
      <span>{{ msg }}</span>
      <p>{{ failMsg }}</p>
    </section>
    <div class="footer">
      <span slot="footer" class="dialog-footer">
        <el-button
          size="small"
          type="primary"
          plain
          @click="knowBtn"
          v-if="isMsg"
        >
          知道了
        </el-button>
        <el-button
          v-if="!!failureNumber && !isInstanceExport"
          size="small"
          type="primary"
          @click="downFailData"
        >
          下载失败数据<i class="el-icon-download el-icon--right"></i>
        </el-button>
      </span>
    </div>
  </el-dialog>
</template>
<script>
  import { exportBlob } from '@/api/common';
  import { getToken } from '@/util/auth';
  import { downloadXls } from '@/util/util';

  export default {
    props: {
      action: {
        type: String,
        require: true
      },
      // 模版下载路径
      templateUrl: {
        type: String,
        require: true
      },
      // 导入失败原因下载路径
      failListUrl: {
        type: String,
        require: true
      },
      downLoadFileName: {
        type: String,
        default: () => {
          return '轴承库模板';
        }
      },
      isCovered: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      //  导入导出目前是 轴承库 和 传感器实例在使用
      isInstanceExport: {
        type: Boolean,
        default: () => {
          return false;
        }
      }
    },
    data() {
      return {
        visible: false,
        msg: '',
        failMsg: '',
        excelForm: {},
        loading: false,
        excelOption: {
          submitBtn: false,
          emptyBtn: false,
          column: []
        },

        columnConfig: [
          {
            label: '模板上传',
            prop: 'excelFile',
            type: 'upload',
            showFileList: false,
            drag: true,
            fileSize: 2000, // 2000kB = 2M
            maxFileSize: 2000,
            accept: '.xlsx,.xls',
            loadText: '导入中，请稍等',
            span: 24,
            propsHttp: {
              res: 'data'
            },
            fileType: 'xls,xlsx',
            tip: '请上传 .xls,.xlsx 标准格式文件,且不超过2M',
            action: `/api${this.action}`,
            uploadError: this.uploadError,
            uploadBefore: this.uploadBefore
          },

          {
            disabled: this.isInstanceExport ? true : false,
            label: '数据覆盖',
            prop: 'isCovered',
            type: 'switch',
            align: 'center',
            width: 120,
            dicData: [
              {
                label: '否',
                value: 0
              },
              {
                label: `是  ${this.isInstanceExport ? ' 默认不覆盖' : ''}`,
                value: 1
              }
            ],
            value: 0,
            slot: true,
            rules: [
              {
                required: true,
                message: '请选择是否覆盖',
                trigger: 'blur'
              }
            ]
          },
          {
            label: '模板下载',
            prop: 'excelTemplate',
            formslot: true,
            span: 24
          }
        ],
        isMsg: false,
        failureNumber: undefined
      };
    },
    watch: {
      'excelForm.isCovered'() {
        console.log('watch.............', this.excelForm.isCovered);
        if (this.excelForm.isCovered !== '') {
          const column = this.findObject(this.excelOption.column, 'excelFile');
          column.action = `/api${this.action}?isCovered=${this.excelForm.isCovered}`;
        }
      },
      visible() {
        // 导入清除文件
        this.excelForm.excelFile = [];
      }
    },
    methods: {
      show() {
        this.visible = true;
        this.excelOption.column = [...this.columnConfig];
      },
      handleTemplate() {
        this.$message.warning('模板下载中，请稍后...');
        exportBlob(
          `/api${this.templateUrl}?${this.website.tokenHeader}=${getToken()}`
        ).then((res) => {
          downloadXls(res.data, `${this.downLoadFileName}.xlsx`);
        });
      },
      // 下载失败数据
      downFailData() {
        this.$message.warning('数据下载中，请稍后...');
        exportBlob(
          `/api${this.failListUrl}?${this.website.tokenHeader}=${getToken()}`
        ).then((res) => {
          downloadXls(res.data, '导入失败数据.xlsx');
        });
        this.knowBtn();
      },
      // 知道了
      knowBtn() {
        this.visible = false;
        this.excelForm.excelFile = [];
        this.msg = '';
        this.isMsg = false;
        this.failMsg = '';
        this.excelForm.isCovered = 0;
        this.failureNumber = undefined;
      },

      uploadError() {
        console.log('error...');
        this.$message.error(
          '导入失败，请上传 .xls,.xlsx 标准格式文件,且不超过2M'
        );
        this.knowBtn();
      },
      async uploadBefore(file) {
        const formData = new FormData();
        formData.append('file', file);
        //  如果是传感器实例导入，将参数去掉
        let url = this.isInstanceExport
          ? `/api${this.action}?isCovered=0`
          : `/api${this.action}?isCovered=${this.excelForm.isCovered}`;
        await this.$http
          .post(url, formData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          })
          .then(async (res) => {
            let data = res.data;
            await this.uploadAfter(data.data);
            return true;
          })
          .catch(() => {
            this.knowBtn();
            return false;
          });
      },
      uploadAfter(res) {
        if (res) {
          console.log('res................', res);
          //  添加判断 如果是轴承库的导入，
          if (!this.isInstanceExport) {
            let isSuccess = res.detailVOList[0].success;
            if (isSuccess) {
              // 如果失败条数为0 直接关闭，刷新列表，提示导入成功 2. 如果失败条数不为0 展示 成功几条，失败几条， 下载失败模板，和知道了按钮 3. 如果格式不匹配：展示文案，和知道了按钮
              this.failureNumber = Number(res.failureNumber);
              if (this.failureNumber === 0) {
                this.$message.success('导入成功');
                this.knowBtn();
              } else {
                this.isMsg = true;
                this.msg = res.detailVOList[0].message;
                this.excelOption.column = [];
              }
            } else {
              this.isMsg = true;
              this.msg = res.detailVOList[0].message;
              this.excelOption.column = [];
            }
          } else {
            //    下面是传感器实例操作字段
            this.failureNumber = res.firstFailNumber;
            if (this.failureNumber === 0) {
              //证明导入成功
              this.$message.success('导入成功');
              this.knowBtn();
            } else {
              // 存在失败
              this.successNumber = res.successNumber;
              this.msg = `${res.successNumber}条导入成功,从第${res.firstFailNumber}条起失败;`;
              this.failMsg = `失败信息提示：${res.failureMessage}`;
              this.isMsg = true;
              this.excelOption.column = [];
            }
          }
          this.$emit('refresh');
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .footer {
    display: flex;
    justify-content: flex-end;
  }

  .msg {
    padding-bottom: 30px;
    //line-height: 160px;
    text-align: center;
    height: 90%;
    p {
      text-align: center;
    }
  }

  ::v-deep {
    .el-dialog__body {
      height: 280px;
    }
  }
</style>
