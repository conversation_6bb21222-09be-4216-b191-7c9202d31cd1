<template>
  <dialog-popup
    title="选择设备保养标准"
    ref="dialogs"
    :visible="visible"
    @closed="closed"
    width="80%"
    class="selectSensor"
  >
    <el-row>
      <el-row>
        <el-col :span="24" style="height: 460px; overflow-y: scroll">
          <el-table
            ref="multipleTable"
            v-loading="loading"
            :data="dataSource"
            border
            size="small"
            :key="key"
            row-key="id"
            :headerCellStyle="{ background: '#fafafa' }"
            :reserve-selection="true"
            @select="handleCheckBox"
            @select-all="handleSelectAll"
          >
            <el-table-column
              v-if="!isRadio"
              type="selection"
              width="55"
              align="center"
            >
            </el-table-column>
            <el-table-column
              property="monitorName"
              label="保养部位"
              align="center"
            ></el-table-column>
            <el-table-column
              property="standard"
              label="保养标准"
              align="center"
            ></el-table-column>

            <el-table-column property="method" label="保养方法" align="center">
            </el-table-column>
            <!-- <el-table-column
              property="needConfirmName"
              label="是否需要确认"
              align="center"
            >
            </el-table-column> -->
            <el-table-column
              v-if="isRadio"
              property="statusName"
              label="操作"
              align="center"
            >
              <template v-slot="{ row }">
                <el-button type="text" @click="onOneChoose(row)"
                  >选择</el-button
                >
              </template>
            </el-table-column></el-table
          >
        </el-col>
        <!--        <el-col-->
        <!--          v-if="!isRadio"-->
        <!--          :span="8"-->
        <!--          style="height: 460px; overflow-y: scroll"-->
        <!--        >-->
        <!--          <el-tag-->
        <!--            v-for="tag in allPageSelect"-->
        <!--            :closable="true"-->
        <!--            :key="tag.id"-->
        <!--            :disable-transitions="false"-->
        <!--            @close="handleClose(tag)"-->
        <!--            style="margin-bottom: 5px; margin-left: 10px"-->
        <!--          >-->
        <!--            {{ tag.name }}-->
        <!--          </el-tag>-->
        <!--          <el-empty-->
        <!--            :image-size="100"-->
        <!--            v-if="allPageSelect.length === 0"-->
        <!--            description="暂无选择数据"-->
        <!--          ></el-empty>-->
        <!--        </el-col>-->
      </el-row>
      <el-row>
        <el-col :span="12" style="height: 100%">
          <div
            style="
              display: flex;
              align-items: flex-end;
              justify-content: flex-end;
            "
          >
            <btn type="confirm" @click="confirm" :loading="loading"></btn>
            <btn type="close" @click="closed"></btn>
          </div>
        </el-col>
      </el-row>
    </el-row>
  </dialog-popup>
</template>
<script>
  import { maintainStandardDetail } from '@/api/equiment-full-life-api/maintenance';

  export default {
    name: 'sel-maintain-standard',
    props: {
      //  是点检选择设备还是保养选择设备
      selType: {
        type: String,
        default: ''
      },
      //  有的模块，默认是要传递status的
      status: {
        type: String,
        default: undefined
      },
      //  选择状态列表  主要是在点巡检计划新增选择设备、保养计划选择设备、维修选择设备
      statusList: {
        type: Array,
        default: () => {
          return undefined;
        }
      }
    },
    data() {
      return {
        isRadio: false,
        visible: false,
        loading: false,
        equipmentId: '',
        dataSource: [],
        total: 0,
        searchParams: {
          size: 10,
          current: 1
        },
        key: 0,
        // 所有页上多选的数据之和
        allPageSelect: [],
        originLength: 0 // 已存在的长度
      };
    },
    watch: {},
    methods: {
      onSubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      handleCheckBox(rows, row) {
        if (rows.includes(row)) {
          // 新勾选
          this.$set(row, 'num', 1);
          this.$set(row, 'disabled', false);
          this.allPageSelect.push(row);
        } else {
          // 取消勾选
          this.$set(row, 'disabled', true);
          this.$set(row, 'num', undefined);
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item.id !== row.id
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        if (rows.length) {
          rows.forEach((row) => {
            this.$set(row, 'num', 1);
            this.$set(row, 'disabled', false);
            if (!this.allPageSelect.find((item) => item.id === row.id)) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.dataSource.forEach((row) => {
            this.$set(row, 'num', undefined);
            this.$set(row, 'disabled', true);
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item.id !== row.id
            );
          });
        }
      },

      handleClose(row) {
        this.$set(row, 'num', undefined);
        this.$set(row, 'disabled', true);
        this.$refs.multipleTable.toggleRowSelection(row);
        let index = this.allPageSelect.findIndex((it) => it.id === row.id);
        index !== -1 && this.allPageSelect.splice(index, 1);
      },
      search() {
        this.searchParams.current = 1;
        this.getList();
      },
      show({ originList = [], isRadio, equipmentId }) {
        this.equipmentId = equipmentId;
        this.isRadio = isRadio || false;
        this.resetFrom();
        this.allPageSelect = [...originList];
        this.visible = true;
        this.searchParams.current = 1;
        this.getList(); // 部位列表
        this.key++;
      },

      resetFrom() {
        this.searchParams = {
          size: 10,
          current: 1
        };
        this.allPageSelect = [];
      },
      closed() {
        this.visible = false;
        this.allPageSelect = [];
      },
      // 点击clearable时触发
      clear() {
        this.searchParams.current = 1;
        this.getList();
      },
      //
      async getList() {
        this.loading = true;
        try {
          let res = await maintainStandardDetail({
            equipmentId: this.equipmentId
          });
          let data = res.data.data.maintainStandardList || [];
          this.dataSource = data;
          //   .map((item) => {
          //   const mathingObj = this.allPageSelect.find((m) => item.id === m.id);
          //   if (mathingObj) {
          //     return { ...mathingObj, ...item };
          //   } else {
          //     return item;
          //   }
          // });
          this.$nextTick(() => {
            this.dataSource.map((item) => {
              const mathingObj = this.allPageSelect.find(
                (m) => item.id === m.id
              );
              if (mathingObj) {
                this.$refs.multipleTable.toggleRowSelection(item, true);
              }
            });
            this.total = res.data.data.total;
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      confirm() {
        if (this.allPageSelect.length === 0) {
          this.$message.warning('请选择保养标准！');
          return;
        }
        this.$emit('on-confirm', {
          equipmentId: this.equipmentId,
          list: this.allPageSelect
        });
        this.visible = false;
      },
      onOneChoose(row) {
        this.$emit('on-choose', row);
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .pagination-container {
    text-align: left !important;
  }

  .clear {
    float: right;
  }

  /deep/ .el-pagination__jump {
    display: none !important;
  }

  .right {
    margin-left: 20px;
  }

  /deep/ {
    .el-form-item--small {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      padding: 10px 0;
    }
  }

  .table {
    :deep(.el-table__cell) {
      padding: 8px 0 !important;
    }
  }

  .selectSensor {
    :deep(.el-dialog__body) {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  ::v-deep {
    .el-input-number--mini {
      width: 100px !important;
    }
  }
</style>
