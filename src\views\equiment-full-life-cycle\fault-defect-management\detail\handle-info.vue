<template>
  <div>
    <section style="margin-bottom: 15px">
      <span style="margin: 0 5px 0 27px; color: red"> *</span>
      <span style="margin: 0 15px 0 0; color: #606266; font-size: 14px"
        >处理方式:</span
      >
      <el-radio-group v-model="type" @input="changeType">
        <el-radio :label="1">处理</el-radio>
        <el-radio :label="2">维修上报</el-radio>
      </el-radio-group>
    </section>

    <el-form
      class="_form"
      ref="baseForm"
      :model="form"
      size="small"
      :inline="true"
      label-suffix="："
      :rules="rules"
      label-width="120px"
    >
      <el-row v-if="type === 2">
        <el-col :span="8"
          ><el-form-item label="故障缺陷名称" prop="faultName">
            <el-input
              v-model.trim="form.faultName"
              size="small"
              placeholder="请输入故障缺陷名称"
              style="margin-right: 10px"
              :maxlength="50"
              clearable
            >
            </el-input> </el-form-item
        ></el-col>
        <el-col :span="8"
          ><el-form-item label="故障缺陷类型" prop="repairType">
            <el-select
              v-model="form.repairType"
              placeholder="请选择故障缺陷类型"
              clearable
            >
              <el-option
                v-for="dict in serviceDicts.type['repair_type']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <!-- <el-col :span="12">
          <el-form-item label="部位">
            {{ locationPath }}
          </el-form-item></el-col
        > -->
        <el-col :span="8" v-if="Number(form.repairType) === 1">
          <el-form-item label="异常等级" prop="faultLevel">
            <el-select
              v-model="form.faultLevel"
              placeholder="请选择异常等级"
              clearable
            >
              <el-option
                v-for="dict in serviceDicts.type['defect_level']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="24">
          <el-form-item label="异常描述" prop="problemComment">
            <el-input
              v-model="form.problemComment"
              placeholder="请输入异常描述"
              type="textarea"
            >
            </el-input> </el-form-item
        ></el-col>
      </el-row>
      <el-row class="add-info" v-if="type === 2">
        <el-col :span="24">
          <el-form-item prop="image" label="补充图片">
            <upload-img
              v-model="form.image"
              placeholder="上传图片"
              :limit="9"
              formatLimit="jpeg,png,jpg"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!--       处理form-->
      <el-row v-if="type === 1">
        <el-col :span="12">
          <el-form-item label="处理结果" prop="result">
            <el-select
              v-model="form.result"
              placeholder="请选择处理结果"
              clearable
            >
              <el-option
                v-for="dict in serviceDicts.type['fault_handle_result']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="operateRemark">
            <el-input
              v-model="form.operateRemark"
              placeholder="请输入描述"
              type="textarea"
            >
            </el-input> </el-form-item
        ></el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  import UploadImg from '@/components/uploadImage.vue';
  import { getUserInfo } from '@/api/system/user';

  export default {
    name: 'alarmSearch',
    components: { UploadImg },
    serviceDicts: ['defect_level', 'repair_type', 'fault_handle_result'],
    watch: {
      detail: {
        handler(val) {
          if (val && val.abnormalComment) {
            this.form.problemComment = val.abnormalComment;
            this.originalComment = val.abnormalComment;
          }
        },
        deep: true
      }
    },
    props: {
      locationPath: {
        type: String,
        default: () => '-'
      },
      detail: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        type: 2, // 查看是什么类型 如何处理
        originalComment: '', // 初始异常描述
        form: {
          image: [], // zhaopian
          result: undefined, // 处理结果
          faultName: undefined, // 故障名称
          repairType: undefined, // 维修类型
          faultLevel: undefined, // 故障等级
          problemComment: undefined, // 描述 - 描述
          operateRemark: undefined //  备注
        },
        rules: {
          result: [
            { required: true, message: '请选择处理结果', trigger: 'change' }
          ],
          faultName: [
            { required: true, message: '请输入故障缺陷名称', trigger: 'blur' }
          ],
          repairType: [
            { required: true, message: '请选择故障缺陷类型', trigger: 'change' }
          ],
          faultLevel: [
            { required: true, message: '请选择等级', trigger: 'change' }
          ],
          problemComment: [
            { required: true, message: '请输入描述', trigger: 'blur' }
          ]
        }
      };
    },
    methods: {
      changeType() {
        this.$refs['baseForm'] && this.$refs['baseForm'].resetFields();
        this.$nextTick(() => {
          this.form.image = [];
          this.form.problemComment = this.originalComment;
        });
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          let attachId;
          if (this.form.image.length > 0) {
            attachId = this.form.image
              .map((it) => {
                return it.id;
              })
              .join(',');
          }
          let params;
          if (this.type === 2) {
            // 获取用户手机号
            const { data } = await getUserInfo();
            this.form.tel = data.data.phone;
            params = {
              repair: {
                ...this.form,
                monitorId: this.detail.monitorId,
                monitorName: this.detail.monitorName,
                attachId: attachId
              }
            };
            delete params.repair.image;
          } else {
            params = {
              ...this.form,
              monitorId: this.detail.monitorId,
              monitorName: this.detail.monitorName,
              attachId: attachId
            };
            delete params.image;
          }
          return params;
        } else {
          return false;
        }
      },
      resetForm() {
        this.type = 2;
        this.$refs['baseForm'] && this.$refs['baseForm'].resetFields();
        this.form = {
          image: [], // 补充图片
          result: undefined, // 处理结果
          faultName: undefined, // 故障名称
          repairType: undefined, // 维修类型
          faultLevel: undefined, // 故障等级
          problemComment: undefined,
          remark: undefined //  描述
        };
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep {
    .el-form._form {
      margin-bottom: 15px;

      .el-form-item,
      .el-select {
        width: 100%;
      }

      .el-form-item__content {
        width: calc(100% - 120px);
      }
    }
  }
</style>
