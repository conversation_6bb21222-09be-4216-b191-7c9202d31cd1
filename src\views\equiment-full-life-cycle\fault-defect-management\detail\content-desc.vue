<template>
  <div>
    <section>
      <article>
        {{ details.remark || '-' }}
      </article>
      <!-- 缺陷图片-->
      <section v-if="details.attachList">
        <p>缺陷图片：</p>
        <el-image
          v-for="item in details.attachList"
          :key="item.id"
          style="width: 100px; height: 100px; margin-right: 20px"
          :src="getFileFullUrl(item.id)"
          :preview-src-list="[getFileFullUrl(item.id)]"
        >
        </el-image>
      </section>
    </section>
  </div>
</template>

<script>
  import Img from '@/views/equiment-full-life-cycle/asset/images/3.png';
  import { getFileFullUrl } from '@/util/file';
  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      details: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },
    data() {
      return { Img };
    },
    mounted() {},
    methods: { getFileFullUrl }
  };
</script>

<style lang="scss" scoped></style>
