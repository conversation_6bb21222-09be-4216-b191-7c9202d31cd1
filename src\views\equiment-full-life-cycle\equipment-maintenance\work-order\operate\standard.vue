<template>
  <el-form
    :model="form"
    inline
    label-suffix="："
    ref="baseForm"
    label-width="120px"
    label-position="left"
    size="small"
  >
    <section
      class="container"
      v-for="(item, index) in form.list"
      :key="item.id"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="24">
          <el-form-item
            label-width="85px"
            label="检查结果"
            :prop="'list.' + index + '.isAbnormal'"
            :rules="{
              required: true,
              message: '请选择检查结果',
              trigger: 'change'
            }"
          >
            <el-radio-group v-model="item.isAbnormal">
              <el-radio :label="0">正常</el-radio>
              <el-radio :label="1">异常</el-radio>
            </el-radio-group></el-form-item
          >
        </el-col>
        <el-col :span="24">
          <el-form-item
            label-width="85px"
            style="margin-left: 10px"
            label="检查部位"
          >
            {{ item.monitorName || '-' }}</el-form-item
          >
        </el-col>
        <el-col :span="24">
          <el-form-item
            label-width="85px"
            style="margin-left: 10px"
            label="保养标准"
          >
            {{ item.standard || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label-width="85px"
            style="margin-left: 10px"
            label="保养方法"
          >
            {{ item.method || '-' }}
          </el-form-item>
        </el-col>
      </el-row>
      <!--       正常不展示，异常展示下面的内容-->
      <section
        v-if="item.isAbnormal === 1"
        style="
          padding: 15px;
          margin-left: 10px;
          border-left: 1px dashed #409eff;
        "
      >
        <el-row class="add-info" :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="异常描述"
              :prop="'list.' + index + '.abnormalComment'"
              :rules="{
                required: true,
                message: '请输入异常描述',
                trigger: 'blur'
              }"
            >
              <el-input
                v-model="item.abnormalComment"
                type="textarea"
                placeholder="请输入异常描述"
                clearable
                show-word-limit
                :maxlength="200"
                autosize
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="异常等级"
              :prop="'list.' + index + '.abnormalLevel'"
              :rules="{
                required: true,
                message: '请选择异常等级',
                trigger: 'change'
              }"
            >
              <el-select
                v-model="item.abnormalLevel"
                filterable
                placeholder="请选择异常等级"
                clearable
              >
                <el-option
                  v-for="dict in serviceDicts.type['defect_level']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="是否现场处理"
              :prop="'list.' + index + '.isHandled'"
              :rules="{
                required: true,
                message: '请选择是否现场处理',
                trigger: ['change']
              }"
            >
              <el-select
                v-model="item.isHandled"
                filterable
                placeholder="请选择是否现场处理"
                clearable
              >
                <el-option
                  v-for="dict in systemDicts.type['yes_no']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :prop="'list.' + index + '.attachList'"
              label="异常图片"
              style="padding-left: 10px"
              label-width="110px"
            >
              <upload-img
                v-model="item.attachList"
                placeholder="上传图片"
                :limit="3"
                formatLimit="jpeg,png,jpg"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </section>
    </section>
  </el-form>
</template>

<script>
  import OperateClasses from '@/views/equiment-full-life-cycle/equipment-inspection/plan/operate/operating-classes.vue';
  import UploadImg from '@/components/uploadImage.vue';

  export default {
    name: 'DeviceBasicList',
    components: { OperateClasses, UploadImg },
    serviceDicts: ['defect_level'],
    systemDicts: ['yes_no'],
    props: {
      detail: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            let standardList = this.detail.standardList;
            this.form.list = standardList.map((item) => {
              return {
                ...item,
                isAbnormal: 0
              };
            });
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        form: {
          list: []
        }
      };
    },

    mounted() {},
    beforeDestroy() {},

    methods: {
      //  获取参数
      async validForm() {
        //   先校验
        let bool = await this.$refs.baseForm.validate();
        if (bool) {
          let par = this.form.list.map((item) => {
            return {
              abnormalComment: item.abnormalComment,
              abnormalImage:
                item.attachList &&
                item.attachList.map((attach) => attach.id).join(','),
              abnormalLevel: item.abnormalLevel,
              isAbnormal: item.isAbnormal,
              isHandled: item.isHandled,
              monitorId: item.monitorId,
              standardId: item.id
            };
          });
          return par;
        } else {
          return false;
        }
      },
      resetForm() {
        this.form.list = [];
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep {
    .el-form-item,
    .el-select {
      width: 100%;
    }
    .el-form-item__content {
      width: calc(100% - 120px);
    }
    .el-textarea .el-input__count {
      background: unset !important;
    }
    .avatar-uploader-icon {
      width: 100px;
      height: 100px;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }
  .container {
    display: grid;
    grid-template-columns: 30% 70%;
    border: 1px dashed #409eff;
    margin-bottom: 20px;
    padding: 20px;
    border-left: 4px solid #409eff; //#1586ef;
    border-radius: 4px;
  }

  .el-col {
    margin-bottom: 0 !important;
  }
</style>
