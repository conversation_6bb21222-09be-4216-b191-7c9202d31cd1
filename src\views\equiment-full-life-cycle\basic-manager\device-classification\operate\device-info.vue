<template>
  <dialog-drawer
    title="部位配置"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <section>
        <span class="el-base-title">基础信息</span>
        <device-detail-base :details="details"></device-detail-base>
      </section>
      <!-- 部位信息 -->
      <span class="el-base-title">部位信息</span>
      <add-position
        ref="addPosition"
        :id="eqId"
        :monitorList="monitorList"
        @success="addSuccess"
      ></add-position>
    </div>
    <div class="oper_btn">
      <btn type="save" @click="submit" :loading="loading"></btn>
      <btn type="close" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>
<script>
  import AddPosition from './add-position';
  import DeviceDetailBase from './device-detail-base.vue'; // 设备基本信息
  import {
    getMonitorListApi,
    submitMonitorApi
  } from '@/api/equiment-full-life-api/equipment-account';
  import { getAccountDetail } from '@/api/equiment-full-life-api/ledger';

  export default {
    name: 'RepairViewIndex',
    components: {
      DeviceDetailBase,
      AddPosition
    },
    data() {
      return {
        eqId: '',
        visible: false,
        loading: false,
        details: {}, // 详情数据
        monitorList: [] // 设备部位
      };
    },
    methods: {
      async submit() {
        try {
          const monitorList = await this.$refs.addPosition.validForm();
          if (monitorList) {
            this.loading = true;
            console.log('monitorList', monitorList);
            const res = await submitMonitorApi({
              id: this.eqId,
              monitorList
            });
            this.$message.success('操作成功');
            this.loading = false;
            this.visible = false;
          }
        } catch (e) {
          this.loading = false;
        }
      },
      closed() {
        this.details = {};
        this.monitorList = [];
        // this.$refs.addPosition.resetForm();
        this.visible = false;
      },

      addSuccess() {
        this.getMonitorList();
        // 刷新左侧树
        this.$emit('refreshTree');
      },
      add() {
        this.$refs.addPosition.show(this.monitorList);
      },
      // 点击展示
      async show(id) {
        if (id) {
          this.visible = true;
          this.eqId = id;
          await this.getDetail(id);
          await this.getMonitorList(id);
        }
      },
      // 获取部位详情 monitorDetail
      async getMonitorList(id) {
        try {
          this.loading = true;
          const res = await getMonitorListApi({ equipmentId: id });
          this.monitorList = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 获取详情接口
      async getDetail(id) {
        try {
          this.loading = true;
          const res = await getAccountDetail({ id });
          // console.log('设备信息', res);
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }
</style>
