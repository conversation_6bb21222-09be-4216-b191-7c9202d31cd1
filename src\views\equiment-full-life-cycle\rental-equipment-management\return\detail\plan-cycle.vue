<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="120px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="出租方">
            {{ initData?.lessorVo?.lessorName || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系人">{{
            initData?.lessorVo?.contactPerson || '-'
          }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="联系电话">{{
            initData?.lessorVo?.contactPhone || '-'
          }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="租赁日期">{{
            initData?.lessorVo?.leaseStartDate
              ? `${initData?.lessorVo?.leaseStartDate} 至 ${initData?.lessorVo?.leaseEndDate}`
              : '-'
          }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="实际租赁日期" prop="time">
            <el-date-picker
              v-model="form.time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              clearable
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="归还人" prop="backPersonIdName">
            <el-input
              v-model="form.backPersonIdName"
              readonly
              placeholder="请选择归还人"
              @focus.prevent="onChooseUser"
            >
              <template slot="append">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      form.backPersonId = undefined;
                      form.backPersonIdName = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
    <!--     选择人员-->
    <recipient-dialog
      ref="recipient"
      @selected="onUserSelect"
    ></recipient-dialog>
  </div>
</template>

<script>
  import { getMidnight } from '../../../equipment-inspection/util';
  import DeptDialog from '@/components/dept-dialog/index.vue';
  import { getUserListByDeptId } from '@/api/equiment-full-life-api/common';
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import { mapGetters } from 'vuex';
  export default {
    name: 'AddDeviceInfo',
    components: { DeptDialog, RecipientDialog },
    props: {
      initData: {
        type: Object,
        default: () => {}
      }
    },

    data() {
      return {
        executeUserList: [], // 责任人
        userLoading: false,
        form: {
          time: undefined,
          receiveEndDate: undefined,
          receiveStartDate: undefined,
          backPersonId: undefined,
          backPersonIdName: undefined,
          demand: undefined,
          additionalRemarks: undefined
        },
        edit: false,
        rules: {
          time: [
            {
              required: true,
              message: '请选择计划开始时间',
              trigger: ['change']
            }
          ],
          backPersonIdName: [
            {
              required: true,
              message: '请选择申请人',
              trigger: ['change']
            }
          ],
          chargeDeptName: [
            {
              required: true,
              message: '请选择润滑部门',
              trigger: ['change']
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    created() {},
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods: {
      //  选择人员
      onChooseUser() {
        this.$refs['recipient'].show();
      },
      onUserSelect(user) {
        this.form.backPersonId = user.id;
        this.form.backPersonIdName = user.realName;
      },
      // 选择人员
      async getUser(deptId) {
        this.userLoading = true;
        try {
          let params = {
            // alias: 'lubricate_user',
            deptId: deptId
          };

          let res = await getUserListByDeptId(params);
          this.executeUserList = res.data.data;

          this.userLoading = false;
        } catch ({ message }) {
          this.userLoading = false;
          console.log(message);
        }
      },
      // change
      clearEndTime(val) {
        if (
          val &&
          this.form.endTime &&
          getMidnight(val) >= getMidnight(this.form.endTime)
        ) {
          this.form.endTime = val;
        } else {
          this.form.endTime = undefined;
        }

        // 更新 picker-options 的 disabledDate 函数
        this.endOptions.disabledDate = this.handleEndDateDisabled;
        this.$emit('changeTime', {
          startTime: val,
          endTime: this.form.endTime
        });
      },
      //  结束时间改变
      endTime(val) {
        this.$emit('changeTime', {
          startTime: this.form.startTime,
          endTime: val
        });
      },
      handleEndDateDisabled(date) {
        // 结束时间不能小于开始时间
        return (
          getMidnight(date) < getMidnight(this.form.startTime) ||
          getMidnight(date) <= getMidnight(new Date())
        );
      },

      // 选择部门
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.chargeDept = dept.id;
        this.form.chargeDeptName = dept.deptName;
        this.form.chargeUser = undefined;
        this.getUser(dept.id);
      },
      setData(initData) {
        if (initData.chargeDept) {
          this.getUser(initData.chargeDept);
        }
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
        this.$emit('changeTime', {
          startTime: this.form.startTime,
          endTime: this.form.endTime
        });
      },

      select(data) {
        this.form.name = data.name;
        this.form.id = data.id;
      },

      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return this.form;
        } else {
          return false;
        }
      },
      resetForm() {
        this.executeUserList = [];
        this.$refs['baseForm'].resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-input-number {
        width: 100%;

        .el-input__inner {
          text-align: left;
        }
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
