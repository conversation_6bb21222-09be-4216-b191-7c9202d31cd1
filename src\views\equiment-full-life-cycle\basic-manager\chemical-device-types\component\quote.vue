<template>
  <el-dialog
    title="分类级别选择"
    :visible.sync="visible"
    width="50%"
    @closed="closed"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <h3>请根据企业管理需求设置设备分类级别</h3>
    <el-radio-group v-model="radio" :loading="loading">
      <el-radio :label="it.level" v-for="it in data" :key="it.id"
        >{{ it.name }}
      </el-radio>
    </el-radio-group>
    <span slot="footer" class="dialog-footer">
      <span style="font-size: 12px"
        >提示：设备分类引用后，会覆盖以前设备分类，请慎重选择。</span
      >
      <span>
        <el-button size="small" :loading="loading" @click="closed"
          >取 消</el-button
        >
        <el-button
          type="primary"
          :loading="loading"
          size="small"
          @click="confirm"
          >确 定</el-button
        >
      </span>
    </span>
  </el-dialog>
</template>

<script>
  import {
    getDeviceTypeReference,
    getDeviceTypeTemplatePageList
  } from '@/api/equiment-full-life-api/device-type';

  export default {
    components: {},
    props: {},
    data() {
      return {
        radio: 3,
        visible: false,
        data: [],
        loading: false,
        temId: undefined
      };
    },
    watch: {},
    methods: {
      async getList() {
        try {
          let res = await getDeviceTypeTemplatePageList({
            parentId: this.temId,
            size: -1
          });
          this.data = res.data.data.records || [];
        } catch (e) {
          console.log(e);
        }
      },
      async save() {
        this.loading = true;
        try {
          await getDeviceTypeReference({
            templateId: this.temId,
            level: this.radio
          });
          this.$message.success('引用成功，请前往设备类型管理页面查看');
          this.visible = false;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      show(id) {
        console.log(id);
        this.temId = id;
        this.visible = true;
        this.getList();
      },
      confirm() {
        console.log('选中的值', this.radio);
        this.save();
      },
      closed() {
        this.radio = 3;
        this.visible = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  .el-radio {
    padding: 10px;
  }

  ::v-deep {
    .el-dialog__body {
      padding: 10px;
    }

    .el-radio {
      display: block;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
  }
</style>
