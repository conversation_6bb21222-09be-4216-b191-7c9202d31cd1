<template>
  <div class="alarmTrend">
    <span class="title">
      <section>
        <i class="wel_icon iconfont el-icon-ud-biaotitubiao2"></i>
        <span style="color: #3d446e"> 工单完成情况</span>
      </section>

      <search @changeTime="changeTime"></search>
    </span>
    <span class="all">工单总数：{{ total }}</span>
    <div id="pie" v-if="flag" v-loading="loading"></div>
    <custom-empty :size="70" v-else></custom-empty>
  </div>
</template>
<script>
  import { getStatistics } from '@/api/equiment-full-life-api/maintenance';
  import * as echarts from 'echarts/core';
  import {
    TooltipComponent,
    LegendComponent,
    GridComponent,
    TitleComponent
  } from 'echarts/components';
  import { PieChart } from 'echarts/charts';
  import { CanvasRenderer } from 'echarts/renderers';
  import CustomEmpty from '@/components/custom-empty.vue';
  import { markRaw } from 'vue';
  import Search from './search.vue';
  echarts.use([
    TooltipComponent,
    TitleComponent,
    Pie<PERSON><PERSON>,
    CanvasRenderer,
    LegendComponent,
    GridComponent
  ]);

  export default {
    name: 'alarmTrend',
    props: {},
    components: { Search, CustomEmpty },

    data() {
      return {
        inspectOrders: [],
        loading: false,
        flag: true,
        total: 0
      };
    },
    mounted() {
      this.$nextTick(async () => {
        await this.getInspectOrders(0);
      });
      window.addEventListener('resize', this.resizeChart);
    },
    unmounted() {
      window.removeEventListener('resize', this.resizeChart);
    },
    methods: {
      changeTime(e) {
        this.getInspectOrders(e);
      },
      //  设备分类统计
      async getInspectOrders(day) {
        this.loading = true;
        try {
          const res = await getStatistics({
            queryDate: day
          });
          if (res.data.data.maintainOrders) {
            this.flag = true;
            this.inspectOrders = res.data.data.maintainOrders;
            this.total = this.inspectOrders.reduce(
              (accumulator, currentValue) => {
                return accumulator + currentValue.value;
              },
              0
            );
          } else {
            this.inspectOrders = [];
            this.flag = false;
          }
          await this.init();
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },

      resizeChart() {
        this.status_chart &&
          this.status_chart.resize({ width: 'auto', height: 'auto' });
      },
      init() {
        let chartDom = document.getElementById('pie');
        this.status_chart = markRaw(echarts.init(chartDom));

        let option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          color: ['#03D6EF', '#1276FF', '#F83818', '#7114FF', '#FFC53D'],

          legend: {
            orient: 'vertical',
            bottom: '10%',
            right: '5%',
            itemGap: 12,
            itemWidth: 8,
            itemHeight: 8,
            // 使用回调函数
            formatter: function (name) {
              let data = option.series[0].data;
              let total = 0;
              let tarValue;
              for (let i = 0, l = data.length; i < l; i++) {
                total += data[i].value;
                if (data[i].name == name) {
                  tarValue = data[i].value;
                }
              }
              let p = ((tarValue / total) * 100).toFixed(2);
              return (
                '{a|' + name + '}' + '  {b|' + tarValue + '  }' + ' ' + p + '%'
              );
            },
            textStyle: {
              fontSize: 12,
              color: '#3d446e',
              rich: {
                a: {
                  color: '#3d446e',
                  width: 40
                },
                b: {
                  width: 50
                }
              }
            }
          },
          series: [
            {
              name: '工单完成情况',
              type: 'pie',
              radius: ['30%', '70%'],
              center: ['30%', '60%'],
              label: { show: false },
              selectedOffset: 30,
              selectedMode: true,
              itemStyle: {
                borderRadius: 8
              },
              labelLine: {
                show: false
              },
              data: this.inspectOrders.map((it) => {
                return {
                  name: it.key,
                  value: it.value
                };
              }),
              roundCap: 1 //可选项为1和2，不填则采用原有拼接方式
            }
          ]
        };
        this.status_chart && this.status_chart.setOption(option);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .alarmTrend {
    width: 100%;
    height: 100%;
    padding: 15px;
    background: var(--el-bg-color-overlay);
    border-radius: 5px;
  }

  #pie {
    width: 100%;
    height: calc(100% - 50px);
  }

  .title {
    display: flex;
    justify-content: space-between;

    section {
      width: calc(100% - 220px);
      overflow: hidden;
      text-wrap: nowrap;
      text-overflow: ellipsis;
    }

    em {
      display: inline-block;
      color: var(--el-text-color-primary);
      font-size: 14px;
      font-style: normal;
      line-height: 10%;
    }
  }

  ::v-deep {
    .el-tabs__nav-wrap::after {
      background-color: unset;
    }

    .el-tabs__nav-wrap {
      top: -20px;
    }
  }
  .all {
    float: right;
    margin-top: 10px;
    font-size: 14px;
    font-weight: 600;
    color: #3d446e;
  }
</style>
