<template>
  <div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      border
      :header-cell-style="{ background: '#fafafa' }"
      size="small"
      height="450px"
      stripe
    >
      <el-table-column type="index" label="#"></el-table-column>
      <el-table-column prop="dictNo" label="备品备件编号" show-overflow-tooltip>
      </el-table-column>

      <el-table-column
        prop="dictName"
        label="备品备件名称"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.dictName || '-' }}</template>
      </el-table-column>
      <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.model || '-' }}</template>
      </el-table-column>
      <el-table-column prop="resultName" label="盘点状态" show-overflow-tooltip>
        <template v-slot="{ row }">{{ row.resultName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="warehouseName"
        label="库房名称"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.warehouseName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="beforeSystemStock"
        label="当前库存"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.beforeSystemStock || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="inventoryUserName"
        label="盘点人员"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.inventoryUserName || '-' }}</template>
      </el-table-column>

      <el-table-column
        prop="afterCountedStock"
        label="盘点数量"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.afterCountedStock || '-' }}</template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  export default {
    name: 'DeviceBasicList',
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    watch: {},
    data() {
      return {
        loading: false
      };
    },

    mounted() {},
    methods: {}
  };
</script>

<style scoped lang="scss"></style>
