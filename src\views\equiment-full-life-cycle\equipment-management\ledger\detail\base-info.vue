<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="SN编号：">{{
        details.sn || '-'
      }}</el-descriptions-item>
      <el-descriptions-item
        label="设备名称："
        :labelStyle="{ width: '152px', textAlign: 'right' }"
        >{{ details.name || '-' }}</el-descriptions-item
      >
      <el-descriptions-item label="规格型号：">{{
        details.model || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="设备类型：">{{
        details.categoryName || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="设备等级：">{{
        details.importantLevelName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="特种设备类型：">{{
        details.specialTypeName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="特种设备检查周期(天)：">{{
        details.specialInspectPeriod || '-'
      }}</el-descriptions-item>
      <!--      <el-descriptions-item label="是否配置RFID：">{{-->
      <!--        details.rfid ? '是' : '否'-->
      <!--      }}</el-descriptions-item>-->
      <!--      <el-descriptions-item label="RFID卡号：">{{-->
      <!--        details.rfid || '-'-->
      <!--      }}</el-descriptions-item>-->

      <el-descriptions-item label="计量单位："
        >{{ details.measureUnitName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="设备状态："
        >{{ details.statusName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="使用部门：">{{
        details.useDeptName || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="使用人员：">
        {{ details.userName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="购买日期：">
        {{ details.purchaseDate || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="投产日期：">
        {{ details.productDate || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="设备位置：">
        {{ details.locationPath || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="使用状态：">
        {{ details.statusName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="生产厂家：">
        {{ details.supplier || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="联系人员：">
        {{ details.contact || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="联系电话：">
        {{ details.tel || '-' }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
