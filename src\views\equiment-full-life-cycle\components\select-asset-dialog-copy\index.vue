<template>
  <dialog-popup
    title="选择设备"
    ref="dialogs"
    :visible="visible"
    @closed="closed"
    width="80%"
    class="selectSensor"
  >
    <search ref="search" @search="onSubmit" :selType="selType"></search>
    <el-row>
      <el-row>
        <el-col
          :span="isRadio ? 24 : 16"
          style="height: 460px; overflow-y: scroll"
        >
          <el-table
            ref="multipleTable"
            v-loading="loading"
            :data="dataSource"
            border
            size="small"
            :key="key"
            row-key="id"
            :headerCellStyle="{ background: '#fafafa' }"
            :reserve-selection="true"
            @select="handleCheckBox"
            @select-all="handleSelectAll"
          >
            <el-table-column
              v-if="!isRadio"
              type="selection"
              width="55"
              align="center"
              :selectable="isCheck"
            >
            </el-table-column>
            <el-table-column
              property=""
              label="设备编号"
              align="center"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              property="sn"
              label="设备SN"
              align="center"
              show-overflow-tooltip
            ></el-table-column>
            <!--            <el-table-column-->
            <!--              property="code"-->
            <!--              label="设备编号"-->
            <!--              align="center"-->
            <!--              show-overflow-tooltip-->
            <!--            ></el-table-column>-->
            <el-table-column
              property="name"
              label="设备名称"
              align="center"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              property="standard"
              label="是否添加标准"
              align="center"
              show-overflow-tooltip
            >
              <template v-slot="{ row }"
                ><span style="color: red">{{
                  row.standard || '-'
                }}</span></template
              >
            </el-table-column>
            <el-table-column
              property="model"
              label="设备型号"
              align="center"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.model || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              property="measureUnitName"
              label="计量单位"
              align="center"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.measureUnitName || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              property="useDeptName"
              label="使用部门"
              align="center"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.useDeptName || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              property="userName"
              label="使用人"
              align="center"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.userName || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              property="locationPath"
              label="存放地点"
              align="center"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                {{ row.locationPath || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              property="statusName"
              label="设备状态"
              align="center"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">
                <i
                  :style="`color:${equipmentStatusColor(
                    row.status
                  )};font-size:18px`"
                  >●</i
                >
                {{ row.statusName || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              v-if="isRadio"
              property="statusName"
              label="操作"
              align="center"
            >
              <template v-slot="{ row }">
                <el-button type="text" @click="onOneChoose(row)"
                  >选择</el-button
                >
              </template>
            </el-table-column></el-table
          >
        </el-col>
        <el-col
          v-if="!isRadio"
          :span="8"
          style="height: 460px; overflow-y: scroll"
        >
          <el-tag
            v-for="tag in allPageSelect"
            :closable="true"
            :key="tag.id"
            :disable-transitions="false"
            @close="handleClose(tag)"
            style="margin-bottom: 5px; margin-left: 10px"
          >
            {{ tag.name }}
          </el-tag>
          <el-empty
            :image-size="100"
            v-if="allPageSelect.length === 0"
            description="暂无选择数据"
          ></el-empty>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" style="height: 100%">
          <pagination
            :page-size.sync="searchParams.size"
            :page-no.sync="searchParams.current"
            :total="total"
            @pagination="getList"
          />
        </el-col>
        <el-col :span="12" style="height: 100%">
          <div
            style="
              display: flex;
              align-items: flex-end;
              justify-content: flex-end;
            "
          >
            <btn type="confirm" @click="confirm" :loading="loading"></btn>
            <btn type="close" @click="closed"></btn>
          </div>
        </el-col>
      </el-row>
    </el-row>
  </dialog-popup>
</template>
<script>
  import { accountListPage } from '@/api/equiment-full-life-api/ledger';
  import Search from '@/views/equiment-full-life-cycle/components/select-asset-dialog/search.vue';

  export default {
    components: { Search },
    props: {
      //  是点检选择设备还是保养选择设备
      selType: {
        type: String,
        default: ''
      },
      nqStatus: {
        type: String,
        default: undefined
      }, // 不要什么状态
      //  有的模块，默认是要传递status的
      status: {
        type: String,
        default: undefined
      },
      //  选择状态列表  主要是在点巡检计划新增选择设备、保养计划选择设备、维修选择设备
      statusList: {
        type: Array,
        default: () => {
          return undefined;
        }
      }
    },
    data() {
      return {
        isRadio: false,
        visible: false,
        loading: false,
        dataSource: [],
        total: 0,
        searchParams: {
          size: 10,
          current: 1
        },
        key: 0,
        // 所有页上多选的数据之和
        allPageSelect: [],
        originLength: 0 // 已存在的长度
      };
    },
    watch: {},
    methods: {
      // 设备台账 设备状态状态颜色
      equipmentStatusColor(val) {
        let status = Number(val);
        switch (status) {
          case 2:
            return '#35C24B'; // 在用
          case 1:
            return '#155CFF'; // 备用
          case 3:
            return '#EE7C11'; // 维修
          case 4:
            return '#E23F3F'; // 报废
        }
      },
      isCheck(row) {
        if (this.selType === 'maintain') {
          this.$set(row, 'standard', row.canMaintainSelect ? '-' : '无标准');
          return row.canMaintainSelect; // 保养
        } else if (this.selType === 'inspect') {
          this.$set(row, 'standard', row.canSelect ? '-' : '无标准');
          return row.canSelect; // 点检
        } else if (this.selType === 'oiling') {
          this.$set(row, 'standard', row.canLubricateSelect ? '-' : '无标准');
          return row.canLubricateSelect; // 润滑
        } else if (this.selType === 'scrap') {
          // 如果是报废
          return row.status !== 4;
        } else if (this.selType === 'overhaul') {
          this.$set(row, 'standard', row.canOverhaulSelect ? '-' : '无标准');
          return row.canOverhaulSelect; // 检修模块
        } else {
          // 如果是其他类型，根据搜索条件来判断
          return true;
        }
      },
      onSubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      getKey() {
        return Date.now() + '';
      },
      handleCheckBox(rows, row) {
        if (rows.includes(row)) {
          // 新勾选
          this.$set(row, 'num', 1);
          this.$set(row, 'disabled', false);
          this.allPageSelect.push(row);
        } else {
          // 取消勾选
          this.$set(row, 'disabled', true);
          this.$set(row, 'num', undefined);
          this.allPageSelect = this.allPageSelect.filter(
            (item) => item.id !== row.id
          );
        }
      },
      //该方法是当页全选的方法
      handleSelectAll(rows) {
        if (rows.length) {
          rows.forEach((row) => {
            this.$set(row, 'num', 1);
            this.$set(row, 'disabled', false);
            if (!this.allPageSelect.find((item) => item.id === row.id)) {
              this.allPageSelect.push(row);
            }
          });
        } else {
          this.dataSource.forEach((row) => {
            this.$set(row, 'num', undefined);
            this.$set(row, 'disabled', true);
            this.allPageSelect = this.allPageSelect.filter(
              (item) => item.id !== row.id
            );
          });
        }
      },

      handleClose(row) {
        this.$set(row, 'num', undefined);
        this.$set(row, 'disabled', true);
        let obj = this.dataSource.find((it) => {
          return it.id === row.id;
        });
        this.$refs.multipleTable.toggleRowSelection(obj);
        let index = this.allPageSelect.findIndex((it) => it.id === row.id);
        index !== -1 && this.allPageSelect.splice(index, 1);
      },
      search() {
        this.searchParams.current = 1;
        this.getList();
      },
      show(originList, isRadio) {
        this.isRadio = isRadio || false;
        this.resetFrom();
        this.allPageSelect = originList;
        this.visible = true;
        this.searchParams.current = 1;
        this.getList(); // 部位列表
        this.key++;
      },

      resetFrom() {
        this.searchParams = {
          size: 10,
          current: 1
        };
        this.allPageSelect = [];
      },
      closed() {
        this.visible = false;
        this.allPageSelect = [];
      },
      // 点击clearable时触发
      clear() {
        this.searchParams.current = 1;
        this.getList();
      },
      //
      async getList() {
        this.loading = true;
        try {
          let params;
          params = {
            ...this.searchParams,
            filterStatus: this.statusList
              ? this.statusList.join(',')
              : undefined
          };
          console.log(params, this.status);
          // if (this.status) {
          //   params.status = this.status;
          // }

          let res = await accountListPage({
            ...params,
            nqStatus: this.nqStatus
          });
          let data = res.data.data.records || [];
          this.dataSource = data.map((item) => {
            const mathingObj = this.allPageSelect.find((m) => item.id === m.id);
            if (mathingObj) {
              return { ...mathingObj, ...item };
            } else {
              return item;
            }
          });
          this.$nextTick(() => {
            this.dataSource.map((item) => {
              if (item.num) {
                this.$refs.multipleTable.toggleRowSelection(item, true);
              }
            });
            this.total = res.data.data.total;
          });
          this.loading = false;
        } catch (e) {
          this.loading = false;
          console.log(e);
        }
      },
      confirm() {
        if (this.allPageSelect.length === 0) {
          this.$message.warning('请选择设备！');
          return;
        }
        this.$emit('getAssetList', this.allPageSelect);
        this.visible = false;
      },
      onOneChoose(row) {
        this.$emit('on-choose', row);
        this.visible = false;
      }
    }
  };
</script>
<style lang="scss" scoped>
  /deep/ .el-pagination__sizes {
    display: none !important;
  }

  .search {
    margin-bottom: 10px;
  }

  /deep/ .pagination-container {
    text-align: left !important;
  }

  .clear {
    float: right;
  }

  /deep/ .el-pagination__jump {
    display: none !important;
  }

  .right {
    margin-left: 20px;
  }

  /deep/ {
    .el-form-item--small {
      margin-top: 0 !important;
      margin-bottom: 0 !important;
      padding: 10px 0;
    }
  }

  .table {
    :deep(.el-table__cell) {
      padding: 8px 0 !important;
    }
  }

  .selectSensor {
    :deep(.el-dialog__body) {
      padding-top: 15px;
      padding-bottom: 15px;
    }
  }

  ::v-deep {
    .el-input-number--mini {
      width: 100px !important;
    }
  }
</style>
