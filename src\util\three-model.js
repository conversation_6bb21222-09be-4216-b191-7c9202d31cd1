import '../styles/three-model.css';

import * as THREE from 'three';
// import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import Stats from 'three/examples/jsm/libs/stats.module';
// import { CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer';
import {
  CSS3DObject,
  // CSS3DObject,
  CSS3DRenderer
} from 'three/examples/jsm/renderers/CSS3DRenderer';
// import * as dat from 'dat.gui';
import { Message } from 'element-ui';

import * as echarts from 'echarts';
import { AxesHelper, PointLightHelper } from 'three';
// import {  } from 'three/addons/loaders/FBXLoader';

class ThreeModel {
  static getInstance() {
    return new ThreeModel();
  }

  constructor(containerSelector, option) {
    // 存放对象的数组
    this.meshArr = [];
    this.sensorArr = [];
    this.width = option.width;
    this.height = option.height;
    this.deviceData = option.deviceData;
    this.onLoadProgress = option.onLoadProgress;
    // 是否自动旋转镜头
    this.isControlsUpdate = option.isControlsUpdate || false;
    this.container = document.querySelector(containerSelector);
    this.clock = new THREE.Clock();
    // this.state = this.initStats();
    this.animate = this.animate.bind(this); //绑定this的指向
    this.init();
  }

  init() {
    // 场景
    this.initScene();
    // 相机
    this.initCamera();
    // 渲染
    this.initRender();
    // 光源
    this.initLights();
    // 控制器
    this.initOrbitControls();
    // 模型
    this.initContent('1');
    // this.initGUI();
    // 辅助观察
    this.initAxesHelper();
    /* 监听事件 */
    // this.windowResize();
    this.mouseEvent();
  }

  /* 场景 */
  initScene() {
    this.scene = new THREE.Scene();
    // this.sizeGroup = new THREE.Group();
  }

  /* 场景 */
  initAxesHelper() {
    const axesHelper = new AxesHelper(150);
    this.scene.add(axesHelper);
  }

  /* 相机 */
  initCamera() {
    this.camera = new THREE.PerspectiveCamera(
      45,
      this.width / this.height,
      0.1,
      10000
    );
    this.camera.position.set(50, 20, 50);
    this.camera.lookAt(new THREE.Vector3(0, 0, 0));
    this.length = this.camera.position.length();
  }

  /* 渲染器 */
  initRender() {
    console.log('!!!!', this.width, this.height);
    // this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    this.renderer.setSize(this.width, this.height);
    // 设置背景颜色
    this.renderer.setClearColor(0x262626, 0.5);
    // this.renderer.setClearColor(0xe4e4e4, 1);
    this.container.appendChild(this.renderer.domElement);

    this.css3dRenderer = new CSS3DRenderer();
    this.css3dRenderer.setSize(this.width, this.height);
    this.container.appendChild(this.css3dRenderer.domElement);
  }
  initSensor() {
    //创建一个长方体几何对象Geometry
    const geometry1 = new THREE.CylinderGeometry(1, 1, 3, 100);
    // var geometry = new THREE.BoxGeometry(100, 60, 20);//对比三个参数分别对应xyz轴哪个方向
    //材质对象Material
    const material1 = new THREE.MeshLambertMaterial({
      color: 0x00ffff, //设置材质颜色
      transparent: true, //开启透明
      opacity: 0.5 //设置透明度
    });
    const material2 = new THREE.MeshLambertMaterial({
      color: 0x00ffff, //设置材质颜色
      transparent: true, //开启透明
      opacity: 0.5 //设置透明度
    });
    const mesh1 = new THREE.Mesh(geometry1, material1); //网格模型对象Mesh
    mesh1.isSensor = true;
    mesh1.name = '传感器1';
    this.scene.add(mesh1); //网格模型添加到场景中
    // 设置模型mesh的xyz坐标
    mesh1.position.set(0, 6.3, 19.5);
    // 设置模型mesh的绕y轴旋转45度
    // mesh.rotateY(Math.PI / 4);
    //创建一个长方体几何对象Geometry
    const geometry2 = new THREE.CylinderGeometry(1, 1, 3, 100);
    // var geometry = new THREE.BoxGeometry(100, 60, 20);//对比三个参数分别对应xyz轴哪个方向
    const mesh2 = new THREE.Mesh(geometry2, material2); //网格模型对象Mesh
    mesh2.isSensor = true;
    mesh2.name = '传感器2';
    this.scene.add(mesh2); //网格模型添加到场景中
    // 设置模型mesh的xyz坐标
    mesh2.position.set(6.3, 0, 19.5);
    // 设置模型mesh的绕y轴旋转45度
    mesh2.rotateZ(Math.PI / 2);
  }
  /* 灯光 */
  initLights() {
    // 前后打光 两个点光源
    //点光源
    const pointLight1 = new THREE.PointLight(0xffffff, 1.0);
    pointLight1.position.set(400, 200, 300); //点光源位置
    this.scene.add(pointLight1); //点光源添加到场景中
    //点光源
    const pointLight2 = new THREE.PointLight(0xffffff, 1.0);
    pointLight2.position.set(-400, 200, -300); //点光源位置
    this.scene.add(pointLight2); //点光源添加到场景中
    // 环境光
    this.scene.add(new THREE.AmbientLight(0xffffff, 0.4));
    const pointLightHelper1 = new PointLightHelper(pointLight1, 10);
    const pointLightHelper2 = new PointLightHelper(pointLight2, 10);
    this.scene.add(pointLightHelper1);
    this.scene.add(pointLightHelper2);
  }
  log() {
    console.log('this.scene', this.scene);
  }
  /* 控制器 */
  initOrbitControls() {
    console.log(
      'initOrbitControls',
      this.camera,
      this.css3dRenderer.domElement
    );
    this.controls = new OrbitControls(
      this.camera,
      this.css3dRenderer.domElement
    );
    this.controls.autoRotate = true;
  }

  /* 调试插件 */
  initGUI() {
    // this.guiControls = new (function () {
    //   this.rotationSpeed = 0.02;
    //   this.circleSpeed = 0.02;
    //   this.upDownSpeed = 0.02;
    // })();
    // let gui = new dat.GUI();
    // gui.add(this.guiControls, 'rotationSpeed', 0, 1); //添加旋转的速度数值控制 数值大小在0-1之间
    // gui.add(this.guiControls, 'circleSpeed', 0, 1); //绕点旋转速度
    // gui.add(this.guiControls, 'upDownSpeed', 0, 1); //上下活塞运动速度
    // gui.domElement.style = 'position:absolute;top:60px;right:0px';
  }

  /* 场景中的内容 */
  initContent() {
    // 加载 glTF 格式的模型
    // let loader = new GLTFLoader(); /*实例化加载器*/
    const loader = new FBXLoader();
    // 根据参数判断模型
    let url = '/model/lixinbeng.FBX';
    // this.renderer.setClearColor(0x262626, 1);
    // if (type === '1') {
    // 	url =
    // 		'https://ossbao.oss-cn-qingdao.aliyuncs.com/model/%E6%9C%BA%E6%A2%B0%E8%87%8202.gltf';
    // 	this.renderer.setClearColor(0x262626, 1);
    // }
    // if (type === '2') {
    // 	url =
    // 		'https://ossbao.oss-cn-qingdao.aliyuncs.com/model/%E9%BE%99%E9%97%A8%E5%90%8A01.gltf';
    // 	this.renderer.setClearColor(0xe4e4e4, 1);
    // }
    // if (type === '3') {
    // 	url =
    // 		'https://ossbao.oss-cn-qingdao.aliyuncs.com/model/%E5%B7%A5%E4%B8%9A%E8%AE%BE%E5%A4%8733.gltf';
    // 	this.renderer.setClearColor(0xe4e4e4, 1);
    // }
    this.remove();
    const _this = this;
    loader.load(
      url,
      function (obj) {
        console.log('obj', obj);
        console.log('material', obj.material);
        obj.scale.set(0.1, 0.1, 0.1);
        obj = _this.changeGroupMaterial(obj);
        // 如果obj为group 则 向下遍历children修改材质
        //位置
        // obj.scene.position.set(0, 0, 0);
        // _this.meshArr.push(obj.scene);
        // _this.scene.add(obj.scene);
        obj.position.set(6.5, 11.9, 14);
        obj.isDevice = true;
        obj.addEventListener('click', (event) => {
          _this.showDeviceInfo(event);
        });
        _this.meshArr.push(obj);
        _this.scene.add(obj);

        // 添加传感器 模拟
        _this.initSensor();
      },
      function (xhr) {
        if (_this.onLoadProgress) {
          _this.onLoadProgress(parseInt(xhr.loaded / xhr.total) * 100);
        }
      },
      function (error) {
        console.log('load error!' + error.getWebGLErrorMessage());
      }
    );

    this.createViewGrid();
  }
  changeGroupMaterial(obj) {
    if (obj.isGroup) {
      if (obj.children && obj.children.length > 0) {
        obj.children.forEach((item) => {
          this.changeGroupMaterial(item);
        });
      }
    } else if (obj.isMesh) {
      obj.isDevice = true;
      obj.material.color.set(0x3f7b9d);
    }
    return obj;
  }
  showDeviceInfo(event) {
    console.log('showDeviceInfo', event);
  }
  /* 创建地面网格 */
  createViewGrid() {
    // 网格的对象
    // const helper = new THREE.GridHelper(30, 30);
    // this.scene.add(helper);
  }

  /* 鼠标点击事件 */
  mouseEvent() {
    this.container.addEventListener('click', choose);
    const _this = this;
    function choose(event) {
      console.log('event', event);
      let raycaster = new THREE.Raycaster();
      let mouse = new THREE.Vector2();
      console.log('event.clientX', event.clientX);
      console.log('event.clientY', event.clientY);
      mouse.x = (event.offsetX / _this.width) * 2 - 1;
      mouse.y = -(event.offsetY / _this.height) * 2 + 1;

      console.log('this.camera', _this.camera);
      console.log('this.scene', _this.scene);
      // 用新的原点更新射线
      raycaster.setFromCamera(mouse, _this.camera);

      // 射线交集检测
      let intersects = raycaster.intersectObjects(_this.scene.children);
      console.log('intersects', intersects);

      if (intersects.length > 0) {
        console.log(intersects[0]);

        let object = intersects[0].object;

        if (object.isDevice) {
          console.log('点击了设备');
          // 	object.material.color.set(0x409eff);
          //
          // 	object.material.transparent = false;
          // 	console.log('动画播放完毕');
          //添加一个展示标签
          const div = document.createElement('div');
          div.innerHTML =
            '<div class="element">' +
            '<div class="number">编号：' +
            _this.deviceData.code +
            '</div>' +
            '<div class="symbol">名称：' +
            _this.deviceData.name +
            '</div>' +
            '<div class="details">温度：56度</div>' +
            '</div>';
          // let html =
          // 	'<div class="element">' +
          // 	'<div class="number">编号：001</div>' +
          // 	'<div class="symbol">机械臂</div>' +
          // 	'<div class="details">温度：56度</div>' +
          // 	'</div>';

          // css3DObject
          let css3DObject = new CSS3DObject(div);
          css3DObject.name = '3dLabel';
          //高度 = 自身高度 / 2 + 父对象高度的 / 2
          css3DObject.position.y = 15;
          css3DObject.position.x = 10;
          css3DObject.position.z = 10;
          css3DObject.scale.set(0.1, 0.1, 0.1);
          // const deviceMesh = _this.meshArr.find((item) => item.isDevice);
          // deviceMesh.remove(deviceMesh.getObjectByName('3dLabel'));
          // deviceMesh.add(css3DObject);
          _this.scene.remove(_this.scene.getObjectByName('3dLabel'));
          _this.scene.add(css3DObject);
        } else if (object.isSensor) {
          // if (object.isSensor) {
          object.material.transparent = true;
          if (object.material.opacity < 1) {
            object.material.opacity = 1;
          } else {
            object.material.opacity = 0.4;
          }
          Message.info('点击了' + object.name);
        }
      }
    }
  }

  /* 性能插件 */
  initStats() {
    let stats = new Stats();
    stats.domElement.style.top = '60px';
    this.container.appendChild(stats.domElement);
    return stats;
  }

  /* 窗口变动触发 */
  windowResize() {
    window.addEventListener('resize', onWindowResize, false);

    function onWindowResize() {
      let { innerWidth: width, innerHeight: height } = window;
      console.log('this.camera.aspect', this.camera.aspect);
      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(width, height);
      this.css3dRenderer.setSize(width, height);
    }
  }

  /* 数据更新 */
  update() {
    // this.state.update();
    if (this.isControlsUpdate) {
      this.controls.update();
    }
  }

  /* 循环渲染 */

  animate() {
    // const spt = this.clock.getDelta() * 1000; //毫秒
    // const deviceMesh = this.meshArr.find((item) => item.isDevice);
    // if (deviceMesh) {
    // 	deviceMesh.rotateY(0.01); //每次绕y轴旋转0.01弧度
    // }
    requestAnimationFrame(this.animate);
    this.renderer.render(this.scene, this.camera);
    this.css3dRenderer.render(this.scene, this.camera);
    this.update();
  }

  initEcharts(div) {
    let pieChart = echarts.init(div, null, { width: 300, height: 300 });
    // 根据实际情况，改查参数传入option
    let option = {
      title: {
        text: '数据统计',
        subtext: '',
        x: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: ['1月', '2月', '3月']
      },
      series: [
        {
          name: '工作时间',
          type: 'pie',
          radius: '55%',
          center: ['50%', '60%'],
          data: [
            { value: 335, name: '1月' },
            { value: 310, name: '2月' },
            { value: 234, name: '3月' }
          ],
          itemStyle: {
            emphasis: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    pieChart.setOption(option);

    pieChart.on('finished', function () {
      const spriteMap = new THREE.TextureLoader().load(pieChart.getDataURL());

      const spriteMaterial = new THREE.SpriteMaterial({
        transparent: true,
        map: spriteMap,
        side: THREE.DoubleSide
      });

      this.sprite = new THREE.Sprite(spriteMaterial);

      this.sprite.scale.set(25, 25, 25);
      this.sprite.position.set(20, 20, 0);
      this.scene.add(this.sprite);
    });
  }

  // 移除元素
  remove() {
    // while(scene.children.length > 0){
    //     scene.remove(scene.children[0]);
    // }
    this.meshArr.forEach(function (mesh) {
      this.scene.remove(mesh);
    });
  }

  // 缩小
  smallerClick() {
    this.scene.scale.x -= 0.1;
    this.scene.scale.y -= 0.1;
    this.scene.scale.z -= 0.1;
  }

  // 放大
  greaterClick() {
    this.scene.scale.x += 0.1;
    this.scene.scale.y += 0.1;
    this.scene.scale.z += 0.1;
    // vm.bool = false;
  }

  // 正视图
  zhengClick() {
    this.camera.position.set(0, 0, this.length);
    this.camera.lookAt(this.scene.position);
  }

  // 俯视图
  fuClick() {
    this.camera.position.set(0, this.length, 0);
    this.camera.lookAt(this.scene.position);
  }

  // 侧视图
  ceClick() {
    this.camera.position.set(-this.length, 0, 0);
    this.camera.lookAt(this.scene.position);
  }

  // 轴视图
  zhouClick() {
    const vec3 = new THREE.Vector3(1, 1, 1).normalize();
    this.camera.position.set(vec3.x * length, vec3.y * length, vec3.z * length);
    this.camera.lookAt(this.scene.position);
  }

  // 隐藏
  hideClick() {
    this.meshArr.forEach(function (mesh) {
      mesh.visible = false;
    });
  }

  // 显示
  viewClick() {
    this.meshArr.forEach(function (mesh) {
      mesh.visible = true;
    });
  }

  // 数据展示
  dataClick() {
    console.log('动画播放完毕');
    //添加一个标签，类似公告牌
    // let html =
    // 	'<div class="element">' +
    // 	'<div class="number">编号：001</div>' +
    // 	'<div class="symbol">机械臂</div>' +
    // 	'<div class="details">温度：56度</div>' +
    // 	'</div>';

    //3D
    // let css3DObject = new CSS3DObject($(html)[0]);
    // css3DObject.name = '3dLabel002';
    // //高度 = 自身高度 / 2 + 父对象高度的 / 2
    // css3DObject.position.y = 10;
    // css3DObject.position.x = -10;
    // css3DObject.scale.set(0.1, 0.1, 0.1);
    // this.scene.remove(this.scene.getObjectByName('3dLabel002'));
    // this.scene.add(css3DObject);
  }
}

export { ThreeModel };
