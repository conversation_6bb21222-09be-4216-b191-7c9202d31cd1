<template>
  <basic-container>
    <search @search="query" />
    <el-row>
      <el-col :span="24">
        <div class="tool-box">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="small"
            @click="handleAdd"
            >新增</el-button
          >
          <!-- <el-button
            type="danger"
            icon="el-icon-delete"
            size="small"
            @click="handleMultiDelete"
            >批量删除</el-button
          > -->
          <el-button
            type="success"
            icon="el-icon-upload2"
            size="small"
            plain
            @click="handleImport"
            >导入
          </el-button>
          <el-button
            type="warning"
            icon="el-icon-download"
            size="small"
            plain
            @click="handleExport"
            >导出
          </el-button>
        </div>
      </el-col>
    </el-row>
    <table-info :source="tableData" :loading="loading" @dispatch="dispatch" />
    <yk-pagination
      small
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.current"
      :limit.sync="queryParams.size"
      @pagination="getList"
    />
    <!-- 新增/编辑/详情 -->
    <add-edit
      :dialogTitle="dialogTitle"
      :formVisible="formVisible"
      :viewMode="viewMode"
      :orderId="orderId"
      @refresh="request"
      @close="handleDialogClose"
    />
    <!-- 导入 -->
    <yk-import-excel
      title="订单数据导入"
      action="/demo/order/import-order"
      templateUrl="/demo/order/export-template"
      failReasonUrl="/demo/order/download-order-import-fail-info"
      :importVisible="importVisible"
      @close="importVisible = false"
      @refresh="request"
    />
  </basic-container>
</template>

<script>
  import { Search, TableInfo, AddEdit } from './order-components';
  import { getPage, remove } from '@/api/demo/order';
  import { mapGetters } from 'vuex';
  import { payType } from '@/constant/common';
  import { exportBlob } from '@/api/common';
  import { getToken } from '@/util/auth';
  import { downloadXls } from '@/util/util';
  import { dateNow } from '@/util/date';
  import NProgress from 'nprogress';
  import 'nprogress/nprogress.css';

  export default {
    name: 'Order',
    components: { Search, TableInfo, AddEdit },
    data() {
      return {
        payType,
        // 查询条件
        queryParams: {
          num: '',
          size: 10,
          current: 1
        },
        // 表格加载中
        loading: false,
        // 列表条目总数量
        total: 0,
        // 列表数据
        tableData: [],
        // 选中的数据
        multiSelection: [],
        // 是否显示
        formVisible: false,
        orderId: '',
        // 是否查看
        viewMode: false,
        // dialog标题
        dialogTitle: '',
        // 导入弹窗
        importVisible: false
      };
    },
    computed: {
      ...mapGetters(['permission']),
      ids() {
        let ids = [];
        this.multiSelection.forEach((ele) => {
          ids.push(ele.id);
        });
        return ids.join(',');
      }
    },
    methods: {
      // 查询
      query(params) {
        console.log(params);
        Object.assign(
          this.queryParams,
          {
            current: 1,
            size: 10
          },
          params
        );
        this.request();
      },
      // 分页查询
      getList({ page, limit }) {
        Object.assign(this.queryParams, {
          current: page,
          size: limit
        });
        this.request();
      },
      // 请求列表数据
      async request() {
        try {
          this.loading = true;
          const res = await getPage(this.queryParams);
          const { total = 0, records = [] } = res.data.data;
          this.total = total;
          this.tableData = records;
          this.multiples = [];
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 列表操作
      dispatch(type, data) {
        switch (type) {
          case 'edit':
            return this.handleEdit(data);
          case 'view':
            return this.handleView(data);
          case 'checked':
            return this.selectChange(data);
          case 'refresh':
            return this.request();
          default:
            return false;
        }
      },
      selectChange(val) {
        this.multiSelection = val;
      },
      handleAdd() {
        this.dialogTitle = '新增订单';
        this.formVisible = true;
        this.viewMode = false;
      },
      handleView(row) {
        this.dialogTitle = '查看订单';
        this.formVisible = true;
        this.viewMode = true;
        this.orderId = row.id;
      },
      handleEdit(row) {
        this.dialogTitle = '修改订单';
        this.formVisible = true;
        this.viewMode = false;
        this.orderId = row.id;
      },
      handleDialogClose() {
        this.formVisible = false;
        this.orderId = '';
        this.viewMode = false;
      },
      handleMultiDelete() {
        console.log('multi-delete');
        if (this.multiSelection.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.$confirm('确定删除选择的数据吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          remove(this.ids).then((res) => {
            if (res.data.success) {
              this.onLoad();
              this.$message({
                type: 'success',
                message: '操作成功！'
              });
            } else {
              this.$message({
                type: 'error',
                message: res.data.msg
              });
            }
          });
        });
      },
      handleImport() {
        this.importVisible = true;
      },
      handleExport() {
        this.$confirm('是否导出订单数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          NProgress.start();
          exportBlob(
            `/api/demo/order/export-order?${
              this.website.tokenHeader
            }=${getToken()}&num=${this.queryParams.num}`
          ).then((res) => {
            downloadXls(res.data, `订单数据表${dateNow()}.xlsx`);
            NProgress.done();
          });
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-pagination {
    margin-top: 20px;
  }
</style>
