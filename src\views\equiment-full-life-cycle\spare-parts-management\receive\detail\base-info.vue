<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
      size="small"
    >
      <el-descriptions-item label="领用单号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="领用名称：">{{
        details.name || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="领用人：">{{
        details.receiveUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="领用部门：">{{
        details.receiveDeptName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="领用时间：">{{
        details.createTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="备件数量：">{{
        details.totalQuantity || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="领用原因：">{{
        details.remark || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="审核人：">{{
        details.auditUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="审核时间：">{{
        details.auditTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="状态：">{{
        details.statusName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item v-if="details.rejectReason" label="驳回原因：">{{
        details.rejectReason || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }
</style>
