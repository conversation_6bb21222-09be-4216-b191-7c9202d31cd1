import request from '@/router/axios';

// 添加运维资料
export const postAddPreFile = (data) => {
  return request({
    url: '/api/szyk-simas/equipment-file/ai-tools-add-pre-file',
    method: 'post',
    data
  });
};

// 删除运维资料
export const postDelPreFile = (params) => {
  return request({
    url: '/api/szyk-simas/equipment-file/ai-tools-remove-pre-file',
    method: 'post',
    params
  });
};

// 生成运维标准
export const postGenerateStandards = (data) => {
  return request({
    url: '/api/szyk-simas/ai-tools/generate-operate-standards',
    method: 'post',
    data
  });
};
