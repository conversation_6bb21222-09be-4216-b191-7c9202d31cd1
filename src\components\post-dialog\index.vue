<template>
  <el-dialog
    title="选择岗位"
    :visible.sync="visible"
    width="60%"
    custom-class="_dialogStyle"
    @closed="closed"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
  >
    <el-form :model="form" inline size="small">
      <el-form-item label="岗位类型" prop="category">
        <el-select v-model="form.category" placeholder="请选择岗位类型">
          <el-option
            v-for="dict in systemDicts.type['post_category']"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="岗位名称" prop="postName">
        <el-input v-model="form.postName" placeholder="请输入岗位名称">
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="getList" type="primary">查询</el-button>
        <el-button @click="onFormReset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      style="width: 100%"
      border
      size="small"
      :header-cell-style="{ background: '#fafafa' }"
      height="500px"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column prop="deptName" label="岗位类型" show-overflow-tooltip>
      </el-table-column>

      <el-table-column
        prop="fullName"
        label="岗位编号"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="fullName"
        label="岗位名称"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column label="操作" align="center" width="250">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-view"
            type="text"
            size="mini"
            @click="handleSelect(scope.row)"
          >
            选择
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
  </el-dialog>
</template>

<script>
  import { getList } from '@/api/system/post';
  export default {
    systemDicts: ['post_category'],

    name: 'dept-dialog',
    components: {},
    props: {},
    data() {
      return {
        form: {
          category: undefined,
          postName: undefined
        },
        loading: false,
        total: 0,
        current: undefined,
        visible: false,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },
    mounted() {},
    methods: {
      show() {
        this.visible = true;
        this.$nextTick(() => {
          this.getList();
        });
      },
      onFormReset() {
        this.form.category = '';
        this.form.postName = '';
      },
      handleSelect(row) {
        this.visible = false;
        this.$emit('select', row);
      },

      async getList() {
        this.loading = true;
        try {
          let params = { ...this.form, ...this.searchParams };
          let res = await getList(params);
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          console.log(e);
        }
        this.loading = false;
      },
      closed() {}
    }
  };
</script>

<style scoped lang="scss"></style>
