<template>
  <el-table size="small" :data="list" border stripe>
    <el-table-column type="index" label="#" width="50"></el-table-column>
    <el-table-column prop="monitorName" label="检查部位">
      <template v-slot="{ row }">{{ row.monitorName || '-' }}</template>
    </el-table-column>
    <el-table-column
      prop="standard"
      label="检查标准"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="method"
      label="检查方法"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="abnormalStatus"
      label="检查结果"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{ row.abnormalStatus || '-' }}</template>
    </el-table-column>
    <el-table-column
      prop="abnormalComment"
      label="异常描述"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{
        (row.inspectRecord && row.inspectRecord.abnormalComment) || '-'
      }}</template>
    </el-table-column>
    <el-table-column
      prop="abnormalLevelName"
      label="异常等级"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{
        (row.inspectRecord && row.inspectRecord.abnormalLevelName) || '-'
      }}</template>
    </el-table-column>
    <el-table-column prop="abnormalImageList" label="异常图片" width="80">
      <template v-slot="{ row }">
        <span v-if="row.inspectRecord && row.inspectRecord.abnormalImage">
          <el-image
            v-for="img in row.inspectRecord.abnormalImageList"
            :key="img.id"
            style="width: 50px; height: 50px"
            :src="getFileFullUrl(img.id)"
            fit="cover"
            :preview-src-list="[getFileFullUrl(img.id)]"
          ></el-image
        ></span>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column
      prop="isHandledName"
      label="是否现场处理"
      show-overflow-tooltip
    >
      <template v-slot="{ row }">{{
        (row.inspectRecord && row.inspectRecord.isHandledName) || '-'
      }}</template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { getFileFullUrl } from '@/util/file';

  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => {
          return [];
        }
      }
    },
    data() {
      return { getFileFullUrl };
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
