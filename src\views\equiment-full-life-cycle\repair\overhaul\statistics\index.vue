<template>
  <el-row style="background: #fff">
    <el-col class="top" :span="8">
      <work-order-pie ref="pie"></work-order-pie>
    </el-col>
    <el-col class="top center" :span="8">
      <device-inspect-bar ref="iBar"></device-inspect-bar>
    </el-col>
    <el-col class="top" :span="8">
      <spot-check-task ref="spot"></spot-check-task>
    </el-col>
    <el-col class="bottom" :span="24">
      <message-info ref="messageInfo"></message-info>
    </el-col>
  </el-row>
</template>
<script>
  import DeviceInspectBar from './component/device-inspect-bar.vue'; // 设备点巡检
  import WorkOrderPie from './component/work-order-pie.vue'; // 工单
  import SpotCheckTask from './component/spot-check-task.vue'; //点巡检任务情况统计
  import MessageInfo from './component/result-list/index.vue';
  export default {
    name: 'chart',
    components: { DeviceInspectBar, WorkOrderPie, SpotCheckTask, MessageInfo },
    data() {
      return {};
    },
    watch: {},
    computed: {},
    methods: {},
    mounted() {
      this.$nextTick(() => {});
    }
  };
</script>

<style lang="scss">
  .wel_icon {
    float: left;
    color: #5093fe;
    font-weight: 600;
    font-size: 24px;
  }
  .top {
    height: 400px;
  }
  .center {
    border-left: 1px dashed #efefef;
    border-right: 1px dashed #efefef;
  }

  ::v-deep {
    .basic-container__card {
      height: 100% !important;
    }
  }
</style>
