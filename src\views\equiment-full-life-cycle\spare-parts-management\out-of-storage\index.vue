<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-if="permission['spare-out-add-edit']"
        icon="el-icon-plus"
        type="primary"
        size="small"
        @click="operate"
        >新增</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 200px)"
      border
      ref="table"
      stripe
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column align="center" type="index" label="#"></el-table-column>
      <el-table-column
        align="center"
        prop="no"
        label="出库单号"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="outboundTypeName"
        label="出库类型"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i
            :style="`color: ${
              row.outboundType === '1' ? '#E23F3F' : '#EE7C11'
            };font-size:18px`"
            >●</i
          >
          {{ row.outboundTypeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="outboundUseName"
        align="center"
        label="用途"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="receiveUserName"
        align="center"
        label="领用人"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="receiveDeptName"
        align="center"
        label="领用部门"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="outboundDate"
        align="center"
        label="出库日期"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column
        prop="totalQuantity"
        align="center"
        label="备件数量"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.totalQuantity || '-' }}</template>
      </el-table-column>

      <el-table-column
        align="center"
        prop="statusName"
        label="状态"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <i
            :style="`color: ${
              row.status === 1 ? '#00BB7D' : '#808080'
            };font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createUserName"
        align="center"
        label="创建人"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="createTime"
        label="创建时间"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="updateUserName"
        align="center"
        label="更新人"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        align="center"
        prop="updateTime"
        label="更新时间"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" fixed="right">
        <template v-slot="{ row }">
          <section class="cell-operate-class">
            <el-button
              v-if="permission['spare-out-view']"
              type="text"
              size="small"
              @click="detail(row)"
              >查看</el-button
            >
            <!-- 完成1  已撤销2-->

            <el-button
              type="text"
              size="small"
              @click="operate(row)"
              v-if="row.status === 2 && permission['spare-out-add-edit']"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="small"
              class="danger-btn"
              @click="cancelOrder(row)"
              v-if="
                row.status === 1 &&
                row.allowCancel &&
                permission['spare-out-revocation']
              "
              >撤销</el-button
            >
            <el-popconfirm
              title="确定删除吗？"
              @confirm="() => handleDelete(row)"
              v-if="row.status === 2 && permission['spare-out-delete']"
            >
              <el-button
                class="danger-btn"
                slot="reference"
                type="text"
                size="small"
                >删除</el-button
              >
            </el-popconfirm>
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <add-device-base ref="add" @success="getList"></add-device-base>
    <detail-index ref="detailIndex"></detail-index>
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import Search from './search';
  import AddDeviceBase from './operate/index.vue';
  import Pagination from '@/components/pagination';
  import { convertFileUrl } from '@/util/util';
  import {
    getSparePartsOutListApi,
    delSparePartsOutApi,
    cancelSparePartsOutApi
  } from '@/api/equiment-full-life-api/spare-parts';

  import { mapGetters } from 'vuex';
  export default {
    name: 'OutOfStorage',
    components: {
      Search,
      AddDeviceBase,
      DetailIndex,
      Pagination
    },
    props: {},
    computed: {
      ...mapGetters(['permission', 'userInfo'])
    },
    data() {
      return {
        convertFileUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },

    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      //  撤销
      cancelOrder(row) {
        this.$confirm(`确定撤销此出库单？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            await cancelSparePartsOutApi(row.id);
            this.$message.success('操作成功');
            await this.getList();
          } catch (e) {
            this.$message.warning(e.data.msg);
          }
        });
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getSparePartsOutListApi({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      operate(row) {
        const obj = { id: row.id, no: row.no };
        this.$refs.add.show(obj);
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      },
      async handleDelete(row) {
        this.loading = true;
        try {
          await delSparePartsOutApi(row.id);
          if (this.list.length === 1) {
            this.searchParams.current = 1;
          }
          this.$message({
            type: 'success',
            message: '删除成功'
          });
          await this.getList();
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
