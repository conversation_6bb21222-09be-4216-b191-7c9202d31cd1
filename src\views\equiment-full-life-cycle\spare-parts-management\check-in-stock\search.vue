<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      label-position="left"
      class="search-form"
    >
      <el-form-item label="备品备件编号" prop="dictNo">
        <el-input
          v-model="form.dictNo"
          placeholder="请输入备品备件编号"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="备品备件名称" prop="dictName">
        <el-input
          v-model="form.dictName"
          placeholder="请输入备品备件名称"
          clearable
        >
        </el-input>
      </el-form-item>

      <el-form-item label="库房" prop="warehouseId" label-width="60px">
        <el-select
          v-model="form.warehouseId"
          placeholder="请选择入库库房"
          clearable
          filterable
        >
          <el-option
            v-for="item in whorehouse"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <!--      <el-form-item-->
      <!--        label="日期范围"-->
      <!--        prop="time"-->
      <!--        label-width="75px"-->
      <!--        class="_label"-->
      <!--      >-->
      <!--        <el-date-picker-->
      <!--          v-model="form.time"-->
      <!--          type="daterange"-->
      <!--          range-separator="至"-->
      <!--          start-placeholder="开始日期"-->
      <!--          end-placeholder="结束日期"-->
      <!--          value-format="yyyy-MM-dd"-->
      <!--          clearable-->
      <!--        >-->
      <!--        </el-date-picker>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { in_storage_status } from '../util';
  import { getCheckRepairTypeListApi } from '@/api/equiment-full-life-api/spare-parts';
  export default {
    name: 'DeviceListSearch',
    components: {},
    data() {
      return {
        in_storage_status,
        form: {
          dictNo: undefined,
          dictName: undefined,
          warehouseId: undefined
        },
        whorehouse: []
      };
    },
    created() {
      this.getStoreHouseList();
    },
    methods: {
      //  获取库放列表
      async getStoreHouseList() {
        let res = await getCheckRepairTypeListApi();
        this.whorehouse = res.data.data;
      },
      reset() {
        this.form.dictNo = undefined;
        this.form.dictName = undefined;
        this.form.warehouseId = '';
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
