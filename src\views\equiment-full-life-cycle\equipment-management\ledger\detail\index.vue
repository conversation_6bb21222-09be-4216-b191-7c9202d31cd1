<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @closed="close"
    class="detail-drawer"
    width="80%"
  >
    <div class="details" v-loading="loading">
      <el-tabs v-model="activeTab" tab-position="top" style="height: 100%">
        <el-tab-pane label="设备信息" name="device">
          <!-- 基本信息 -->
          <section>
            <span class="el-base-title">基础信息</span>
            <base-info :details="details"></base-info>
          </section>
          <section v-if="details.attrList && details.attrList.length">
            <span class="el-base-title">拓展属性</span>
            <Extended :details="details"></Extended>
          </section>
          <section>
            <span class="el-base-title">文件资料</span>
            <file-name :list="details.equipmentFileInfoList"></file-name>
          </section>
          <section v-if="details.imageList">
            <span class="el-base-title">设备图片</span>
            <el-image
              style="width: 100px; height: 100px"
              :src="convertFileUrl(details.imageList[0].domain)"
              fit="cover"
              :preview-src-list="[convertFileUrl(details.imageList[0].domain)]"
            ></el-image>
          </section>
        </el-tab-pane>
        <el-tab-pane label="关联工单" name="related">
          <order-list
            v-if="visible"
            ref="orderList"
            :equipmentId="id"
          ></order-list>
        </el-tab-pane>
        <!-- v-if="isSpecialDevice" -->
        <!-- <el-tab-pane label="特种设备使用记录" name="use">
          <UseList v-if="visible" ref="useList" :equipmentId="id"></UseList>
        </el-tab-pane> -->
        <!-- v-if="isSpecialDevice" -->
        <el-tab-pane label="特种设备检查记录" name="inspection">
          <InspectionList
            v-if="visible"
            ref="inspectionList"
            :equipmentId="id"
          ></InspectionList>
        </el-tab-pane>
        <!-- <el-tab-pane label="设备履历">设备履历</el-tab-pane> -->
      </el-tabs>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from './base-info.vue';
  import Extended from './extended.vue';
  import FileName from '@/views/equiment-full-life-cycle/pre-management/detail/standard.vue';
  import { convertFileUrl } from '@/util/util';
  import { getAccountDetail } from '@/api/equiment-full-life-api/ledger';
  import OrderList from './order-list.vue';
  import UseList from './use-records.vue';
  import InspectionList from './inspect-records.vue';
  export default {
    name: 'RepairViewIndex',
    components: {
      BaseInfo,
      FileName,
      OrderList,
      Extended,
      UseList,
      InspectionList
    },
    data() {
      return {
        convertFileUrl,
        activeTab: 'device',
        id: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: { id: undefined } // 详情数据
      };
    },
    computed: {
      isSpecialDevice() {
        return !!this.details.specialType;
      }
    },

    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getAccountDetail({ id: id });
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      // 点击展示
      async show(id) {
        this.visible = true;
        if (id) {
          this.id = id;
          await this.getDetail(id);
        }
      },

      // 关闭弹窗
      close() {
        this.$refs['orderList'].resetForm();
        this.activeTab = 'device';
        this.visible = false;
        this.detail = { id: undefined };
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

</style>
