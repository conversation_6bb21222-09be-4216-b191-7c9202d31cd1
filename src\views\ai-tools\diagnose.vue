<template>
  <div class="diagnose-container">
    <el-form
      ref="form"
      class="diagnose-form"
      :model="form"
      :rules="rules"
      size="small"
      label-suffix="："
      label-position="top"
      label-width="100px"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="设备名称" prop="deviceName" ref="equipment">
            <el-input
              v-model="form.deviceName"
              readonly
              placeholder="请选择设备名称"
              @focus.prevent="onChooseDeviceClick"
              style="width: 240px"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="异常描述" prop="defectDesc" class="desc-wrapper">
            <el-input
              v-model="form.defectDesc"
              placeholder="请输入异常描述"
              style="margin-right: 30px"
              @keydown.native.prevent.enter="handleGenerate"
            >
            </el-input>
            <el-button
              type="primary"
              size="small"
              :disabled="btnDisabled"
              @click="handleGenerate"
              >生成</el-button
            >
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="维修建议" class="mb-0">
            <el-input
              v-model="repairAdvise"
              type="textarea"
              placeholder="根据故障缺陷以及异常描述，自动生成"
              rows="12"
              maxlength="3000"
              style="width: 100%"
              v-loading="loading"
              element-loading-text="正在生成维修建议..."
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="">
            <div class="source-wrapper">
              <div class="source" v-if="sourceData.length > 0">
                <el-button
                  type="text"
                  class="source-item"
                  v-for="(item, index) in sourceData"
                  :key="index"
                  @click="handlePreview(item)"
                >
                  {{ handleSourceItemTitle(item) }}
                </el-button>
              </div>
              <i
                v-if="repairAdvise.length > 0 && !btnDisabled"
                class="el-icon-copy-document"
                title="复制维修建议"
                @click="handleCopy"
              ></i>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!-- 设备选择弹窗 -->
    <asset-list
      ref="assetList"
      selType="maintain"
      :statusList="[4]"
      @on-choose="onChooseDeviceSuccess"
    ></asset-list>
  </div>
</template>

<script>
  import AssetList from '@/views/equiment-full-life-cycle/components/select-asset-dialog/index.vue';
  import { getSseData } from '@/util/sse.js';
  import { copyText } from '@/util/util';
  import { previewFile, previewFileByLink } from '@/util/preview';

  export default {
    components: {
      AssetList
    },
    data() {
      return {
        loading: false,
        btnDisabled: false,
        form: {
          deviceId: undefined,
          deviceName: undefined,
          defectDesc: undefined
        },
        repairAdvise: '',
        sourceData: [],
        rules: {
          deviceName: [
            { required: true, message: '请选择设备', trigger: 'blur' }
          ],
          defectDesc: [
            { required: true, message: '请输入异常描述', trigger: 'blur' }
          ]
        }
      };
    },
    methods: {
      setForm({ deviceId, deviceName, defectDesc }) {
        this.form.deviceId = deviceId;
        this.form.deviceName = deviceName;
        this.form.defectDesc = defectDesc;
      },
      // 选择设备
      onChooseDeviceClick() {
        this.$refs.assetList.show([], true);
      },
      // 选择设备回调
      onChooseDeviceSuccess(device) {
        this.form.deviceId = device.id;
        this.form.deviceName = device.name;
        this.$refs['equipment'].clearValidate();
      },
      // 生成维修建议
      async handleGenerate() {
        this.$refs['form'].validate(async (valid) => {
          if (!valid) return;
          this.sourceData = [];
          this.btnDisabled = true;
          this.loading = true;
          try {
            this.repairAdvise = '';
            const url =
              '/api/szyk-simas/ai-tools/generate-repair-suggest-stream';
            getSseData({
              url,
              data: this.form,
              onMessage: (event) => {
                if (
                  event.data.includes(`"event":"text_chunk"`) &&
                  event.data.includes('from_variable_selector')
                ) {
                  if (this.loading) this.loading = false;
                  let text = JSON.parse(event.data)?.data?.text;
                  if (text) this.repairAdvise += text;
                } else if (event.data.includes(`"event":"source_list"`)) {
                  let { data } = JSON.parse(event.data);
                  if (data) this.sourceData = data;
                }
              },
              onClose: () => {
                this.btnDisabled = false;
              },
              onError: () => {
                this.loading = false;
                this.$message.error('生成维修建议失败，请稍后重试');
              }
            });
          } catch (e) {
            this.btnDisabled = false;
            this.loading = false;
            console.error(e);
            this.$message.error('生成维修建议失败，请稍后重试');
          }
        });
      },
      // 复制维修建议
      handleCopy() {
        if ((this.repairAdvise || '').length === 0) {
          return this.$message({
            message: '请先生成维修建议',
            type: 'warning'
          });
        }
        copyText(this.repairAdvise);
      },
      // 来源展示名称
      handleSourceItemTitle(item) {
        if (
          ['故障案例库', '设备知识库', '煤炭设备知识库'].includes(
            item.sourceType
          )
        ) {
          let caseName = item.fileName;
          if (caseName.endsWith('.')) caseName = caseName.slice(0, -1);
          return `${item.sourceType} - ${caseName}`;
        } else {
          return item.sourceType;
        }
      },
      // 预览文件
      handlePreview({ sourceType, fileId, fileName, fileLink }) {
        if (sourceType === '故障案例库') {
          this.$router.push({
            name: 'fault-case',
            params: {
              id: fileId
            }
          });
        } else if (sourceType === '设备知识库') {
          if (!fileId) return this.$message.warning('文件不存在');
          // 预览
          const temp = {
            id: fileId,
            originalName: fileName,
            extension: fileName.split('.').pop()
          };
          previewFile(temp);
        } else if (sourceType === '煤炭设备知识库') {
          if (!fileLink) return this.$message.warning('文件不存在');
          // 预览
          const temp = {
            link: fileLink,
            originalName: fileName,
            extension: fileName.split('.').pop()
          };
          previewFileByLink(temp);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .diagnose-form ::v-deep {
    .el-form-item {
      width: 100%;

      .el-form-item__content {
        width: 100%;
      }

      &.mb-0 {
        margin-bottom: 0 !important;
      }

      &.desc-wrapper {
        .el-form-item__content {
          display: flex;
          justify-content: space-between;
        }
      }
    }

    .btn-wrapper {
      text-align: right;

      .el-form-item__content {
        width: 100%;
        text-align: right;
      }
    }

    .source-wrapper {
      position: relative;
      margin-top: 10px;
      padding-right: 40px;

      .source {
        padding-left: 42px;

        &::before {
          position: absolute;
          top: 1px;
          left: 0;
          color: #135eff;
          line-height: normal;
          content: '来源:';
        }

        .source-item {
          display: block;
          margin-left: 0;
          padding: 0;
          font-size: 14px;
          line-height: normal;
        }
      }

      .el-icon-copy-document {
        position: absolute;
        top: 7px;
        right: 0;
        font-size: 18px;
        cursor: pointer;
      }
    }
  }
</style>
