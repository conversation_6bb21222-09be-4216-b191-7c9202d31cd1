{"name": "simas-mine", "version": "1.0.0", "updateDate": "000000", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode test", "build:show": "vue-cli-service build --mode show", "lint": "vue-cli-service lint", "lint:eslint": "eslint --fix --ext .js,.vue src", "lint:stylelint": "stylelint 'src/**/*.(vue|css|less|scss)' --fix", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@kangc/v-md-editor": "^1.7.12", "@microsoft/fetch-event-source": "^2.0.1", "@smallwei/avue": "^2.9.5", "avue-plugin-ueditor": "^0.1.4", "axios": "^0.18.0", "axios-extra": "0.0.6", "babel-polyfill": "^6.26.0", "classlist-polyfill": "^1.2.0", "crypto-js": "^4.0.0", "dayjs": "^1.11.13", "echarts": "^5.4.0", "el-table-infinite-scroll": "^2.0.0", "element-ui": "^2.15.6", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "jsbarcode": "^3.11.6", "lodash": "^4.17.21", "mockjs": "^1.0.1-beta3", "node-gyp": "^5.0.6", "nprogress": "^0.2.0", "onlyoffice-vue": "^1.0.1", "portfinder": "^1.0.23", "promise-queue-plus": "^1.2.2", "qrcode": "^1.5.3", "script-loader": "^0.7.2", "spark-md5": "^3.0.2", "vue": "^2.6.10", "vue-axios": "^2.1.2", "vue-cropper": "0.5.2", "vue-i18n": "^8.7.0", "vue-jsonp": "^2.0.0", "vue-router": "^3.0.1", "vuex": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^8.2.2", "chai": "^4.1.2", "eslint": "^4.19.1", "eslint-config-prettier": "^8.5.0", "eslint-loader": "^2.1.2", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^4.7.1", "html-webpack-plugin": "^4.5.0", "husky": "3.0.9", "lint-staged": "9.4.3", "mini-css-extract-plugin": "^2.6.1", "node-sass": "^6.0.1", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.5", "prettier": "^2.6.2", "sass-loader": "^10.0.5", "stylelint": "^14.5.3", "stylelint-config-clean-order": "^0.8.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-recommended-scss": "^5.0.2", "stylelint-config-recommended-vue": "^1.3.0", "stylelint-config-standard-scss": "^3.0.0", "stylelint-scss": "^4.3.0", "vue-template-compiler": "^2.5.17", "webpack-bundle-analyzer": "^3.0.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"], "src/**/*.{nvue,vue,less,css,scss}": ["stylelint --fix", "git add"]}}