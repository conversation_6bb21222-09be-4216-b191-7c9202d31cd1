<template>
  <div>
    <el-form
      :model="form"
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      size="small"
      label-width="110px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="润滑方式" prop="name">
            <el-input
              placeholder="请输入润滑方式"
              v-model.trim="form.name"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              placeholder="请输入备注"
              v-model.trim="form.remark"
              maxlength="200"
              clearable
              show-word-limit
              autosize
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'AddWay',
    props: {
      initData: {
        type: Object,
        default: () => {}
      }
    },

    data() {
      return {
        form: {
          name: undefined,
          remark: undefined
        },
        edit: false,
        rules: {
          name: [
            {
              required: true,
              message: '请输入润滑方式',
              trigger: 'blur'
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    created() {},
    computed: {},
    methods: {
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return {
            ...this.form
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs['baseForm'].resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped></style>
