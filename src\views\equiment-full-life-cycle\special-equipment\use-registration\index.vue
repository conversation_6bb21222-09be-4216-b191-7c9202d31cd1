<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <search ref="search" @search="onsubmit" />
    <div class="res-wrapper">
      <div class="res-text">查询结果列表</div>
    </div>
    <table-info
      :loading="loading"
      :tableData="list"
      @dispatch="handleTableDispatch"
    />
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!-- 详情 -->
    <ledger-detail ref="ledgerDetail"></ledger-detail>
    <!-- 使用登记 -->
    <register-dialog @success="getList" ref="register"></register-dialog>
  </basic-container>
</template>

<script>
  import Pagination from '@/components/pagination';
  import LedgerDetail from '@/components/ledger-detail';
  import Search from './search';
  import TableInfo from './table-info';
  import RegisterDialog from '@/components/ledger-detail/register-dialog.vue';
  import { specialPage } from '@/api/equiment-full-life-api/ledger';

  export default {
    name: 'DeviceGradingList',
    components: {
      Pagination,
      LedgerDetail,
      Search,
      TableInfo,
      RegisterDialog
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10,
          isSpecialType: true
        }
      };
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },
      // 查询
      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      // 设备分级管理
      handleSetting() {
        this.$refs.setting.show();
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await specialPage({
            ...this.searchParams
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 查看
      detail(row) {
        this.$refs['ledgerDetail'].show(row.id);
      },
      // 使用登记
      showRegister(row) {
        this.$refs.register.show(row);
      },
      // 表格回调
      handleTableDispatch(type, data) {
        switch (type) {
          case 'view':
            return this.detail(data);
          case 'register':
            return this.showRegister(data);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .res-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;

    .res-text {
      font-weight: bold;
    }
  }
</style>
