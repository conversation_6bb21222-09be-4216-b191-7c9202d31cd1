<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
    >
      <el-form-item label="设备编号" prop="code">
        <el-input
          style="width: 100%"
          v-model.trim="form.code"
          placeholder="请输入设备编号"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="设备名称" prop="name">
        <el-input
          style="width: 100%"
          v-model.trim="form.name"
          placeholder="请输入设备名称"
          clearable
          :maxlength="50"
        ></el-input>
      </el-form-item>

      <el-form-item label="使用部门" prop="useDeptName">
        <el-input
          style="width: 100%"
          placeholder="请选择使用部门"
          v-model.trim="form.useDeptName"
          @focus.prevent="onSelectDeptClick"
          readonly
          clearable
        >
          <template slot="append">
            <i
              class="el-icon-circle-close"
              @click="
                () => {
                  form.useDept = undefined;
                  form.useDeptName = undefined;
                }
              "
            ></i>
          </template>
        </el-input>
      </el-form-item>
      <!--      <el-form-item label="RFID卡号" prop="rfid">-->
      <!--        <el-input-->
      <!--          style="width: 100%"-->
      <!--          v-model.trim="form.rfid"-->
      <!--          placeholder="请输入RFID卡号"-->
      <!--          clearable-->
      <!--          :maxlength="50"-->
      <!--        ></el-input>-->
      <!--      </el-form-item>-->
      <el-form-item label="是否贴码" prop="isPasted">
        <el-select
          v-model="form.isPasted"
          placeholder="请选择是否贴码"
          clearable
        >
          <el-option
            v-for="item in systemDicts.type['yes_no']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import DeptDialog from '@/components/dept-dialog/index.vue';
  export default {
    name: 'DeviceListSearch',
    components: { DeptDialog },
    systemDicts: ['yes_no'],
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        form: {
          code: undefined,
          name: undefined,
          useDeptName: undefined,
          useDept: undefined,
          isPasted: undefined
        }
      };
    },
    methods: {
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        console.log('选择部门', dept);
        this.form.useDept = dept.id;
        this.form.useDeptName = dept.deptName;
      },
      reset() {
        this.form.useDept = undefined;
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        this.$emit('search', this.form);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
