<template>
  <div>
    <el-popover
      placement="bottom"
      width="300"
      trigger="click"
      @show="handleNoticeShow"
      @after-leave="handleNoticeHide"
      popper-class="notice_popper"
    >
      <el-tabs v-model="activeName" class="box_tabs">
        <el-tab-pane name="NOTICE" label="通知"> </el-tab-pane>
      </el-tabs>
      <div class="content-wrapper">
        <el-button
          v-if="total > 0"
          type="primary"
          size="mini"
          @click="handleShowOrder"
          >特种设备工单提醒</el-button
        >
        <el-empty v-else description="暂无通知" :image-size="100"></el-empty>
      </div>
      <div slot="reference">
        <i class="el-icon-chat-line-square"></i>
        {{ $t('navbar.notice') }}
        <el-divider direction="vertical"></el-divider>
      </div>
    </el-popover>
    <special-notice ref="specialNotice" />
  </div>
</template>

<script>
  import SpecialNotice from '../special-notice.vue';
  import { getCheckRepairOrderPageApi } from '@/api/equiment-full-life-api/repair';
  import { mapGetters } from 'vuex';

  export default {
    name: 'top-notice',
    components: { SpecialNotice },
    data() {
      return {
        activeName: undefined,
        listData: [],
        total: 0
      };
    },
    computed: {
      ...mapGetters(['hasOverhaulOrderPermission'])
    },
    methods: {
      // 判断是否存在未完成的特种设备检修工单
      async checkSpecialRepairOrder() {
        try {
          const { data } = await getCheckRepairOrderPageApi({
            current: 1,
            size: 10,
            onlyQuerySpecialType: true,
            statuses: '1,3,5,6'
          });
          this.listData = data.data.records || [];
          this.total = data.data.total;
        } catch (e) {
          console.error(e);
        }
      },
      // 展示特种设备工单提醒弹窗
      handleShowOrder() {
        this.$refs.specialNotice.show(this.listData, this.total);
      },
      // 弹出框显示回调
      handleNoticeShow() {
        this.activeName = 'NOTICE';
        this.hasOverhaulOrderPermission && this.checkSpecialRepairOrder();
      },
      // 弹出框隐藏回调
      handleNoticeHide() {
        this.activeName = undefined;
        this.listData = [];
        this.total = 0;
      }
    }
  };
</script>

<style lang="scss">
  .notice_popper {
    padding: 0;

    .box_tabs {
      .el-tabs__header {
        margin: 0;

        .el-tabs__nav {
          transform: translateX(24px) !important;

          .el-tabs__item {
            height: 50px;
            line-height: 50px;
          }
        }
      }
    }

    .content-wrapper {
      height: 260px;
      padding: 15px;
    }
  }
</style>

<style lang="scss" scoped>
  .el-popover__reference,
  .el-popover__reference:hover {
    color: unset !important;
  }
</style>
