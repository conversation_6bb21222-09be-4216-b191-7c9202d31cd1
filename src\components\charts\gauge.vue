<template>
  <div :style="customStyle" :id="chartId"></div>
</template>
<script>
  import * as echarts from 'echarts';

  export default {
    name: 'charts-gauge',
    props: {
      height: {
        type: String,
        default: '100%'
      },
      title: {
        type: String,
        default: ''
      },
      itemStyle: {
        type: Object,
        default: () => {
          return {
            color: '#FFAB91'
          };
        }
      }
    },
    data() {
      return {
        chartId: Math.random(),
        value: 0
      };
    },
    mounted() {
      // this.init();
    },
    computed: {
      customStyle() {
        return {
          height: this.height
        };
      }
    },
    methods: {
      setChartData(val, option = {}) {
        this.value = val;
        this.init({
          itemStyle: option.itemStyle || this.itemStyle
        });
      },
      init({ itemStyle }) {
        const chartDom = document.getElementById(this.chartId);
        const myChart = echarts.init(chartDom);
        var option = {
          title: {
            text: this.title, // 标题文本
            left: 'center', // 标题位置
            top: 'bottom', // 标题的顶部位置，与进度条对齐
            textStyle: {
              color: '#555', // 标题颜色
              fontSize: 14 // 标题字体大小
            }
          },
          series: [
            {
              type: 'gauge',
              radius: '120%',
              center: ['50%', '70%'],
              startAngle: 180,
              endAngle: 0,
              min: 0,
              max: 100,
              splitNumber: 0,
              itemStyle: itemStyle,
              progress: {
                show: true,
                width: 15
              },
              pointer: {
                show: false
              },
              axisLine: {
                lineStyle: {
                  width: 15
                }
              },
              axisTick: {
                distance: 0,
                splitNumber: 5,
                lineStyle: {
                  width: 2,
                  color: '#999'
                }
              },
              splitLine: {
                show: false
              },
              axisLabel: {
                show: false
              },
              anchor: {
                show: false
              },
              title: {
                show: false
              },
              detail: {
                valueAnimation: true,
                width: '60%',
                lineHeight: 12,
                borderRadius: 8,
                offsetCenter: [0, '-15%'],
                fontSize: 12,
                fontWeight: 'bolder',
                formatter: '{value} %',
                color: 'inherit'
              },
              data: [
                {
                  value: this.value
                }
              ]
            }
          ]
        };

        myChart.setOption(option);
      }
    }
  };
</script>
