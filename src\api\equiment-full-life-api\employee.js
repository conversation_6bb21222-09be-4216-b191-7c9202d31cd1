import request from '@/router/axios';

/**
 * 人员表接口
 * @param url 接口地址
 */
// 分页列表
export const userListPage = (params) => {
  return request({
    url: '/api/szyk-simas/staff/page',
    method: 'get',
    params
  });
};

// 详情
export const userDetail = (params) => {
  return request({
    url: '/api/szyk-simas/staff/detail',
    method: 'get',
    params
  });
};

// 新增或修改
export const userSave = (data) => {
  return request({
    url: '/api/szyk-simas/staff/submit',
    method: 'post',
    data
  });
};

// 删除接口
export const userDelete = (params) => {
  return request({
    url: '/api/szyk-simas/staff/remove',
    method: 'post',
    params
  });
};

// 导出人员接口
export const userExport = (params) => {
  return request({
    url: '/api/szyk-simas/staff/export-staff',
    method: 'get',
    params
  });
};

// 所属岗位下拉列表
export const postList = () => {
  return request({
    url: '/api/szyk-system/post/select',
    method: 'get'
  });
};
