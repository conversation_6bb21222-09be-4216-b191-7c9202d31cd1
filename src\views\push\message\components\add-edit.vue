<template>
  <div>
    <el-dialog
      width="900px"
      append-to-body
      :close-on-click-modal="viewMode"
      :visible.sync="open"
      :title="dialogTitle"
    >
      <el-form
        v-loading="loading"
        :model="form"
        :rules="rules"
        ref="msgPushForm"
        label-width="110px"
        label-suffix=": "
        size="mini"
        :disabled="viewMode"
      >
        <el-form-item label="推送标题" prop="title">
          <el-input
            v-model="form.title"
            maxlength="20"
            show-word-limit
            placeholder="请输入推送标题"
            autocomplete="off"
            style="width: 460px"
          ></el-input>
        </el-form-item>
        <el-form-item label="推送内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            rows="4"
            maxlength="500"
            show-word-limit
            placeholder="请输入推送内容"
            autocomplete="off"
            style="width: 460px"
          ></el-input>
        </el-form-item>
        <el-form-item label="推送时间" prop="scheduleTime">
          <el-radio-group v-model="form.isImmediate">
            <el-radio :label="2">立即推送</el-radio>
            <el-radio :label="1">定时推送</el-radio>
          </el-radio-group>
          <el-date-picker
            v-show="form.isImmediate === 1"
            v-model="form.scheduleTime"
            style="margin-left: 12px"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择定时推送时间"
            autocomplete="off"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="推送方式" prop="type">
          <el-checkbox-group v-model="form.type">
            <el-checkbox label="IN_APP">应用内信息</el-checkbox>
            <el-checkbox label="PUSH">PUSH</el-checkbox>
            <el-checkbox label="SMS">短信</el-checkbox>
            <el-checkbox label="EMAIL">邮件</el-checkbox>
            <el-checkbox label="POP_UP">弹窗浮层</el-checkbox>
            <el-checkbox label="WORK_TODO">待办</el-checkbox>
            <el-checkbox label="NOTICE">通知</el-checkbox>
            <el-checkbox label="WARNING">预警</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="推送渠道" prop="channel">
          <el-checkbox-group v-model="form.channel">
            <el-checkbox label="PC">PC</el-checkbox>
            <el-checkbox label="APP">APP</el-checkbox>
            <el-checkbox label="MINI_PROGRAM">小程序</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="推送用户类型" prop="receiverType">
          <el-radio-group v-model="form.receiverType">
            <el-radio label="USER">具体用户</el-radio>
            <el-radio label="ROLE">指定角色</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="推送用户"
          prop="userList"
          v-if="form.receiverType === 'USER'"
        >
          <person v-if="!viewMode" v-model="form.userList" />
          <div>
            <el-tag
              class="user_tag"
              v-for="user in form.userList"
              :key="user.id"
              :closable="!viewMode"
              @close="handleDelUser(user.id)"
              >{{ user.realName }}</el-tag
            >
          </div>
        </el-form-item>
        <el-form-item
          label="推送角色"
          prop="roleIds"
          v-if="form.receiverType === 'ROLE'"
        >
          <avue-input-tree
            v-model="form.roleIds"
            type="tree"
            check-strictly
            multiple
            placeholder="请选择所属角色"
            style="width: 460px"
            :dic="roleData"
          ></avue-input-tree>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="open = false" :disabled="btnLoading"
          >取 消</el-button
        >
        <el-button
          v-if="!viewMode"
          :disabled="btnLoading"
          size="small"
          type="primary"
          @click="handleSubmit('msgPushForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { Person } from '@/components/yk-organization-select';
  import { getRoleTree } from '@/api/system/role';
  import { formatTreeData, flatTreeData } from '@/util/util';
  import { getDetail, postMessage } from '@/api/message/message';
  import { mapGetters } from 'vuex';

  export default {
    name: 'MessagePushAddEdit',
    components: { Person },
    props: {
      dialogTitle: {
        type: String,
        default: ''
      },
      formVisible: {
        type: Boolean,
        default: false
      },
      viewMode: {
        type: Boolean,
        default: false
      },
      msgId: {
        type: String,
        require: true
      }
    },
    watch: {
      msgId: {
        handler(val) {
          val && this.getDetail();
        },
        immediate: true
      }
    },
    data() {
      const validateScheduleTime = (rule, value, callback) => {
        if (this.form.isImmediate === 2) {
          callback();
        } else if (!value) {
          callback(new Error('请选择推送时间'));
        } else if (new Date(value).getTime() < Date.now()) {
          callback(new Error('推送时间不得早于当前时间'));
        } else {
          callback();
        }
      };

      return {
        // 表单加载
        loading: false,
        // 按钮加载
        btnLoading: false,
        // 校验规则
        rules: {
          title: [
            { required: true, message: '请输入推送标题', trigger: 'blur' }
          ],
          content: [
            { required: true, message: '请输入推送内容', trigger: 'blur' }
          ],
          scheduleTime: [
            { required: true, validator: validateScheduleTime, trigger: 'blur' }
          ],
          type: [
            { required: true, message: '请选择推送方式', trigger: 'change' }
          ],
          channel: [
            { required: true, message: '请选择推送渠道', trigger: 'change' }
          ],
          receiverType: [
            { required: true, message: '请选择推送用户类型', trigger: 'change' }
          ],
          userList: [
            {
              type: 'array',
              required: true,
              message: '请选择推送用户',
              trigger: 'change'
            }
          ],
          roleIds: [
            { required: true, message: '请选择推送角色', trigger: 'change' }
          ]
        },
        // 角色数据
        roleData: [],
        // 表单映射模型
        form: {
          // 1 - 立即推送, 2 - 定时推送
          isImmediate: 2,
          type: [],
          channel: [],
          receiverType: 'USER',
          roleIds: [],
          userList: []
        },
        // 时间选择器选项
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 86400000;
          }
        }
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      // 初始化角色, 机构, 岗位数据
      async initData(tenantId) {
        try {
          const roleRes = await getRoleTree(tenantId);
          this.roleData = formatTreeData(roleRes.data.data, {
            label: 'title'
          });
        } catch (e) {
          console.error(e);
        }
      },
      // 删除选择用户
      handleDelUser(userId) {
        const index = this.form.userList.findIndex(
          (item) => item.id === userId
        );
        this.form.userList.splice(index, 1);
      },
      // 重置弹窗表单
      resetForm(formName) {
        this.$refs[formName].clearValidate();
      },
      // 获取订单详情
      async getDetail() {
        try {
          this.loading = true;
          const { data = {} } = await getDetail(this.msgId);
          if (data.success) {
            const formData = {
              ...data.data,
              channel: data.data.channel.split(','),
              type: data.data.type.split(',')
            };
            if (formData.receiverType === 'ROLE') {
              formData.roleIds = formData.receiverInfoVo.roleList.map(
                (item) => item.roleId
              );
            } else if (formData.receiverType === 'USER') {
              formData.userList = formData.receiverInfoVo.userList;
            }
            this.form = formData;
          }
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      // 选择用户
      handleSelectUser() {
        console.log('user');
      },
      // 提交表单
      handleSubmit(formName) {
        // 表单验证
        this.$refs[formName].validate(async (valid) => {
          if (valid) {
            try {
              this.btnLoading = true;
              // 判断推送用户类型
              let receiverInfoVo = {};
              if (this.form.receiverType === 'USER') {
                receiverInfoVo.userList = this.form.userList;
              } else if (this.form.receiverType === 'ROLE') {
                receiverInfoVo.roleList = this.form.roleIds.map((item) => {
                  const role = flatTreeData(this.roleData).find(
                    (r) => r.value === item
                  );
                  return {
                    roleId: role.value,
                    roleName: role.label
                  };
                });
              }
              const messageForm = {
                ...this.form,
                receiverInfoVo,
                sender: this.userInfo.nick_name,
                channel: this.form.channel.join(','),
                type: this.form.type.join(',')
              };
              const { data } = await postMessage(messageForm);
              if (data.success) {
                this.open = false;
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                });
                this.$emit('refresh');
              }
            } catch (e) {
              console.error(e);
            } finally {
              this.btnLoading = false;
            }
          }
        });
      }
    },
    computed: {
      ...mapGetters(['userInfo']),
      open: {
        get() {
          return this.formVisible;
        },
        set() {
          this.form.isImmediate = 2;
          this.$refs.msgPushForm.resetFields();
          this.form = {
            // 1 - 立即推送, 2 - 定时推送
            isImmediate: 2,
            type: [],
            channel: [],
            receiverType: 'USER',
            roleIds: []
          };
          console.log(this.form);
          this.$emit('close');
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .user_tag {
    margin-top: 10px;
    margin-right: 10px;
  }

  .dialog-footer {
    .el-button + .el-button {
      margin-left: 10px !important;
    }
  }
</style>
