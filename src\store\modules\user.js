import { removeToken, removeRefreshToken } from '@/util/auth';
import { Message } from 'element-ui';
import { setStore, getStore } from '@/util/store';
import { getToken, setToken } from '@/util/auth';
import { isURL, validatenull } from '@/util/validate';
import { deepClone } from '@/util/util';
import website from '@/config/website';
import {
  loginByUsername,
  loginBySocial,
  getUserInfo,
  logout,
  refreshToken,
  getButtons
} from '@/api/user';
import { getRoutes } from '@/api/system/menu';
import md5 from 'js-md5';

function addPath(ele, first, type = '') {
  // 在这里加上不同系统的前缀 在刷新的时候 根据前缀判断是哪个系统
  // ele.path = `${type}` + ele.path;
  // ele.activeMenu = ele.activeMenu ? `${type}` + ele.activeMenu : '';
  const menu = website.menu;
  const propsConfig = menu.props;
  const propsDefault = {
    label: propsConfig.label || 'name',
    path: propsConfig.path || 'path',
    icon: propsConfig.icon || 'icon',
    children: propsConfig.children || 'children'
  };
  const icon = ele[propsDefault.icon];
  ele[propsDefault.icon] = validatenull(icon) ? menu.iconDefault : icon;
  const isChild =
    ele[propsDefault.children] && ele[propsDefault.children].length !== 0;
  if (!isChild) ele[propsDefault.children] = [];
  if (!isChild && first && !isURL(ele[propsDefault.path])) {
    ele[propsDefault.path] = ele[propsDefault.path] + '/index';
  } else {
    ele[propsDefault.children].forEach((child) => {
      addPath(child, false, type);
    });
  }
}

const user = {
  state: {
    tenantId: getStore({ name: 'tenantId' }) || '',
    userInfo: getStore({ name: 'userInfo' }) || [],
    permission: getStore({ name: 'permission' }) || {},
    roles: getStore({ name: 'userRoles' }) || [],
    menuId: {},
    topMenu: [],
    menu: [],
    menuAll: [],
    token: getToken() || '',
    refreshToken: getStore({ name: 'refreshToken' }) || '',
    topOrgId: getStore({ name: 'topOrgId' }) || ''
  },
  actions: {
    //根据用户名登录
    LoginByUsername({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        loginByUsername(
          userInfo.tenantId,
          userInfo.tenantName,
          userInfo.deptId,
          userInfo.roleId,
          userInfo.username,
          md5(userInfo.password),
          userInfo.type,
          userInfo.key,
          userInfo.code
        )
          .then((res) => {
            const data = res.data;
            if (data.error_description) {
              Message({
                message: data.error_description,
                type: 'error'
              });
            } else {
              commit('SET_TOKEN', data.access_token);
              commit('SET_REFRESH_TOKEN', data.refresh_token);
              commit('SET_TENANT_ID', data.tenant_id);
              commit('SET_USER_INFO', data);
              commit('DEL_ALL_TAG');
              commit('CLEAR_LOCK');
            }
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    //根据手机号登录
    LoginByPhone({ commit }, userInfo) {
      return new Promise((resolve) => {
        loginByUsername(userInfo.phone, userInfo.code).then((res) => {
          const data = res.data.data;
          commit('SET_TOKEN', data);
          commit('DEL_ALL_TAG');
          commit('CLEAR_LOCK');
          resolve();
        });
      });
    },
    //根据第三方信息登录
    LoginBySocial({ commit }, userInfo) {
      return new Promise((resolve) => {
        loginBySocial(
          userInfo.tenantId,
          userInfo.source,
          userInfo.code,
          userInfo.state
        ).then((res) => {
          const data = res.data;
          if (data.error_description) {
            Message({
              message: data.error_description,
              type: 'error'
            });
          } else {
            commit('SET_TOKEN', data.access_token);
            commit('SET_REFRESH_TOKEN', data.refresh_token);
            commit('SET_USER_INFO', data);
            commit('DEL_ALL_TAG');
            commit('CLEAR_LOCK');
          }
          resolve();
        });
      });
    },
    //获取用户信息
    GetUserInfo({ commit }) {
      return new Promise((resolve, reject) => {
        getUserInfo()
          .then((res) => {
            const data = res.data.data;
            commit('SET_ROLES', data.roles);
            resolve(data);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    //刷新token
    refreshToken({ state, commit }, userInfo) {
      window.console.log('handle refresh token');
      return new Promise((resolve, reject) => {
        refreshToken(
          state.refreshToken,
          state.tenantId,
          !validatenull(userInfo) ? userInfo.deptId : state.userInfo.dept_id,
          !validatenull(userInfo) ? userInfo.roleId : state.userInfo.role_id
        )
          .then((res) => {
            const data = res.data;
            commit('SET_TOKEN', data.access_token);
            commit('SET_REFRESH_TOKEN', data.refresh_token);
            commit('SET_USER_INFO', data);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 登出
    LogOut({ commit }) {
      return new Promise((resolve, reject) => {
        logout()
          .then(() => {
            commit('SET_TOKEN', '');
            commit('SET_MENU', []);
            commit('SET_TOP_ORG_ID', '');
            commit('SET_MENU_ALL_NULL', []);
            commit('SET_TOP_MENU_NULL', []);
            commit('SET_ROLES', []);
            commit('SET_TAG_LIST', []);
            commit('DEL_ALL_TAG');
            commit('CLEAR_LOCK');
            removeToken();
            removeRefreshToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    //注销session
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '');
        commit('SET_MENU', []);
        commit('SET_TOP_ORG_ID', '');
        commit('SET_MENU_ALL_NULL', []);
        commit('SET_TOP_MENU_NULL', []);
        commit('SET_ROLES', []);
        commit('SET_TAG_LIST', []);
        commit('DEL_ALL_TAG');
        commit('CLEAR_LOCK');
        removeToken();
        removeRefreshToken();
        resolve();
      });
    },
    //获取顶部菜单
    GetTopMenu() {
      return new Promise((resolve) => {
        resolve([]);
        // getRoleTopMenu().then((res) => {
        //   const data = res.data.data || [];
        //   commit('SET_TOP_MENU', data);
        //   resolve(data);
        // });
      });
    }, //获取系统菜单

    GetMenu({ commit, dispatch }, topMenuId) {
      return new Promise((resolve) => {
        getRoutes(topMenuId).then((res) => {
          const data = res.data.data;
          let menu;
          if (!data || JSON.stringify(data) === '{}') {
            menu = [];
          } else {
            menu = deepClone(data);
          }
          menu.forEach((ele) => {
            addPath(ele, true);
          });
          commit('SET_MENU_ALL', menu);
          commit('SET_MENU', menu);
          dispatch('GetButtons');
          resolve(menu);
        });
      });
    },
    //获取系统按钮
    GetButtons({ commit }, type) {
      return new Promise((resolve) => {
        getButtons(type).then((res) => {
          const data = res.data.data;
          commit('SET_PERMISSION', data);
          resolve();
        });
      });
    }
    // // 获取顶层组织id
    // GetTopOrgId({ commit }) {
    //   return new Promise((resolve) => {
    //     getTopId().then((res) => {
    //       const data = res.data.data.id;
    //       commit('SET_TOP_ORG_ID', data);
    //       resolve();
    //     });
    //   });
    // }
  },
  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
      setToken(state.token);
    },
    SET_MENU_ID(state, menuId) {
      state.menuId = menuId;
    },
    SET_MENU_ALL: (state, menuAll) => {
      // let menu = state.menuAll;
      // menuAll.forEach((ele) => {
      //   if (
      //     !menu.find(
      //       (item) => item.label === ele.label && item.path === ele.path
      //     )
      //   ) {
      //     menu.push(ele);
      //   }
      // });
      // state.menuAll = menu;
      state.menuAll = menuAll;
      // setStore({ name: 'menuAll', content: state.menuAll });
    },
    SET_MENU_ALL_NULL: (state) => {
      state.menuAll = [];
      // setStore({ name: 'menuAll', content: state.menuAll });
    },
    SET_TOP_MENU: (state, menu) => {
      state.topMenu = menu;
      // setStore({ name: 'menu', content: state.menu });
    },
    SET_TOP_MENU_NULL: (state) => {
      state.topMenu = [];
      // setStore({ name: 'menu', content: state.menu });
    },
    SET_MENU: (state, menu) => {
      state.menu = menu;
      // setStore({ name: 'menu', content: state.menu });
    },
    SET_TOP_ORG_ID: (state, id) => {
      state.topOrgId = id;
      setStore({ name: 'topOrgId', content: state.topOrgId });
    },
    SET_REFRESH_TOKEN: (state, refreshToken) => {
      state.refreshToken = refreshToken;
      setStore({ name: 'refreshToken', content: state.refreshToken });
    },
    SET_TENANT_ID: (state, tenantId) => {
      state.tenantId = tenantId;
      setStore({ name: 'tenantId', content: state.tenantId });
    },
    SET_USER_INFO: (state, userInfo) => {
      if (validatenull(userInfo.avatar)) {
        userInfo.avatar = '/img/bg/img-logo.png';
      }
      state.userInfo = userInfo;
      // 设置用户角色
      const roles = userInfo.role_name.split(',');
      state.roles = roles;
      setStore({ name: 'userInfo', content: state.userInfo });
      setStore({ name: 'userRoles', content: state.roles });
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMISSION: (state, permission) => {
      let result = [];

      function getCode(list) {
        list.forEach((ele) => {
          if (typeof ele === 'object') {
            const chiildren = ele.children;
            const code = ele.code;
            if (chiildren) {
              getCode(chiildren);
            } else {
              result.push(code);
            }
          }
        });
      }

      getCode(permission);
      state.permission = {};
      result.forEach((ele) => {
        state.permission[ele] = true;
      });
      setStore({ name: 'permission', content: state.permission });
    }
  }
};
export default user;
