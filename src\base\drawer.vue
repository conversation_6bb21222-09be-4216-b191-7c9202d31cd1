<template>
  <el-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    :visible.sync="currentVisible"
    :direction="direction"
    :size="size"
    :fullscreen="fullscreen"
    :destroy-on-close="destoryEle"
    @close="closed"
  >
    <div slot="title">
      {{ title }}
      <i @click="zoScreen" v-if="classNameBool" class="el-icon-full-screen"></i>
      <i @click="zoScreen" v-else class="el-dialog__close el-icon-news"></i>
      <div class="drawer_header_tip">
        {{ tip }}
      </div>
    </div>
    <div class="drawer_body">
      <slot></slot>
    </div>
  </el-drawer>
</template>

<script>
  export default {
    name: 'dialogDrawer',
    components: {},
    props: {
      // 弹窗标题
      title: {
        type: String,
        default: () => {
          return '标题';
        }
      },
      tip: {
        type: String,
        default: () => {
          return '';
        }
      },
      visible: {
        type: Boolean,
        required: true,
        default: () => {
          return false;
        }
      },
      // 抽屉的方向
      direction: {
        type: String,
        default: () => {
          return 'rtl';
        }
      },
      // 尺寸
      size: {
        type: String,
        default: () => {
          return '60%';
        }
      },
      // 控制是否在关闭抽屉之后将元素全部销毁
      destoryEle: {
        type: Boolean,
        default: () => {
          return false;
        }
      }
    },
    data() {
      return {
        currentVisible: false,
        fullscreen: false,
        classNameBool: true // 展示哪个类名bool
      };
    },
    watch: {
      visible: {
        handler(val) {
          this.currentVisible = val;
        },
        immediate: true
      },
      fullscreen(val) {
        if (val) {
          this.size = '100%';
          this.$emit('update:spanVal', 6);
          this.$emit('update:dialogWidth', 5); // 弹窗实际宽度
        } else {
          this.size = '1000px';
          this.$emit('update:spanVal', 12);
          this.$emit('update:dialogWidth', 3); // 弹窗实际宽度
        }
      }
    },

    methods: {
      closed() {
        this.$emit('update:visible', false);
        this.$emit('closed');
      },
      // beforeClose() {
      //   this.$emit('beforeClose')
      // },
      // 点击放大缩小屏幕按钮
      zoScreen() {
        this.classNameBool = !this.classNameBool;
        this.fullscreen = !this.fullscreen;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .drawer_body {
    //height: 100% !important;
    padding: 10px 20px;
  }

  /deep/.el-drawer__header {
    padding-bottom: 10px;
    color: rgba(0, 0, 0, 85%);
    font-weight: 500;
    font-size: 18px;
    word-wrap: break-word;

    //color: #909399;
    border-bottom: 1px solid #ebeef5;

    .drawer_header_tip {
      display: inline-block;
      margin-left: 5px;
      color: #f59a23;
      font-size: 14px;
    }

    div > i {
      float: right;
      margin-top: 4px;
      margin-right: 20px;
      cursor: pointer;
    }
  }

  /deep/.el-drawer__body {
    //padding: 30px 10px 60px 30px;
  }
</style>
