<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <base-info
        ref="info"
        :initData="detail"
        :whorehouse="whorehouse"
        @outTypeChange="outTypeChange"
        @selectedRowParent="selectedRowParent"
        @warehouseId="getWarehouse"
      ></base-info>
      <p class="el-base-title">备件列表</p>
      <sel-asset
        ref="asset"
        :detail="detail"
        :outType="outType"
        :receiveList="receiveDetail"
        :warehouseId="warehouseId"
      ></sel-asset>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="close" @click="closed" :loading="loading"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import BaseInfo from './base-info.vue';
  import SelAsset from './sel-asset.vue';
  import {
    addSparePartsOutApi,
    editSparePartsOutApi,
    getCheckRepairTypeListApi,
    getSparePartsOutViewApi,
    getSparePartsReceiveViewApi
  } from '@/api/equiment-full-life-api/spare-parts';
  import { mapGetters } from 'vuex';
  export default {
    name: 'AddDevice',
    components: {
      BaseInfo,
      SelAsset
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        receiveDetail: {}, // 领用单详情数据
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {},
        whorehouse: [],
        outType: '1',
        warehouseId: undefined
      };
    },
    watch: {},
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods: {
      //  拿到入库仓库的id
      getWarehouse(e) {
        this.warehouseId = e;
        this.$nextTick(() => {
          this.$refs['asset'].form.list = [];
        });
      },
      //  请用单传回来的数据
      selectedRowParent(row) {
        //   根据请用单返回的数据，找到请用单的详情
        if (row) {
          this.getReceiveDetail(row.id);
        } else {
          this.receiveDetail = {};
        }
      },
      //  获取领用单详情数据
      async getReceiveDetail(id) {
        this.loading = true;
        try {
          const res = await getSparePartsReceiveViewApi(id);
          this.receiveDetail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      // 请领相关 -  领用单列表、领用单详情
      //  出库类型改变
      outTypeChange(e) {
        this.outType = e;
      },
      //  获取库放列表
      async getStoreHouseList() {
        let res = await getCheckRepairTypeListApi({
          manager: this.userInfo.user_id
        });
        this.whorehouse = res.data.data;
      },
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getSparePartsOutViewApi(id);
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      show(row) {
        this.visible = true;
        this.edit = !!row.id;
        this.eqId = row.id;
        this.getStoreHouseList();
        if (row.id) {
          this.getDetail(row.id);
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.warehouseId = undefined;
        this.receiveDetail = {};
        this.$refs['info'].resetForm();
        this.$refs['asset'].resetForm();
        this.detail = {};
        this.visible = false;
      },

      async submit() {
        let params = await this.$refs['info'].validForm();
        console.log(params);
        let asset = await this.$refs['asset'].validForm();
        console.log(params, asset);
        if (params && asset) {
          this.edit
            ? await this.update({
                ...params,
                itemList: asset,
                id: this.edit ? this.eqId : undefined
              })
            : await this.save({
                ...params,
                itemList: asset
              });
        }
      },
      async update(params) {
        this.loading = true;
        try {
          await editSparePartsOutApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 提交
      async save(params) {
        this.loading = true;
        try {
          await addSparePartsOutApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
