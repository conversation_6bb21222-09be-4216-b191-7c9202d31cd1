<template>
  <div class="num">
    <el-form
      :model="form"
      inline
      label-suffix="："
      ref="baseForm"
      label-position="right"
      size="small"
    >
      <el-row
        class="add-info"
        :gutter="20"
        v-for="(item, idx) in form.list"
        :key="idx"
      >
        <el-col :span="11">
          <el-form-item
            class="nums"
            :label="`${idx + 1}班开始时间`"
            label-width="120px"
            :prop="'list.' + idx + '.startTime'"
            :rules="{
              required: true,
              message: '请选择开始',
              trigger: ['change']
            }"
          >
            <el-time-picker
              v-model="item.startTime"
              format="HH:mm"
              value-format="HH:mm"
              placeholder="请选择开始时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="11">
          <el-form-item
            class="nums"
            :label="`${idx + 1}班结束时间`"
            label-width="120px"
            :prop="'list.' + idx + '.endTime'"
            :rules="{
              required: true,
              message: '请选择结束时间',
              trigger: ['change']
            }"
          >
            <el-time-picker
              v-model="item.endTime"
              format="HH:mm"
              placeholder="请选择结束时间"
              value-format="HH:mm"
            />
          </el-form-item>
        </el-col>
        <el-col :span="1" v-if="form.list.length > 1">
          <i class="el-icon-remove" @click="del(idx)"> </i>
        </el-col>
      </el-row>
    </el-form>
    <div class="button">
      <el-button
        type="primary"
        @click="add"
        :disabled="this.form.list.length === 10"
        size="small"
      >
        + 继续添加执行时间段
      </el-button>
    </div>
  </div>
</template>

<script>
  import { validateTimeRanges } from '../../util';

  export default {
    name: 'bearingLibraryIndex',
    props: {
      detail: {
        type: Object,
        default: () => {
          return {};
        }
      }
    },

    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              if (this.detail.byDaySet && this.detail.byDaySet.length > 0) {
                this.form.list = this.detail.byDaySet.map((item) => {
                  return {
                    startTime: item.startTime,
                    endTime: item.endTime
                  };
                });
              }
            });
          }
        }
      }
    },
    data() {
      function codeRepeat(all, val) {
        // 两种判重逻辑 1、本地判重 2、接口返回判重
        let codeArr = all.map((it) => it.teamRank);
        let first = codeArr.indexOf(val);
        let last = codeArr.lastIndexOf(val);

        return first !== last;
      }
      return {
        repeatRule: [
          {
            required: true,
            message: '请输入班次名称',
            trigger: 'blur'
          },
          {
            validator: (rule, value, callback) => {
              // 如果是null 空字符串
              if (!value) {
                callback();
              } else if (codeRepeat(this.form.list, value)) {
                callback(new Error('名称不能重复!'));
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        form: {
          list: [{ startTime: undefined, endTime: undefined }]
        },
        obj: {
          startTime: undefined,
          endTime: undefined
        }
      };
    },

    mounted() {},
    methods: {
      add() {
        this.form.list.push({ ...this.obj });
      },
      async validForm() {
        if (this.form.list.length === 0) {
          this.$message.warning('请添加时间');
          return false;
        }
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          let list = this.form.list.map((item) => {
            return {
              ...item
              // startTime: item.time[0],
              // endTime: item.time[1]
            };
            // 校验
          });
          let bool = validateTimeRanges(list);
          console.log('bool', bool);
          if (bool) {
            return list;
          } else {
            this.$message.warning('工作时间设置超过1天');
            return false;
          }
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs['baseForm'].resetFields();
        this.form.list = [{ time: undefined }];
      },
      del(idx) {
        this.form.list.splice(idx, 1);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .button {
    display: flex;
    justify-content: center;
  }

  :deep(.el-form-item) {
    width: 100% !important;
    margin-bottom: 0 !important;
  }

  .el-col {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .el-row {
    margin-bottom: 15px;
  }

  .el-icon-remove {
    margin-top: 5px;
    color: red;
    font-size: 20px;
    cursor: pointer;
  }
</style>
