<template>
  <basic-container>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="true">
      <el-form-item label="名称" prop="deptName">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row>
      <el-col :span="24">
        <div class="tool-box">
          <el-button
            type="primary"
            icon="el-icon-circle-plus-outline"
            size="small"
            @click="handleAdd"
            >新增</el-button
          >
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="small"
            @click="handleMultiDelete"
            >批量删除</el-button
          >
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-table
        :data="data"
        @selection-change="selectChange"
        style="width: 100%"
      >
        <el-table-column prop="id" type="selection" width="55">
        </el-table-column>
        <el-table-column prop="name" label="名称" width="180">
        </el-table-column>
        <el-table-column prop="description" label="描述"> </el-table-column>
        <el-table-column prop="remark" label="备注"> </el-table-column>
        <el-table-column prop="createTime" label="创建日期" width="180">
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="250">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type=""
              plain
              v-if="permission.dict_delete11"
              @click="handleView(scope.$index, scope.row)"
              >查看
            </el-button>
            <el-button
              size="mini"
              type="primary"
              plain
              v-hasPermi="['notice_add', 'notice_view']"
              @click="handleEdit(scope.$index, scope.row)"
              >编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
    <el-row>
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChange"
        @current-change="currentChange"
        :total="page.total"
      >
      </el-pagination>
    </el-row>
    <el-dialog
      append-to-body
      :title="dialogTitle"
      width="600px"
      :visible.sync="formVisible"
      @close="resetForm('productForm')"
    >
      <el-form :model="product" :rules="rules" ref="productForm">
        <el-form-item label="名称" prop="name" label-width="55px">
          <el-input v-model="product.name" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="介绍" label-width="55px">
          <el-input
            v-model="product.description"
            autocomplete="off"
            type="textarea"
            :rows="5"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" label-width="55px">
          <el-input
            v-model="product.remark"
            autocomplete="off"
            type="textarea"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formVisible = false">取 消</el-button>
        <el-button
          v-if="!viewMode"
          type="primary"
          @click="submitproduct('productForm')"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import { getPage, getDetail, save, remove } from '@/api/demo/product';

  import { mapGetters } from 'vuex';

  export default {
    data() {
      return {
        // 是否显示
        formVisible: false,
        // 是否查看
        viewMode: false,
        // dialog标题
        dialogTitle: '',
        // 列表数据
        data: [],
        // 选中的数据
        multiSelection: [],
        // 分页数据
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        // 校验规则
        rules: {
          title: [{ required: true, message: '请输入标题', trigger: 'blur' }]
        },
        // 表单映射模型
        product: {
          id: '',
          name: '',
          remark: '',
          description: ''
        },
        // 查询参数
        queryParams: {
          name: undefined
        }
      };
    },
    created() {
      this.onLoad();
    },
    computed: {
      ...mapGetters(['permission']),

      // permissionList() {
      //   return {
      //     addBtn: this.vaildData(this.permission.notice_add, false),
      //     viewBtn: this.vaildData(this.permission.notice_view, false),
      //     delBtn: this.vaildData(this.permission.notice_delete, false),
      //     editBtn: this.vaildData(this.permission.notice_edit, false)
      //   };
      // },
      ids() {
        let ids = [];
        this.multiSelection.forEach((ele) => {
          ids.push(ele.id);
        });
        return ids.join(',');
      }
    },
    methods: {
      /** 重置按钮操作 */
      resetQuery() {
        this.resetForm('queryForm');
        this.onLoad();
      },
      handleQuery() {
        this.onLoad();
      },
      onLoad() {
        getPage(
          this.page.currentPage,
          this.page.pageSize,
          this.queryParams
        ).then((res) => {
          this.data = res.data.data.records;
          this.page.total = res.data.data.total;
        });
      },
      selectChange(val) {
        this.multiSelection = val;
        console.log(this.multiSelection);
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      resetForm(formName) {
        this.$refs[formName].clearValidate();
      },
      handleAdd() {
        console.log('######', this.permission);
        this.dialogTitle = '新增';
        this.formVisible = true;
        this.viewMode = false;
        console.log('add');
      },
      handleView(index, row) {
        this.dialogTitle = '查看';
        this.formVisible = true;
        this.viewMode = true;
        getDetail(row.id).then((res) => {
          if (res.data.success) {
            this.product = res.data.data;
          }
        });
        console.log('view');
        console.log(index);
        console.log(row);
      },
      handleEdit(index, row) {
        this.dialogTitle = '修改';
        this.formVisible = true;
        this.viewMode = false;
        getDetail(row.id).then((res) => {
          if (res.data.success) {
            this.product = res.data.data;
          }
        });
        console.log('edit');
        console.log(index);
        console.log(row);
      },
      handleDelete(index, row) {
        this.$confirm('确定删除选择的数据吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          remove(row.id).then((res) => {
            if (res.data.success) {
              this.onLoad();
              this.$message({
                type: 'success',
                message: '操作成功！'
              });
            } else {
              this.$message({
                type: 'error',
                message: res.data.msg
              });
            }
          });
        });
        console.log('delete');
        console.log(index);
        console.log(row);
      },
      handleMultiDelete() {
        console.log('multi-delete');
        if (this.multiSelection.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.$confirm('确定删除选择的数据吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          remove(this.ids).then((res) => {
            if (res.data.success) {
              this.onLoad();
              this.$message({
                type: 'success',
                message: '操作成功！'
              });
            } else {
              this.$message({
                type: 'error',
                message: res.data.msg
              });
            }
          });
        });
      },
      submitproduct(formName) {
        // 表单验证
        this.$refs[formName].validate((valid) => {
          if (valid) {
            console.log(this.product);
            save(this.product).then((res) => {
              if (res.data.success) {
                this.formVisible = false;
                this.onLoad();
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                });
              } else {
                this.$message({
                  type: 'error',
                  message: res.data.msg
                });
              }
            });
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .el-pagination {
    margin-top: 20px;
  }
</style>
