<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
      size="small"
    >
      <el-descriptions-item label="计划单号：">{{
        details.no || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="盘点名称：">{{
        details.name || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="制定人员：">{{
        details.createUserName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="盘点日期："
        >{{ details.startDate || '-' }} ~
        {{ details.endDate || '-' }}</el-descriptions-item
      >
      <el-descriptions-item label="完成时间：">{{
        details.completeTime || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="盘点部门：">{{
        details.inventoryDeptName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="已盘/全部："
        >{{ details.inventory }}/{{
          details.totalQuantity
        }}</el-descriptions-item
      >

      <el-descriptions-item label="状态：">{{
        details.statusName || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="备注：">{{
        details.remark || '-'
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }
</style>
