<template>
  <basic-container :autoHeight="true">
    <!-- <search ref="search" @query="search"></search> -->
    <el-button
      type="primary"
      size="small"
      style="margin-bottom: 20px"
      class="el-icon-plus"
      @click="$emit('dispatch', 'add')"
      >新增</el-button
    >
    <el-table
      size="small"
      :data="list"
      border
      v-loading="loading"
      height="calc(100% - 40px)"
    >
      <el-table-column
        type="index"
        align="center"
        label="#"
        width="50"
      ></el-table-column>
      <el-table-column
        prop="attrName"
        label="字段名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="attrTypeName"
        label="字段类型"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="是否必填" align="center"
        ><template slot-scope="{ row }">{{
          row.isRequire ? '是' : '否'
        }}</template>
      </el-table-column>
      <el-table-column label="待选值" align="center"
        ><template slot-scope="{ row }">
          <el-tag
            class="latency-time"
            v-for="item in row.selectData"
            :key="item.id"
            size="mini"
            type="success"
            >{{ item.label }}</el-tag
          >
          <span v-if="!row.selectData.length">--</span>
        </template>
      </el-table-column>
      <el-table-column label="精度" align="center"
        ><template slot-scope="{ row }">{{
          row.attrType === '2' ? row.precision : '--'
        }}</template>
      </el-table-column>
      <el-table-column label="日期类型" align="center"
        ><template slot-scope="{ row }">{{
          row.attrType === '3' ? row.dataType : '--'
        }}</template>
      </el-table-column>
      <el-table-column label="字段长度" align="center"
        ><template slot-scope="{ row }">{{
          row.attrType === '4' ? row.fieldLength : '--'
        }}</template>
      </el-table-column>

      <el-table-column
        prop="model"
        label="操作"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="text"
            @click="$emit('dispatch', 'edit', row)"
            >编辑</el-button
          >
          <el-popconfirm
            title="确定删除吗？"
            @confirm="() => $emit('dispatch', 'del', row)"
          >
            <el-button
              link
              size="small"
              type="text"
              slot="reference"
              style="margin-left: 10px"
              >删除
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    /> -->

    <point-dialog ref="pointDialog"></point-dialog>
  </basic-container>
</template>

<script>
  // import Search from './search';
  import PointDialog from '../operate/device-info.vue';
  import { ledgerExpandList } from '@/api/equiment-full-life-api/classification';
  export default {
    name: 'bearingLibraryIndex',
    components: { PointDialog },
    props: {
      locationId: {
        // 设备id
        type: String,
        default: () => {
          return '';
        }
      },
      // 因为部位绑定轴承和齿数 和 波形绑定算法前面相同，只是配置页面不同，所以公用一套
      where: {
        type: String,
        default: () => {
          return 'position-bearing';
        }
      }
    },
    data() {
      return {
        params: {},
        nodeId: undefined,
        isShowBtn: false,
        searchParams: {
          size: 10,
          current: 1
        },
        total: 0,
        loading: false,
        list: []
      };
    },
    watch: {
      locationId: {
        handler(val) {
          this.isShowBtn = !!val;
          if (val) {
            this.nodeId = val;
            this.getList();
          }
        }
      }
    },
    mounted() {},
    methods: {
      // 點擊搜索
      search(params) {
        this.params = params;
        this.getList();
      },
      // 配置轴承、齿数信息
      configuration(row) {
        console.log('row', row);
        this.$refs['pointDialog'].show(row.id);
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          // ...this.params, ...this.searchParams
          let res = await ledgerExpandList({
            id: this.locationId
          });
          let list = res.data.data;
          list.forEach((el, index) => {
            el.sort = index;
            el.selectData = JSON.parse(el.selectData);
          });
          this.list = list;
          this.loading = false;
        } catch (message) {
          this.loading = false;
          console.log('err', message);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  ::v-deep {
    .el-scrollbar__view {
      height: 98% !important;
    }
  }
</style>
