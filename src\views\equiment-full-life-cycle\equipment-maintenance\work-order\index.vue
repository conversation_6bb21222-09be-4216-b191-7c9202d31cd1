<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-if="permission['maintenance-order-batchAudit']"
        type="primary"
        size="small"
        @click="handleBatchAudit('agree')"
        >批量审核确认</el-button
      >
      <el-button
        v-if="permission['maintenance-order-batchAudit']"
        type="danger"
        size="small"
        @click="handleBatchAudit('refuse')"
        >批量驳回</el-button
      >
      <!-- <el-button
        v-if="permission['maintenance-order-export']"
        icon="el-icon-upload2"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :key="tableKey"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 150px)"
      border
      stripe
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        :selectable="(row) => row.status === 5"
      ></el-table-column>
      <el-table-column
        prop="no"
        align="center"
        label="工单编号"
        width="130"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span :style="`color:${scope.row.status === 6 ? 'red' : '#606266'}`">
            {{ scope.row.no || '-' }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="orderName"
        align="center"
        label="计划名称"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.orderName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentCode"
        label="设备编号"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentCode || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeDeptName"
        label="保养部门"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.executeDeptName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="责任人"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.executeUserName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="startTime"
        label="开始时间"
        align="center"
        min-width="90"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        align="center"
        prop="endTime"
        label="结束时间"
        min-width="90"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="cycleTypeName"
        label="计划周期"
        align="center"
        show-overflow-tooltip
        width="70px"
      ></el-table-column>

      <el-table-column align="center" label="审核人员" show-overflow-tooltip>
        <template v-slot="{ row }">{{
          (row.isNeedApproval && row.approvalUserName) || '-'
        }}</template>
      </el-table-column>

      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="90px"
      >
        <template v-slot="{ row }">
          <i
            :style="`color:${inspectOrderStatusColor(
              row.status
            )};font-size:18px`"
            >●</i
          >
          {{ row.statusName || '-' }}
        </template>
      </el-table-column>

      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
        show-overflow-tooltip
        width="136px"
      >
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template slot-scope="scope">
          <section class="cell-operate-class">
            <el-button type="text" size="small" @click="detail(scope.row)"
              >查看</el-button
            >
            <!--            执行中、已超期、已驳回都可以进行保养操作-->
            <el-button
              v-if="
                permission['maintenance-order-operate'] &&
                (Number(scope.row.status) === 1 ||
                  Number(scope.row.status) === 3 ||
                  Number(scope.row.status) === 6)
              "
              type="text"
              size="small"
              @click="maintence(scope.row)"
              >保养</el-button
            >
            <!--           待确认状态下-->
            <el-button
              v-if="
                Number(scope.row.status) === 5 &&
                permission['maintenance-order-confirmAndReview']
              "
              class="green-btn"
              type="text"
              size="small"
              @click="examine(scope.row)"
              >审核确认</el-button
            >
            <el-button
              v-if="
                Number(scope.row.status) === 5 &&
                permission['maintenance-order-confirmAndReview']
              "
              class="danger-btn"
              type="text"
              size="small"
              @click="turnDown(scope.row)"
              >驳回</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />

    <detail-index ref="detailIndex"></detail-index>
    <!--     驳回-->
    <turn-down ref="turnDown" @success="getList"></turn-down>
    <!--    保养操作-->
    <maintain-operate
      ref="maintainOperate"
      @success="getList"
    ></maintain-operate>
  </basic-container>
</template>

<script>
  import TurnDown from '@/views/equiment-full-life-cycle/equipment-maintenance/work-order/operate/turn-down.vue';
  import DetailIndex from '@/views/equiment-full-life-cycle/equipment-maintenance/work-order/detail/index.vue'; // 查看详情页面
  import { inspectOrderStatusColor } from '@/views/equiment-full-life-cycle/equipment-inspection/util.js';

  import Search from '@/views/equiment-full-life-cycle/equipment-maintenance/work-order/search';
  import Pagination from '@/components/pagination';
  import MaintainOperate from '@/views/equiment-full-life-cycle/equipment-maintenance/work-order/operate/maintenct-operate.vue';
  import {
    auditOrder,
    batchAuditOrder,
    getOrderList
  } from '@/api/equiment-full-life-api/maintenance';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  import { mapGetters } from 'vuex';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      DetailIndex,
      Pagination,
      TurnDown,
      MaintainOperate
    },
    props: {},
    data() {
      return {
        inspectOrderStatusColor,
        loading: false,
        total: 0,
        list: [],
        tableKey: Math.random(),
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {},
        multipleSelection: []
      };
    },
    activated() {
      const { fromAssist, status } = this.$route.params;
      if (fromAssist) {
        let fields = { field: 'status', value: status };
        this.$refs.search.setFields(fields);
      } else {
        this.getList();
      }
    },

    mounted() {
      // this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },
    computed: {
      ...mapGetters(['permission'])
    },
    methods: {
      // 批量审核
      handleBatchAudit(type) {
        console.log(this.multipleSelection);
        if (this.multipleSelection.length === 0) {
          return this.$message.warning('请选择至少一条工单');
        }

        if (type === 'agree') {
          this.handleBatchArgee();
        } else {
          this.$refs['turnDown'].show(this.multipleSelection, true);
        }
      },
      // 批量审核确认
      handleBatchArgee() {
        this.$confirm(`确认选中的工单结果符合标准？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await batchAuditOrder({
                orderIds: this.multipleSelection.map((item) => item.id),
                status: 2
              });
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      },
      // 表格多选回调
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      //  点击保养操作
      maintence(row) {
        this.$refs['maintainOperate'].show(row.no);
      },
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/maintain-order/export-order?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/maintain-order/export-order?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '保养工单.xlsx'
        );
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await getOrderList({
            ...this.searchParams
          });
          this.tableKey = Math.random();
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },

      detail(row) {
        this.$refs['detailIndex'].show(row.no);
      },
      //  驳回
      turnDown(row) {
        this.$refs['turnDown'].show(row);
      },
      examine(row) {
        this.$confirm(`确认保养结果符合标准？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(async () => {
            try {
              await auditOrder({
                id: row.id,
                status: 2
              });
              this.$message.success('操作成功');
              await this.getList();
            } catch (e) {
              this.$message.warning(e.data.msg);
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
