<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <div class="top-info">
      <search
        ref="search"
        :statusArr="serviceDicts.type['repair_status']"
        :isShowOrderStatus="activeName === 'order'"
        @search="onsubmit"
        :is-show-dept="false"
      >
      </search>
      <!-- <el-button
        slot="button"
        icon="el-icon-upload2"
        type="primary"
        size="small"
        @click="exportExcel"
        >导出</el-button
      > -->
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="维修工单统计" name="order">
        <order-statics ref="order"></order-statics>
      </el-tab-pane>
      <el-tab-pane label="维修设备统计" name="device">
        <device-statics ref="device"></device-statics>
      </el-tab-pane>
      <el-tab-pane label="故障缺陷统计" name="fault">
        <fault-statics ref="fault"></fault-statics>
      </el-tab-pane>
    </el-tabs>
  </basic-container>
</template>

<script>
  import Search from '../component/search.vue';
  import DeviceStatics from './component/device.vue';
  import OrderStatics from './component/order.vue';
  import FaultStatics from './component/fault.vue';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  export default {
    name: 'DeviceBasicList',
    components: {
      FaultStatics,
      Search,
      DeviceStatics,
      OrderStatics
    },
    serviceDicts: ['repair_status'],
    props: {},
    data() {
      return {
        activeName: 'order',
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        data: [
          {
            exportPath: '/api/szyk-simas/statistical-report/export-repair?',
            refs: 'order',
            name: '维修工单统计'
          },
          {
            exportPath:
              '/api/szyk-simas/statistical-report/export-equipment-repair?bizModule=REPAIR_ORDER&',
            refs: 'device',
            name: '维修设备统计'
          },
          {
            exportPath:
              '/api/szyk-simas/statistical-report/export-fault-defect?',
            refs: 'fault',
            name: '故障缺陷统计'
          }
        ]
      };
    },
    mounted() {},
    // 页面销毁取消监听
    methods: {
      getObj() {
        // 返回data中和activaName 相同的对象
        return this.data.find((item) => item.refs === this.activeName);
      },
      async exportExcel() {
        let obj = await this.getObj();
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = obj.exportPath;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `${obj.exportPath}${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          `${obj.name}.xlsx`
        );
      },
      handleClick() {
        this.searchParams.current = 1;
        // this.getLists();
        this.$refs.search.resetStatus();
      },
      getLists() {
        this.searchParams.current = 1;
        let ref = this.getObj().refs;
        this.$refs[ref].getData(this.searchParams);
      },
      onsubmit(param) {
        this.exportParams = { ...param };
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getLists(this.searchParams);
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }

  ::v-deep {
    .el-tabs {
      height: calc(100% - 150px);
    }

    .el-tabs__content {
      height: calc(100% - 10px);
    }

    .el-tab-pane {
      height: calc(100% - 50px);
    }
  }
</style>
