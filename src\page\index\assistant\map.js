const keyMap = new Map();
const valueMap = new Map();

// 关联关系
keyMap.set('关联关系', 'type');
valueMap.set('我下发的', 'assign');
valueMap.set('我负责的', 'receive');
valueMap.set('我代发的', 'agent');

// 工作内容
keyMap.set('工作内容', 'content');

// 专项名称
keyMap.set('会议名称', 'meetingName');

// 状态
// keyMap.set("状态", "status");

// 下发时间
keyMap.set('下发时间', 'createDateRange');

// 完成时限
keyMap.set('完成时限', 'completeDateRange');

// 年度
keyMap.set('年度', 'year');

// 月份
keyMap.set('月份', 'month');

// 所属业务
keyMap.set('所属业务', 'bizTypeName');

// 所属部门
keyMap.set('所属部门', 'undertakeDeptName');

// 下发人
keyMap.set('下发人', 'assignUser');

// 责任人
keyMap.set('责任人', 'undertakeUser');

// 菜单编码
keyMap.set('菜单编码', 'menuCode');

// 菜单名称
keyMap.set('菜单名称', 'menuName');

// 工单状态
keyMap.set('工单状态', 'status');

// 工单类型
const orderTypeDict = [
  { label: '点巡检', value: 'INSPECT_ORDER' },
  { label: '保养', value: 'MAINTAIN_ORDER' },
  { label: '润滑', value: 'LUBRICATE_ORDER' },
  { label: '外委维修', value: 'EXTERNAL_REPAIR' },
  { label: '内部维修', value: 'INTERNAL_REPAIR' },
  { label: '检修', value: 'OVERHAUL_ORDER' }
];

// 操作类型
const optTypeDict = [
  { label: '工单审核', value: 'audit' },
  { label: '工单查询统计', value: 'statistics' }
];

// 工单类型跳转路径
const orderAuditRoutes = [
  'inspection-work-order',
  'maintenance-work-order',
  'lubrication-work-order',
  'repair-external',
  'repair-internal',
  'overhaul-work-order'
];

export { keyMap, valueMap, orderTypeDict, optTypeDict, orderAuditRoutes };
