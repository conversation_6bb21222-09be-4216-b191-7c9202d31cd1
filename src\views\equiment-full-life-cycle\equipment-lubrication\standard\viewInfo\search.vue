<template>
  <el-form
    class="_form search-form"
    ref="form"
    :model="form"
    size="small"
    :inline="true"
    label-suffix="："
    label-position="left"
  >
    <el-form-item label="设备编号" prop="code">
      <el-input
        v-model.trim="form.code"
        size="small"
        placeholder="请输入设备编号"
        :maxlength="50"
        clearable
      >
      </el-input>
    </el-form-item>
    <el-form-item label="SN编号" prop="sn">
      <el-input
        v-model.trim="form.sn"
        size="small"
        placeholder="请输入SN编号"
        :maxlength="50"
        clearable
      >
      </el-input>
    </el-form-item>
    <el-form-item label="设备名称" prop="name">
      <el-input
        v-model.trim="form.name"
        size="small"
        placeholder="请输入设备名称"
        :maxlength="50"
        clearable
      >
      </el-input>
    </el-form-item>

    <el-form-item label="设备类型" prop="categoryName">
      <el-input
        placeholder="请选择设备类型"
        type="text"
        v-model="form.categoryName"
        readonly
        @focus.prevent="selectAssetCategory"
      >
        <template slot="append">
          <i
            class="el-icon-circle-close"
            @click="
              () => {
                form.categoryId = undefined;
                form.categoryName = undefined;
              }
            "
          ></i>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item>
      <btn type="search" @click="onSubmit" />
      <btn type="reset" @click="reset" />
    </el-form-item>
    <!--     选择设备类型-->
    <device-type-dialog
      ref="device-type"
      @success="getSingleRow"
    ></device-type-dialog>
  </el-form>
</template>

<script>
  import 'element-ui/lib/theme-chalk/display.css';
  import DeviceTypeDialog from '@/views/equiment-full-life-cycle/components/select-asset-category-dialog/single-index.vue';
  export default {
    name: 'alarmSearch',
    components: { DeviceTypeDialog },
    data() {
      return {
        form: {
          code: undefined,
          sn: undefined,
          name: undefined,
          categoryId: undefined,
          categoryName: undefined
        }
      };
    },
    created() {},
    methods: {
      //  选择类型
      getSingleRow(row) {
        this.form.categoryId = row.id;
        this.form.categoryName = row.categoryName;
      },
      selectAssetCategory() {
        this.$refs['device-type'].show();
      },
      onSubmit() {
        let searchParams = {
          ...this.form
        };
        this.$emit('query', searchParams);
      },
      reset() {
        this.form.categoryId = undefined;
        this.$refs['form'].resetFields();
        this.$emit('query', this.form);
      }
    }
  };
</script>

<style scoped lang="scss"></style>
