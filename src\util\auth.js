import { getStore, removeStore } from '@/util/store';
import Cookies from 'js-cookie';

const TokenKey = 'Admin-Token';

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token);
}

export function removeToken() {
  return Cookies.remove(Token<PERSON>ey);
}

export function getRefreshToken() {
  return getStore({ name: 'refreshToken' });
}

export function removeRefreshToken() {
  return removeStore({ name: 'refreshToken' });
}
