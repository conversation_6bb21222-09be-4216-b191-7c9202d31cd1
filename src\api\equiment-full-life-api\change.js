import request from '@/router/axios';
//  维修相关接口

// 变更分页列表
export const equipmentchangeList = (params) => {
  return request({
    url: `/api/szyk-simas/equipmentchange/page`,
    method: 'get',
    params
  });
};

// 新增编辑变更
export const addOrEditRepairApi = (data) => {
  return request({
    url: `/api/szyk-simas/equipmentchange/save`,
    method: 'post',
    data
  });
};

// 根据设备id查询设备资料
export const listFileByEquipmentId = (params) => {
  return request({
    url: `/api/szyk-simas/equipmentchange/listFileByEquipmentId`,
    method: 'get',
    params
  });
};
// 变更详情
export const getChangeViewApi = (params) => {
  return request({
    url: `/api/szyk-simas/equipmentchange/fetchById`,
    method: 'get',
    params
  });
};
// 变更验证
export const verifyChangeApi = (data) => {
  return request({
    url: `/api/szyk-simas/equipmentchange/accept`,
    method: 'post',
    data
  });
};

//  变更审核
export const changeAudit = (data) => {
  return request({
    url: `/api/szyk-simas/equipmentchange/audit`,
    method: 'post',
    data
  });
};
//  变更撤销
export const changeCancel = (params) => {
  return request({
    url: `/api/szyk-simas/equipmentchange/cancel`,
    method: 'post',
    params
  });
};
//  变更变更一个资料
export const updateOneFile = (data) => {
  return request({
    url: `/api/szyk-simas/equipmentchange/updateOneFile`,
    method: 'post',
    data
  });
};
