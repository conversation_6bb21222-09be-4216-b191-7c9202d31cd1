<template>
  <basic-container auto-height no-scrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <el-button
        v-hasPermi="['changes-add']"
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="onOperateClick('add')"
        >新增</el-button
      >
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="calc(100% - 180px)"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="changeNumber"
        label="变更单号"
        width="130px"
        align="center"
      ></el-table-column>
      <el-table-column label="变更单名称" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.changeName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="申请人" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.applyUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="申请时间" align="center" width="136px">
        <template slot-scope="scope">
          {{ scope.row.createTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="变更设备" align="center">
        <template slot-scope="scope">
          {{ scope.row.equipmentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        label="设备类型"
        align="center"
        width="120px"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.equipmentCategoryName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="90">
        <template v-slot="{ row }">
          <change-status
            :status="row.changeStatus"
            :statusName="row.changeStatusName"
          ></change-status>
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center">
        <template slot-scope="scope">
          {{ scope.row.auditPersonName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="receiveUserName"
        label="审核时间"
        align="center"
        width="136px"
      >
        <template slot-scope="scope">
          {{ scope.row.auditTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="210" fixed="right">
        <template v-slot="{ row }">
          <section class="cell-operate-class">
            <el-button type="text" size="mini" @click="detail(row)"
              >查看</el-button
            >
            <el-button
              type="text"
              v-if="['1', '3'].includes(row.changeStatus)"
              v-hasPermi="['changes-add']"
              size="mini"
              @click="onOperateClick(row)"
              >编辑</el-button
            >
            <el-popconfirm
              title="确定撤销该申请吗？"
              v-if="['1', '3'].includes(row.changeStatus)"
              v-hasPermi="['changes-recall']"
              @confirm="onCloseRepair(row)"
            >
              <el-button type="text" slot="reference" size="mini"
                >撤销</el-button
              >
            </el-popconfirm>
            <el-button
              v-if="['1', '2'].includes(row.changeStatus)"
              v-hasPermi="['changes-audit']"
              type="text"
              size="mini"
              @click="examine(row)"
              >审核</el-button
            >
            <el-button
              type="text"
              v-if="['5'].includes(row.changeStatus)"
              v-hasPermi="['changes-add']"
              size="mini"
              @click="detail(row, 'verify')"
              >验收</el-button
            >
          </section>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!-- 详情 -->
    <detail-index ref="detailIndex" @success="getList"></detail-index>
    <!-- 审核详情 -->
    <audit-index ref="auditIndex" @success="getList" />
    <repair-internal-operate
      ref="operate"
      @success="getList"
    ></repair-internal-operate>
    <repair-internal-dispatch
      ref="dispatch"
      @success="getList"
    ></repair-internal-dispatch>
  </basic-container>
</template>

<script>
  import DetailIndex from './detail/index.vue'; // 查看详情页面
  import AuditIndex from './audit-detail/index.vue'; // 查看详情页面
  import Search from './search';
  import Pagination from '@/components/pagination';
  import ChangeStatus from './components/change-status';
  // import { downloadFileBlob } from '@/util/util';
  // import { getToken } from '@/util/auth';
  import {
    changeCancel,
    equipmentchangeList
  } from '@/api/equiment-full-life-api/change';
  import RepairInternalOperate from './operate/index.vue';
  import RepairInternalDispatch from '@/views/equiment-full-life-cycle/repair/internal/components/dispatch.vue';
  // import dayjs from 'dayjs';
  export default {
    name: 'DeviceBasicList',
    components: {
      RepairInternalDispatch,
      RepairInternalOperate,
      Search,
      DetailIndex,
      AuditIndex,
      Pagination,
      ChangeStatus
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [{}],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {}
      };
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      onOperateClick(row) {
        this.$refs.operate.show(row);
      },
      onDispatchClick(row) {
        this.$refs.dispatch.show(row);
      },

      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },
      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let { changeStatus } = this.searchParams;
          let res = await equipmentchangeList({
            ...this.searchParams,
            changeStatusList: changeStatus
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      detail(row, type) {
        this.$refs['detailIndex'].show(row.id, type);
      },
      // 审核
      examine(row) {
        this.$refs['auditIndex'].show(row.id);
      },
      async onCloseRepair(row) {
        try {
          await changeCancel({ id: row.id });
          this.$message.success('操作成功');
          await this.getList();
        } catch (e) {
          console.log('error changeCancel', e);
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    margin-bottom: 10px;
  }
</style>
