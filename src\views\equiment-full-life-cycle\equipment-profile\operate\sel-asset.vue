<template>
  <div>
    <el-button
      size="small"
      type="primary"
      style="margin-bottom: 15px"
      @click="selAsset"
      v-if="isDele"
      >+ 选择设备类型</el-button
    >
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      border
      :header-cell-style="{ background: '#fafafa' }"
      size="small"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="categoryName"
        label="设备类型名称"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="pathName"
        label="路径"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.pathName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="remark"
        label="类型编码"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.remark || '-' }}</template>
      </el-table-column>

      <el-table-column
        prop="model"
        label="操作"
        width="200"
        align="center"
        v-if="isDele"
      >
        <template v-slot="scope">
          <el-popconfirm title="确定删除吗？" @confirm="() => del(scope)">
            <el-button
              icon="el-icon-delete"
              slot="reference"
              type="text"
              size="small"
              style="margin-left: 10px"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!--     选择设备分类列表-->
    <select-asset-category-dialog
      ref="assetList"
      @success="getAssetList"
    ></select-asset-category-dialog>
  </div>
</template>

<script>
  import SelectAssetCategoryDialog from '../../components/select-asset-category-dialog';

  export default {
    name: 'DeviceBasicList',
    components: { SelectAssetCategoryDialog },
    props: {
      isDele: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      detail: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.list = this.detail.categoryList || [];
            });
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },

    mounted() {},
    methods: {
      del(scope) {
        this.list.splice(scope.$index, 1);
      },
      validForm() {
        if (this.list.length === 0) {
          this.$message.warning('请选择设备类型');
          return;
        } else {
          let ids = this.list
            .map((it) => {
              return it.id;
            })
            .join(',');
          return { categoryId: ids };
        }
      },
      resetForm() {
        this.list = [];
      },
      //  获取设备
      getAssetList(val) {
        this.list = val;
      },
      selAsset() {
        let list = this.list.map((item) => {
          return {
            ...item,
            num: 1
          };
        });
        this.$refs['assetList'].show(list);
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }

  .operateBtn {
    margin-bottom: 15px;
  }

  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
