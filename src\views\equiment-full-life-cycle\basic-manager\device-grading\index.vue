<template>
  <basic-container class="table-content" :auto-height="true" noScrollbar>
    <search ref="search" @search="onsubmit" />
    <div class="res-wrapper">
      <div class="res-text">查询结果列表</div>
      <el-button size="small" type="primary" @click="handleSetting"
        >设备分级管理</el-button
      >
    </div>
    <table-info
      :loading="loading"
      :tableData="list"
      @dispatch="handleTableDispatch"
    />
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!-- 详情 -->
    <detail-index ref="detailIndex"></detail-index>
    <!-- 分级管理弹窗 -->
    <setting ref="setting" />
  </basic-container>
</template>

<script>
  import Pagination from '@/components/pagination';
  import DetailIndex from '@/views/equiment-full-life-cycle/equipment-management/ledger/detail/index.vue'; // 查看详情页面
  import Search from './search';
  import TableInfo from './table-info';
  import Setting from './setting';
  import { accountListPage } from '@/api/equiment-full-life-api/ledger';

  export default {
    name: 'DeviceGradingList',
    components: {
      Pagination,
      DetailIndex,
      Search,
      TableInfo,
      Setting
    },
    props: {},
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },
    mounted() {
      this.getList();
      window.addEventListener('resize', this.handleResize);
    },
    // 页面销毁取消监听
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },

    methods: {
      handleResize() {
        // 触发表格更新
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },
      // 查询
      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      // 设备分级管理
      handleSetting() {
        this.$refs.setting.show();
      },
      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await accountListPage({
            ...this.searchParams
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      detail(row) {
        this.$refs['detailIndex'].show(row.id);
      },
      // 表格回调
      handleTableDispatch(type, data) {
        switch (type) {
          case 'view':
            this.detail(data);
            break;
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .res-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;

    .res-text {
      font-weight: bold;
    }
  }
</style>
