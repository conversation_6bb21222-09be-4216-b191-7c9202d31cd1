<template>
  <div style="width: 100%; display: flex; justify-content: space-between">
    <el-input
      placeholder="下限值"
      style="width: 45%"
      clearable
      @change="downInputChange"
      v-model="curValue.down"
      :size="size"
    >
      <template slot="append" v-if="unit">{{ unit }}</template>
    </el-input>
    -
    <el-input
      placeholder="上限值"
      style="width: 45%"
      clearable
      @change="upInputChange"
      v-model="curValue.up"
      :size="size"
    >
      <template slot="append" v-if="unit">{{ unit }}</template>
    </el-input>
  </div>
</template>

<script>
  export default {
    name: 'numberRange',
    props: {
      value: {
        type: Object,
        default: () => {}
      },
      unit: {
        type: String,
        default: ''
      },
      size: {
        type: String,
        default: ''
      },
      min: {
        type: Number,
        default: undefined
      },
      max: {
        type: Number,
        default: undefined
      }
    },
    data() {
      return {
        curValue: {
          up: undefined,
          down: undefined
        }
      };
    },
    watch: {
      value: {
        immediate: true,
        deep: true,
        handler(newValue) {
          if (newValue) {
            this.curValue = { ...newValue };
          }
        }
      }
    },
    methods: {
      upInputChange(value) {
        value = parseFloat(value);
        // if (value <= 0) {
        //   this.curValue.up = 0;
        // }
        if (this.max && value > this.max) {
          this.curValue.up = this.max;
        }
        let down = parseFloat(this.curValue.down);
        if (down && value < down) {
          this.curValue.up = this.curValue.down;
        }
        this.$emit('input', this.curValue);
      },
      downInputChange(value) {
        value = parseFloat(value);
        // if (value <= 0) {
        //   this.curValue.down = 0;
        // }
        if (this.min && value < this.min) {
          this.curValue.down = this.min;
        }
        let up = parseFloat(this.curValue.up);
        if (up && value > up) {
          this.curValue.down = this.curValue.up;
        }
        this.$emit('input', this.curValue);
      }

      // upInputChange(value) {
      //   // if (value <= 0) {
      //   //   this.curValue.up = 0;
      //   // }
      //   if (this.max && value > this.max) {
      //     this.curValue.up = this.max;
      //   }
      //   let down = this.curValue.down;
      //   if (down && value < down) {
      //     this.curValue.up = this.curValue.down;
      //   }
      //   this.$emit('input', this.curValue);
      // },
      // downInputChange(value) {
      //   // if (value <= 0) {
      //   //   this.curValue.down = 0;
      //   // }
      //   if (this.min && value < this.min) {
      //     this.curValue.down = this.min;
      //   }
      //   let up = this.curValue.up;
      //   if (up && value > up) {
      //     console.log('最小值 出现 大于 最大值 了 ');
      //     this.curValue.down = this.curValue.up;
      //   }
      //   this.$emit('input', this.curValue);
      // }
    }
  };
</script>

<style lang="scss" scoped></style>
