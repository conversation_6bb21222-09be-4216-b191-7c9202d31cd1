<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="110px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="入库类型" prop="inboundType">
            <el-select
              v-model="form.inboundType"
              placeholder="请选择入库类型"
              clearable
              @change="changeInboundType"
            >
              <el-option
                v-for="item in serviceDicts.type['inbound_type']"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商" prop="supplierId">
            <el-select
              v-model="form.supplierId"
              placeholder="请选择供应商"
              clearable
              filterable
              :disabled="edit"
            >
              <el-option
                v-for="item in supList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="入库库房" prop="warehouseId">
            <el-select
              v-model="form.warehouseId"
              placeholder="请选择入库库房"
              clearable
              filterable
            >
              <el-option
                v-for="item in whorehouse"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="入库日期" prop="inboundDate">
            <el-date-picker
              v-model="form.inboundDate"
              type="date"
              clearable
              placeholder="请选择入库日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="入库人" prop="inboundUserName">
            <el-input
              placeholder="请选择入库人"
              type="text"
              v-model="form.inboundUserName"
              readonly
              @focus.prevent="onChooseUser"
            >
              <template slot="append">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      form.inboundUserId = undefined;
                      form.inboundUserName = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注" prop="remark">
            <el-input
              placeholder="请输入备注"
              v-model.trim="form.remark"
              :maxlength="200"
              clearable
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <!--     选择人员-->
    <recipient-dialog
      ref="recipient"
      @selected="onUserSelect"
    ></recipient-dialog>
  </div>
</template>

<script>
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import { mapGetters } from 'vuex';
  export default {
    components: { RecipientDialog },
    serviceDicts: ['inbound_type'],
    name: 'InStorageType',
    props: {
      initData: {
        type: Object,
        default: () => {}
      },
      whorehouse: {
        type: Array,
        default: () => []
      },
      supList: {
        type: Array,
        default: () => []
      }
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    data() {
      return {
        form: {
          inboundUserName: undefined,
          inboundUserId: undefined,
          inboundDate: undefined,
          inboundType: undefined,
          supplierId: undefined,
          warehouseId: undefined,
          remark: undefined
        },
        edit: false,
        rules: {
          inboundType: [
            {
              required: true,
              message: '请选择入库类型',
              trigger: 'change'
            }
          ],
          warehouseId: [
            {
              required: true,
              message: '请选择入库仓库',
              trigger: 'change'
            }
          ],
          inboundUserName: [
            {
              required: true,
              message: '请选择入库人',
              trigger: 'change'
            }
          ],
          inboundDate: [
            {
              required: true,
              message: '请选择入库日期',
              trigger: 'change'
            }
          ],
          supplierId: [
            {
              required: false,
              message: '请选择供应商',
              trigger: 'change'
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      },
      'form.warehouseId': {
        immediate: true,
        deep: true,
        handler(val) {
          this.$emit('warehouseId', val);
        }
      },
      whorehouse: {
        immediate: true,
        deep: true,
        handler(val) {
          if (val.length > 0) {
            this.form.warehouseId = val[0].id;
          }
        }
      }
    },
    mounted() {},
    created() {
      this.form.inboundUserId = this.userInfo.user_id;
      this.form.inboundUserName = this.userInfo.real_name;
    },
    methods: {
      changeInboundType() {
        console.log(this.form.inboundType);
        if (Number(this.form.inboundType) == 4) {
          this.rules.supplierId = [
            {
              required: true,
              message: '请选择供应商',
              trigger: 'change'
            }
          ];
        } else {
          this.rules.supplierId = [
            {
              required: false,
              message: '请选择供应商',
              trigger: 'change'
            }
          ];
        }
      },
      //  选择人员
      onChooseUser() {
        this.$refs['recipient'].show();
      },
      onUserSelect(user) {
        this.form.inboundUserId = user.id;
        this.form.inboundUserName = user.realName;
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },

      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          return this.form;
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs['baseForm'].resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-input-number {
        width: 100%;

        .el-input__inner {
          text-align: left;
        }
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
