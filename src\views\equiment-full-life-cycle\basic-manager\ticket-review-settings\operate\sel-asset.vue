<template>
  <div>
    <el-button
      size="small"
      type="primary"
      style="margin-bottom: 15px"
      @click="selAsset"
      v-if="isDele"
      >+ 选择设备</el-button
    >
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      border
      :header-cell-style="{ background: '#fafafa' }"
      size="small"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="name"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="categoryName"
        label="设备类型"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.categoryName || '-' }}</template>
      </el-table-column>
      <el-table-column
        prop="locationPath"
        label="存放地点"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.locationPath || '-' }}</template>
      </el-table-column>
      <el-table-column
        v-if="detail.id"
        prop="orderNo"
        label="工单号"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.orderNo || '-' }}</template>
      </el-table-column>
      <el-table-column
        v-if="detail.id"
        prop="bizStatus"
        label="工单状态"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">{{ row.bizStatus || '-' }}</template>
      </el-table-column>

      <el-table-column prop="model" label="操作" align="center" v-if="isDele">
        <template v-slot="scope">
          <el-button type="danger" link size="mini" @click="del(scope)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <asset-list
      ref="assetList"
      @getAssetList="getAssetList"
      selType="maintain"
      :statusList="[3, 4]"
    ></asset-list>
  </div>
</template>

<script>
  import AssetList from '../../../components/select-asset-dialog';
  export default {
    name: 'DeviceBasicList',
    components: { AssetList },
    props: {
      isDele: {
        type: Boolean,
        default: () => {
          return true;
        }
      },
      detail: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      'detail.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.list = this.detail.equipmentList;
            });
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        }
      };
    },

    mounted() {},
    methods: {
      del(scope) {
        this.list.splice(scope.$index, 1);
      },
      validForm() {
        if (this.list.length === 0) {
          this.$message.warning('请选择设备');
          return;
        }
        let ids = this.list
          .map((it) => {
            return it.id;
          })
          .join(',');
        return { equipmentIds: ids };
      },
      resetForm() {
        this.list = [];
      },
      //  获取设备
      getAssetList(val) {
        this.list = val;
      },
      selAsset() {
        let list = this.list.map((it) => {
          return {
            ...it,
            num: 1
          };
        });
        this.$refs['assetList'].show(list);
      }
    }
  };
</script>

<style scoped lang="scss">
  .top-info {
    padding: 0 0 15px;
  }

  ::v-deep {
    .table-content {
      height: calc(100% - 70px) !important;
    }
  }

  .operateBtn {
    margin-bottom: 15px;
  }

  :deep {
    .el-popover__reference {
      margin: 0 10px;
    }
  }
</style>
