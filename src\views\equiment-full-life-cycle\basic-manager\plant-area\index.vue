<template>
  <basic-container :auto-height="true" noScrollbar>
    <search ref="search" @search="onsubmit" />
    <!--    <el-button-->
    <!--      type="primary"-->
    <!--      size="small"-->
    <!--      class="el-icon-plus"-->
    <!--      @click="handleOperate('add')"-->
    <!--      >新增</el-button-->
    <!--    >-->
    <el-table
      v-loading="loading"
      :data="tableData"
      ref="treeTable"
      style="width: 100%; margin-bottom: 20px; margin-top: 15px"
      row-key="id"
      border
      lazy
      height="calc(100% - 170px)"
      :load="tableLoad"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      size="small"
      :expand-row-keys="expandRowkeys"
      :expand-on-click-node="false"
    >
      <el-table-column prop="name" label="厂区名称" align="left">
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="left">
        <template v-slot="{ row }">{{ row.remark || '-' }}</template>
      </el-table-column>
      <el-table-column prop="address" label="操作" align="center" width="250">
        <template v-slot="scope">
          <el-button
            type="text"
            size="small"
            link
            @click="handleOperate('add', scope.row)"
            >新增子项</el-button
          >
          <el-button
            type="text"
            size="small"
            link
            @click="handleOperate('edit', scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            v-if="Number(scope.row.parentId) !== 0"
            title="确定删除吗？"
            @confirm="() => handleDelete(scope.row)"
          >
            <el-button
              link
              size="small"
              type="text"
              slot="reference"
              style="margin-left: 10px"
              >删除
            </el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-if="searchParams.keywords"
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <handle-operate ref="view" @success="onOperateSuccess"></handle-operate>
  </basic-container>
</template>

<script>
  import Search from './search';
  import Pagination from '@/components/pagination';
  import HandleOperate from './operate/index.vue';
  import {
    getLocationLazyList,
    getLocationPage,
    removeLocation
  } from '@/api/equiment-full-life-api/location';
  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      Pagination,
      HandleOperate
    },
    props: {},
    data() {
      return {
        maps: new Map(),
        loading: false,
        total: 0,
        tableData: [],
        searchParams: {
          keywords: '',
          size: 10,
          current: 1
        },
        expandRowkeys: [],
        node: undefined,
        nodeId: undefined // 点击左边的树结构，传递的id
      };
    },

    mounted() {
      this.getList();
    },
    methods: {
      onsubmit(form) {
        if (form.keywords) {
          this.searchParams.keywords = form.keywords;
          this.getPage();
        } else {
          this.getList();
        }
      },
      async getPage() {
        const res = await getLocationPage({
          ...this.searchParams
        });
        this.maps.clear();
        this.tableData = res.data.data.records;
        this.total = res.data.data.total;
      },
      async tableLoad(tree, treeNode, resolve) {
        const res = await getLocationLazyList({
          parentId: tree.id
        });
        this.maps.set(tree.id, { tree, treeNode, resolve });
        console.log('load。。。。', this.maps);
        let data = res.data.data;
        data.forEach((item) => {
          this.maps.set(item.id, { ...item, tree, treeNode, resolve });
        });
        resolve(data);
      },
      async handleDelete(row) {
        let res = await removeLocation(row.id);
        console.log(res.data);
        if (res.data.data.failureNumber === 0) {
          this.$message.success('操作成功');
        } else {
          this.$message.error(res.data.data.detailVOList[0].message);
        }
        this.refreshLoad(row.parentId);
      },
      handleOperate(type, row) {
        this.$refs['view'].show(type, row);
      },
      onOperateSuccess(row) {
        this.getList();
        this.refreshLoad(row.parentId);
      },
      getList() {
        this.loading = true;
        getLocationLazyList({ parentId: '0' })
          .then((res) => {
            this.tableData = res.data.data;
            this.loading = false;
          })
          .catch((e) => {
            this.loading = false;
          });
      },
      // 获取map中的数据，重新加载
      refreshLoad(parentId) {
        console.log(parentId);
        // 根据父级ID取出对应节点数据
        let obj = this.maps.get(parentId);
        console.log(obj);
        if (obj) {
          const { tree, treeNode, resolve } = obj;
          // 根据父节点id更新子节点数据
          // -- 先给table标签添加一个ref="table1"
          // -- parentId: 就是父节点的ID
          // -- []：就是子节点的数据
          if (tree) {
            this.$set(
              this.$refs.treeTable.store.states.lazyTreeNodeMap,
              parentId,
              []
            );
          }
          // 将取出对应数据再传给load方法，重新加载数据。
          this.tableLoad(tree, treeNode, resolve);
        }
      }
    }
  };
</script>

<style scoped lang="scss"></style>
