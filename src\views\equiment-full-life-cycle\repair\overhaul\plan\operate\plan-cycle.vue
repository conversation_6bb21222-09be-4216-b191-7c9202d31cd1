<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix="："
      :rules="rules"
      ref="baseForm"
      label-width="120px"
      :label-position="'right'"
      size="small"
    >
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="计划周期" prop="cycleType">
            <el-select
              v-model="form.cycleType"
              placeholder="请选择计划周期"
              @change="changeType"
              clearable
            >
              <!-- 检修没有计划按天的计划周期-->
              <el-option
                v-for="dict in serviceDicts.type['plan_cycle'].filter(
                  (item) => {
                    return item.value !== 'DAY';
                  }
                )"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option> </el-select
          ></el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重复执行间隔" prop="cycleInterval">
            <el-input
              placeholder="请输入重复执行间隔"
              v-model.trim="form.cycleInterval"
              clearable
            >
              <template slot="prepend">每隔</template>
              <template slot="append"
                >{{ returnCycleText(form.cycleType) }}执行一次</template
              >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划名称" prop="name">
            <el-input
              placeholder="请输入计划名称"
              v-model.trim="form.name"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="计划开始时间" prop="startDate">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              placeholder="请选择计划开始时间"
              clearable
              value-format="yyyy-MM-dd"
              :picker-options="startOptions"
              @change="clearEndTime"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计划截止时间" prop="endDate">
            <el-date-picker
              v-model="form.endDate"
              type="date"
              placeholder="请选择计划截止时间"
              clearable
              value-format="yyyy-MM-dd"
              :picker-options="endOptions"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="负责部门" prop="executeDeptName">
            <el-input
              style="width: 100%"
              placeholder="请选择负责部门"
              v-model.trim="form.executeDeptName"
              @focus.prevent="onSelectDeptClick"
              readonly
            >
              <template slot="append">
                <i
                  class="el-icon-circle-close"
                  @click="
                    () => {
                      form.executeDept = undefined;
                      form.executeDeptName = undefined;
                      form.executeUser = undefined;
                    }
                  "
                ></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="add-info" :gutter="20">
        <el-col :span="8">
          <el-form-item label="责任人" prop="executeUser">
            <el-select
              v-loading="userLoading"
              v-model="form.executeUser"
              filterable
              placeholder="请选择责任人"
              clearable
            >
              <el-option
                v-for="item in executeUserList"
                :key="item.id"
                :label="item.realName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="执行浮动时间" prop="floatDate">
            <el-input
              placeholder="请输入执行浮动时间"
              v-model.trim="form.floatDate"
              clearable
            >
              <template slot="append"> 天</template>
            </el-input>
          </el-form-item>
        </el-col>
        <!-- 这是按日生成工单 -->
        <!--        <el-col :span="24" v-if="form.cycleType === 'DAY'">-->
        <!--          <el-form-item label="" prop="">-->
        <!--            <template slot="label"-->
        <!--              ><span style="color: red">*</span>工作时间设置</template-->
        <!--            >-->
        <!--            <operate-classes ref="classes" :detail="initData"></operate-classes>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <!--        按月-->
        <el-col :span="8" v-if="form.cycleType === 'MONTH'">
          <el-form-item label="工作时间设置" prop="byMonthSet">
            <el-select
              v-model="form.byMonthSet"
              placeholder="请选择工作时间设置"
              multiple
              clearable
            >
              <el-option
                v-for="k in dayOfMonthList"
                :key="k.value"
                :label="k.label"
                :value="k.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!--  按周-->
      <el-row class="add-info" :gutter="20">
        <el-col :span="8" v-if="form.cycleType === 'WEEK'">
          <el-form-item label="工作时间设置" prop="byWeekSet">
            <el-select
              v-model="form.byWeekSet"
              placeholder="请选择工作时间设置"
              multiple
              clearable
            >
              <el-option
                v-for="dict in serviceDicts.type['week_date']"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              ></el-option>
            </el-select>
          </el-form-item> </el-col
      ></el-row>
    </el-form>

    <!--     选择部门-->
    <dept-dialog ref="dept-dialog" @select="onSelectDept"></dept-dialog>
  </div>
</template>

<script>
  import {
    dayOfWeekList,
    dayOfMonthList,
    getIntervalMax,
    returnCycleText,
    monthOfYear,
    getMidnight
  } from '../../../../equipment-inspection/util';
  import DeptDialog from '@/components/dept-dialog/index.vue';
  import { isLiftNum0 } from '@/util/func';
  import { getUserListByDeptId } from '@/api/equiment-full-life-api/common';
  import { mapGetters } from 'vuex';
  export default {
    serviceDicts: ['plan_cycle', 'week_date'],
    name: 'AddDeviceInfo',
    components: { DeptDialog },
    props: {
      initData: {
        type: Object,
        default: () => {}
      }
    },

    data() {
      const validateCode = (rule, value, callback) => {
        //  浮动时间算法， 如果选择的是按周，每周7天，用输入的间隔*7；如果选择的是按月，按照每月28天算，用输入的间隔*28
        let type = this.form.cycleType; // 存储类型
        let cycleInterval = this.form.cycleInterval; // 存储间隔
        let max = 0;
        if (type && cycleInterval) {
          max =
            type === 'WEEK'
              ? cycleInterval * 7
              : type === 'MONTH'
              ? cycleInterval * 28
              : 0;
        }
        // 如果是null 空字符串
        if (!value) {
          callback();
        } else if (isLiftNum0(value, max)) {
          callback();
        } else {
          callback(new Error('请输入大于等于0且小于等于计划周期*间隔的整数'));
        }
      };
      return {
        monthOfYear,
        getIntervalMax,
        returnCycleText,
        dayOfWeekList,
        dayOfMonthList,
        executeUserList: [], // 责任人
        userLoading: false,
        form: {
          cycleType: undefined,
          cycleInterval: 0,
          name: undefined,
          startDate: undefined,
          endDate: undefined,
          executeDeptName: undefined,
          executeDept: undefined,
          executeUser: undefined,
          byDaySet: undefined,
          byMonthSet: undefined,
          byWeekSet: undefined,
          floatDate: 0 // 浮动时间
        },
        edit: false,
        startOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now();
            // return false;
          }
        },
        endOptions: {
          disabledDate(time) {
            return time.getTime() <= Date.now();
          }
        },
        rules: {
          cycleType: [
            {
              required: true,
              message: '请选择计划周期',
              trigger: ['change']
            }
          ],
          cycleInterval: [
            {
              required: true,
              message: '请输入重复执行间隔',
              trigger: ['blur']
            },
            {
              validator: (rule, value, callback) => {
                // 如果是null 空字符串
                if (!value) {
                  callback();
                } else if (isLiftNum0(value, 9999)) {
                  callback();
                } else {
                  callback(new Error('请输入大于等于0且小于9999的整数'));
                }
              },
              trigger: 'blur'
            }
          ],
          name: [
            {
              required: true,
              message: '请输入计划名称',
              trigger: ['blur']
            }
          ],
          startDate: [
            {
              required: true,
              message: '请选择计划开始时间',
              trigger: ['change']
            }
          ],
          endDate: [
            {
              required: true,
              message: '请选择计划截止时间',
              trigger: ['change']
            }
          ],
          executeDeptName: [
            {
              required: true,
              message: '请选择负责部门',
              trigger: ['change']
            }
          ],

          byMonthSet: [
            {
              required: true,
              message: '请选择选择工作时间设置',
              trigger: 'change'
            }
          ],
          byWeekSet: [
            {
              required: true,
              message: '请选择选择工作时间设置',
              trigger: 'change'
            }
          ],
          //  浮动时间
          floatDate: [
            {
              required: true,
              message: '请输入浮动时间',
              trigger: 'blur'
            },
            {
              validator: validateCode
            }
          ]
        }
      };
    },
    watch: {
      'initData.id': {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.setData(this.initData);
          }
        }
      }
    },
    created() {},
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods: {
      // 选择人员
      async getUser(deptId) {
        this.userLoading = true;
        try {
          let params = {
            // alias: 'repair_user',
            deptId: deptId
          };

          let res = await getUserListByDeptId(params);
          this.executeUserList = res.data.data;

          this.userLoading = false;
        } catch ({ message }) {
          this.userLoading = false;
          console.log(message);
        }
      },
      // change
      clearEndTime(val) {
        if (
          val &&
          this.form.endDate &&
          getMidnight(val) >= getMidnight(this.form.endDate)
        ) {
          this.form.endDate = val;
        } else {
          this.form.endDate = undefined;
        }

        // 更新 picker-options 的 disabledDate 函数
        this.endOptions.disabledDate = this.handleEndDateDisabled;
      },
      handleEndDateDisabled(date) {
        // 结束时间不能小于开始时间
        return (
          getMidnight(date) < getMidnight(this.form.startDate) ||
          getMidnight(date) <= getMidnight(new Date())
        );
      },

      async timeSetting(type) {
        if (type === 'DAY') {
          let res = await this.$refs['classes'].validForm();
          if (res) {
            return { byDaySet: res };
          } else {
            return false;
          }
        } else if (type === 'MONTH') {
          return { byMonthSet: this.form.byMonthSet };
        } else if (type === 'WEEK') {
          return { byWeekSet: this.form.byWeekSet };
        }
      },
      //  选择人员
      receiveUsers() {
        if (this.form.executeDeptName) {
          this.$refs['recipient'].show(this.form.executeDept);
        } else {
          this.$message.warning('请先选择负责部门');
        }
      },

      // 选择部门
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.executeDept = dept.id;
        this.form.executeDeptName = dept.deptName;
        this.form.executeUser = undefined;
        this.getUser(dept.id);
      },
      setData(initData) {
        if (initData.executeDept) {
          this.getUser(initData.executeDept);
        }
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },

      select(data) {
        this.form.name = data.name;
        this.form.id = data.id;
      },
      //  切换类型的时候,清空执行日期
      changeType() {
        this.form.cycleInterval = undefined;
        this.form.byDaySet = undefined;
        this.form.byWeekSet = undefined;
        this.form.byMonthSet = undefined;
      },

      async validForm() {
        let valid = await this.$refs['baseForm'].validate();
        if (valid) {
          let timeSetting = await this.timeSetting(this.form.cycleType);
          if (timeSetting) {
            return {
              ...this.form,
              ...timeSetting
            };
          } else {
            return false;
          }
        } else {
          return false;
        }
      },
      resetForm() {
        this.executeUserList = [];
        this.$refs['baseForm'].resetFields();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .add-info {
    ::v-deep {
      .el-form-item {
        display: flex;
        width: 100%;
      }

      .el-input-number {
        width: 100%;

        .el-input__inner {
          text-align: left;
        }
      }

      .el-form-item__content {
        flex: 1;
      }

      .el-select {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }

      .el-select,
      .el-date-editor {
        width: 100%;
      }

      .el-cascader {
        width: 100%;
      }

      .el-icon-circle-close {
        margin-top: 1px !important;
      }
    }
  }
</style>
