<template>
  <dialog-drawer
    :append-to-body="true"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    direction="rtl"
    :title="'详情'"
    :visible.sync="visible"
    @close="close"
    class="detail-drawer"
    size="80%"
  >
    <div class="details" v-loading="loading" v-if="visible">
      <!-- 基本信息 -->
      <section>
        <p class="el-base-title">基本信息</p>
        <base-info :details="details"></base-info>
      </section>

      <el-tabs v-model="activeName">
        <el-tab-pane label="工单信息" name="first">
          <sel-order ref="orderInfo" :list="orderList"></sel-order>
          <pagination
            :page-size.sync="orderSearchParams.size"
            :page-no.sync="orderSearchParams.current"
            :total="orderTotal"
            @pagination="getList"
        /></el-tab-pane>
        <el-tab-pane label="设备信息" name="second">
          <sel-asset ref="deviceInfo" :list="deviceList"></sel-asset>
          <pagination
            :page-size.sync="searchParams.size"
            :page-no.sync="searchParams.current"
            :total="total"
            @pagination="getDeviceList"
        /></el-tab-pane>
      </el-tabs>
    </div>
  </dialog-drawer>
</template>

<script>
  import SelAsset from './sel-asset.vue';
  import SelOrder from './sel-order.vue';
  import BaseInfo from './base-info.vue';
  import {
    getPlanDetailNew,
    getInspectDeviceList,
    getInspectOrderList
  } from '@/api/equiment-full-life-api/inspect';
  export default {
    name: 'RepairViewIndex',
    components: { BaseInfo, SelAsset, SelOrder },
    data() {
      return {
        activeName: 'first',
        searchParams: {
          current: 1,
          size: 10
        },
        orderSearchParams: {
          current: 1,
          size: 10
        },
        total: 0,
        orderTotal: 0,
        orderList: [],
        deviceList: [],
        id: undefined,
        visible: false, // 弹窗展示隐藏
        loading: false, // loading
        details: {} // 详情数据
      };
    },

    methods: {
      async getDetail(no) {
        this.loading = true;
        try {
          const res = await getPlanDetailNew({ no: no });
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 设备信息
      async getDeviceList() {
        this.loading = true;
        try {
          let res = await getInspectDeviceList({
            ...this.searchParams,
            planId: this.details.id
          });
          this.deviceList = res.data.data.records;
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 工单数据分页
      async getList() {
        this.loading = true;
        try {
          let res = await getInspectOrderList({
            ...this.orderSearchParams,
            planId: this.details.id
          });
          this.orderList = res.data.data.records;
          this.orderTotal = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },
      // 点击展示
      async show(no) {
        this.visible = true;
        if (no) {
          await this.getDetail(no);
          await this.getList();
          await this.getDeviceList();
        }
      },

      // 关闭弹窗
      close() {
        this.visible = false;
        this.detail = {};
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

</style>
