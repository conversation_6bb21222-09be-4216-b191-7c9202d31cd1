<template>
  <div>
    <p class="el-base-title">选择变更设备</p>
    <el-row>
      <el-col :span="8">
        <el-form-item label="设备名称">
          <span>{{ form.equipmentName || '-' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="规格型号">
          <span>{{ form.model || '-' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="设备类型">
          <span>{{ form.equipmentCategoryName || '-' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="使用部门">
          <span>{{ form.useDeptName || '-' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="2">
        <el-button
          style="margin-left: 36px"
          type="text"
          @click="onChooseDeviceClick"
          >{{ form.equipmentId ? '选择' : '选择' }}</el-button
        >
      </el-col>
    </el-row>
    <p class="el-base-title">变更明细</p>
    <el-row>
      <el-col :span="8">
        <el-form-item label="变更单号">
          <span>{{ form.changeNumber || '-' }}</span>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="变更名称" prop="changeName">
          <el-input
            v-model.trim="form.changeName"
            clearable
            maxlength="50"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="变更原因" prop="changeReason">
          <el-input
            v-model.trim="form.changeReason"
            clearable
            maxlength="200"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="变更目的" prop="changePurpose">
          <el-input
            v-model.trim="form.changePurpose"
            clearable
            maxlength="200"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="变更类型" prop="changeCategory">
          <el-select
            v-model="form.changeCategory"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in serviceDicts.type['equipment_change_category']"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="预计实施时间" prop="expectedImplementationDate">
          <el-date-picker
            v-model="form.expectedImplementationDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="变更内容" prop="changeContent">
          <el-input
            v-model.trim="form.changeContent"
            clearable
            maxlength="200"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="实施方案" prop="implementationPlan">
          <el-input
            v-model.trim="form.implementationPlan"
            clearable
            maxlength="200"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="预期效果" prop="expectedEffect">
          <el-input
            v-model.trim="form.expectedEffect"
            clearable
            maxlength="200"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="备注" prop="remarks">
          <el-input
            type="textarea"
            v-model="form.remarks"
            maxlength="1000"
            placeholder="请输入(1000字以内)"
          >
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <asset-list
      ref="assetList"
      @on-choose="onChooseDeviceSuccess"
      selType="maintain"
      :statusList="[4]"
    ></asset-list>
  </div>
</template>
<script>
  import AssetList from '@/views/equiment-full-life-cycle/components/select-device-dialog/index.vue';
  export default {
    name: 'ChangeInfo',
    components: { AssetList },
    serviceDicts: ['equipment_change_category'],
    props: {
      form: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        userInfo: this.$store.getters.userInfo
      };
    },
    methods: {
      onChooseDeviceSuccess(device) {
        this.form.equipmentId = device.id;
        this.form.equipmentName = device.name;
        this.form.model = device.model;
        this.form.equipmentCategoryName =
          device.equipmentCategoryName || device.categoryName;
        this.form.useDeptName = device.useDeptName;

        // this.form.receiveUser = undefined; // 切换部门的时候，重置维修人员
      },
      onChooseDeviceClick() {
        this.$refs.assetList.show([], true);
      }
    }
  };
</script>
<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  ::v-deep .form-content-height {
    .el-form-item__content {
      height: auto;
    }
  }

  /deep/.el-form-item__content {
    height: 32px;
  }
</style>
