<template>
  <basic-container :auto-height="true" noScrollbar>
    <div class="top-info">
      <search ref="search" @search="onsubmit" />
      <section class="btn">
        <el-button
          icon="el-icon-upload2"
          type="success"
          size="small"
          @click="exportExcel"
          >导出</el-button
        >
        <el-button
          icon="el-icon-printer"
          type="primary"
          plain
          size="small"
          @click="print"
          >打印</el-button
        >
      </section>
    </div>

    <div class="content" v-loading="loading" style="height: calc(100% - 100px)">
      <div>
        <el-table
          class="table"
          :data="list"
          row-key="id"
          size="small"
          border
          ref="singleTable"
          :header-cell-style="{ background: '#fafafa' }"
          @current-change="handleCurrentChange"
          highlight-current-row
          @selection-change="handleSelectionChange"
          height="calc(100% - 50px)"
        >
          <el-table-column
            type="index"
            label="#"
            align="center"
          ></el-table-column>
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column
            prop="sn"
            label="SN编号"
            align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="name"
            label="设备名称"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.name || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="useDeptName"
            label="使用部门"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.useDeptName || '-' }}
            </template>
          </el-table-column>

          <el-table-column
            prop="rfid"
            label="NFC卡号"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              {{ scope.row.nfc || '-' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="isPastedName"
            label="是否贴码"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-tag
                size="small"
                :type="scope.row.isPasted === 0 ? 'warning' : 'success'"
              >
                {{ scope.row.isPastedName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="imageList"
            label="设备图片"
            align="center"
            show-overflow-tooltip
            width="90px"
          >
            <template slot-scope="scope">
              <el-image
                v-if="scope.row.imageList"
                style="width: 50px; height: 50px"
                :src="getFileFullUrl(scope.row.imageList[0].id)"
                :preview-src-list="[getFileFullUrl(scope.row.imageList[0].id)]"
                fit="cover"
              ></el-image>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            prop=""
            label="操作"
            align="center"
            show-overflow-tooltip
            width="100px"
          >
            <template slot-scope="scope">
              <!--     点击绑卡的时候、展示弹窗-->
              <el-button
                type="text"
                size="mini"
                @click="handleBindClick(scope.row)"
                >绑卡</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :page-size.sync="searchParams.size"
          :page-no.sync="searchParams.current"
          :total="total"
          @pagination="getList"
        />
      </div>
      <div class="code" style="width: 450px">
        <code-card
          ref="qrcode"
          :isShow="isShow"
          :current-row="currentRow"
        ></code-card>
      </div>
    </div>
    <ws-connect
      ref="bindCard"
      @doDateMessage="getDoDateMessage"
      @request="Request"
    ></ws-connect>
  </basic-container>
</template>

<script>
  import CodeCard from './qrCode/index.vue';
  import WsConnect from './components/bind-card.vue';
  import Search from './search';
  import Pagination from '@/components/pagination';
  import {
    accountListPage,
    bindCard
  } from '@/api/equiment-full-life-api/ledger';
  import { downloadFileBlob } from '@/util/util';
  import { getToken } from '@/util/auth';
  import { printQrCodeImg } from '@/util/print.js';
  import WebReader from './components/connect';
  import { getFileFullUrl } from '@/util/file';

  export default {
    name: 'DeviceBasicList',
    components: {
      Search,
      Pagination,
      CodeCard,
      WsConnect
    },
    props: {},
    data() {
      return {
        getFileFullUrl,
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10
        },
        exportParams: {},
        currentRow: {},
        isShow: true,
        multipleSelection: [],
        reader: undefined,
        //--------------------
        g_device: '0x', //设备句柄号
        g_isOpen: false, // 检查读写器USB 接口是否已经打开
        g_blockAddr: undefined,
        g_blockData: undefined,
        g_key: undefined,
        g_keyType: undefined,
        g_value: undefined,
        g_wantFunc: 0,

        // 封装功能方法编号
        GFUNC: {
          M1_findCard: 1,
          M1_authentication: 2,
          M1_read: 3,
          M1_write: 4,
          M1_initVal: 5,
          M1_increment: 6,
          M1_decrement: 7,
          M1_readVal: 8,
          M1_updateKey: 9
        },
        UUID: undefined,
        isLinkFlag: true,
        rData: {},
        init: false
        //
      };
    },
    watch: {},
    created() {
      this.reader = WebReader.WebReader.OBJ();
      console.log(';;;;qw', this.reader);
      this.linkWs();
    },
    beforeDestroy() {
      if (this.reader) {
        this.reader.Disconnect();
      }
    },
    // 页面销毁取消监听
    methods: {
      linkWs() {
        this.$nextTick(async () => {
          await this.reader.createSocket(); // 创建连接\
          await this.resp();
          await this.getList();
          await this.Connect();
        });
      },
      handleBindClick(row) {
        this.currentRow = row;
        this.init = true;
        if (!this.g_isOpen) {
          this.Connect();
        } else {
          this.$refs.bindCard.show(this.currentRow.code, this.currentRow.nfc);
        }
      },
      showDialog(isLinkFlag, strStatus) {
        if (!isLinkFlag && strStatus !== '00') {
          return;
        } else if (isLinkFlag && strStatus === '00') {
          this.$refs.bindCard.show(this.currentRow.code, this.currentRow.nfc);
        }
      },
      // 点击绑卡之后，返回的数据
      getDoDateMessage(UUID) {
        this.bindCard(this.currentRow.id, UUID);
      },
      // 绑卡
      async bindCard(id, UUID) {
        try {
          await bindCard({ id: id, nfc: UUID });
          await this.getList();
          this.$message.success('绑卡成功');
        } catch (e) {
          console.log(e);
          // this.$message.error(e.msg);
        }
      },

      //  应答
      resp() {
        const _this = this;
        this.reader.onResult(function (rData) {
          _this.rData = rData;
          switch (rData.strCmdCode) {
            case '0007': // 开始链接
              if (rData.strStatus != '00') {
                // 设备没有链接

                _this.isLinkFlag = false; //没有链接
                _this.showDialog(_this.isLinkFlag, rData.strStatus);
                _this.$message.error(
                  '读卡设备连接失败，检查是否插入读卡器设备'
                );
              } else {
                _this.g_isOpen = true;
                _this.isLinkFlag = true;
                if (_this.init) {
                  _this.showDialog(_this.isLinkFlag, rData.strStatus);
                }
                _this.$message.success('读卡设备连接成功');
              }
              break;

            case '0009': //Sys_Close关闭USB连接
              if (rData.strStatus != '00') {
                _this.$message.error('USB链接已经关闭');
              } else {
                _this.$message.error('读卡器已成功断开连接');
              }
              break;

            case '0106': //Sys_SetBuzzer // 设置蜂鸣时间
              break;

            case '0105': //Sys_GetSnr // 读取读写器唯一序列号
              if (rData.strStatus != '00') {
                _this.$message.error('获取设备序列号失败');
              } else {
                _this.$message.success('成功获取设备序列号' + rData.strData);
              }
              break;

            case '1001': //读卡失败 00 状态下是读卡成功
              if (rData.strStatus != '00') {
                // 如果发现读卡失败，3s关闭弹窗，状态初始化
                _this.$message.error('获取卡号失败');
                console.log(_this.isLinkFlag, _this.g_isOpen);
                _this.$refs['bindCard'].centerDialogVisible = false;
                _this.g_isOpen = false;
                return;
              }
              switch (_this.g_wantFunc) {
                case _this.GFUNC.M1_findCard:
                case _this.GFUNC.M1_authentication:
                case _this.GFUNC.M1_read:
                case _this.GFUNC.M1_write:
                case _this.GFUNC.M1_initVal:
                case _this.GFUNC.M1_increment:
                case _this.GFUNC.M1_decrement:
                case _this.GFUNC.M1_readVal:
                case _this.GFUNC.M1_updateKey:
                  // _this.reader.send(_this.g_device + '1002'); //TyA_Anticollision
                  _this.reader.send(_this.g_device + '1101'); //TyA_Anticollision 防冲撞
                  break;
              }

              break;
            //  获取 卡号
            case '1101':
              if (rData.strStatus != '00') {
                // document.getElementById('tfTips').value = 'TyA_CS_Read faild !';
                _this.$message.error('获取卡号失败');
                console.log(_this.isLinkFlag, _this.g_isOpen);
                _this.$refs['bindCard'].centerDialogVisible = false;
                _this.g_isOpen = false;
              } else {
                _this.$message.success('读卡成功！');
                // _this.UUID = rData.strData;
                _this.$refs.bindCard.UUID = rData.strData;
              }
              break;
            //  下面没用了
            case '1002': //TyA_Anticollision // 防冲撞
              if (rData.strStatus != '00') {
                _this.$message.error('TyA_防碰撞失败');
                return;
              }
              switch (_this.g_wantFunc) {
                case _this.GFUNC.M1_findCard:
                // document.getElementById('tfUID').value = rData.strData;
                // _this.$message.success('读卡成功');
                // // _this.UUID = rData.strData;
                // _this.$refs.bindCard.UUID = rData.strData;
                case _this.GFUNC.M1_authentication:
                case _this.GFUNC.M1_read:
                case _this.GFUNC.M1_write:
                case _this.GFUNC.M1_initVal:
                case _this.GFUNC.M1_increment:
                case _this.GFUNC.M1_decrement:
                case _this.GFUNC.M1_readVal:
                case _this.GFUNC.M1_updateKey:
                  _this.reader.send(_this.g_device + '1003' + rData.strData); //TyA_Select  //选定卡
                  _this.reader.send(_this.g_device + '100B' + '00');
                  break;
              }

              break;

            case '1003': //TyA_Select
              if (rData.strStatus != '00') {
                _this.$message.error('选定卡片失败');
                return;
              }
              switch (_this.g_wantFunc) {
                case _this.GFUNC.M1_authentication:
                case _this.GFUNC.M1_read:
                case _this.GFUNC.M1_write:
                case _this.GFUNC.M1_initVal:
                case _this.GFUNC.M1_increment:
                case _this.GFUNC.M1_decrement:
                case _this.GFUNC.M1_readVal:
                case _this.GFUNC.M1_updateKey:
                  console.log('send......');
                  _this.reader.send(
                    _this.g_device +
                      '100A' +
                      _this.g_keyType +
                      _this.g_blockAddr +
                      _this.g_key
                  ); //TyA_CS_Authentication2
                  break;
              }

              break;

            case '100A': //TyA_CS_Authentication2
              if (rData.strStatus != '00') {
                _this.$message.error('直接传入密码验证Mifare卡失败！');
                return;
              }

              switch (_this.g_wantFunc) {
                case _this.GFUNC.M1_read:
                  _this.reader.send(
                    _this.g_device + '100B' + _this.g_blockAddr
                  ); //TyA_CS_Read
                  break;

                case _this.GFUNC.M1_write:
                  _this.reader.send(
                    _this.g_device +
                      '100C' +
                      _this.g_blockAddr +
                      _this.g_blockData
                  );
                  break;

                case _this.GFUNC.M1_initVal:
                  _this.reader.send(
                    _this.g_device + '100D' + _this.g_blockAddr + _this.g_value
                  );
                  break;

                case _this.GFUNC.M1_readVal:
                  _this.reader.send(
                    _this.g_device + '100E' + _this.g_blockAddr
                  );
                  break;

                case _this.GFUNC.M1_decrement:
                  _this.reader.send(
                    _this.g_device + '100F' + _this.g_blockAddr + _this.g_value
                  );
                  break;

                case _this.GFUNC.M1_increment:
                  _this.reader.send(
                    _this.g_device + '1010' + _this.g_blockAddr + _this.g_value
                  );
                  break;
              }

              break;

            case '100B': //TyA_CS_Read
              if (rData.strStatus != '00') {
                // document.getElementById('tfTips').value = 'TyA_CS_Read faild !';
                _this.$message.error('读块失败！');
                _this.$message.error('获取卡号失败');
                console.log(_this.isLinkFlag, _this.g_isOpen);
                _this.$refs['bindCard'].centerDialogVisible = false;
                _this.g_isOpen = false;
              } else {
                _this.$message.success('读块成功！');
                // _this.UUID = rData.strData;
                let str = rData.strData.substr(0, 14);
                _this.$refs.bindCard.UUID = str;
              }
              break;

            // case '100C': //TyA_CS_Write
            //   if (rData.strStatus != '00') {
            //     _this.$message.error('写块失败！');
            //   } else {
            //     _this.$message.warning('写块成功！');
            //   }
            //
            //   break;
            //
            // case '100D': //TyA_CS_InitValue
            //   if (rData.strStatus != '00') {
            //     _this.$message.error('初始化钱包失败！');
            //   } else {
            //     _this.$message.success('初始化钱包成功！');
            //   }
            //   break;
            //
            // case '100E': //TyA_CS_ReadValue
            //   if (rData.strStatus != '00') {
            //     _this.$message.error('读取钱包余额失败！');
            //   } else {
            //     var hexValue = rData.strData;
            //     hexValue =
            //       hexValue.substr(6, 2) +
            //       hexValue.substr(4, 2) +
            //       hexValue.substr(2, 2) +
            //       hexValue.substr(0, 2); //Reverse sorting of high and low bytes (高低字节反过来排序)
            //     var decValue = parseInt(hexValue, 16); //Convert hexadecimal string to decimal string (十六进制字符串转换为十进制字符串)
            //     _this.$message.success('读取钱包余额成功！' + decValue);
            //   }
            //   break;
            //
            // case '100F': //TyA_CS_Decrement
            //   if (rData.strStatus != '00') {
            //     _this.$message.error('扣款失败！');
            //   } else {
            //     _this.$message.success('扣款成功！');
            //   }
            //   break;
            //
            // case '1010': //TyA_CS_Increment
            //   if (rData.strStatus != '00') {
            //     _this.$message.error('充值失败！');
            //   } else {
            //     _this.$message.success('充值成功！');
            //   }
            //   break;
          }
        });
      },
      LedGreen() {
        this.reader.send(this.g_device + '0107' + '02');
      },
      /**
       * Reading card number
       * (寻卡并读卡号)
       **/
      Request() {
        const _this = this;
        if (this.g_isOpen != true) {
          _this.$message.error('请先连接设备！');
          return;
        }
        //Clear UID edit box
        _this.UUID = '';
        //Start read UID
        this.reader.send(_this.g_device + '1001' + '52'); //TyA_Request // 寻卡
        this.g_wantFunc = _this.GFUNC.M1_findCard;
      },
      /**
       * Connect the reader and initialize the working mode
       * (连接读卡器并初始化工作模式)
       **/
      Connect() {
        this.reader.send(this.g_device + '0007' + '00'); //Open the USB device with index number 0. (打开索引号为0的USB设备)
        this.reader.send(this.g_device + '0109' + '41'); //Set to ISO14443a working mode. (设置为ISO14443A工作模式)
        this.reader.send(this.g_device + '0108' + '01'); //Turn on the reader antenna. (打开读卡器天线)
        this.LedGreen();
        const _this = this;
        setTimeout(_this.LedRed, '200');
        this.reader.send(this.g_device + '0106' + '10'); //Beeps. (蜂鸣提示)
      },
      /**
       * Turn on the red light
       * (亮红灯)
       **/
      LedRed() {
        this.reader.send(this.g_device + '0107' + '01');
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      setCurrent(row) {
        this.$refs.singleTable.setCurrentRow(row);
      },
      handleCurrentChange(val) {
        this.currentRow = val;
      },
      async print() {
        let list;
        let batch = this.multipleSelection.length > 0 ? true : false;
        if (!batch) {
          list = [
            {
              ...this.currentRow,
              useUserName: this.currentRow.userName || '-',
              useDeptName: this.currentRow.useDeptName || '-'
            }
          ];
        } else {
          let ids = [];
          list = this.multipleSelection.map((item) => {
            ids.push(item.id);
            return {
              ...item,
              useUserName: item.userName || '-',
              useDeptName: item.useDeptName || '-'
            };
          });
        }
        printQrCodeImg(list);
      },
      async exportExcel() {
        let params = '';
        let path = '';
        if (Object.keys(this.exportParams).length === 0) {
          path = `/api/szyk-simas/equipment-account/export-label?`;
        } else {
          for (const key in this.exportParams) {
            if (this.exportParams[key]) {
              params += `${key}=${this.exportParams[key]}&`;
            }
          }
          path = `/api/szyk-simas/equipment-account/export-label?${params}`;
        }
        downloadFileBlob(
          ` ${path}${this.website.tokenHeader}=${getToken()}`,
          '设备台账.xlsx'
        );
      },

      onsubmit(param) {
        this.exportParams = param;
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      },
      async getList() {
        this.loading = true;
        try {
          let res = await accountListPage({
            ...this.searchParams
          });
          this.list = res.data.data.records || [];
          if (this.list.length > 0) {
            this.setCurrent(this.list[0]);
            this.currentRow = this.list[0];
            this.isShow = true;
          } else {
            this.currentRow = {};
            this.isShow = false;
          }
          this.total = res.data.data.total;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  .content {
    display: grid;
    grid-template-columns: calc(100% - 480px) 500px;
    grid-gap: 20px;
    //height: calc(100% - 150px);
  }
  .pagination-container {
    text-align: left;
  }
  .code {
    width: 450px;
    display: flex;
    justify-content: flex-start;
  }
  .btn {
    width: 100%;
    margin-bottom: 10px;
  }
  ::v-deep {
    .el-table__body-wrapper {
      //height: 100%;
    }
  }
</style>
