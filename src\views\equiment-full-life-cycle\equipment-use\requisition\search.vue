<template>
  <div class="search-content">
    <el-form
      label-suffix="："
      :inline="true"
      ref="search"
      :model="form"
      size="small"
      class="search-form"
    >
      <el-form-item label="领用单号" prop="orderNo">
        <el-input
          v-model.trim="form.orderNo"
          placeholder="请输入"
          style="width: 150px"
          clearable
          maxlength="50"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="申请人" prop="applyUserName">
        <el-input
          v-model.trim="form.applyUserName"
          placeholder="请输入"
          style="width: 150px"
          clearable
          maxlength="50"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="领用状态" prop="status">
        <el-select
          v-model="form.status"
          style="width: 150px"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in serviceDicts.type['device_receive_status']"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请日期范围" prop="time" class="_label">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          clearable
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <btn type="search" @click="submit" />
        <btn type="reset" @click="reset" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'DeviceUseSearch',
    serviceDicts: ['device_receive_status'],
    data() {
      return {
        orgListLoading: false,
        useDeptOptions: [],
        form: {
          applyUserName: undefined,
          orderNo: undefined,
          startApplyDate: undefined,
          endApplyDate: undefined,
          time: undefined,
          status: undefined
        }
      };
    },
    methods: {
      onSelectDeptClick() {
        this.$refs['dept-dialog'].show();
      },
      // 选择部门回调
      onSelectDept(dept) {
        this.form.chargeDept = dept.id;
        this.form.chargeDeptName = dept.deptName;
      },
      reset() {
        this.$refs['search'].resetFields();
        this.submit();
      },
      submit() {
        let params = {
          ...this.form,
          startApplyDate: this.form.time ? this.form.time[0] : undefined,
          endApplyDate: this.form.time ? this.form.time[1] : undefined
        };
        delete params.time;
        this.$emit('search', params);
      }
    }
  };
</script>

<style lang="scss" scoped></style>
