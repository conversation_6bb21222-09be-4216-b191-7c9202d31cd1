<template>
  <div v-if="visible" v-loading="loading">
    <add-standard
      ref="standard"
      :id="eqId"
      :details="details"
      :monitorList="monitorList"
      :oilType="oilType"
      :wayList="wayList"
    ></add-standard>
  </div>
</template>
<script>
  import AddStandard from './add-standard.vue';
  import {
    userDetail,
    userAddOrUpdate,
    getPartList,
    oilingTypeList,
    getOilingWaySelect
  } from '@/api/equiment-full-life-api/oiling';

  export default {
    name: 'RepairViewIndex',
    components: {
      AddStandard
    },
    data() {
      return {
        eqId: '',
        type: 'ADD',
        visible: false,
        loading: false,
        monitorList: [],
        oilType: [], // 油品
        wayList: [], // 手段
        details: {
          equipmentAccount: {},
          lubricateStandardsList: []
        },
        standList: []
      };
    },
    methods: {
      async handleSubmit(standList) {
        try {
          await userAddOrUpdate({
            equipmentId: this.eqId,
            lubricateStandardsList: standList
          });
          this.$emit('success');
          this.$message.success('操作成功');
          this.getDetail(this.eqId);
        } catch (e) {
          console.error(e);
        }
      },
      // 滚动条定位到第一个校验失败的div的位置
      locateToErr() {
        setTimeout(() => {
          const errorDiv = document.getElementsByClassName('is-error');
          errorDiv && errorDiv.length && errorDiv[0].scrollIntoView();
        }, 0);
      },
      async submit() {
        try {
          this.loading = true;
          const standList = await this.$refs.standard.validForm();
          if (standList.length === 0) {
            const msg =
              this.type === 'ADD'
                ? '您还没有添加任何标准，是否确认提交？'
                : `是否确定清空${this.details.equipmentAccount.name}的润滑标准？清空不影响已生成的工单，但是此设备将不再生成新的工单！`;
            this.$confirm(msg, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                this.handleSubmit(standList);
                this.type = 'ADD';
              })
              .catch(() => {});
          } else {
            this.handleSubmit(standList);
          }

          this.loading = false;
        } catch (e) {
          // 校验未通过时, 滚动到错误信息位置
          this.locateToErr();
          console.log(e);
          this.loading = false;
        }
      },
      getMonitorStandardsData() {
        return this.$refs.standard.$data.form.monitorList;
      },
      setMonitorStandardsData(data) {
        this.$refs.standard.setList(data);
      },
      close() {
        this.visible = false;
      },
      //  获取手段；列表
      async oilingWaySelect() {
        try {
          this.loading = true;
          const res = await getOilingWaySelect();
          this.wayList = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  获取油品类型信息
      async getOilTypeList() {
        try {
          this.loading = true;
          const res = await oilingTypeList();
          this.oilType = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      //  获取部位信息
      async getMonitorList(id) {
        try {
          this.loading = true;
          const res = await getPartList({ equipmentId: id });
          this.monitorList = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      // 点击展示
      show(id, type) {
        this.visible = true;
        this.type = type;
        if (id) {
          this.eqId = id;
          this.getDetail(id);
          this.getMonitorList(id);
          this.getOilTypeList(); // 油品类型
          this.oilingWaySelect(); // 润滑方式
        }
      },

      // 获取详情接口
      async getDetail(id) {
        try {
          this.loading = true;
          const res = await userDetail({ equipmentId: id });
          this.details = res.data.data;
          this.type =
            this.details.lubricateStandardsList &&
            this.details.lubricateStandardsList.length
              ? 'EDIT'
              : 'ADD';
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }
</style>
