<template>
  <dialog-drawer
    title="详情"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <section>
        <span class="el-base-title">基础信息</span>
        <base-info :details="details.equipmentAccount"></base-info>
      </section>
      <!-- 部位信息 -->
      <span class="el-base-title">标准内容</span>
      <standard
        ref="addPosition"
        :list="details.monitorStandardList"
      ></standard>
    </div>
  </dialog-drawer>
</template>
<script>
  import Standard from './standard.vue';
  import BaseInfo from '../operate/base-info.vue';
  import { userDetail } from '@/api/equiment-full-life-api/inspect';
  export default {
    name: 'RepairViewIndex',
    components: {
      BaseInfo,
      Standard
    },
    data() {
      return {
        visible: false,
        loading: false,
        details: { equipmentAccount: {}, monitorStandardList: [] } // 详情数据
      };
    },
    methods: {
      closed() {
        this.details = { equipmentAccount: {}, monitorStandardList: [] };
        this.visible = false;
      },

      // 点击展示
      async show(id) {
        this.visible = true;
        if (id) {
          await this.getDetail(id);
        }
      },
      // 获取详情接口
      async getDetail(id) {
        try {
          this.loading = true;
          const res = await userDetail({ equipmentId: id });
          this.details = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }
</style>
