<template>
  <!-- 详情基本信息 -->
  <div>
    <el-descriptions
      border
      :labelStyle="{ width: '110px', textAlign: 'right' }"
      :contentStyle="{
        width: '300px',
        wordBreak: 'break-all',
        wordWrap: 'break-word'
      }"
      contentClassName="contentClassName"
    >
      <el-descriptions-item label="人员姓名：">{{
        details.name || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="联系电话：">{{
        details.tel || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="所属部门：">{{
        details.deptName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="所属岗位：">{{
        details.postName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="人员性别：">{{
        details.sexName || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="身份证号：">{{
        details.idNumber || '-'
      }}</el-descriptions-item>
      <el-descriptions-item label="年龄：">{{
        details.age || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="出生年月："
        >{{ details.birthday || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="员工性质：">{{
        details.propertyName || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="学历：">
        {{ details.qualificationName || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="职称：">
        {{ details.title || '-' }}
      </el-descriptions-item>
      <el-descriptions-item label="工作年限：">
        {{ details.workYears || '-' }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>
  export default {
    props: {
      // 传递的基本信息
      details: {
        type: Object,
        required: true
      }
    },
    components: {},

    data() {
      return {};
    },

    methods: {}
  };
</script>

<style lang="scss" scoped>
  /deep/.el-descriptions {
    margin-bottom: 10px;
  }

  /deep/.el-image {
    margin-right: 10px;
  }
</style>
