<template>
  <dialog-drawer
    title="派单"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    size="80%"
    class="device-add"
  >
    <section v-loading="loading">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-suffix="："
        label-position="right"
        label-width="120px"
      >
        <p class="el-base-title">报修信息</p>
        <el-row>
          <el-col :span="8">
            <el-form-item label="维修人员" prop="receiveUser">
              <el-select
                v-model="form.receiveUser"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.realName"
                  :value="user.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预计完成时间" prop="completeTime">
              <el-date-picker
                style="width: 100%"
                type="datetime"
                v-model="form.completeTime"
                value-format="yyyy-MM-dd HH:mm:00"
                placeholder="请选择预计完成时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="派单说明" prop="">
              <el-input
                style="width: 100%"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 4 }"
                v-model="form.remark"
                maxlength="200"
                placeholder="请输入派单说明"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
    <!--     选择人员-->
    <recipient-dialog
      ref="recipient"
      @selected="onUserSelect"
    ></recipient-dialog>
  </dialog-drawer>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { dispatchRepairApi } from '@/api/equiment-full-life-api/repair';
  import RecipientDialog from '@/components/recipient-dialog/index.vue';
  import { roleUserListApi } from '@/api/user';
  export default {
    name: 'repair-internal-dispatch',
    components: { RecipientDialog },
    data() {
      return {
        visible: false,
        detail: {},
        loading: false,
        userListLoading: false,
        userList: [],
        edit: false,
        form: {
          // 维修人
          receiveUser: '',
          // 预计完成时间
          completeTime: '',
          // 派单说明
          remark: ''
        },
        rules: {
          receiveUser: [
            { required: true, message: '请选择维修人员', trigger: 'blur' }
          ],
          completeTime: [
            { required: true, message: '请选择预计完成时间', trigger: 'blur' }
          ]
        }
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'permission'])
    },
    methods: {
      show(row) {
        if (row && row.id) {
          this.visible = true;
          this.getUserList();
          this.detail = row;
        } else {
          this.$message.warning('该数据异常');
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.form.receiveUser = '';
        this.form.completeTime = '';
        this.form.remark = '';
        this.detail = {};
        this.visible = false;
      },
      async submit() {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            try {
              this.loading = true;
              await dispatchRepairApi({
                id: this.detail.id,
                ...this.form
              });
              this.$message.success('操作成功');
              this.$emit('success');
              this.visible = false;
              this.loading = false;
            } catch (e) {
              this.loading = false;
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      async getUserList() {
        try {
          this.userListLoading = true;
          const res = await roleUserListApi({
            alias: 'repair_user'
          });
          this.userList = res.data.data;
          this.userListLoading = false;
        } catch (e) {
          this.userListLoading = false;
          this.monitorList = [];
        }
      },
      //  选择人员
      onChooseUser() {
        this.$refs['recipient'].show();
      },
      onUserSelect(user) {
        console.log('user', user);
        this.form.receiveUser = user.id;
        this.form.receiveUserName = user.realName;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-form-item__content {
    height: 32px;
  }
</style>
