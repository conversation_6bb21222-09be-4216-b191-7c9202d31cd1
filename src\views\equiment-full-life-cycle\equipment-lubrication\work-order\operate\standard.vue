<template>
  <el-form
    :model="form"
    inline
    label-suffix="："
    ref="baseForm"
    label-width="120px"
    label-position="left"
    size="small"
    :rules="rule"
  >
    <section class="container">
      <el-row class="add-info" :gutter="20">
        <el-col :span="24">
          <el-form-item label="检查结果" prop="isAbnormal">
            <el-radio-group v-model="form.isAbnormal">
              <el-radio :label="0">正常</el-radio>
              <el-radio :label="1">异常</el-radio>
            </el-radio-group></el-form-item
          >
        </el-col>
        <el-col :span="24">
          <el-form-item label="润滑部位">
            {{ detail.equipmentMonitorName || '-' }}</el-form-item
          >
        </el-col>
        <el-col :span="24">
          <el-form-item label="润滑方式">
            {{ detail.methodsName || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="油品类型">
            {{ detail.oilTypeName || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上次润滑时间">
            {{ detail.lastTime || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="计划润滑时间">
            {{ detail.planTime || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="本次润滑时间">
            {{ detail.executeTime || '-' }}
          </el-form-item>
        </el-col>
      </el-row>
      <!--       正常不展示，异常展示下面的内容-->
      <section
        v-if="form.isAbnormal === 1"
        style="
          padding: 15px;
          margin-left: 10px;
          border-left: 1px dashed #409eff;
        "
      >
        <el-row class="add-info" :gutter="20">
          <el-col :span="24">
            <el-form-item label="异常描述" prop="abnormalComment">
              <el-input
                v-model="form.abnormalComment"
                type="textarea"
                placeholder="请输入异常描述"
                clearable
                show-word-limit
                :maxlength="200"
                autosize
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="异常等级" prop="abnormalLevel">
              <el-select
                v-model="form.abnormalLevel"
                filterable
                placeholder="请选择异常等级"
                clearable
              >
                <el-option
                  v-for="dict in serviceDicts.type['defect_level']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否现场处理" prop="isHandled">
              <el-select
                v-model="form.isHandled"
                filterable
                placeholder="请选择是否现场处理"
                clearable
              >
                <el-option
                  v-for="dict in systemDicts.type['yes_no']"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="attachList" label="异常图片">
              <upload-img
                v-model="form.attachList"
                placeholder="上传图片"
                :limit="3"
                formatLimit="jpeg,png,jpg"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </section>
    </section>
  </el-form>
</template>

<script>
  import OperateClasses from '@/views/equiment-full-life-cycle/equipment-inspection/plan/operate/operating-classes.vue';
  import UploadImg from '@/components/uploadImage.vue';

  export default {
    name: 'DeviceBasicList',
    components: { OperateClasses, UploadImg },
    serviceDicts: ['defect_level'],
    systemDicts: ['yes_no'],
    props: {
      detail: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        form: {
          attachList: [],
          isAbnormal: 0,
          abnormalComment: undefined,
          abnormalLevel: undefined,
          isHandled: undefined
        },
        rule: {
          isHandled: [
            {
              required: true,
              message: '请选择是否现场处理',
              trigger: ['change']
            }
          ],
          isAbnormal: [
            {
              required: true,
              message: '请选择检查结果',
              trigger: 'change'
            }
          ],
          abnormalComment: [
            {
              required: true,
              message: '请输入异常描述',
              trigger: 'blur'
            }
          ],
          abnormalLevel: [
            {
              required: true,
              message: '请选择异常等级',
              trigger: 'change'
            }
          ],
          attachList: [
            {
              required: true,
              message: '请选择异常图片',
              trigger: 'change'
            }
          ]
        }
      };
    },
    watch: {},
    mounted() {},
    beforeDestroy() {},

    methods: {
      //  获取参数
      async validForm() {
        //   先校验
        let bool = await this.$refs.baseForm.validate();
        if (bool) {
          return {
            ...this.form,
            attachIds:
              this.form.attachList &&
              this.form.attachList.map((attach) => attach.id).join(','),
            monitorId: this.detail.monitorId,
            standardId: this.detail.id
          };
        } else {
          return false;
        }
      },
      resetForm() {
        this.$refs.baseForm.resetFields();
      }
    }
  };
</script>

<style scoped lang="scss">
  ::v-deep {
    .el-form-item,
    .el-select {
      width: 100%;
    }
    .el-form-item__content {
      width: calc(100% - 120px);
    }
    .el-textarea .el-input__count {
      background: unset !important;
    }
    .avatar-uploader-icon {
      width: 100px;
      height: 100px;
      display: flex !important;
      flex-direction: column !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }
  .container {
    display: grid;
    grid-template-columns: 30% 70%;
    border: 1px dashed #409eff;
    margin-bottom: 20px;
    padding: 20px;
    border-left: 4px solid #409eff; //#1586ef;
    border-radius: 4px;
  }

  .el-col {
    margin-bottom: 0 !important;
  }
</style>
