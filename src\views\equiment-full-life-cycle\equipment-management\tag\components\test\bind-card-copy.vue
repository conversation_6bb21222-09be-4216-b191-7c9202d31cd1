<template>
  <span>
    <a @click="bindCardCode">{{ content }}</a>
    <el-dialog
      title="提示"
      :visible.sync="centerDialogVisible"
      width="30%"
      center
      :append-to-body="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <section v-loading="loading">
        <div class="cards">
          <span :class="returnValue === '00000000' ? 'nones' : 'normal'"
            >设备卡号：{{ returnValue }}</span
          >
          <span>设备编号：{{ devEqumentNo }}</span>
        </div>
        <p class="desc">确定将以上设备卡号和设备编号绑定吗？</p>
        <span slot="footer" class="dialog-footer">
          <el-button @click="centerDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
        </span>
      </section>
    </el-dialog>
  </span>
</template>
<script>
  export default {
    props: {
      content: {
        type: String,
        default: () => {
          return '读取';
        }
      },
      devEqumentNo: {
        type: String,
        default: () => {
          return '暂无';
        }
      }
    },
    watch: {
      devEqumentNo: {
        handler(val) {
          console.log(val);
        }
      }
    },
    data() {
      return {
        ws: undefined,
        m_isConnectWS: undefined,
        m_closed: undefined,
        callbacklist: { 9001: [] },
        centerDialogVisible: false,
        loading: false,
        returnValue: undefined
      };
    },
    created() {
      this.onload();
    },
    beforeDestroy() {
      this.onunload();
    },
    methods: {
      connect() {
        // 2
        // console.log("链接");
        // let url = "ws://127.0.0.1:36667";
        let url = 'ws://localhost:39002/RFID Reader Service';

        if ('WebSocket' in window) {
          this.ws = new WebSocket(url);
        } else {
          this.$message.warning(
            '浏览器版本过低,\r\n浏览器要求：Chrome14+/FireFox7+/Opera11+'
          );
        }
        // 2
        this.ws.onopen = () => {
          // console.log("打开链接");
          this.m_isConnectWS = true;
          this.m_closed = false;
          this.openwshandle();
        };
        // 5
        this.ws.onmessage = (evt) => {
          console.log('onmessage', evt);
          if (typeof evt.data == 'string') {
            let str = evt.data;
            if (str.length <= 0) {
              return;
            }
            console.log(str);
            this.onResiveServerMsg(str);
          }
        };
        this.ws.onerror = (evt) => {
          // console.log("error", evt);
        };
        this.ws.onclose = () => {
          this.m_isConnectWS = false;
          setTimeout(() => {
            this.connect();
          }, 10000);
        };
      },
      // 1
      disconnect() {
        console.log('Disconnected');
      },
      // 点检之后 4
      sendMsg(body) {
        //CONNECTING：值为0，表示正在连接；
        // OPEN：值为1，表示连接成功，可以通信了；
        // CLOSING：值为2，表示连接正在关闭；
        // CLOSED：值为3，表示连接已经关闭，或者打开连接失败。
        switch (this.ws.readyState) {
          case 0:
            this.$message.warning('正在连接......');
            break;
          case 1:
            this.ws.send(JSON.stringify(body));
            break;
          case 2:
            this.$message.warning('连接正在关闭......');
            break;
          case 3:
            this.$message.warning('连接已经关闭');
        }
      },

      onload() {
        this.connect();
      },
      onunload() {
        this.disconnect();
      },
      onResiveServerMsg(message) {
        console.log('message');
        if (message['recode'] != '0') {
          return;
        }
        if ('9001' == message['bizcode']) {
          this.do9001(message['body']);
        }
      },
      do9001(body) {
        if (!this.docallback('9001', body)) {
          let idcardinfo = body['idcardinfo'];
          let sss = '';
          let first = true;
          for (var key in idcardinfo) {
            if (first) {
              first = false;
            } else {
              sss += '\n';
            }
            sss += key + ':' + idcardinfo[key];
          }
          this.returnValue = idcardinfo[key];
          this.loading = false;
          return sss;
        }
      },
      confirm() {
        this.$emit('doDateMessage', this.returnValue, false);
        this.centerDialogVisible = false;
      },
      // 3
      openwshandle() {
        // console.log("openwshandle");
      },
      docallback(type, body) {
        if (this.callbacklist[type].length >= 1) {
          while (this.callbacklist[type].length >= 1) {
            let callback = this.callbacklist[type].shift();
            if (typeof callback === 'function') {
              try {
                this.callback(body);
              } catch (e) {
                console.log(e);
              }
            }
          }
          return true;
        } else {
          return false;
        }
      },
      bindCardCode(iblock) {
        this.sendMsg({
          bizcode: '9001',
          body: { iblock: '' + iblock, skey: 'FFFFFFFFFFFF' }
        });
        setTimeout(() => {
          if (this.returnValue === undefined) {
            this.$message.error('读卡失败, 请检查是否连接了读卡器');
            this.returnValue = undefined; // 值置空
            this.centerDialogVisible = false; // 关弹窗
            this.loading = false; // 关loading
          }
        }, 5000);
        this.returnValue = undefined;
        this.centerDialogVisible = true;
        this.loading = true;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .cards {
    display: flex;
    justify-content: space-between;
    padding-left: 50px;
    padding-right: 50px;
  }
  .desc {
    text-align: center;
    color: rgb(245, 108, 108);
  }
  .nones {
    color: rgb(245, 108, 108);
  }
  .normal {
    color: #606266;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
  }
  a {
    color: #1890ff;
    cursor: pointer;
  }
</style>
