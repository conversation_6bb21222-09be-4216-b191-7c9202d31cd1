<template>
  <basic-container :autoHeight="true">
    <search ref="search" @query="search"></search>
    <el-table
      size="small"
      :data="list"
      border
      v-loading="loading"
      height="calc(100% - 100px)"
    >
      <el-table-column type="index" label="#" align="center"></el-table-column>
      <el-table-column
        prop="no"
        label="故障缺陷编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="faultName"
        label="故障缺陷名称"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="faultTypeName"
        label="故障缺陷类型"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="equipmentName"
        label="设备名称"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.equipmentName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="equipmentCategoryName"
        label="设备类型"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          {{ row.equipmentCategoryName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="equipmentSn"
        label="设备SN编号"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.equipmentSn || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="monitorName"
        label="故障缺陷位置"
        align="center"
        show-overflow-tooltip
      >
        <template v-slot="{ row }"> {{ row.monitorName || '-' }} </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="生成时间"
        align="center"
        show-overflow-tooltip
        width="150px"
      >
        <template v-slot="{ row }"> {{ row.createTime || '-' }} </template>
      </el-table-column>

      <el-table-column
        prop=""
        label="操作"
        align="center"
        width="80"
        show-overflow-tooltip
      >
        <template v-slot="{ row }">
          <el-button size="mini" type="text" @click="view(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!--     查看详情-->
    <view-info ref="viewInfo" @success="getList"></view-info>
  </basic-container>
</template>

<script>
  import Search from './search.vue';
  import ViewInfo from '../detail/index.vue';
  import { getCaseList } from '@/api/equiment-full-life-api/defect';

  export default {
    name: 'bearingLibraryIndex',
    components: { Search, ViewInfo },
    props: {},
    data() {
      return {
        searchParams: {
          size: 10,
          current: 1
        },
        total: 0,
        loading: false,
        list: []
      };
    },
    watch: {},
    mounted() {
      this.getList();
    },
    activated() {
      const { id } = this.$route.params;
      id && this.$refs['viewInfo'].show(id);
    },
    methods: {
      view(row) {
        this.$refs['viewInfo'].show(row.no);
      },

      // 點擊搜索
      search(params) {
        this.searchParams = { ...this.searchParams, ...params };
        this.getList();
      },

      // 获取列表
      async getList() {
        this.loading = true;
        try {
          let res = await getCaseList({
            ...this.searchParams
          });
          this.list = res.data.data.records;
          this.total = res.data.data.total;
          this.loading = false;
        } catch (message) {
          this.loading = false;
          console.log('err', message);
        }
      }
    }
  };
</script>

<style lang="scss" scoped></style>
