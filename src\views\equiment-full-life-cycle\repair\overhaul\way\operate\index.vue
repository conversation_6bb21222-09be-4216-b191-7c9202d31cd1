<template>
  <dialog-drawer
    :title="edit ? '编辑' : '新增'"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <section v-loading="loading">
      <p class="el-base-title">基本信息</p>
      <plan-cycle ref="info" :initData="detail"></plan-cycle>
    </section>
    <div class="oper_btn">
      <btn type="submit" @click="submit" :loading="loading"> </btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </dialog-drawer>
</template>

<script>
  import PlanCycle from './plan-cycle.vue';
  import {
    getCheckRepairTypeViewApi,
    addOrEditCheckRepairTypeApi
  } from '@/api/equiment-full-life-api/repair';
  export default {
    name: 'AddDevice',
    components: {
      PlanCycle
    },
    props: {},
    data() {
      return {
        visible: false,
        detail: {},
        list: [],
        loading: false,
        eqId: '',
        edit: false,
        form: {
          image: []
        }
      };
    },
    watch: {},
    methods: {
      async getDetail(id) {
        this.loading = true;
        try {
          const res = await getCheckRepairTypeViewApi({ id: id });
          this.detail = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },

      show(id) {
        this.visible = true;
        this.edit = !!id;
        this.eqId = id;

        if (id) {
          this.getDetail(id);
        }
      },
      setData(initData) {
        for (const initDataKey in this.form) {
          this.form[initDataKey] = initData[initDataKey];
        }
      },
      closed() {
        this.edit = false;
        this.eqId = '';
        this.visible = false;
        this.$refs['info'].resetForm();
        this.detail = {};
      },

      async submit() {
        let params = await this.$refs['info'].validForm();
        console.log(params);
        if (params) {
          await this.save({
            ...params,
            id: this.edit ? this.eqId : undefined
          });
        }
      },

      // 提交设备
      async save(params) {
        this.loading = true;
        try {
          await addOrEditCheckRepairTypeApi(params);
          this.$message.success('操作成功');
          this.$emit('success');
          this.visible = false;

          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .device-add {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }
</style>
