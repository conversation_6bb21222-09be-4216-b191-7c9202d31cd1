<template>
  <div style="height: 100%">
    <div class="top-info">
      <el-form
        label-suffix="："
        label-width="90px"
        :inline="true"
        ref="search"
        :model="form"
        size="small"
      >
        <el-form-item prop="timeType">
          <el-select
            v-model="form.timeType"
            placeholder="请选择"
            @change="query"
          >
            <el-option label="全部" :value="undefined"></el-option>
            <el-option
              v-for="dict in serviceDicts.type['time_period']"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      class="table"
      :data="list"
      row-key="id"
      size="small"
      height="500px"
      border
      ref="table"
      :header-cell-style="{ background: '#fafafa' }"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="no"
        label="工单编号"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <!-- <el-table-column
        prop="moduleName"
        label="工单类型"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.moduleName || '-' }}
        </template>
      </el-table-column> -->
      <el-table-column
        prop="startTime"
        label="工单开始时间"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.startTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="endTime"
        label="工单结束时间"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.endTime || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="executeUserName"
        label="责任人"
        align="center"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.executeUserName || '-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="statusName"
        label="工单状态"
        align="center"
        show-overflow-tooltip
        width="90px"
      ></el-table-column>

      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button
            icon="el-icon-view"
            type="text"
            size="small"
            @click="detail(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :page-size.sync="searchParams.size"
      :page-no.sync="searchParams.current"
      :total="total"
      @pagination="getList"
    />
    <!-- 检修工单详情 -->
    <detail-index ref="detailIndex"></detail-index>
  </div>
</template>

<script>
  import Pagination from '@/components/pagination';
  import DetailIndex from '@/views/equiment-full-life-cycle/repair/overhaul/work-order/detail/index.vue'; // 查看详情页面
  import { getCheckRepairOrderPageApi } from '@/api/equiment-full-life-api/repair';
  import dayjs from 'dayjs';

  export default {
    name: 'DeviceBasicList',
    serviceDicts: ['time_period'],
    components: {
      Pagination,
      DetailIndex
    },
    props: {
      equipmentId: {
        type: String,
        default: () => {
          return undefined;
        }
      }
    },
    watch: {
      equipmentId: {
        immediate: true,
        deep: true,
        handler(val) {
          if (val) {
            this.eqId = val;
            this.query();
          } else {
            this.eqId = undefined;
          }
        }
      }
    },
    data() {
      return {
        loading: false,
        total: 0,
        list: [],
        searchParams: {
          current: 1,
          size: 10,
          onlyQuerySpecialType: true
        },
        eqId: undefined,
        form: {
          timeType: 'THIRTY_DAYS'
        }
      };
    },

    methods: {
      query() {
        let num = 0;
        if (this.form.timeType === 'THIRTY_DAYS') {
          num = 30;
        } else if (this.form.timeType === 'ONE_HUNDRED_EIGHTY_DAYS') {
          num = 180;
        } else if (this.form.timeType === 'ONE_YEAR') {
          num = 365;
        }
        if (num) {
          let startDate = dayjs().subtract(num, 'days').format('YYYY-MM-DD');
          let endDate = dayjs().format('YYYY-MM-DD');
          this.searchParams = {
            ...this.searchParams,
            startDate: startDate,
            endDate: endDate
          };
        } else {
          this.searchParams.startDate = undefined;
          this.searchParams.endDate = undefined;
        }
        this.getList();
      },
      detail(row) {
        this.$refs.detailIndex.show(row.no);
      },

      async getList() {
        this.loading = true;
        try {
          let res = await getCheckRepairOrderPageApi({
            ...this.searchParams,
            equipmentId: this.eqId
          });
          this.list = res.data.data.records || [];
          this.total = res.data.data.total;
        } catch (e) {
          this.loading = false;
        }
        this.loading = false;
      },

      onsubmit(param) {
        this.searchParams.current = 1;
        Object.assign(this.searchParams, param);
        this.getList();
      }
    }
  };
</script>

<style scoped lang="scss">
  .table-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
  }

  .top-info {
    width: 100%;
    text-align: right;
  }
</style>
