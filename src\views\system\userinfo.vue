<template>
  <div>
    <basic-container autoHeight>
      <el-tabs v-model="index" @tab-click="handleTabClick">
        <el-tab-pane label="个人信息" name="0"></el-tab-pane>
        <el-tab-pane label="修改密码" name="1"></el-tab-pane>
      </el-tabs>

      <el-form
        ref="form"
        :rules="rules"
        v-show="index == 0"
        :model="form"
        label-width="130px"
        label-position="right"
        label-suffix="："
      >
        <el-form-item label="头像" prop="avatar">
          <upload-img
            v-model="form.avatar"
            placeholder="上传图片"
            :limit="1"
            cutImage
            formatLimit="jpeg,png,jpg"
          />
        </el-form-item>
        <el-form-item label="登录账号" prop="account">
          <el-input
            v-model.trim="form.account"
            placeholder="请输入登录账号"
            clearable
            disabled
            type="textarea"
            autosize
            :maxlength="100"
          />
        </el-form-item>
        <el-form-item label="用户姓名" prop="realName">
          <el-input
            v-model.trim="form.realName"
            placeholder="请输入用户姓名"
            clearable
            type="textarea"
            autosize
            :maxlength="20"
          />
        </el-form-item>
        <!-- <el-form-item label="用户名" prop="name">
          <el-input
            v-model.trim="form.name"
            placeholder="请输入"
            clearable
            type="textarea"
            autosize
            :maxlength="20"
          />
        </el-form-item> -->
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model.trim="form.phone"
            placeholder="请输入手机号"
            clearable
            type="textarea"
            autosize
            :maxlength="11"
          />
        </el-form-item>
        <el-form-item label="所属部门" prop="deptName">
          <el-input
            v-model.trim="form.deptName"
            placeholder="请输入所属部门"
            clearable
            disabled
            type="textarea"
            autosize
            :maxlength="100"
          />
        </el-form-item>
        <el-form-item label="所属角色" prop="roleName">
          <el-input
            v-model.trim="form.roleName"
            placeholder="请输入所属角色"
            clearable
            disabled
            type="textarea"
            autosize
            :maxlength="100"
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model.trim="form.email"
            placeholder="请输入邮箱"
            clearable
            type="textarea"
            autosize
            :maxlength="20"
          />
        </el-form-item>
      </el-form>
      <el-form
        ref="form2"
        :rules="rules2"
        v-show="index == 1"
        :model="formPassword"
        label-width="130px"
        label-position="right"
        label-suffix="："
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model.trim="formPassword.oldPassword"
            placeholder="请输入原密码"
            clearable
            type="password"
            show-password
            autosize
            :maxlength="16"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model.trim="formPassword.newPassword"
            placeholder="请输入新密码"
            clearable
            show-password
            type="password"
            autosize
            :maxlength="100"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="newPassword1">
          <el-input
            v-model.trim="formPassword.newPassword1"
            placeholder="请输入确认密码"
            clearable
            show-password
            type="password"
            autosize
            :maxlength="100"
          />
        </el-form-item>
      </el-form>
      <div style="width: 100%; height: 80px"></div>
      <div>
        <el-button
          icon="el-icon-circle-close"
          :loading="btnLoading"
          type="primary"
          @click="resetQuery"
          >清空</el-button
        >
        <el-button
          :loading="btnLoading"
          type="primary"
          icon="el-icon-circle-plus-outline"
          @click="handleSubmit"
          >保存</el-button
        >
      </div>
    </basic-container>
  </div>
</template>

<script>
  import option from '@/option/user/info';
  import Avatar from './avatar.vue';
  import reg from '@/util/regexp';
  import { mapGetters } from 'vuex';
  import { regexp } from '@/constant/common';
  import { getUserInfo, updateInfo, updatePassword } from '@/api/system/user';
  import md5 from 'js-md5';

  import UploadImg from '@/components/uploadImage.vue';
  import { getFileFullUrl } from '@/util/file';
  const form = {
    avatar: '',
    account: '',
    realName: '',
    name: '',
    phone: '',
    email: '',
    deptName: '',
    roleName: ''
  };
  const password = {
    oldPassword: '',
    newPassword: '',
    newPassword1: ''
  };
  export default {
    components: {
      UploadImg,
      Avatar
    },
    data() {
      return {
        index: '0',
        option: option,
        btnLoading: false,
        form: { ...form },
        formPassword: { ...password },
        rules: {
          realName: [
            {
              required: true,
              message: '请输入用户姓名',
              trigger: ['blur']
            }
          ],
          name: [
            {
              required: true,
              message: '请输入',
              trigger: ['blur']
            }
          ],
          phone: [
            {
              required: true,
              message: '请输入手机号',
              trigger: ['blur']
            },
            {
              pattern: reg.phone,
              message: '请输入正确格式手机号',
              trigger: 'blur'
            }
          ],
          email: [
            {
              pattern: reg.email,
              message: '请输入正确格式邮箱',
              trigger: 'blur'
            }
          ]
        },
        rules2: {
          oldPassword: [
            {
              required: true,
              message: '请输入原密码',
              trigger: ['blur']
            }
          ],
          newPassword: [
            {
              required: true,
              message: '请输入新密码',
              trigger: ['blur']
            },
            { validator: this.validatePass }
          ],
          newPassword1: [
            {
              required: true,
              message: '请输入确认密码',
              trigger: ['blur']
            },
            { validator: this.validatePass2 }
          ]
        }
      };
    },
    created() {
      this.handleWitch();
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    methods: {
      validatePass2(rule, value, callback) {
        console.log(this.formPassword.newPassword, value);
        if (value === this.formPassword.newPassword) {
          callback();
        } else {
          callback(new Error('两次输入密码不一致!'));
        }
      },
      validatePass(rule, value, callback) {
        if (value && !regexp.test(value)) {
          callback(
            new Error(
              '密码必须包含大写字母、小写字母、特殊符号、数字四种类型的8~16位字符。'
            )
          );
        } else {
          callback();
        }
      },
      resetQuery() {
        // this.$router.$avueRouter.closeTag();
        // this.$router.push(`/wel/index`);
        if (this.index == 0) {
          let { account, deptName, roleName } = this.form;
          this.form = { ...form };
          this.form.account = account;
          this.form.deptName = deptName;
          this.form.roleName = roleName;
          this.$refs.form.clearValidate();
        } else {
          this.formPassword = { ...password };
          this.$refs.form2.clearValidate();
        }
      },
      validateForm(form) {
        let bool = false;
        this.$refs[form].validate((val) => {
          bool = val;
        });
        return bool;
      },
      handleSubmit() {
        let flag = this.index == 0 ? 'form' : 'form2';
        let bool = this.validateForm(flag);
        if (!bool) return;
        if (this.index == 0) {
          updateInfo({ ...this.form, avatar: this.form.avatar[0].id }).then(
            (res) => {
              if (res.data.success) {
                let userInfo = this.userInfo;
                let { avatar, realName, phone } = this.form;
                userInfo.avatar = avatar;
                userInfo.nick_name = realName;
                userInfo.phone = phone;
                // this.form
                this.$store.commit('SET_USER_INFO', userInfo);
                this.$message({
                  type: 'success',
                  message: '修改信息成功!'
                });
              } else {
                this.$message({
                  type: 'error',
                  message: res.data.msg
                });
              }
            },
            (error) => {
              window.console.log(error);
            }
          );
        } else {
          updatePassword(
            md5(this.formPassword.oldPassword),
            md5(this.formPassword.newPassword),
            md5(this.formPassword.newPassword1)
          ).then(
            (res) => {
              if (res.data.success) {
                this.$message({
                  type: 'success',
                  message: '修改密码成功!'
                });
              } else {
                this.$message({
                  type: 'error',
                  message: res.data.msg
                });
              }
            },
            (error) => {
              window.console.log(error);
            }
          );
        }
      },
      handleWitch() {
        if (this.index == 0) {
          getUserInfo().then((res) => {
            const user = res.data.data;
            this.form = {
              id: user.id,
              // avatar: user.avatar,
              account: user.account,
              name: user.name,
              realName: user.realName,
              phone: user.phone,
              email: user.email,
              deptName: user.deptName,
              roleName: user.roleName
            };
            console.log(user);
            this.form.avatar = [
              {
                id: user.avatar,
                filePath: getFileFullUrl(user.avatar)
              }
            ];
          });
        }
      },
      handleTabClick() {
        this.$nextTick(() => {
          let flag = this.index == 0 ? 'form' : 'form2';
          this.$refs[flag].clearValidate();
        });

        this.handleWitch();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .btn-group {
    position: fixed;
    bottom: 8px;
    left: 270px;
    z-index: 5;
    box-sizing: border-box;
    width: calc(100% - 300px);
    padding: 0 20px;
    text-align: right;
    background-color: rgba(66, 78, 89, 79%);
    border-radius: 4px;

    .el-button--text {
      color: #cae4fb;
    }
  }
</style>
