<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      ref="crud"
      v-model="form"
      :permission="permissionList"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      @row-del="rowDel"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      @tree-load="treeLoad"
    >
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          v-if="permission.app_menu_delete"
          plain
          @click="handleDelete"
          >删 除
        </el-button>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click.stop="handleAdd(scope.row, scope.index)"
          v-if="
            userInfo.role_name.includes('admin') && scope.row.category === 1
          "
          >新增子项
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {
    getLazyList,
    removeCheck,
    remove,
    update,
    add,
    getMenu,
    getMenuTree
  } from '@/api/system/menu';
  import { mapGetters } from 'vuex';
  import { APP_MENU_ROOT_ID } from '@/constant/common';

  export default {
    name: 'AppMenu',
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        selectionList: [],
        parentId: APP_MENU_ROOT_ID,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        option: {
          lazy: true,
          tip: false,
          simplePage: true,
          searchShow: true,
          searchMenuSpan: 6,
          dialogWidth: '60%',
          tree: true,
          border: true,
          index: true,
          selection: true,
          viewBtn: true,
          menuWidth: 300,
          dialogClickModal: false,
          column: [
            {
              label: '菜单名称',
              prop: 'name',
              search: true,
              rules: [
                {
                  required: true,
                  message: '请输入菜单名称',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '路由地址',
              prop: 'path',
              rules: [
                {
                  required: true,
                  message: '请输入路由地址',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '上级菜单',
              prop: 'parentId',
              type: 'tree',
              dicData: [],
              hide: true,
              addDisabled: false,
              props: {
                label: 'title'
              },
              rules: [
                {
                  required: false,
                  message: '请选择上级菜单',
                  trigger: 'click'
                }
              ]
            },
            {
              label: '菜单编号',
              prop: 'code',
              search: true,
              rules: [
                {
                  required: true,
                  message: '请输入菜单编号',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '菜单类型',
              prop: 'category',
              type: 'radio',
              dicData: [
                {
                  label: '菜单',
                  value: 1
                },
                {
                  label: '按钮',
                  value: 2
                }
              ],
              hide: true,
              rules: [
                {
                  required: true,
                  message: '请选择菜单类型',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '菜单排序',
              prop: 'sort',
              type: 'number',
              span: 12,
              rules: [
                {
                  required: true,
                  message: '请输入菜单排序',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '菜单备注',
              prop: 'remark',
              type: 'textarea',
              span: 24,
              minRows: 2,
              hide: true
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(['userInfo', 'permission']),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.app_menu_add, false),
          viewBtn: this.vaildData(this.permission.app_menu_view, false),
          delBtn: this.vaildData(this.permission.app_menu_delete, false),
          editBtn: this.vaildData(this.permission.app_menu_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach((ele) => {
          ids.push(ele.id);
        });
        return ids.join(',');
      }
    },
    methods: {
      initData() {
        getMenuTree(undefined, 'APP').then((res) => {
          const column = this.findObject(this.option.column, 'parentId');
          column.dicData = res.data.data;
        });
      },
      handleAdd(row) {
        console.log(row);
        this.parentId = row.id;
        const column = this.findObject(this.option.column, 'parentId');
        column.value = row.id;
        column.addDisabled = true;
        this.$refs.crud.rowAdd();
      },
      rowSave(row, done, loading) {
        const rowValue = Object.assign({}, row, {
          type: 'APP',
          parentId: row.parentId || APP_MENU_ROOT_ID
        });
        add(rowValue).then(
          (res) => {
            // 获取新增数据的相关字段
            !row.parentId && this.onLoad(this.page);
            this.$message({
              type: 'success',
              message: '操作成功!'
            });

            const data = res.data.data;
            row.id = data.id;
            // 数据回调进行刷新
            done(row);
          },
          (error) => {
            window.console.log(error);
            loading();
          }
        );
      },
      rowUpdate(row, index, done, loading) {
        const rowValue = Object.assign({}, row, {
          type: 'APP'
        });
        update(rowValue).then(
          () => {
            this.onLoad(this.page);
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            // 数据回调进行刷新
            done(row);
          },
          (error) => {
            window.console.log(error);
            loading();
          }
        );
      },
      async rowDel(row, index, done) {
        // 检验是否有下级菜单
        const { data } = await removeCheck(row.id);
        let msg = '确定删除选择的数据吗？';
        if (!data.success) {
          msg = '当前菜单存在下级菜单，是否继续删除？';
        }
        this.$confirm(msg, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            // 数据回调进行刷新
            done(row);
          });
      },
      async handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        // 检验是否有下级菜单
        const { data } = await removeCheck(this.ids);
        let msg = '确定删除选择的数据吗？';
        if (!data.success) {
          msg = '当前菜单存在下级菜单，是否继续删除？';
        }
        this.$confirm(msg, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            // 刷新表格数据并重载
            this.data = [];
            this.parentId = APP_MENU_ROOT_ID;
            this.$refs.crud.refreshTable();
            this.$refs.crud.toggleSelection();
            // 表格数据重载
            this.onLoad(this.page);
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
          });
      },
      searchReset() {
        this.query = {};
        this.parentId = APP_MENU_ROOT_ID;
        this.$refs.crud.refreshTable();
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.parentId = APP_MENU_ROOT_ID;
        this.page.currentPage = 1;
        this.$refs.crud.refreshTable();
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      beforeOpen(done, type) {
        if (['add', 'edit'].includes(type)) {
          this.initData();
        }
        if (['edit', 'view'].includes(type)) {
          getMenu(this.form.id).then((res) => {
            this.form = res.data.data;
          });
        }
        done();
      },
      beforeClose(done) {
        this.parentId = APP_MENU_ROOT_ID;
        const column = this.findObject(this.option.column, 'parentId');
        column.value = '';
        column.addDisabled = false;
        done();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getLazyList(
          this.parentId,
          Object.assign(params, this.query),
          'APP'
        ).then((res) => {
          this.data = res.data.data;
          this.loading = false;
          this.$refs.crud.refreshTable();
          this.selectionClear();
        });
      },
      treeLoad(tree, treeNode, resolve) {
        const parentId = tree.id;
        getLazyList(parentId, {}, 'APP').then((res) => {
          resolve(res.data.data);
        });
      }
    }
  };
</script>

<style></style>
