<template>
  <el-row :gutter="10" style="background: #f2f6fc">
    <el-col style="height: 270px" :span="17">
      <Welcome :noFinishOrderObj="noFinishOrderObj" ref="welcome"></Welcome>
    </el-col>
    <el-col style="height: 270px" :span="7">
      <Entrances ref="entrances"></Entrances>
    </el-col>
    <el-col class="top" style="height: 479px; margin-top: 5px" :span="16"
      ><work-order-pie ref="pie1"></work-order-pie
    ></el-col>
    <el-col class="top" style="height: 479px; margin-top: 5px" :span="8"
      ><work-order-pie2 ref="pie2"></work-order-pie2
    ></el-col>
    <el-col class="top" style="height: 412px; margin-top: 5px" :span="16">
      <device-inspect-bar
        :curYearEquipmentArr="curYearEquipmentArr"
        ref="iBar"
      ></device-inspect-bar>
    </el-col>
    <el-col class="top" style="height: 412px; margin-top: 5px" :span="8">
      <spot-check-task ref="spot"></spot-check-task>
    </el-col>
  </el-row>
</template>
<script>
  import Welcome from './component/welcome.vue'; // 欢迎
  import Entrances from './component/commonly-used-function-entrances.vue'; // 欢迎
  import DeviceInspectBar from './component/device-inspect-bar.vue'; // 设备点巡检
  import WorkOrderPie from './component/work-order-pie.vue'; // 工单
  import WorkOrderPie2 from './component/work-order-pie2.vue'; // 工单
  import SpotCheckTask from './component/spot-check-task.vue'; //点巡检任务情况统计
  import { noFinishOrderStat, curYearEquipmentStatus } from '@/api/home';
  import { mapGetters } from 'vuex';

  export default {
    components: {
      Welcome,
      Entrances,
      DeviceInspectBar,
      WorkOrderPie,
      WorkOrderPie2,
      SpotCheckTask
    },
    data() {
      return {
        noFinishOrderObj: {},
        curYearEquipmentArr: []
      };
    },
    computed: {
      ...mapGetters(['menu'])
    },
    mounted() {
      this.noFinishOrderStat();
      this.curYearEquipmentStatus();
      this.getAllMenuCodes();
    },
    methods: {
      getAllMenuCodes() {
        let arr = [];
        this.menu.forEach((item) => {
          item.code && arr.push(item.code);
          if (item.children && item.children.length) {
            const temp = item.children.reduce((acc, cur) => {
              cur.code && acc.push(cur.code);
              return acc;
            }, []);
            arr.push(...temp);
          }
        });
        return arr;
      },
      async noFinishOrderStat() {
        try {
          let {
            data: { data }
          } = await noFinishOrderStat();
          this.noFinishOrderObj = data || {};
        } catch (error) {
          console.log(error);
        }
      },
      async curYearEquipmentStatus() {
        try {
          let {
            data: { data }
          } = await curYearEquipmentStatus();
          this.curYearEquipmentArr = data || [];
        } catch (error) {
          console.log(error);
        }
      }
    }
  };
</script>

<style lang="scss">
  .wel_icon {
    float: left;
    color: #5093fe;
    font-weight: 600;
    font-size: 24px;
  }

  .top {
    height: 400px;
  }

  .center {
    border-right: 1px dashed #efefef;
    border-left: 1px dashed #efefef;
  }

  ::v-deep {
    .basic-container__card {
      height: 100% !important;
    }
  }

  .el-row {
    position: relative;

    .entrance-img {
      position: fixed;
      right: 180px;
      bottom: 160px;
      width: 200px;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 12%), 0 0 6px rgba(0, 0, 0, 4%);
      }
    }
  }
</style>
