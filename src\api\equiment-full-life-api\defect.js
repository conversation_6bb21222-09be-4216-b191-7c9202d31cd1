import request from '@/router/axios';
// 缺陷管理相关接口
// 分页列表
export const getDefectList = (params) => {
  return request({
    url: `/api/szyk-simas/fault-defect/page`,
    method: 'get',
    params
  });
};

// 详情列表
export const getDetail = (params) => {
  return request({
    url: `/api/szyk-simas/fault-defect/view`,
    method: 'get',
    params
  });
};

// 处理接口
export const handle = (data) => {
  return request({
    url: `/api/szyk-simas/fault-defect/handle`,
    method: 'post',
    data
  });
};

// 故障缺陷案例库
export const getCaseList = (params) => {
  return request({
    url: `/api/szyk-simas/fault-defect/case/page`,
    method: 'get',
    params
  });
};

// 故障缺陷案例库查看
export const getCaseDetail = (params) => {
  return request({
    url: `/api/szyk-simas/fault-defect/case/view`,
    method: 'get',
    params
  });
};

// 新增故障缺陷
export const addDefect = (data) => {
  return request({
    url: `/api/szyk-simas/fault-defect/add`,
    method: 'post',
    data
  });
};
