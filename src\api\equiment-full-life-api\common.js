import request from '@/router/axios';

// 带角色的用户列表
export const getUserList = (params) =>
  request({
    url: '/api/szyk-system/role-user-list',
    method: 'get',
    params
  });

// 根据部门选择人员下拉列表
export const getUserListByDeptId = (params) =>
  request({
    url: '/api/szyk-system/user-list',
    method: 'get',
    params
  });

// 手动执行工单生成任务 - 润滑
export const manualLubricationTaskApi = () =>
  request({
    url: '/api/szyk-simas/lubricate/order/manual-start',
    method: 'get'
  });

// 手动执行工单生成任务 - 检修
export const manualRepairTaskApi = () =>
  request({
    url: '/api/szyk-simas/overhaul-order/manual-start',
    method: 'get'
  });

// 日志
export const getLogList = (params) =>
  request({
    url: '/api/szyk-simas/bizLog/page',
    method: 'get',
    params
  });

// 获取计量单位 列表
export const getUnitList = (params) =>
  request({
    url: '/api/szyk-common/measureunit/page',
    method: 'get',
    params
  });
// 获取供应商列表
export const getSupplierList = (params) =>
  request({
    url: '/api/szyk-common/supplier/supplier/page',
    method: 'get',
    params
  });
