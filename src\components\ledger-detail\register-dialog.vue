<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="visible"
    @closed="resetForm"
    top="5vh"
    width="740px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="details">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-suffix="："
        size="small"
        v-loading="loading"
      >
        <!-- 基本信息 -->
        <section>
          <p class="el-base-title">检验基本信息</p>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="设备编号" prop="equipmentCode">
                <el-input
                  :value="form.equipmentCode"
                  disabled
                  style="width: 220px"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备名称" prop="equipmentName">
                <el-input
                  :value="form.equipmentName"
                  disabled
                  style="width: 220px"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检验类型" prop="inspectType">
                <el-select
                  v-model="form.inspectType"
                  placeholder="请选择检验类型"
                  clearable
                  style="width: 220px"
                >
                  <el-option
                    v-for="item in serviceDicts.type['se_inspect_type']"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检验机构" prop="inspectOrg">
                <el-input
                  v-model="form.inspectOrg"
                  placeholder="请输入检验机构"
                  style="width: 220px"
                  maxlength="30"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检验日期" prop="inspectDate">
                <el-date-picker
                  v-model="form.inspectDate"
                  type="date"
                  placeholder="请选择检验日期"
                  value-format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="下次检验日期" prop="nextInspectDate">
                <el-date-picker
                  v-model="form.nextInspectDate"
                  type="date"
                  placeholder="请选择下次检验日期"
                  value-format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检验人员" prop="inspectUser">
                <el-input
                  v-model="form.inspectUser"
                  placeholder="请输入检验人员"
                  style="width: 220px"
                  maxlength="30"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="安全管理员" prop="securityAdmin">
                <el-input
                  v-model="form.securityAdmin"
                  placeholder="请输入安全管理员"
                  style="width: 220px"
                  maxlength="30"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
        <!-- 基本信息 -->
        <section>
          <p class="el-base-title">检验结论</p>
          <el-form-item label="检验结论" prop="inspectResult">
            <el-radio-group v-model="form.inspectResult">
              <el-radio
                v-for="dict in serviceDicts.type['se_inspect_result']"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="主要问题" prop="importantIssues">
            <el-input
              v-model="form.importantIssues"
              placeholder="请输入主要问题"
              type="textarea"
              :rows="3"
              maxlength="1000"
              resize="none"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="处理措施" prop="measures">
            <el-input
              v-model="form.measures"
              placeholder="请输入处理措施"
              type="textarea"
              :rows="3"
              maxlength="1000"
              resize="none"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              placeholder="请输入备注"
              type="textarea"
              :rows="3"
              maxlength="1000"
              resize="none"
            >
            </el-input>
          </el-form-item>
        </section>
        <div class="attach-wrapper">
          <div class="attach-title">
            <div class="attach-title-text">附件上传</div>
            <div class="attach-title-comment">
              (检验报告、整改报告及验证资料、其他相关证明材料等)
            </div>
          </div>
          <upload-file
            formatLimit="pdf,jpg,png,jpeg,xlsx,docx"
            accept=".pdf,.jpg,.png,.jpeg,.xlsx,.docx"
            v-model="form.attachList"
            url="/api/szyk-system/attach/put-file-attach-for-simas"
            ref="file"
            :limit="100"
          ></upload-file>
        </div>
      </el-form>
    </div>

    <div slot="footer" class="oper_btn">
      <btn type="submit" @click="handleFormSubmit" :loading="loading"></btn>
      <btn type="cancel" @click="closed"></btn>
    </div>
  </el-dialog>
</template>

<script>
  import UploadFile from '@/components/upload-file.vue';
  import {
    getInspectLogDetail,
    postInspectLog
  } from '@/api/equiment-full-life-api/special-device';
  import dayjs from 'dayjs';

  export default {
    name: 'SpecialEquipmentUseRegister',
    components: { UploadFile },
    serviceDicts: ['se_inspect_type', 'se_inspect_result'],
    data() {
      return {
        visible: false,
        loading: false,
        dialogTitle: '',
        // 使用明细
        form: {
          equipmentId: '',
          equipmentCode: '',
          equipmentName: '',
          inspectType: '',
          inspectOrg: '',
          inspectDate: '',
          nextInspectDate: '',
          inspectUser: '',
          securityAdmin: '',
          inspectResult: '',
          importantIssues: '',
          measures: '',
          remark: '',
          attachList: []
        },
        rules: {
          inspectType: [
            { required: true, message: '请选择检验类型', trigger: 'change' }
          ],
          inspectOrg: [
            { required: true, message: '请输入检验机构', trigger: 'change' }
          ],
          inspectDate: [
            { required: true, message: '请选择检验日期', trigger: 'change' }
          ],
          nextInspectDate: [
            { required: true, message: '请选择下次检验日期', trigger: 'change' }
          ],
          inspectUser: [
            { required: true, message: '请输入检验人员', trigger: 'change' }
          ],
          securityAdmin: [
            { required: true, message: '请输入安全管理员', trigger: 'change' }
          ],
          inspectResult: [
            { required: true, message: '请选择检验结论', trigger: 'change' }
          ]
        }
      };
    },
    methods: {
      async getDetail(id) {
        try {
          this.loading = true;
          const res = await getInspectLogDetail({ id });
          this.form = res.data.data;
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      show(row, isEdit) {
        console.log('row', JSON.parse(JSON.stringify(row)));
        this.visible = true;
        this.dialogTitle = isEdit ? '编辑' : '新增';
        if (isEdit) {
          row.id && this.getDetail(row.id);
        } else {
          this.form.equipmentId = row.id;
          this.form.equipmentCode = row.code;
          this.form.equipmentName = row.name;
        }
      },
      // 点击提交
      handleFormSubmit() {
        try {
          this.$refs.form.validate(async (valid) => {
            if (!valid) return;
            // 检查日期不能晚于下次检查日期
            if (
              dayjs(this.form.inspectDate) > dayjs(this.form.nextInspectDate)
            ) {
              this.$message.warning('下次检验日期不能早于检验日期');
              return;
            }
            this.loading = true;
            // 提取附件id
            const attachId = (this.form.attachList || [])
              .map((item) => {
                return item.attachId ? item.attachId : item.id;
              })
              .join(',');
            const pData = {
              ...this.form,
              attachId,
              attachList: null
            };
            await postInspectLog(pData);
            this.loading = false;
            this.$emit('refresh');
            this.$message.success('提交成功');
            this.visible = false;
          });
        } catch (e) {
          console.error(e);
        } finally {
          this.loading = false;
        }
      },
      resetForm() {
        this.visible = false;
        this.loading = false;
        this.$refs.form.resetFields();
        this.form.attachList = [];
      },
      closed() {
        this.visible = false;
      }
    }
  };
</script>

<style scoped lang="scss">
  .details {
    padding-bottom: 30px;
  }

  ::v-deep {
    .el-dialog__body {
      padding: 10px 20px;
    }

    .el-radio input[aria-hidden='true'] {
      display: none !important;
    }

    .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled)
      .el-radio__inner {
      box-shadow: none !important;
    }
  }

  .attach-wrapper {
    padding-left: 10px;

    .attach-title {
      display: flex;
      margin-bottom: 12px;
      font-size: 14px;

      &-text {
        color: #3e4553;
        font-weight: bold;
      }

      &-comment {
        margin-left: 12px;
        color: #a4a8b5;
      }
    }
  }
</style>
