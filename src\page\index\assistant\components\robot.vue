<template>
  <el-card class="message-card ai-message mb-4">
    <template #header>
      <div class="flex justify-between items-center">
        <span>回答:</span>
        <!-- <el-button
          icon="el-icon-copy-document"
          circle
          @click="copyText(message.content)"
        ></el-button> -->
      </div>
    </template>
    <!-- 文本类型 -->
    <template v-if="msg.type.includes('text') && msg.content">
      <v-md-preview :text="msg.content"></v-md-preview>
    </template>
    <!-- 表格类型 -->
    <el-table
      v-if="
        msg.type === 'table' &&
        msg.tableData.columns.length &&
        msg.tableData.rows.length
      "
      border
      size="mini"
      :data="msg.tableData.rows"
    >
      <el-table-column
        v-for="(column, index) in msg.tableData.columns"
        :key="index"
        :prop="column"
        :label="column"
      >
      </el-table-column>
    </el-table>
    <!-- 图表类型 -->
    <bar-chart
      v-if="msg.type === 'bar' && msg.chartData"
      :cdata="msg.chartData"
      class="chart-container"
    />
    <!-- 菜单跳转 -->
    <div
      v-if="msg.menuData && msg.menuData.menuCode && msg.menuData.menuName"
      class="menu-item clickable"
      @click="jumpToRoute"
    >
      >>{{ msg.menuData.menuName }}
    </div>
    <!-- 参考文档 -->
    <div v-if="msg.fileData && msg.fileData.length" class="file-wrapper">
      <div class="file-title">参考文档：</div>
      <el-tag
        class="file-item clickable"
        v-for="(file, index) in msg.fileData"
        :key="index"
        @click.native="handleFileClick(file)"
        type="success"
      >
        {{ handleSourceItemTitle(file) }}
      </el-tag>
    </div>
  </el-card>
</template>

<script>
  import { BarChart } from '../charts';
  import { previewFile, previewFileByLink } from '@/util/preview';

  export default {
    name: 'robot',
    components: { BarChart },
    props: {
      msg: {
        type: Object,
        default() {
          return {};
        }
      }
    },
    data() {
      return {};
    },
    methods: {
      // 跳转到某个路由 - name
      jumpToRoute() {
        const { menuCode, menuName, ...restParams } = this.msg.menuData;
        if (menuCode === this.$route.name) {
          setTimeout(() =>
            this.$router.push({ name: 'jumping', params: { menuName } })
          );
        }
        setTimeout(() => {
          if (['standard', 'diagnose'].includes(menuCode)) {
            this.$router.push({
              name: 'aitools',
              params: {
                type: menuCode
              }
            });
            this.$emit('close');
          } else {
            this.$router.push({
              name: menuCode,
              params: {
                fromAssist: 1,
                ...restParams
              }
            });
          }
        }, 120);
      },
      // 来源展示名称
      handleSourceItemTitle(item) {
        if (
          ['故障案例库', '设备知识库', '煤炭设备知识库'].includes(
            item.sourceType
          )
        ) {
          let caseName = item.fileName;
          if (caseName.endsWith('.')) caseName = caseName.slice(0, -1);
          return `${item.sourceType} - ${caseName}`;
        } else {
          return item.sourceType;
        }
      },
      // 下载参考文件
      handleFileClick({ sourceType, fileId, fileName, fileLink }) {
        if (sourceType === '故障案例库') {
          this.$router.push({
            name: 'fault-case',
            params: {
              id: fileId
            }
          });
        } else if (sourceType === '设备知识库') {
          if (!fileId) return this.$message.warning('文件不存在');
          // 预览
          const temp = {
            id: fileId,
            originalName: fileName,
            extension: fileName.split('.').pop()
          };
          previewFile(temp);
        } else if (sourceType === '煤炭设备知识库') {
          if (!fileLink) return this.$message.warning('文件不存在');
          // 预览
          const temp = {
            link: fileLink,
            originalName: fileName,
            extension: fileName.split('.').pop()
          };
          previewFileByLink(temp);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .message-card {
    width: 100%;
    margin-bottom: 12px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 5%);
    transition: all 0.3s ease;

    &.ai-message {
      border-left: 4px solid #67c23a;
    }

    ::v-deep .github-markdown-body {
      padding: 0;
      font-family: inherit;
      line-height: normal;

      p {
        &:last-child {
          margin-bottom: 0 !important;
        }
      }
    }

    .el-table {
      font-size: 14px;
    }

    .clickable {
      margin-top: 4px;
      color: #1373f0;
      cursor: pointer;
    }

    .menu-item {
      margin-top: 12px;
      font-size: 14px;
    }

    .file-wrapper {
      margin-top: 12px;

      .file-title {
        color: #6b7280;
        font-size: 14px;
      }

      .file-item {
        color: #67c23a;
        font-size: 12px;

        &:not(:last-child) {
          margin-right: 4px;
        }
      }
    }

    .chart-container {
      width: 100%;
    }
  }
</style>
