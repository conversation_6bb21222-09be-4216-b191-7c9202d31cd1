<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
  export default {
    name: 'app',
    data() {
      return {};
    },
    watch: {},
    created() {},
    methods: {},
    computed: {}
  };
</script>
<style lang="scss">
  #app {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .avue--detail .el-col {
    margin-bottom: 0;
  }

  // 详情页面description组件内容宽度样式
  .el-descriptions-item__content {
    min-width: 200px;
  }
</style>
