<template>
  <div>
    <el-form
      :model="form"
      inline
      label-suffix=""
      ref="listForm"
      size="small"
      :show-message="true"
    >
      <!--      选择备品备件-->
      <el-button
        @click="selAsset"
        size="small"
        type="primary"
        style="margin-bottom: 10px"
        :loading="loading"
        >+ 选择备品备件字典</el-button
      >
      <el-table
        v-loading="loading"
        class="table"
        :data="form.list"
        border
        :header-cell-style="{ background: '#fafafa' }"
        size="small"
        stripe
      >
        <el-table-column type="index" label="#"></el-table-column>
        <el-table-column
          prop="dictNo"
          label="备品备件编号"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="dictName"
          label="备品备件名称"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column prop="model" label="规格型号" show-overflow-tooltip>
          <template v-slot="{ row }">{{ row.model || '-' }}</template>
        </el-table-column>

        <el-table-column
          prop="resultName"
          label="盘点状态"
          show-overflow-tooltip
        >
          <template v-slot="{ row }">{{ row.resultName || '-' }}</template>
        </el-table-column>

        <el-table-column
          prop="beforeSystemStock"
          label="当前库存"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="inventoryUserName"
          label="盘点人员"
          show-overflow-tooltip
        >
          <template v-slot="{ row }" style="color: red">{{
            row.inventoryUserName || '-'
          }}</template>
        </el-table-column>
        <el-table-column
          prop="afterCountedStock"
          label="盘点数量"
          width="300px"
        >
          <template v-slot="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.afterCountedStock'"
              :rules="[
                {
                  required: true,
                  message: '请输入盘点数量',
                  trigger: 'blur'
                },
                {
                  validator: (rule, value, callback) =>
                    validateCode(rule, value, callback)
                }
              ]"
              label=" "
            >
              <!--              <el-input-number-->
              <!--                :disabled="!!tabActive"-->
              <!--                size="small"-->
              <!--                :controls="false"-->
              <!--                v-model="scope.row.afterCountedStock"-->
              <!--                :min="0"-->
              <!--                :max="99999"-->
              <!--                :precision="scope.row.measureUnitPrecision"-->
              <!--                placeholder="请输入盘点数量"-->
              <!--              ></el-input-number>-->
              <el-input
                :disabled="!!tabActive"
                style="width: 240px"
                size="small"
                placeholder="请输入盘点数量"
                v-model.trim="scope.row.afterCountedStock"
                clearable
                maxlength="50"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column>
          <template v-slot="scope" label="操作">
            <el-button
              :disabled="scope.row.nDelete"
              type="text"
              size="mini"
              style="color: red"
              @click="del(scope.$index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!--     选择备品备件字典-->
    <asset-list ref="assetList" @getAssetList="getAssetList"></asset-list>
  </div>
</template>

<script>
  import AssetList from '@/views/equiment-full-life-cycle/components/select-spare-parts-dialog';
  import { getSparePartsCheckItemListApi } from '@/api/equiment-full-life-api/spare-parts';
  import { mapGetters } from 'vuex';
  import { validateValueThen0 } from '@/util/func';
  export default {
    name: 'DeviceBasicList',
    components: { AssetList },

    watch: {},
    data() {
      const validateCode = (rule, value, callback) => {
        // 获取当前数组
        let arr = rule.field.split('.');
        let idx = Number(arr[1]);
        let max = 99999;
        let measureUnitPrecision = this.form.list[idx].measureUnitPrecision;
        let val = validateValueThen0(value, max, measureUnitPrecision);
        if (!value) {
          callback();
        } else if (val) {
          callback();
        } else {
          callback(
            new Error(
              `请输入大于0且小于${max}的值，精度${measureUnitPrecision}`
            )
          );
        }
      };
      return {
        validateCode,
        loading: false,
        total: 0,
        form: {
          list: []
        },
        tabActive: undefined,
        originList: [], // 原始数据list
        newList: [] // 这个存放新的添加的备件字典
      };
    },
    computed: {
      ...mapGetters(['userInfo'])
    },
    mounted() {},
    methods: {
      //  点击删除
      del(idx) {
        this.form.list.splice(idx, 1);
        this.$emit('deleteSpare');
      },
      //  选择备品备件字典
      async selAsset() {
        if (!this.tabActive) {
          this.$refs.assetList.show(this.form.list);
        } else {
          // 如果不在全部展示，先让他展示在全部上
          await this.$emit('selectDict');
        }
      },
      openSpareDialog() {
        this.$refs.assetList.show(this.form.list);
      },
      //  选择备品备件回显
      getAssetList(list) {
        this.newList = [];
        this.newList = list.filter((it) => {
          return !it.listShow;
        });

        //  接下来，就是要看，他在那个tab 选项中，主要关注的是全部和未盘点选项
        if (this.tabActive === 'NOT_START' || !this.tabActive) {
          this.form.list = list;
        }
        console.log('this.newList......', this.newList);
        //
        this.$emit('newSpareList', this.newList.length);
      },
      async getList(params) {
        console.log(params);
        this.tabActive = params.resultEnum;
        this.loading = true;
        try {
          const res = await getSparePartsCheckItemListApi(params);
          let list = res.data.data.records || [];
          this.form.list = list.map((item) => {
            return {
              ...item,
              name: item.dictName,
              no: item.dictNo,
              num: 1,
              listShow: true, // 标志列表返回
              nDelete: !!item.stockId, // 标志不能删除
              afterCountedStock: item.afterCountedStock
                ? item.afterCountedStock
                : item.beforeSystemStock
            };
          });
          if (
            this.newList.length &&
            (this.tabActive === 'NOT_START' || !this.tabActive)
          ) {
            this.newList.forEach((item) => {
              this.form.list.push(item);
            });
          }
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      },
      async validForm() {
        let valid = await this.$refs['listForm'].validate();
        if (valid) {
          let p = this.form.list.map((it) => {
            let {
              dictId,
              afterCountedStock,
              beforeSystemStock,
              id,
              inventoryOrderId,
              stockId
            } = it;
            return {
              dictId,
              afterCountedStock,
              beforeSystemStock,
              id: it.nDelete ? id : undefined,
              inventoryOrderId,
              stockId
            };
          });
          this.newList = [];
          return p;
        } else {
          return false;
        }
      },
      resetForm() {
        this.newList = [];
        this.form.list = [];
      }
    }
  };
</script>

<style scoped lang="scss"></style>
