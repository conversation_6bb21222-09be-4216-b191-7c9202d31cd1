<template>
  <el-table size="small" :data="list" border>
    <el-table-column label="#" type="index" width="50"></el-table-column>
    <el-table-column
      prop="monitorName"
      label="检修部位"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="standard"
      label="检修标准"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
    <el-table-column
      prop="methodsName"
      label="检修方式"
      align="center"
      show-overflow-tooltip
    ></el-table-column>
  </el-table>
</template>

<script>
  export default {
    name: 'bearingLibraryIndex',
    components: {},
    props: {
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {};
    },
    mounted() {},
    methods: {}
  };
</script>

<style lang="scss" scoped></style>
