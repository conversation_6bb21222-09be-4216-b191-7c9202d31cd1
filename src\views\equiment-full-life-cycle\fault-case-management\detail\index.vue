<template>
  <dialog-drawer
    title="详情"
    :visible="visible"
    @closed="closed"
    ref="drawer"
    class="device-add"
    size="80%"
  >
    <div class="details" v-loading="loading">
      <!-- 基本信息 -->
      <h2 style="text-align: center">{{ details.faultName }}</h2>
      <section>
        <span class="el-base-title">设备信息</span>
        <base-info :details="details"></base-info>
      </section>
      <section>
        <span class="el-base-title">故障缺陷信息</span>
        <fault-info :details="details"></fault-info>
      </section>
      <section>
        <span class="el-base-title">故障缺陷描述</span>
        <el-descriptions
          border
          :labelStyle="{ width: '133px', textAlign: 'right' }"
          :contentStyle="{
            width: '300px',
            wordBreak: 'break-all',
            wordWrap: 'break-word'
          }"
          contentClassName="contentClassName"
          size="small"
        >
          <el-descriptions-item label="故障缺陷描述：">
            {{ details.faultDesc || '-' }}</el-descriptions-item
          >
        </el-descriptions>
      </section>
      <section>
        <span class="el-base-title">故障缺陷原因</span>

        <el-descriptions
          border
          :labelStyle="{ width: '133px', textAlign: 'right' }"
          :contentStyle="{
            width: '300px',
            wordBreak: 'break-all',
            wordWrap: 'break-word'
          }"
          contentClassName="contentClassName"
          size="small"
        >
          <el-descriptions-item label="故障缺陷原因：">
            {{ details.faultReason || '-' }}</el-descriptions-item
          >
        </el-descriptions>
      </section>
      <section>
        <span class="el-base-title">解决方案</span>

        <el-descriptions
          border
          :labelStyle="{ width: '133px', textAlign: 'right' }"
          :contentStyle="{
            width: '300px',
            wordBreak: 'break-all',
            wordWrap: 'break-word'
          }"
          contentClassName="contentClassName"
          size="small"
        >
          <el-descriptions-item label="解决方案：">
            {{ details.solution || '-' }}</el-descriptions-item
          >
        </el-descriptions>
      </section>
      <!-- 部位信息 -->
      <section>
        <span class="el-base-title">更换零件情况</span>
        <replacement-parts
          ref="parts"
          :list="details.materialList"
        ></replacement-parts>
      </section>
    </div>
  </dialog-drawer>
</template>
<script>
  import BaseInfo from './base-info.vue';
  import FaultInfo from './fault-info.vue';
  import ReplacementParts from './replacement-parts.vue';
  import { getCaseDetail } from '@/api/equiment-full-life-api/defect';

  export default {
    name: 'RepairViewIndex',
    components: {
      BaseInfo,
      FaultInfo,
      ReplacementParts
    },
    data() {
      return {
        visible: false,
        loading: false,
        type: undefined,
        details: {
          equipmentAccount: { locationPath: '' },
          monitorStandardList: []
        } // 详情数据
      };
    },
    methods: {
      //   关闭处理
      closed() {
        this.details = {}; // 详情数据
        this.visible = false;
      },

      // 点击展示
      async show(no, type) {
        this.type = type;
        this.visible = true;
        if (no) {
          await this.getDetail(no);
        }
      },
      // 获取详情接口
      async getDetail(no) {
        try {
          this.loading = true;
          const res = await getCaseDetail({ no: no });
          this.details = res.data.data || {};
          this.loading = false;
        } catch (e) {
          this.loading = false;
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .details {
    padding-bottom: 60px;
  }

  .detail-drawer {
    ::v-deep .el-drawer__body {
      padding: 10px 10px 60px;
    }
  }

  /deep/.el-table {
    margin-top: 15px;
  }
</style>
